# SCP-Archive 系统重构设计文档

## 1. 重构概述

### 1.1 重构目标
基于现有scp-archive系统的分析，本重构方案旨在解决以下核心问题：
- **配置管理优化**：增强配置获取失败的容错处理和恢复机制
- **任务状态管理**：统一任务生命周期管理，支持暂停、恢复、失败处理
- **事件驱动架构**：引入完整的事件总线，提升系统解耦度和可扩展性
- **容错与恢复**：设计全面的故障处理和自动恢复机制
- **可观测性增强**：提供完整的监控、日志和告警能力

### 1.2 设计原则
- **安全优先**：配置异常时宁可暂停归档，避免数据风险
- **自动恢复**：系统具备自动故障检测和恢复能力
- **事件驱动**：通过事件解耦模块间依赖，提升可维护性
- **可观测性**：全链路监控，便于问题定位和运维管理
- **向后兼容**：保持现有API的兼容性，平滑迁移

## 2. 整体架构设计

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────┐
│                    API Layer                        │
│  ArchiveSdkClient, EventListener, MetricsAPI       │
├─────────────────────────────────────────────────────┤
│                Application Layer                    │
│  ConfigManager, TaskManager, EventBus, Monitor     │
├─────────────────────────────────────────────────────┤
│                  Domain Layer                       │
│  TaskScheduler, TaskExecutor, FailureHandler       │
├─────────────────────────────────────────────────────┤
│              Infrastructure Layer                   │
│  ConfigSource, DistributedLock, MetricsCollector   │
└─────────────────────────────────────────────────────┘
```

### 2.2 核心组件关系

```mermaid
graph TB
    A[ArchiveSdkClient] --> B[ConfigManager]
    A --> C[TaskManager]
    A --> D[EventBus]
    
    B --> E[ConfigSourceAggregator]
    E --> F[NacosConfigSource]
    E --> G[DubboConfigSource]
    E --> H[FileConfigSource]
    
    C --> I[TaskScheduler]
    C --> J[TaskExecutor]
    C --> K[TaskStateManager]
    
    D --> L[EventPublisher]
    D --> M[EventSubscriber]
    
    N[FailureHandler] --> O[RetryManager]
    N --> P[CircuitBreaker]
    N --> Q[FallbackManager]
```

## 3. 配置管理重构设计

### 3.1 配置管理架构

#### 3.1.1 核心组件

**ConfigManager（配置管理器）**
- 统一配置管理入口
- 配置缓存和版本管理
- 配置变更事件发布
- 配置一致性保证

**ConfigSourceAggregator（配置源聚合器）**
- 多配置源聚合和优先级管理
- 配置源故障切换
- 配置合并和冲突解决

**ConfigSource（配置源接口）**
- 统一配置获取接口
- 支持Nacos、Dubbo、File等多种实现
- 配置变更监听能力

#### 3.1.2 配置获取流程

```mermaid
sequenceDiagram
    participant CM as ConfigManager
    participant CSA as ConfigSourceAggregator
    participant NS as NacosConfigSource
    participant DS as DubboConfigSource
    participant EB as EventBus
    participant TM as TaskManager

    CM->>CSA: getConfig()
    CSA->>NS: getConfig()
    alt Nacos成功
        NS-->>CSA: config
        CSA-->>CM: config
    else Nacos失败
        CSA->>DS: getConfig()
        alt Dubbo成功
            DS-->>CSA: config
            CSA-->>CM: config
        else Dubbo失败
            CSA-->>CM: fallback config
        end
    end
    
    CM->>EB: publish ConfigChangedEvent
    EB->>TM: notify config change
```

### 3.2 配置失败处理机制

#### 3.2.1 重试策略

**RetryManager（重试管理器）**
```java
public class RetryManager {
    private final RetryPolicy retryPolicy;
    private final CircuitBreaker circuitBreaker;
    
    public <T> T executeWithRetry(String operation, Supplier<T> supplier) {
        return retryPolicy.execute(() -> {
            if (circuitBreaker.allowRequest()) {
                try {
                    T result = supplier.get();
                    circuitBreaker.recordSuccess();
                    return result;
                } catch (Exception e) {
                    circuitBreaker.recordFailure();
                    throw e;
                }
            } else {
                throw new CircuitBreakerOpenException();
            }
        });
    }
}
```

**重试配置**
- 最大重试次数：3次（可配置）
- 重试间隔：指数退避（1s, 2s, 4s）
- 重试条件：网络异常、超时异常、服务不可用
- 熔断机制：连续失败5次后熔断30秒

#### 3.2.2 降级策略

**FallbackManager（降级管理器）**
```java
public class FallbackManager {
    private final ConfigCache localCache;
    private final DefaultConfigProvider defaultProvider;
    
    public ArchiveConfig getFallbackConfig(String configKey) {
        // 1. 尝试本地缓存
        ArchiveConfig cached = localCache.get(configKey);
        if (cached != null && !isExpired(cached)) {
            return cached;
        }
        
        // 2. 使用默认配置
        return defaultProvider.getDefaultConfig(configKey);
    }
}
```

### 3.3 任务暂停与恢复机制

#### 3.3.1 任务状态管理

**TaskStateManager（任务状态管理器）**
```java
public class TaskStateManager {
    private final Map<String, TaskState> taskStates = new ConcurrentHashMap<>();
    private final EventBus eventBus;
    
    public void pauseTask(String taskId, PauseReason reason) {
        TaskState state = taskStates.get(taskId);
        if (state != null && state.canPause()) {
            state.pause(reason);
            eventBus.publish(new TaskPausedEvent(taskId, reason));
        }
    }
    
    public void resumeTask(String taskId) {
        TaskState state = taskStates.get(taskId);
        if (state != null && state.canResume()) {
            state.resume();
            eventBus.publish(new TaskResumedEvent(taskId));
        }
    }
}
```

#### 3.3.2 配置失败处理流程

```mermaid
stateDiagram-v2
    [*] --> Running
    Running --> ConfigFailed: 配置获取失败
    ConfigFailed --> Retrying: 开始重试
    Retrying --> Running: 重试成功
    Retrying --> Paused: 重试失败
    Paused --> Retrying: 配置恢复
    Paused --> [*]: 手动停止
```

## 4. 事件驱动架构设计

### 4.1 事件总线设计

#### 4.1.1 核心组件

**EventBus（事件总线）**
```java
public class EventBus {
    private final EventPublisher publisher;
    private final EventSubscriberRegistry registry;
    private final ExecutorService asyncExecutor;
    
    public void publish(ArchiveEvent event) {
        publisher.publish(event);
    }
    
    public void subscribe(EventListener listener) {
        registry.register(listener);
    }
}
```

#### 4.1.2 事件类型定义

**配置相关事件**
- `ConfigChangedEvent`：配置变更事件
- `ConfigLoadFailedEvent`：配置加载失败事件
- `ConfigRecoveredEvent`：配置恢复事件

**任务相关事件**
- `TaskStartedEvent`：任务开始事件
- `TaskCompletedEvent`：任务完成事件
- `TaskFailedEvent`：任务失败事件
- `TaskPausedEvent`：任务暂停事件
- `TaskResumedEvent`：任务恢复事件

**系统相关事件**
- `SystemStartedEvent`：系统启动事件
- `SystemShutdownEvent`：系统关闭事件
- `HealthCheckEvent`：健康检查事件

### 4.2 事件处理机制

#### 4.2.1 事件监听器

```java
@Component
public class ConfigEventListener implements EventListener {
    
    @EventHandler
    public void handleConfigChanged(ConfigChangedEvent event) {
        // 处理配置变更
        taskManager.refreshTaskConfig(event.getConfigKey());
    }
    
    @EventHandler
    public void handleConfigFailed(ConfigLoadFailedEvent event) {
        // 处理配置加载失败
        taskManager.pauseRelatedTasks(event.getConfigKey());
        alertManager.sendAlert(event);
    }
}
```

## 5. 容错与恢复机制设计

### 5.1 故障检测

#### 5.1.1 健康检查

**HealthChecker（健康检查器）**
```java
public class HealthChecker {
    private final List<HealthIndicator> indicators;
    private final ScheduledExecutorService scheduler;
    
    @PostConstruct
    public void startHealthCheck() {
        scheduler.scheduleAtFixedRate(this::performHealthCheck, 0, 30, TimeUnit.SECONDS);
    }
    
    private void performHealthCheck() {
        HealthStatus status = indicators.stream()
            .map(HealthIndicator::check)
            .reduce(HealthStatus.UP, HealthStatus::combine);
            
        eventBus.publish(new HealthCheckEvent(status));
    }
}
```

### 5.2 自动恢复

#### 5.2.1 恢复策略

**RecoveryManager（恢复管理器）**
```java
public class RecoveryManager {
    
    @EventHandler
    public void handleConfigRecovered(ConfigRecoveredEvent event) {
        // 配置恢复后，自动恢复相关任务
        List<String> pausedTasks = taskStateManager.getPausedTasksByReason(
            PauseReason.CONFIG_FAILURE);
        
        for (String taskId : pausedTasks) {
            taskStateManager.resumeTask(taskId);
        }
    }
}
```

## 6. 任务调度与执行重构设计

### 6.1 任务调度器重构

#### 6.1.1 调度器架构

**TaskScheduler（任务调度器）**
```java
public class TaskScheduler {
    private final TaskRegistry taskRegistry;
    private final TaskStateManager stateManager;
    private final SchedulingStrategy strategy;
    private final EventBus eventBus;
    private final DistributedLock distributedLock;

    public void scheduleTask(TaskConfig taskConfig) {
        String taskId = taskConfig.getTaskId();

        // 1. 检查任务状态
        if (!stateManager.canSchedule(taskId)) {
            log.warn("Task cannot be scheduled: {}", taskId);
            return;
        }

        // 2. 获取分布式锁
        String lockKey = "schedule_" + taskId;
        if (!distributedLock.tryLock(lockKey, Duration.ofMinutes(5))) {
            log.info("Task already being scheduled by another instance: {}", taskId);
            return;
        }

        try {
            // 3. 执行调度策略
            ScheduleResult result = strategy.schedule(taskConfig);

            // 4. 更新任务状态
            stateManager.updateScheduleTime(taskId, result.getNextExecuteTime());

            // 5. 发布调度事件
            eventBus.publish(new TaskScheduledEvent(taskId, result));

        } finally {
            distributedLock.unlock(lockKey);
        }
    }
}
```

#### 6.1.2 调度策略

**SchedulingStrategy（调度策略接口）**
```java
public interface SchedulingStrategy {
    ScheduleResult schedule(TaskConfig taskConfig);
    boolean canExecute(TaskConfig taskConfig);
    long calculateNextExecuteTime(TaskConfig taskConfig);
}

// 时间窗口调度策略
public class TimeWindowSchedulingStrategy implements SchedulingStrategy {

    @Override
    public ScheduleResult schedule(TaskConfig taskConfig) {
        long currentTime = System.currentTimeMillis();

        if (isInExecutionWindow(taskConfig, currentTime)) {
            return ScheduleResult.executeNow();
        } else {
            long nextTime = calculateNextWindowTime(taskConfig);
            return ScheduleResult.scheduleAt(nextTime);
        }
    }

    private boolean isInExecutionWindow(TaskConfig taskConfig, long currentTime) {
        if (taskConfig.getExecutionType() == ExecutionType.ALL_DAY) {
            return true;
        }

        LocalTime now = LocalTime.now();
        LocalTime startTime = LocalTime.parse(taskConfig.getStartTime());
        LocalTime endTime = LocalTime.parse(taskConfig.getEndTime());

        return isTimeInRange(now, startTime, endTime);
    }
}

// 负载均衡调度策略
public class LoadBalancingSchedulingStrategy implements SchedulingStrategy {
    private final LoadBalancer loadBalancer;

    @Override
    public ScheduleResult schedule(TaskConfig taskConfig) {
        // 根据系统负载决定是否执行
        if (loadBalancer.isOverloaded()) {
            long delayTime = loadBalancer.calculateDelay();
            return ScheduleResult.scheduleAt(System.currentTimeMillis() + delayTime);
        }

        return ScheduleResult.executeNow();
    }
}
```

### 6.2 任务执行器重构

#### 6.2.1 执行器架构

**TaskExecutor（任务执行器）**
```java
public class TaskExecutor {
    private final ExecutorService executorService;
    private final TaskStateManager stateManager;
    private final CheckpointManager checkpointManager;
    private final EventBus eventBus;
    private final MetricsCollector metricsCollector;

    public CompletableFuture<TaskExecutionResult> executeTask(TaskConfig taskConfig) {
        String taskId = taskConfig.getTaskId();

        return CompletableFuture.supplyAsync(() -> {
            // 1. 前置检查
            if (!preExecutionCheck(taskConfig)) {
                return TaskExecutionResult.failed("Pre-execution check failed");
            }

            // 2. 更新任务状态
            stateManager.updateState(taskId, TaskState.RUNNING);
            eventBus.publish(new TaskStartedEvent(taskId));

            try {
                // 3. 执行任务
                TaskExecutionResult result = doExecuteTask(taskConfig);

                // 4. 处理执行结果
                handleExecutionResult(taskConfig, result);

                return result;

            } catch (Exception e) {
                // 5. 异常处理
                TaskExecutionResult errorResult = handleExecutionError(taskConfig, e);
                return errorResult;

            } finally {
                // 6. 清理资源
                cleanupResources(taskConfig);
            }

        }, executorService);
    }

    private TaskExecutionResult doExecuteTask(TaskConfig taskConfig) {
        String taskId = taskConfig.getTaskId();
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1. 创建执行上下文
            TaskExecutionContext context = createExecutionContext(taskConfig);

            // 2. 加载断点信息
            loadCheckpoint(taskConfig, context);

            // 3. 执行归档逻辑
            ArchiveResult archiveResult = executeArchiveLogic(taskConfig, context);

            // 4. 保存断点信息
            saveCheckpoint(taskConfig, context, archiveResult);

            // 5. 更新执行统计
            updateExecutionMetrics(taskConfig, archiveResult, stopwatch.elapsed());

            return TaskExecutionResult.success(archiveResult);

        } catch (Exception e) {
            log.error("Task execution failed: {}", taskId, e);
            throw new TaskExecutionException("Task execution failed", e);
        }
    }
}
```

#### 6.2.2 执行上下文

**TaskExecutionContext（任务执行上下文）**
```java
public class TaskExecutionContext {
    private final String taskId;
    private final TaskConfig taskConfig;
    private final DataSource dataSource;
    private final ArchiveCheckpoint checkpoint;
    private final Map<String, Object> attributes;
    private final ExecutionMetrics metrics;

    // 上下文状态管理
    private volatile boolean cancelled = false;
    private volatile boolean paused = false;

    public void cancel() {
        this.cancelled = true;
    }

    public void pause() {
        this.paused = true;
    }

    public boolean isCancelled() {
        return cancelled;
    }

    public boolean isPaused() {
        return paused;
    }
}
```

### 6.3 任务生命周期管理

#### 6.3.1 状态机设计

```mermaid
stateDiagram-v2
    [*] --> Created
    Created --> Scheduled: schedule()
    Scheduled --> Running: execute()
    Running --> Completed: success
    Running --> Failed: error
    Running --> Paused: pause()
    Paused --> Running: resume()
    Paused --> Cancelled: cancel()
    Failed --> Scheduled: retry()
    Completed --> [*]
    Cancelled --> [*]
    Failed --> [*]: max retries exceeded
```

#### 6.3.2 状态管理器

**TaskStateManager（任务状态管理器）**
```java
public class TaskStateManager {
    private final Map<String, TaskState> taskStates = new ConcurrentHashMap<>();
    private final EventBus eventBus;
    private final TaskStateStorage storage;

    public void updateState(String taskId, TaskState newState) {
        TaskState oldState = taskStates.get(taskId);

        // 验证状态转换是否合法
        if (!isValidTransition(oldState, newState)) {
            throw new IllegalStateTransitionException(
                String.format("Invalid state transition: %s -> %s", oldState, newState));
        }

        // 更新状态
        taskStates.put(taskId, newState);
        storage.saveTaskState(taskId, newState);

        // 发布状态变更事件
        eventBus.publish(new TaskStateChangedEvent(taskId, oldState, newState));
    }

    public boolean canSchedule(String taskId) {
        TaskState state = taskStates.get(taskId);
        return state == null || state == TaskState.COMPLETED || state == TaskState.FAILED;
    }

    public boolean canExecute(String taskId) {
        TaskState state = taskStates.get(taskId);
        return state == TaskState.SCHEDULED;
    }

    public void pauseTask(String taskId, PauseReason reason) {
        TaskState currentState = taskStates.get(taskId);
        if (currentState == TaskState.RUNNING) {
            TaskState pausedState = TaskState.paused(reason);
            updateState(taskId, pausedState);
        }
    }

    public void resumeTask(String taskId) {
        TaskState currentState = taskStates.get(taskId);
        if (currentState != null && currentState.isPaused()) {
            updateState(taskId, TaskState.SCHEDULED);
        }
    }
}
```

## 7. 事件驱动架构详细设计

### 7.1 事件总线核心实现

#### 7.1.1 事件总线接口

**EventBus（事件总线接口）**
```java
public interface EventBus {
    void publish(ArchiveEvent event);
    void publishAsync(ArchiveEvent event);
    void subscribe(EventListener listener);
    void unsubscribe(EventListener listener);
    void shutdown();
}
```

#### 7.1.2 事件总线实现

**DefaultEventBus（默认事件总线实现）**
```java
@Component
public class DefaultEventBus implements EventBus {
    private final EventPublisher publisher;
    private final EventSubscriberRegistry registry;
    private final ExecutorService asyncExecutor;
    private final EventFilter eventFilter;
    private final MetricsCollector metricsCollector;

    @Override
    public void publish(ArchiveEvent event) {
        try {
            // 1. 事件过滤
            if (!eventFilter.shouldProcess(event)) {
                return;
            }

            // 2. 记录指标
            metricsCollector.recordEventPublished(event.getEventType());

            // 3. 同步发布
            publisher.publish(event);

        } catch (Exception e) {
            log.error("Failed to publish event: {}", event, e);
            metricsCollector.recordEventError(event.getEventType());
        }
    }

    @Override
    public void publishAsync(ArchiveEvent event) {
        asyncExecutor.submit(() -> publish(event));
    }

    @Override
    public void subscribe(EventListener listener) {
        registry.register(listener);
    }
}
```

### 7.2 事件类型体系

#### 7.2.1 事件基类

**ArchiveEvent（归档事件基类）**
```java
public abstract class ArchiveEvent {
    private final String eventId;
    private final String eventType;
    private final long timestamp;
    private final String source;
    private final Map<String, Object> attributes;

    protected ArchiveEvent(String eventType, String source) {
        this.eventId = UUID.randomUUID().toString();
        this.eventType = eventType;
        this.timestamp = System.currentTimeMillis();
        this.source = source;
        this.attributes = new ConcurrentHashMap<>();
    }

    public abstract EventPriority getPriority();
    public abstract boolean isRetryable();
}
```

#### 7.2.2 配置事件

**ConfigChangedEvent（配置变更事件）**
```java
public class ConfigChangedEvent extends ArchiveEvent {
    private final String configKey;
    private final Object oldValue;
    private final Object newValue;
    private final ConfigChangeType changeType;

    public ConfigChangedEvent(String configKey, Object oldValue, Object newValue) {
        super("CONFIG_CHANGED", "ConfigManager");
        this.configKey = configKey;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.changeType = determineChangeType(oldValue, newValue);
    }

    @Override
    public EventPriority getPriority() {
        return EventPriority.HIGH;
    }

    @Override
    public boolean isRetryable() {
        return false;
    }
}

public class ConfigLoadFailedEvent extends ArchiveEvent {
    private final String configKey;
    private final Exception cause;
    private final int retryCount;

    public ConfigLoadFailedEvent(String configKey, Exception cause, int retryCount) {
        super("CONFIG_LOAD_FAILED", "ConfigManager");
        this.configKey = configKey;
        this.cause = cause;
        this.retryCount = retryCount;
    }

    @Override
    public EventPriority getPriority() {
        return EventPriority.CRITICAL;
    }
}
```

### 7.3 事件监听器机制

#### 7.3.1 事件监听器接口

**EventListener（事件监听器接口）**
```java
public interface EventListener {
    void onEvent(ArchiveEvent event);
    boolean supports(Class<? extends ArchiveEvent> eventType);
    int getOrder();
}

// 注解驱动的事件监听器
@Component
public class ArchiveEventListener {

    @EventHandler(priority = EventPriority.HIGH)
    public void handleConfigChanged(ConfigChangedEvent event) {
        log.info("Config changed: {} -> {}", event.getOldValue(), event.getNewValue());
        // 处理配置变更逻辑
    }

    @EventHandler(async = true)
    public void handleTaskCompleted(TaskCompletedEvent event) {
        // 异步处理任务完成事件
        metricsCollector.recordTaskCompletion(event);
    }

    @EventHandler(condition = "event.retryCount > 3")
    public void handleConfigLoadFailed(ConfigLoadFailedEvent event) {
        // 只处理重试次数超过3次的配置加载失败事件
        alertManager.sendCriticalAlert(event);
    }
}
```

#### 7.3.2 事件订阅注册表

**EventSubscriberRegistry（事件订阅注册表）**
```java
@Component
public class EventSubscriberRegistry {
    private final Map<Class<? extends ArchiveEvent>, List<EventListener>> subscribers = new ConcurrentHashMap<>();
    private final EventListenerFactory listenerFactory;

    public void register(EventListener listener) {
        // 扫描监听器支持的事件类型
        Set<Class<? extends ArchiveEvent>> supportedEvents = scanSupportedEvents(listener);

        for (Class<? extends ArchiveEvent> eventType : supportedEvents) {
            subscribers.computeIfAbsent(eventType, k -> new ArrayList<>()).add(listener);
        }

        // 按优先级排序
        subscribers.values().forEach(this::sortByPriority);
    }

    public List<EventListener> getListeners(Class<? extends ArchiveEvent> eventType) {
        return subscribers.getOrDefault(eventType, Collections.emptyList());
    }
}
```

## 8. 容错与恢复机制详细设计

### 8.1 故障检测与分类

#### 8.1.1 故障类型定义

**FailureType（故障类型枚举）**
```java
public enum FailureType {
    CONFIG_FAILURE("配置获取失败", true, RetryStrategy.EXPONENTIAL_BACKOFF),
    NETWORK_FAILURE("网络连接失败", true, RetryStrategy.FIXED_INTERVAL),
    DATABASE_FAILURE("数据库连接失败", true, RetryStrategy.EXPONENTIAL_BACKOFF),
    BUSINESS_FAILURE("业务逻辑失败", false, RetryStrategy.NONE),
    RESOURCE_EXHAUSTION("资源耗尽", true, RetryStrategy.LINEAR_BACKOFF),
    TIMEOUT_FAILURE("执行超时", true, RetryStrategy.FIXED_INTERVAL);

    private final String description;
    private final boolean retryable;
    private final RetryStrategy defaultRetryStrategy;
}
```

#### 8.1.2 故障检测器

**FailureDetector（故障检测器）**
```java
@Component
public class FailureDetector {
    private final List<HealthIndicator> healthIndicators;
    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final EventBus eventBus;

    @Scheduled(fixedDelay = 30000) // 每30秒检测一次
    public void detectFailures() {
        for (HealthIndicator indicator : healthIndicators) {
            try {
                HealthStatus status = indicator.check();

                if (status.isDown()) {
                    FailureEvent failureEvent = new FailureEvent(
                        indicator.getName(),
                        status.getFailureType(),
                        status.getException()
                    );

                    eventBus.publish(failureEvent);
                }

            } catch (Exception e) {
                log.error("Health check failed for: {}", indicator.getName(), e);
            }
        }
    }

    public void reportFailure(String component, FailureType failureType, Exception cause) {
        // 更新熔断器状态
        CircuitBreaker circuitBreaker = circuitBreakerRegistry.get(component);
        circuitBreaker.recordFailure();

        // 发布故障事件
        FailureEvent event = new FailureEvent(component, failureType, cause);
        eventBus.publish(event);
    }
}
```

### 8.2 重试机制设计

#### 8.2.1 重试策略

**RetryStrategy（重试策略接口）**
```java
public interface RetryStrategy {
    long calculateDelay(int attemptNumber);
    boolean shouldRetry(int attemptNumber, Exception lastException);
    int getMaxAttempts();
}

// 指数退避重试策略
public class ExponentialBackoffRetryStrategy implements RetryStrategy {
    private final long initialDelay;
    private final long maxDelay;
    private final double multiplier;
    private final int maxAttempts;

    @Override
    public long calculateDelay(int attemptNumber) {
        long delay = (long) (initialDelay * Math.pow(multiplier, attemptNumber - 1));
        return Math.min(delay, maxDelay);
    }

    @Override
    public boolean shouldRetry(int attemptNumber, Exception lastException) {
        return attemptNumber < maxAttempts && isRetryableException(lastException);
    }
}

// 固定间隔重试策略
public class FixedIntervalRetryStrategy implements RetryStrategy {
    private final long interval;
    private final int maxAttempts;

    @Override
    public long calculateDelay(int attemptNumber) {
        return interval;
    }
}
```

#### 8.2.2 重试执行器

**RetryExecutor（重试执行器）**
```java
@Component
public class RetryExecutor {
    private final ScheduledExecutorService scheduler;
    private final EventBus eventBus;

    public <T> CompletableFuture<T> executeWithRetry(
            String operationName,
            Supplier<T> operation,
            RetryStrategy retryStrategy) {

        CompletableFuture<T> future = new CompletableFuture<>();
        executeAttempt(operationName, operation, retryStrategy, 1, future);
        return future;
    }

    private <T> void executeAttempt(
            String operationName,
            Supplier<T> operation,
            RetryStrategy retryStrategy,
            int attemptNumber,
            CompletableFuture<T> future) {

        try {
            T result = operation.get();
            future.complete(result);

            // 发布重试成功事件
            if (attemptNumber > 1) {
                eventBus.publish(new RetrySuccessEvent(operationName, attemptNumber));
            }

        } catch (Exception e) {
            if (retryStrategy.shouldRetry(attemptNumber, e)) {
                // 计算下次重试延迟
                long delay = retryStrategy.calculateDelay(attemptNumber);

                // 发布重试事件
                eventBus.publish(new RetryAttemptEvent(operationName, attemptNumber, delay));

                // 调度下次重试
                scheduler.schedule(
                    () -> executeAttempt(operationName, operation, retryStrategy, attemptNumber + 1, future),
                    delay,
                    TimeUnit.MILLISECONDS
                );
            } else {
                // 重试失败，发布失败事件
                eventBus.publish(new RetryFailedEvent(operationName, attemptNumber, e));
                future.completeExceptionally(e);
            }
        }
    }
}
```

### 8.3 熔断器机制

#### 8.3.1 熔断器实现

**CircuitBreaker（熔断器）**
```java
public class CircuitBreaker {
    private final String name;
    private final int failureThreshold;
    private final long timeoutDuration;
    private final long retryTimeout;

    private volatile CircuitBreakerState state = CircuitBreakerState.CLOSED;
    private final AtomicInteger failureCount = new AtomicInteger(0);
    private volatile long lastFailureTime = 0;

    public boolean allowRequest() {
        switch (state) {
            case CLOSED:
                return true;
            case OPEN:
                if (System.currentTimeMillis() - lastFailureTime >= retryTimeout) {
                    state = CircuitBreakerState.HALF_OPEN;
                    return true;
                }
                return false;
            case HALF_OPEN:
                return true;
            default:
                return false;
        }
    }

    public void recordSuccess() {
        failureCount.set(0);
        state = CircuitBreakerState.CLOSED;
    }

    public void recordFailure() {
        int failures = failureCount.incrementAndGet();
        lastFailureTime = System.currentTimeMillis();

        if (failures >= failureThreshold) {
            state = CircuitBreakerState.OPEN;
        }
    }
}

enum CircuitBreakerState {
    CLOSED,    // 正常状态
    OPEN,      // 熔断状态
    HALF_OPEN  // 半开状态
}
```

### 8.4 任务恢复机制

#### 8.4.1 任务恢复管理器

**TaskRecoveryManager（任务恢复管理器）**
```java
@Component
public class TaskRecoveryManager {
    private final TaskStateManager taskStateManager;
    private final ConfigManager configManager;
    private final EventBus eventBus;
    private final RetryExecutor retryExecutor;

    @EventHandler
    public void handleConfigRecovered(ConfigRecoveredEvent event) {
        String configKey = event.getConfigKey();

        // 查找因配置失败而暂停的任务
        List<String> pausedTasks = taskStateManager.getPausedTasksByReason(
            PauseReason.CONFIG_FAILURE, configKey);

        for (String taskId : pausedTasks) {
            recoverTask(taskId);
        }
    }

    @EventHandler
    public void handleSystemRecovered(SystemRecoveredEvent event) {
        // 系统恢复后，尝试恢复所有暂停的任务
        List<String> pausedTasks = taskStateManager.getAllPausedTasks();

        for (String taskId : pausedTasks) {
            scheduleTaskRecovery(taskId);
        }
    }

    private void recoverTask(String taskId) {
        retryExecutor.executeWithRetry(
            "recover-task-" + taskId,
            () -> {
                // 验证任务配置是否可用
                TaskConfig taskConfig = configManager.getTaskConfig(taskId);
                if (taskConfig == null) {
                    throw new TaskRecoveryException("Task config not found: " + taskId);
                }

                // 验证依赖资源是否可用
                validateTaskDependencies(taskConfig);

                // 恢复任务
                taskStateManager.resumeTask(taskId);

                // 发布任务恢复事件
                eventBus.publish(new TaskRecoveredEvent(taskId));

                return true;
            },
            new ExponentialBackoffRetryStrategy(1000, 30000, 2.0, 5)
        );
    }

    private void validateTaskDependencies(TaskConfig taskConfig) {
        // 验证数据源连接
        DataSource dataSource = dataSourceManager.getDataSource(taskConfig.getDataSourceName());
        if (!dataSource.isAvailable()) {
            throw new TaskRecoveryException("DataSource not available: " + taskConfig.getDataSourceName());
        }

        // 验证其他依赖...
    }
}
```

### 8.5 断点续传增强

#### 8.5.1 增强的断点管理器

**EnhancedCheckpointManager（增强断点管理器）**
```java
@Component
public class EnhancedCheckpointManager extends CheckpointManager {
    private final CheckpointStorage checkpointStorage;
    private final CheckpointValidator checkpointValidator;
    private final EventBus eventBus;

    @Override
    public boolean loadCheckpoint(String taskName, ArchiveNode archiveNode, ArchiveContext context) {
        try {
            // 1. 加载断点数据
            ArchiveCheckpoint checkpoint = checkpointStorage.loadCheckpoint(taskName);
            if (checkpoint == null) {
                return false;
            }

            // 2. 验证断点有效性
            CheckpointValidationResult validation = checkpointValidator.validate(checkpoint, archiveNode);
            if (!validation.isValid()) {
                log.warn("Invalid checkpoint detected: {}, reason: {}", taskName, validation.getReason());

                // 发布断点无效事件
                eventBus.publish(new CheckpointInvalidEvent(taskName, validation.getReason()));

                // 清理无效断点
                checkpointStorage.removeCheckpoint(taskName);
                return false;
            }

            // 3. 应用断点
            context.setArchiveCheckpoint(checkpoint);
            buildCheckpointConditions(checkpoint, context);

            // 4. 发布断点加载事件
            eventBus.publish(new CheckpointLoadedEvent(taskName, checkpoint));

            return true;

        } catch (Exception e) {
            log.error("Failed to load checkpoint: {}", taskName, e);
            eventBus.publish(new CheckpointLoadFailedEvent(taskName, e));
            return false;
        }
    }

    @Override
    public void saveCheckpoint(ArchiveNode archiveNode, ArchiveContext context, List<Map<String, Object>> queryResults) {
        try {
            if (CollectionUtils.isEmpty(queryResults)) {
                return;
            }

            // 1. 创建断点
            ArchiveCheckpoint checkpoint = createCheckpoint(archiveNode, context, queryResults);

            // 2. 验证断点
            CheckpointValidationResult validation = checkpointValidator.validate(checkpoint, archiveNode);
            if (!validation.isValid()) {
                log.warn("Generated invalid checkpoint: {}, reason: {}", context.getTaskName(), validation.getReason());
                return;
            }

            // 3. 保存断点
            checkpointStorage.saveCheckpoint(checkpoint, context);

            // 4. 发布断点保存事件
            eventBus.publish(new CheckpointSavedEvent(context.getTaskName(), checkpoint));

        } catch (Exception e) {
            log.error("Failed to save checkpoint: {}", context.getTaskName(), e);
            eventBus.publish(new CheckpointSaveFailedEvent(context.getTaskName(), e));
        }
    }
}
```

### 8.6 故障恢复流程

#### 8.6.1 故障恢复协调器

**FailureRecoveryCoordinator（故障恢复协调器）**
```java
@Component
public class FailureRecoveryCoordinator {
    private final Map<FailureType, FailureRecoveryStrategy> recoveryStrategies;
    private final EventBus eventBus;

    @EventHandler
    public void handleFailureEvent(FailureEvent event) {
        FailureType failureType = event.getFailureType();
        FailureRecoveryStrategy strategy = recoveryStrategies.get(failureType);

        if (strategy != null) {
            strategy.recover(event);
        } else {
            log.warn("No recovery strategy found for failure type: {}", failureType);
        }
    }
}

// 配置失败恢复策略
@Component
public class ConfigFailureRecoveryStrategy implements FailureRecoveryStrategy {

    @Override
    public void recover(FailureEvent event) {
        String component = event.getComponent();

        // 1. 暂停相关任务
        pauseRelatedTasks(component);

        // 2. 启动配置恢复检测
        startConfigRecoveryDetection(component);

        // 3. 发送告警
        sendAlert(event);
    }

    private void startConfigRecoveryDetection(String component) {
        // 定期检测配置是否恢复
        scheduler.scheduleAtFixedRate(() -> {
            try {
                // 尝试重新获取配置
                configManager.refreshConfig(component);

                // 配置恢复成功
                eventBus.publish(new ConfigRecoveredEvent(component));

            } catch (Exception e) {
                // 配置仍然不可用，继续等待
                log.debug("Config still unavailable: {}", component);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
}
```

## 9. 监控与告警系统

### 9.1 监控指标体系

#### 9.1.1 核心指标定义

**ArchiveMetrics（归档指标）**
```java
public class ArchiveMetrics {
    // 任务执行指标
    private final Counter taskExecutionCount;
    private final Timer taskExecutionDuration;
    private final Gauge activeTaskCount;
    private final Counter taskFailureCount;

    // 配置相关指标
    private final Counter configLoadCount;
    private final Counter configLoadFailureCount;
    private final Timer configLoadDuration;

    // 系统资源指标
    private final Gauge memoryUsage;
    private final Gauge cpuUsage;
    private final Gauge databaseConnectionCount;

    // 业务指标
    private final Counter archivedRecordCount;
    private final Counter deletedRecordCount;
    private final Gauge checkpointCount;
}
```

#### 9.1.2 指标收集器

**MetricsCollector（指标收集器）**
```java
@Component
public class MetricsCollector {
    private final MeterRegistry meterRegistry;
    private final EventBus eventBus;

    public void recordTaskExecution(String taskId, Duration duration, boolean success) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("archive.task.execution")
            .tag("task_id", taskId)
            .tag("success", String.valueOf(success))
            .register(meterRegistry));

        if (success) {
            Counter.builder("archive.task.success")
                .tag("task_id", taskId)
                .register(meterRegistry)
                .increment();
        } else {
            Counter.builder("archive.task.failure")
                .tag("task_id", taskId)
                .register(meterRegistry)
                .increment();
        }
    }

    public void recordConfigLoad(String configKey, Duration duration, boolean success) {
        Timer.builder("archive.config.load")
            .tag("config_key", configKey)
            .tag("success", String.valueOf(success))
            .register(meterRegistry)
            .record(duration);
    }

    public void recordArchivedRecords(String taskId, long count) {
        Counter.builder("archive.records.archived")
            .tag("task_id", taskId)
            .register(meterRegistry)
            .increment(count);
    }
}
```

### 9.2 告警系统

#### 9.2.1 告警规则引擎

**AlertRuleEngine（告警规则引擎）**
```java
@Component
public class AlertRuleEngine {
    private final List<AlertRule> alertRules;
    private final AlertManager alertManager;
    private final EventBus eventBus;

    @EventHandler
    public void evaluateAlerts(ArchiveEvent event) {
        for (AlertRule rule : alertRules) {
            if (rule.matches(event)) {
                AlertContext context = AlertContext.builder()
                    .event(event)
                    .rule(rule)
                    .timestamp(System.currentTimeMillis())
                    .build();

                if (rule.shouldTrigger(context)) {
                    Alert alert = rule.createAlert(context);
                    alertManager.sendAlert(alert);
                }
            }
        }
    }
}

// 告警规则示例
@Component
public class ConfigFailureAlertRule implements AlertRule {

    @Override
    public boolean matches(ArchiveEvent event) {
        return event instanceof ConfigLoadFailedEvent;
    }

    @Override
    public boolean shouldTrigger(AlertContext context) {
        ConfigLoadFailedEvent event = (ConfigLoadFailedEvent) context.getEvent();
        return event.getRetryCount() >= 3; // 重试3次后告警
    }

    @Override
    public Alert createAlert(AlertContext context) {
        ConfigLoadFailedEvent event = (ConfigLoadFailedEvent) context.getEvent();

        return Alert.builder()
            .level(AlertLevel.CRITICAL)
            .title("配置加载失败")
            .message(String.format("配置 %s 加载失败，已重试 %d 次",
                event.getConfigKey(), event.getRetryCount()))
            .source("scp-archive")
            .timestamp(context.getTimestamp())
            .build();
    }
}
```

#### 9.2.2 告警管理器

**AlertManager（告警管理器）**
```java
@Component
public class AlertManager {
    private final List<AlertChannel> alertChannels;
    private final AlertDeduplicator deduplicator;
    private final AlertStorage alertStorage;

    public void sendAlert(Alert alert) {
        // 1. 去重检查
        if (deduplicator.isDuplicate(alert)) {
            log.debug("Duplicate alert ignored: {}", alert);
            return;
        }

        // 2. 存储告警
        alertStorage.save(alert);

        // 3. 发送告警
        for (AlertChannel channel : alertChannels) {
            if (channel.supports(alert.getLevel())) {
                try {
                    channel.send(alert);
                } catch (Exception e) {
                    log.error("Failed to send alert via channel: {}", channel.getName(), e);
                }
            }
        }
    }
}

// 钉钉告警通道
@Component
public class DingTalkAlertChannel implements AlertChannel {
    private final DingTalkClient dingTalkClient;

    @Override
    public boolean supports(AlertLevel level) {
        return level == AlertLevel.CRITICAL || level == AlertLevel.HIGH;
    }

    @Override
    public void send(Alert alert) {
        DingTalkMessage message = DingTalkMessage.builder()
            .title(alert.getTitle())
            .text(alert.getMessage())
            .build();

        dingTalkClient.send(message);
    }
}
```

## 10. 部署与迁移方案

### 10.1 渐进式迁移策略

#### 10.1.1 迁移阶段规划

**阶段一：基础设施准备**
- 部署新的配置管理组件
- 建立事件总线基础设施
- 配置监控和告警系统

**阶段二：配置管理迁移**
- 启用新的配置管理系统
- 保持与旧系统的兼容性
- 逐步迁移配置数据

**阶段三：任务调度迁移**
- 部署新的任务调度器
- 并行运行新旧调度器
- 逐步切换任务到新调度器

**阶段四：完整切换**
- 停用旧系统组件
- 清理遗留代码和配置
- 性能优化和调优

#### 10.1.2 兼容性适配器

**LegacyCompatibilityAdapter（遗留系统兼容适配器）**
```java
@Component
public class LegacyCompatibilityAdapter {
    private final LegacyConfigManager legacyConfigManager;
    private final NewConfigManager newConfigManager;
    private final MigrationConfig migrationConfig;

    public ArchiveDataConfig getConfig(String configKey) {
        if (migrationConfig.isNewSystemEnabled(configKey)) {
            return newConfigManager.getConfig(configKey);
        } else {
            return legacyConfigManager.getConfig(configKey);
        }
    }

    public void scheduleTask(TaskConfig taskConfig) {
        if (migrationConfig.isTaskMigrated(taskConfig.getTaskId())) {
            newTaskScheduler.scheduleTask(taskConfig);
        } else {
            legacyTaskScheduler.scheduleTask(taskConfig);
        }
    }
}
```

### 10.2 部署配置

#### 10.2.1 应用配置

```yaml
scp:
  archive:
    # 基础配置
    enabled: true
    application-name: "scp-archive"
    environment: "production"

    # 配置管理
    config:
      sources:
        - type: nacos
          servers: "nacos-cluster:8848"
          group: "archive-platform"
          priority: 1
        - type: dubbo
          registry: "nacos://nacos-cluster:8848"
          timeout: 30000
          priority: 2
        - type: file
          path: "/etc/scp-archive/fallback.yml"
          priority: 3

      retry:
        max-attempts: 3
        initial-delay: 1000
        max-delay: 30000
        multiplier: 2.0

    # 任务调度
    scheduler:
      thread-pool-size: 10
      max-concurrent-tasks: 50
      scheduling-interval: 30

    # 事件总线
    event-bus:
      async-executor:
        core-pool-size: 5
        max-pool-size: 20
        queue-capacity: 1000

    # 监控配置
    monitoring:
      enabled: true
      metrics-interval: 60
      health-check-interval: 30

    # 告警配置
    alerting:
      enabled: true
      channels:
        - type: dingtalk
          webhook: "https://oapi.dingtalk.com/robot/send?access_token=xxx"
        - type: email
          smtp-host: "smtp.company.com"
          recipients: ["<EMAIL>"]
```

## 11. 总结与展望

### 11.1 重构收益

#### 11.1.1 技术收益
- **可靠性提升**：通过完善的容错机制和自动恢复能力，系统可用性显著提升
- **可维护性增强**：事件驱动架构和清晰的模块划分，降低了系统复杂度
- **可扩展性改善**：插件化设计和统一的扩展点，支持业务快速迭代
- **可观测性完善**：全面的监控指标和告警机制，提升运维效率

#### 11.1.2 业务收益
- **故障恢复时间缩短**：自动故障检测和恢复机制，减少人工干预
- **配置管理效率提升**：统一的配置管理和热更新能力，提升运维效率
- **数据安全性增强**：完善的异常处理和回滚机制，保障数据安全

### 11.2 实施建议

#### 11.2.1 技术实施
1. **分阶段实施**：采用渐进式迁移策略，降低实施风险
2. **充分测试**：建立完善的测试体系，确保系统稳定性
3. **监控先行**：优先建立监控和告警体系，保障迁移过程可控
4. **文档完善**：建立完整的技术文档和运维手册

#### 11.2.2 团队协作
1. **技能培训**：对团队进行新架构和技术的培训
2. **代码审查**：建立严格的代码审查机制，确保代码质量
3. **运维协作**：与运维团队密切配合，确保部署和运维顺畅

### 11.3 未来展望

#### 11.3.1 技术演进方向
- **云原生化**：支持Kubernetes部署和云原生特性
- **智能化运维**：引入AI/ML技术，实现智能故障预测和自愈
- **多云支持**：支持多云环境部署和数据同步
- **实时处理**：支持流式数据处理和实时归档

#### 11.3.2 功能扩展
- **数据治理**：集成数据质量检查和数据血缘追踪
- **合规支持**：支持GDPR等数据合规要求
- **性能优化**：持续优化归档性能和资源利用率
- **生态集成**：与更多数据生态工具集成

通过本次重构，SCP-Archive系统将具备更强的可靠性、可维护性和可扩展性，为企业数据归档提供更加稳定和高效的解决方案。
