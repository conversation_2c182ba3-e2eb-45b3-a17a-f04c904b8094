{"body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:3faef695dcf3", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:562071a05820", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"messages": {"success": "新增成功"}, "method": "post", "requestAdaptor": "const {orderAttrCode, detailList = [], ...rest} = api.data\nconst detailListN = detailList.map(item => {\n  const {priority, cabinName} = item\n  return {\n    cabinCode: cabinName?.cabinCode,\n    cabinName: cabinName?.cabinName,\n    priority\n  }\n})\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode,\n  detailList: detailListN\n}\nif (detailListN.length === 0) {\n  api.mockResponse = {\n    data: {\n      status: 500,\n      msg: '运输履约分层未配置'\n    }\n  }\n}\n\nreturn api", "silent": false, "url": "/tms/admin/transportAbilityConfig/add"}, "body": [{"formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "id": "u:50ba1ba9b221", "label": "履约分层方案名称", "name": "config<PERSON><PERSON>", "type": "text"}, {"api": {"method": "get", "url": "/tms/admin/enum/getOversizeTransportEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:4173b4e421e0", "initialValue": 0, "label": "大件运输", "name": "oversizeTransport", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getTemperatureRequireEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:428bd6ad724c", "initialValue": 0, "label": "温度要求", "name": "temperatureRequire", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getCommodityValueEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:8a6b26598cbc", "initialValue": 0, "label": "商品价值", "name": "commodityValue", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getFragileProtectEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:87ccb4e8eefd", "initialValue": 0, "label": "易碎防护", "name": "fragileProtection", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getAirEmbargoEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:667deaf0cc60", "initialValue": 0, "label": "航空禁运", "name": "airEmbargo", "type": "select"}, {"api": {"adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}", "method": "get", "url": "/tms/admin/enum/getOrderAttrCode"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:54e93cd680d4", "label": "订单属性", "name": "orderAttrCode", "type": "select"}, {"columns": [{"api": {"adaptor": "return response?.data?.list", "method": "post", "url": "/tms/admin/transportCabin/page"}, "fieldProps": {"fieldNames": {"label": "cabinName", "value": "cabinCode"}, "labelInValue": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:cf04a1df8a2b", "label": "运输履约分层", "name": "cabinName", "type": "select"}, {"formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "label": "优先级", "name": "priority", "valueType": "digit"}, {"actions": [{"actionType": "editTableDelete", "confirmText": "是否确认删除", "danger": true, "id": "u:00acecabe7f0", "label": "删除", "type": "button"}], "id": "u:78e81b1e6903", "label": "操作", "type": "option", "width": 90}], "editable": {"actionRender": " \n  return [defaultDoms.delete];\n"}, "id": "u:9eeafd946583", "name": "detailList", "scroll": {"x": 270}, "type": "editTable"}], "id": "u:1736e7fd2b98", "labelCol": {"span": 5}, "type": "form"}, "id": "u:ee7039d9d87e", "title": "新增", "type": "dialog", "width": 800}, "id": "u:3a46389b7e47", "label": "新增", "level": "primary", "type": "button"}, {"fileName": "导入模板", "fileNameLabel": "模板下载", "fileUrl": "https://h5static.dewucdn.com/node-common/69b128b7-f1f4-65f6-23bf-9d0c4c129569.xlsx", "id": "u:04ecc8b92b8b", "importMod": "importTransportAbilityConfig", "label": "导入", "type": "lmsImport"}, {"id": "u:f7cf0910e7c7", "importMod": "importTransportAbilityConfig", "label": "导入结果查询", "type": "lmsImportResult"}, {"id": "u:de04edb5d785"}], "api": {"adaptor": "const list = response?.data?.list || []\nconst listN = list.map(item => {\n  const {detailList = []} = item\n  const detailListText = detailList.map((v) => v.priority + '-' + v.cabinName).join('；')\n  return {\n    ...item,\n    detailListText,\n  }\n})\n\nreturn {\n  status: 200,\n  code: 200,\n  data: {\n    contents: listN,\n    total: response?.data?.total\n  }\n}", "method": "post", "requestAdaptor": "const {orderAttrCode, ...rest} = api.data || {};\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode\n}\nreturn api", "url": "/tms/admin/transportAbilityConfig/page"}, "formColumns": [{"id": "u:348e14f6ac20", "label": "履约分层方案名称", "name": "config<PERSON><PERSON>", "type": "text"}, {"api": {"method": "get", "url": "/tms/admin/enum/getOversizeTransportEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "id": "u:fa54a86c96d5", "label": "大件运输", "name": "oversizeTransport", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getTemperatureRequireEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "id": "u:720262541c13", "label": "温度要求", "name": "temperatureRequire", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getCommodityValueEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "id": "u:31e47a73f63e", "label": "商品价值", "name": "commodityValue", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getFragileProtectEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "id": "u:d3983b6db22d", "label": "易碎防护", "name": "fragileProtection", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getAirEmbargoEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "id": "u:42de5f825967", "label": "航空禁运", "name": "airEmbargo", "type": "select"}, {"api": {"adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}", "method": "get", "url": "/tms/admin/enum/getOrderAttrCode"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "id": "u:431e3ebd9d1e", "label": "订单属性", "name": "orderAttrCode", "type": "select"}, {"api": {"adaptor": "return {\n              status: 200,\n              data: response?.mapData?.transportAbilityConfigStateEnum || []\n            };", "data": {"enumTypes": "transportAbilityConfigStateEnum"}, "method": "post", "url": "/tms/admin/enum/listEnums"}, "id": "u:de7f6b1b99b3", "initialValue": "ENABLED", "label": "生效状态", "name": "state", "type": "select"}], "id": "u:c61a67ebea74", "name": "chainSearchPage", "rowKey": "id", "search": {"defaultCollapsed": false, "labelWidth": 120}, "tableColumns": [{"id": "u:2f276b8f515e", "label": "履约分层方案名称", "name": "config<PERSON><PERSON>", "type": "text", "width": 200}, {"id": "u:f0c35b3b6f84", "label": "大件运输", "name": "oversizeTransportDesc", "type": "text", "width": 120}, {"id": "u:f3486f1765a7", "label": "温度要求", "name": "temperatureRequireDesc", "type": "text", "width": 120}, {"id": "u:1a3932f59639", "label": "商品价值", "name": "commodityValueDesc", "type": "text", "width": 120}, {"id": "u:2efbec57affc", "label": "易碎防护", "name": "fragileProtectionDesc", "type": "text", "width": 120}, {"id": "u:0e5f80c5bdb9", "label": "航空禁运", "name": "airEmbargoDesc", "type": "text", "width": 120}, {"id": "u:423eb93ff757", "label": "订单属性", "name": "orderAttrCodeDesc", "type": "text", "width": 140}, {"id": "u:806a9d47a248", "label": "运输履约分层", "name": "detailListText", "type": "text", "width": 250}, {"body": [{"color": "${state === 'ENABLED' ? 'green' : 'orange'}", "id": "u:a01974330be5", "label": "${state === 'ENABLED' ? '生效中' : '已失效'}", "type": "tag"}], "id": "u:3af61c69f090", "label": "生效状态", "name": "state", "type": "custom"}, {"actions": [{"actionType": "dialog", "api": {"adaptor": "const {oldFormValues, id} = api.data\nconst {orderAttrCode, ...rest} = response.data\nlet data = {\n  ...rest,\n  orderAttrCode: orderAttrCode ? orderAttrCode : '-1',\n  id,\n  isInit: true\n}\nif (oldFormValues) {\n  data = {\n    ...oldFormValues,\n    id,\n    isInit: true,\n    detailList: response.data?.detailList || []\n  }\n}\nreturn {\n  status: 200,\n  data\n}", "requestAdaptor": "let oldFormValues = null\nif (api.data?.oldFormValues) {\n  const {detailList,queryParam, ...rest} = api.data?.oldFormValues || {}  \n  oldFormValues = {...rest}\n}\napi.data = {\n  ...api.data,\n  oldFormValues: oldFormValues\n}\nconsole.log('api.data11', api.data)\nreturn api", "url": "/tms/admin/transportAbilityConfig/add"}, "dialog": {"actions": [{"actionType": "cancel", "id": "u:1d913fba902c", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:9bef622fc2d3", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$", "id": "${id}", "isInit": "${isInit}"}, "method": "post", "requestAdaptor": "\nconst {orderAttrCode, detailList = [], ...rest} = api.data\n\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode\n}\n\nreturn api", "url": "/tms/admin/transportAbilityConfig/updateById"}, "body": [{"formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "id": "u:72db77048f16", "label": "履约分层方案名称", "name": "config<PERSON><PERSON>", "type": "text"}, {"api": {"method": "get", "url": "/tms/admin/enum/getOversizeTransportEnum"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:0f446f98b374", "initialValue": 0, "label": "大件运输", "name": "oversizeTransport", "proFieldProps": {"mode": "read"}, "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getTemperatureRequireEnum"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:fe4422cf0ca4", "initialValue": 0, "label": "温度要求", "name": "temperatureRequire", "proFieldProps": {"mode": "read"}, "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getCommodityValueEnum"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:408762f76861", "initialValue": 0, "label": "商品价值", "name": "commodityValue", "proFieldProps": {"mode": "read"}, "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getFragileProtectEnum"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": false, "id": "u:ccb30f9b11ef", "initialValue": 0, "label": "易碎防护", "name": "fragileProtection", "proFieldProps": {"mode": "read"}, "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getAirEmbargoEnum"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:1244caef75ec", "initialValue": 0, "label": "航空禁运", "name": "airEmbargo", "proFieldProps": {"mode": "read"}, "type": "select"}, {"api": {"adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}", "method": "get", "url": "/tms/admin/enum/getOrderAttrCode"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:57a6f2c5f442", "label": "订单属性", "name": "orderAttrCode", "proFieldProps": {"mode": "read"}, "type": "select"}, {"columns": [{"api": {"adaptor": "return response?.data?.list", "method": "post", "url": "/tms/admin/transportCabin/page"}, "fieldProps": {"fieldNames": {"label": "cabinName", "value": "cabinCode"}, "labelInValue": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:e50704469e25", "label": "运输履约分层", "name": "cabinName", "type": "select"}, {"formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "label": "优先级", "name": "priority", "valueType": "digit"}, {"actions": [{"actionType": "editTableDelete", "api": {"data": {"detailId": "${id}"}, "messages": {"failed": "删除失败", "success": "删除成功"}, "method": "get", "url": "/tms/admin/transportAbilityConfig/detail/delete"}, "confirmText": "是否确认删除", "danger": true, "id": "u:0982b9286505", "label": "删除", "type": "button"}], "id": "u:b605c35cd410", "label": "操作", "type": "option", "width": 90}], "editable": {"actionRender": " \n  return [defaultDoms.save,defaultDoms.delete];\n"}, "id": "u:b288950eb431", "name": "detailList", "onSaveApi": {"method": "post", "requestAdaptor": "\nconst {editableRow, id} = api.data\nconst {cabinName, priority} = editableRow\napi.data = {\n  cabinCode: cabinName.cabinCode,\n  cabinName: cabinName.cabinName,\n  priority,\n  transportAbilityConfigId: id\n}\n\nreturn api", "url": "/tms/admin/transportAbilityConfig/detail/add"}, "rowKey": "id", "scroll": {"x": 270}, "target": "editDetailForm", "type": "editTable"}], "id": "u:6df3902cb860", "initApi": {"adaptor": "const {oldFormValues, id} = api.data\nconst {orderAttrCode, ...rest} = response.data\nlet data = {\n  ...rest,\n  orderAttrCode: orderAttrCode ? orderAttrCode : '-1',\n  id,\n  isInit: true\n}\nif (oldFormValues) {\n  data = {\n    ...oldFormValues,\n    id,\n    isInit: true,\n    detailList: response.data?.detailList || []\n  }\n}\nreturn {\n  status: 200,\n  data\n}", "data": {"id": "${id}", "oldFormValues": "${oldFormValues}"}, "method": "get", "requestAdaptor": "let oldFormValues = null\nif (api.data?.oldFormValues) {\n  const {detailList,queryParam, ...rest} = api.data?.oldFormValues || {}  \n  oldFormValues = {...rest}\n}\napi.data = {\n  ...api.data,\n  oldFormValues: oldFormValues\n}\nconsole.log('api.data11', api.data)\nreturn api", "sendOn": "${!isInit || !!oldFormValues}", "url": "/tms/admin/transportAbilityConfig/get?keywords=${target}"}, "labelCol": {"span": 5}, "name": "editDetailForm", "type": "form"}], "id": "u:47f046cc1213", "title": "编辑", "type": "dialog", "width": 800}, "id": "u:b929f26710a2", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "dialog", "api": {"adaptor": "const {config<PERSON><PERSON>, detailList, ...rest} = response?.data\n\nreturn {\n  status: 200,\n  data: rest\n}", "requestAdaptor": "const {orderAttrCode, detailList = [], ...rest} = api.data\nconst detailListN = detailList.map(item => {\n  const {priority, cabinName} = item\n  return {\n    cabinCode: cabinName?.cabinCode,\n    cabinName: cabinName?.cabinName,\n    priority\n  }\n})\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode,\n  detailList: detailListN\n}\nif (detailListN.length === 0) {\n  api.mockResponse = {\n    data: {\n      status: 500,\n      msg: '运输履约分层未配置'\n    }\n  }\n}\n\nconsole.log('data', api)\n\nreturn api", "url": "/tms/admin/transportAbilityConfig/add"}, "dialog": {"actions": [{"actionType": "cancel", "id": "u:1d913fba903c", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:9bef622fc2d1", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "新增成功"}, "method": "post", "requestAdaptor": "console.log('api', api)\nconst {orderAttrCode, detailList = [], ...rest} = api.data\nconst detailListN = detailList.map(item => {\n  const {priority, cabinName} = item\n  return {\n    cabinCode: cabinName?.cabinCode,\n    cabinName: cabinName?.cabinName,\n    priority\n  }\n})\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode,\n  detailList: detailListN\n}\nif (detailListN.length === 0) {\n  api.mockResponse = {\n    data: {\n      status: 500,\n      msg: '运输履约分层未配置'\n    }\n  }\n}\nconsole.log('detailListN', detailListN)\n\nconsole.log('data', api)\n\nreturn api", "silent": false, "url": "/tms/admin/transportAbilityConfig/add"}, "body": [{"formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "id": "u:72db77048f17", "label": "履约分层方案名称", "name": "config<PERSON><PERSON>", "type": "text"}, {"api": {"method": "get", "url": "/tms/admin/enum/getOversizeTransportEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:0f446f98b374", "initialValue": 0, "label": "大件运输", "name": "oversizeTransport", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getTemperatureRequireEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:fe4422cf1ca4", "initialValue": 0, "label": "温度要求", "name": "temperatureRequire", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getCommodityValueEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:408762f76881", "initialValue": 0, "label": "商品价值", "name": "commodityValue", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getFragileProtectEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:ccb30f9b21ef", "initialValue": 0, "label": "易碎防护", "name": "fragileProtection", "type": "select"}, {"api": {"method": "get", "url": "/tms/admin/enum/getAirEmbargoEnum"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:1244caef76ec", "initialValue": 0, "label": "航空禁运", "name": "airEmbargo", "type": "select"}, {"api": {"adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}", "method": "get", "url": "/tms/admin/enum/getOrderAttrCode"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:57a6f2c5f432", "label": "订单属性", "name": "orderAttrCode", "type": "select"}, {"columns": [{"api": {"adaptor": "return response?.data?.list", "method": "post", "url": "/tms/admin/transportCabin/page"}, "fieldProps": {"fieldNames": {"label": "cabinName", "value": "cabinCode"}, "labelInValue": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:4dfd0598ce96", "label": "运输履约分层", "name": "cabinName", "type": "select"}, {"formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "label": "优先级", "name": "priority", "valueType": "digit"}, {"actions": [{"actionType": "editTableDelete", "confirmText": "是否确认删除", "danger": true, "id": "u:35fb7d798f0b", "label": "删除", "type": "button"}], "id": "u:0f851c231b2a", "label": "操作", "type": "option", "width": 90}], "editable": {"actionRender": " \n  return [defaultDoms.delete];\n"}, "id": "u:b288950eb231", "name": "detailList", "scroll": {"x": 270}, "type": "editTable"}], "id": "u:6df3902cb810", "initApi": {"adaptor": "const {config<PERSON><PERSON>, detailList, ...rest} = response?.data\n\nreturn {\n  status: 200,\n  data: rest\n}", "data": {"id": "${id}"}, "method": "get", "url": "/tms/admin/transportAbilityConfig/get"}, "labelCol": {"span": 5}, "type": "form"}], "id": "u:47f046cc1233", "title": "复制", "type": "dialog", "width": 800}, "id": "u:b929f26710a3", "label": "复制", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}", "state": "UN_ENABLED"}, "messages": {"failed": "失效失败", "success": "失效成功"}, "method": "post", "url": "/tms/admin/transportAbilityConfig/stateUpdate"}, "confirmText": "是否确认失效", "danger": true, "hiddenOn": "${state != 'ENABLED'}", "id": "u:5f81c62318f9", "label": "失效", "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}", "state": "ENABLED"}, "messages": {"failed": "生效失败", "success": "生效成功"}, "method": "post", "url": "/tms/admin/transportAbilityConfig/stateUpdate"}, "confirmText": "是否确认生效", "hiddenOn": "${state != 'UN_ENABLED'}", "id": "u:5f81c62318f3", "label": "生效", "type": "button"}, {"id": "u:37b02531dafe", "label": "操作记录", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:1b757cbf958e", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:b1066095b496", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"id": "${id || 242}", "tableName": "des_transport_ability_config"}, "method": "post", "url": "/tms/admin/log/getLogList"}, "formColumns": [], "id": "u:4594553c451d", "label": "列表", "name": "chainSearchPage", "rowKey": "id", "search": false, "tableAlertRender": false, "tableColumns": [{"id": "u:023774b7dfbc", "label": "操作人", "name": "name", "type": "text", "width": 40}, {"id": "u:4b054e34abfd", "label": "操作时间", "name": "modifyTime", "type": "text", "width": 100}, {"id": "u:9e3581f42dd0", "label": "操作类型", "name": "operationName", "type": "text", "width": 60}, {"id": "u:746be6b29190", "label": "操作内容", "name": "operationContent", "type": "text"}], "type": "chainSearchPage"}], "id": "u:6ee1612c2e63", "title": "查看操作记录", "type": "dialog", "width": 900}}]}}, "type": "button"}], "fixed": "right", "id": "u:613e023d2028", "label": "操作", "type": "operation", "width": 210}], "type": "chainSearchPage"}], "id": "u:3700901b5652", "subTitle": "数据格式化、下拉枚举查询、新增（form表单中使用编辑表格）、编辑、删除、导入、导出", "title": "表单中使用编辑列表", "type": "page"}