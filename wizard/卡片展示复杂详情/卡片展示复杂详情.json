{"body": [{"body": [{"api": {"method": "get", "responseData": {"&": "$$", "data": "${contents}"}, "url": "/pink/admin/repositories/list"}, "fieldProps": {"fieldNames": {"label": "repositoryName", "value": "repositoryCode"}, "mode": "multiple"}, "id": "u:c29c7f0e9fb8", "label": "仓位", "name": "warehouseCodeList", "type": "select"}, {"fieldProps": {"fetchDataOnSearch": false, "mode": "multiple", "options": [{"label": "待审核", "value": 0}, {"label": "审核通过", "value": 1}, {"label": "审核不通过", "value": 2}], "showSearch": true}, "formItemProps": {}, "id": "u:57c5fa3e08f9", "label": "审核状态", "name": "verifyStatusList", "type": "select"}, {"formItemProps": {"rules": [{"message": "请选择质检不通过时间", "required": true}]}, "id": "u:5d893758fddc", "initialValue": "${\"2022-07-01 00:00:00\"},${\"2024-07-01 00:00:00\"}", "label": "质检不通过时间", "name": "rangePicker", "type": "rangePicker"}], "id": "u:4f99439dc91d", "labelCol": {"style": {"width": 160}}, "layout": "vertical", "mode": "query", "omitNil": false, "submitText": "查询", "target": "dataSource", "type": "form"}, {"api": {"data": {"categoryIdList": "${[]}", "createEndTime": "${DATETOSTR(rangePicker[1], 'YYYY-MM-DD HH:mm:ss')}", "createStartTime": "${DATETOSTR(rangePicker[0], 'YYYY-MM-DD HH:mm:ss')}", "pageNum": "${1}", "pageSize": "${10}", "verifyStatusList": "${verifyStatusList || []}", "warehouseCodeList": "${warehouseCodeList || []}"}, "method": "post", "sendOn": "${IF(rangePicker, true, false)}", "trackExpression": "${rangePicker},${warehouseCodeList.length > 0},${verifyStatusList.length > 0}", "url": "/pink/admin/quality/secondReason/v1/list"}, "body": [{"body": [{"icon": "icon-export", "id": "u:92353b13e5be", "label": "导出", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "toast", "args": {"msg": "导出中，请稍等...", "msgType": "info"}}, {"actionType": "ajax", "api": {"data": {"categoryIdList": "${[]}", "createEndTime": "${DATETOSTR(rangePicker[1], 'YYYY-MM-DD HH:mm:ss')}", "createStartTime": "${DATETOSTR(rangePicker[0], 'YYYY-MM-DD HH:mm:ss')}", "pageNum": "${1}", "pageSize": "${10}", "verifyStatusList": "${verifyStatusList || []}", "warehouseCodeList": "${warehouseCodeList || []}"}, "method": "post", "silent": true, "url": "/pink/admin/quality/secondReason/v1/export"}, "loopApi": {"data": {"taskId": "${taskId}"}, "method": "get", "sendOn": "${downloadStatus == 0}", "silent": true, "url": "/pink/admin/report/queryDownloadResult"}, "outputVar": "exportResult"}, {"actionType": "url", "args": {"url": "${event.data.exportResult.responseData.downloadUrl}"}, "expression": "${event.data.exportResult.responseData.downloadStatus == 1}"}, {"actionType": "toast", "args": {"msg": "导出成功，已自动下载", "msgType": "success"}, "expression": "${event.data.exportResult.responseData.downloadStatus == 1}"}]}}, "type": "button"}], "id": "u:a46a1c05f0b3", "onEvent": {"click": {"actions": []}}, "style": {"paddingBottom": 12, "paddingTop": 12, "textAlign": "right"}, "type": "container"}, {"id": "u:413b38a23e91", "items": {"body": [{"actions": [], "body": [{"alignItems": "flex-start", "id": "u:1d42db36ec27", "items": [{"body": [{"id": "u:89294556b1c2", "style": {"color": "#ff0000", "marginTop": "12px", "textAlign": "left"}, "tpl": "${\"二级瑕疵原因：\"+secondReason}", "type": "tpl", "wrapperComponent": "h3"}, {"id": "u:47b1c508d6ae", "items": [{"body": [{"id": "u:d95b6f085aae", "mode": "contain", "src": "${goodsLogo || \"https://cdn.dewu.com/node-common/344e8524-9fe8-c713-75c5-6c8fb75bc4c0-1440-1446.png\"}", "type": "picture", "width": 100}], "id": "u:2a533c6c304c", "style": {"display": "block", "flex": "0 0 120px", "flexBasis": "120px", "position": "static"}, "type": "wrapper"}, {"body": [{"bordered": false, "column": 2, "columns": [{"body": [{"blank": true, "body": "${uniqueCode|| \"-\"}", "href": "${uniqueCode ? \"https://t1-scm.shizhuang-inc.net/#/scm-pink/operate/operate-in-store-detail?id=\" + itemId : null}", "id": "u:5d5f448c1f1d", "type": "link"}], "copyable": false, "id": "u:7e60b051a688", "label": "唯一码", "name": "uniqueCode", "type": "custom"}, {"body": [{"id": "u:13244e24ef72", "style": {"textAlign": "left"}, "tpl": "${DATETOSTR(createTime, \"YYYY-MM-DD HH:mm:ss\")}", "type": "tpl"}], "fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:23b5571da66f", "label": "质检不通过时间", "name": "createTime", "type": "custom"}, {"fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:3203649cca34", "label": "商品名称", "name": "spuName", "type": "text"}, {"fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:d0b57819c5a7", "label": "瑕疵原因", "name": "firstReason", "type": "text"}, {"fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:fd19d59e35ab", "label": "规格", "name": "spec", "type": "text"}, {"fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:cc05bb94934f", "label": "审核人", "name": "verifyUserName", "type": "text"}, {"copyable": true, "fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:05acf84fc199", "label": "货号", "name": "artNo", "type": "text"}, {"body": [{"id": "u:8cf0b889b65c", "style": {"textAlign": "left"}, "tpl": "${DATETOSTR(verifyTime, \"YYYY-MM-DD HH:mm:ss\")}", "type": "tpl"}], "id": "u:a5e3d094e166", "label": "审核时间", "name": "verifyTime", "type": "custom"}, {"fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:15aa5e646191", "label": "审核不通过原因", "name": "remark", "span": 2, "type": "text"}], "extra": [{"body": [], "id": "u:92dba49805ae", "type": "wrapper"}], "id": "u:315b92acae65", "layout": "horizontal", "size": "small", "style": {"margin-top": "-40px"}, "type": "descriptions"}], "id": "u:ffc9abde49d8", "style": {"display": "block", "flex": "0 0 620px", "flexBasis": "620px", "position": "static"}, "type": "wrapper"}, {"body": [{"hiddenOn": "${!flawImageList || (flawImageList && flawImageList.length === 0)}", "id": "u:03961d16de62", "style": {"color": "#7f7f8e", "textAlign": "left"}, "tpl": "瑕疵图：", "type": "tpl", "wrapperComponent": "div"}, {"dataSource": "${flawImageList}", "height": 80, "hiddenOn": "${!flawImageList || (flawImageList && flawImageList.length === 0)}", "id": "u:b3a99c620aae", "mode": "stretch", "name": "flawImageList", "type": "pictures", "width": 80}, {"hiddenOn": "${!attachImages || (attachImages && attachImages.length === 0)}", "id": "u:39ef910bbbf2", "style": {"color": "#7f7f8e", "margin-top": "20px", "textAlign": "left"}, "tpl": "配件图：", "type": "tpl", "wrapperComponent": "div"}, {"dataSource": "${attachImages}", "height": 80, "hiddenOn": "${!attachImages || (attachImages && attachImages.length === 0)}", "id": "u:6ea6deed9910", "mode": "stretch", "name": "attachImages", "type": "pictures", "width": 80}], "id": "u:d91a5a943ae9", "style": {"display": "block", "flex": "1 1 auto", "flexBasis": "auto", "flexGrow": 1, "position": "static"}, "type": "wrapper"}], "type": "flex"}, {"body": [{"confirmText": "确认审核通过？", "id": "u:8e76e3d4d71a", "label": "审核通过", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "ajax", "api": {"data": {"id": "${id}", "verifyStatus": "${1}"}, "messages": {"failed": "审核通过失败", "success": "审核通过完成"}, "method": "post", "url": "/pink/admin/quality/secondReason/v1/verify"}, "outputVar": "verifyResult"}, {"actionType": "reload", "componentId": "u:41d5ada7817c", "expression": "${event.data.verifyResult.responseStatus == 200}"}]}}, "type": "button"}, {"danger": true, "id": "u:f24f7bfa88c5", "label": "审核不通过", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:5a0e1f3fd49d", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:d65d4a9eabd8", "label": "确认不通过", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"id": "${id}", "remark": "${remark}", "verifyStatus": "${2}"}, "method": "post", "url": "/pink/admin/quality/secondReason/v1/verify"}, "body": [{"fieldProps": {"options": [{"label": "鞋子坏了", "value": "鞋子坏了"}, {"label": "娃娃不叫", "value": "娃娃不叫"}, {"label": "北京限行", "value": "北京限行"}]}, "formItemProps": {"rules": [{"required": true}]}, "id": "u:eb0144650652", "label": "审核不通过原因", "name": "remark", "type": "select"}], "id": "u:69eb7c12cbb5", "type": "form"}], "id": "u:8c4b780fe69b", "title": "请选择审核不通过原因", "type": "dialog"}}]}}, "type": "button"}], "hiddenOn": "${verifyStatus != 0}", "id": "u:0022b0b6b5aa", "style": {"margin-top": "12px"}, "type": "space"}], "id": "u:af64464bde8e", "style": {"display": "block", "flex": "1 1 auto", "flexBasis": "auto", "flexGrow": 1, "position": "static"}, "type": "wrapper"}], "type": "flex"}], "bordered": true, "hoverable": true, "id": "u:71f8ad86b205", "loading": false, "size": "default", "style": {"margin-bottom": "20px", "margin-left": "8px", "width": "100%"}, "type": "card"}], "color": "${IFS(verifyStatus == 0, \"blue\", verifyStatus == 1, \"green\",verifyStatus == 2, \"red\")}", "id": "u:ca494146ee55", "placement": "start", "text": "${IFS(verifyStatus == 0, \"待审核\", verifyStatus == 1, \"审核通过\",verifyStatus == 2, \"审核不通过\")}", "type": "badgeRibbon"}, "placeholder": "暂无数据", "source": "${contents}", "type": "each"}], "data": {"rangePicker": "${['2022-07-01 00:00:00', '2024-07-01 23:59:59']}"}, "id": "u:41d5ada7817c", "name": "dataSource", "type": "service"}], "id": "u:3700901b5652", "subTitle": "顶部设置搜索条件，列表区域用卡片展示多样化布局", "title": "卡片展示的搜索列表页", "type": "page"}