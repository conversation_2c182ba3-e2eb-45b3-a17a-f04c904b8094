{"body": [{"actions": [{"actionType": "export", "api": {"data": {}, "method": "get", "requestAdaptor": " const queryParam =  api.data?.queryParam || {} \n const data = {...queryParam} \n api.data = data \n return api;", "url": "/wmsreport/report/delivery/v1/createReportByTime"}, "id": "u:b0306ac82ca9", "label": "导出", "level": "primary", "type": "button"}], "api": {"adaptor": "const { deliveryType, orderTags } = api.data;\n\nconst data = {\n  contents: response.data.map(item => {\n    const startTimeStr = item.dateStr + \" 00:00:00\";\n    const endTimeStr = item.dateStr + \" 23:59:59\";\n    const startTime = Math.max(new Date(startTimeStr).getTime(), new Date(api.data.startTime).getTime());\n\n    return {\n      ...item,\n      deliveryType,\n      startTime,\n      endTime: new Date(endTimeStr).getTime(),\n      orderTags,\n      lastPickedTime: api.data.lastPickedTime\n    };\n  })\n};\n\nreturn {\n  msg: \"success\",\n  status: 200,\n  data\n};", "data": {}, "method": "post", "requestAdaptor": "const { timeRange, warehouseCodeList, deliveryType, lastPickedTime, orderTags } = api.data;\nlet warehouseCodeStr = null;\nif (!!warehouseCodeList) {\n  warehouseCodeStr = warehouseCodeList.join(\",\");\n}\napi.data = {\n  startTime: timeRange?.[0],\n  endTime: timeRange?.[1],\n  warehouseCodeStr: warehouseCodeStr ? warehouseCodeStr : \"\",\n  deliveryType: deliveryType ? deliveryType : \"\",\n  lastPickedTime: lastPickedTime ? lastPickedTime : \"\",\n  orderTags: orderTags ? orderTags : \"\"\n};\nreturn api;", "url": "/wmsreport/report/delivery/v1/queryDeliveryStatisticalByDay"}, "formColumns": [{"fieldProps": {"maxTagCount": 1, "multiple": true, "type": "wms"}, "id": "u:0ab1dc5b83de", "label": "仓库名称", "name": "warehouseCodeList", "type": "scmWarehouse"}, {"formItemProps": {"rules": [{"message": "请选择时间", "required": true}]}, "id": "u:eae48a240c57", "initialValue": "${DATETOSTR(DATEMODIFY(DATEMODIFY(STARTOF(NOW(), 'day'), -1, 'day'), 19, 'hour'), 'X') + ',' + DATETOSTR(DATEMODIFY(ENDOF(NOW(), 'day'), -5, 'hour'), 'X')}", "label": "时间范围", "name": "timeRange", "type": "rangePicker"}, {"api": {"data": {"enumName": "WmsOutBoundTypeEnum"}, "method": "GET", "url": "/wms/cfg/enum/v1/load"}, "fieldProps": {"fieldNames": {"label": "typeDesc", "value": "typeCode"}, "showSearch": true}, "id": "u:670fdf9c678b", "label": "发货单类型", "name": "deliveryType", "type": "select"}, {"api": {"data": {"enumName": "DeliveryTagEnum"}, "method": "GET", "url": "/wms/cfg/enum/v1/load"}, "fieldProps": {"fieldNames": {"label": "typeDesc", "value": "typeCode"}, "showSearch": true}, "id": "u:317cd06b2cb6", "label": "订单标记", "name": "orderTags", "type": "select"}, {"api": {"data": {"enumName": "LatestPickupTimeEnum"}, "method": "GET", "url": "/wms/cfg/enum/v1/load"}, "fieldProps": {"fieldNames": {"label": "typeDesc", "value": "typeCode"}, "showSearch": true}, "id": "u:c310d3ca90e7", "label": "最晚拣货时间", "name": "lastPickedTime", "type": "select"}], "id": "u:c61a67ebea74", "name": "chainSearchPage", "rowKey": "id", "search": {"defaultCollapsed": false, "labelWidth": 120}, "tableColumns": [{"align": "center", "id": "u:edae717fc990", "label": "日期", "name": "dateStr", "type": "text", "width": 180}, {"align": "center", "id": "u:cc4742a4359f", "label": "仓库", "name": "warehouseName", "type": "text", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "INIT", "warehouseCode": "${warehouseCode}"}, "id": "u:2f509e402a43", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${initNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:f89e9695a757", "label": "初始", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "FAIL_ALLOCATE", "warehouseCode": "${warehouseCode}"}, "id": "u:02ff286d0028", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${failAllocateNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:aa6df3e9221e", "label": "分配失败", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "PART_ALLOCATE", "warehouseCode": "${warehouseCode}"}, "id": "u:a1a571574012", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${partAllocateNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:688805c66bac", "label": "部分分配", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "WHOLE_ALLOCATE", "warehouseCode": "${warehouseCode}"}, "id": "u:1ed30521b268", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${wholeAllocateNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:2db5059e13b7", "label": "完全分配", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "LAUNCH", "warehouseCode": "${warehouseCode}"}, "id": "u:17b74fd39d00", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${launchNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:7acd693f3d9f", "label": "创建波次", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "PICKING", "warehouseCode": "${warehouseCode}"}, "id": "u:4ace8d0de104", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${pickingNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:66af24f02901", "label": "拣货中", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "PICKED", "warehouseCode": "${warehouseCode}"}, "id": "u:4accc81cf578", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${pickedNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:07be9141354e", "label": "拣货完成", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "PACKING", "warehouseCode": "${warehouseCode}"}, "id": "u:3dbe71cff55d", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${packingNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:264bb55876c3", "label": "装箱中", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "PACK_FINISH", "warehouseCode": "${warehouseCode}"}, "id": "u:eb8dbec1776a", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${packFinishNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:fa3f3752b33d", "label": "装箱完成", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "OUTING", "warehouseCode": "${warehouseCode}"}, "id": "u:35a44d3358a7", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${outingNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:6b49bac43f13", "label": "发货中", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "OUTED", "warehouseCode": "${warehouseCode}"}, "id": "u:b6ba79ae8695", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${outedNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:5466bbcbfc14", "label": "发货完成", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "HANDOVER", "warehouseCode": "${warehouseCode}"}, "id": "u:e7ad13f2fa9e", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${handoverNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:8234a98bc4ac", "label": "完成交接", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "CANCEL", "warehouseCode": "${warehouseCode}"}, "id": "u:68cc64d52c57", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${cancelNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:3cb2399d9c00", "label": "取消", "type": "operation", "width": 180}, {"actions": [{"actionType": "dialog", "dialog": {"$ref": "dialogCom", "data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "ALL", "warehouseCode": "${warehouseCode}"}, "id": "u:51e9f11e3b2f", "type": "dialog"}, "id": "u:f60f64afb095", "label": " ${totalNum} ", "level": "link", "type": "button"}], "align": "center", "id": "u:f45b33aabe25", "label": "汇总", "type": "operation", "width": 180}], "type": "chainSearchPage"}], "definitions": {"dialogCom": {"actions": [], "body": [{"actions": [{"actionType": "export", "api": {"data": {}, "method": "post", "requestAdaptor": " const queryParam =  api.data?.queryParam || {} \n const data = {...queryParam} \n api.data = data \n return api;", "url": "/wmsreport/report/delivery/v1/createReportDetailByTime"}, "id": "u:b5eadd982b92", "label": "导出", "level": "primary", "type": "button"}], "api": {"data": {"deliveryType": "${deliveryType}", "endTime": "${endTime}", "lastPickedTime": "${lastPickedTime}", "orderTags": "${orderTags}", "startTime": "${startTime}", "status": "${status}", "warehouseCode": "${warehouseCode}"}, "method": "post", "url": "/wmsreport/report/delivery/v1/queryDeliveryDetailByDay"}, "id": "u:63495c70237f", "name": "table", "tableColumns": [{"align": "center", "label": "单据号", "name": "deliveryOrderCode"}, {"align": "center", "label": "关联单号", "name": "relatedOrderCode"}, {"align": "center", "label": "单据类型", "name": "orderTypeDesc"}, {"align": "center", "label": "数量", "name": "totalPlanQty"}], "type": "table"}], "id": "u:2ce9fd6d98e4", "title": "明细", "type": "dialog", "width": "90%"}}, "id": "u:7003827c792e", "subTitle": "点击表格中数据展示弹框详情明细（共用弹框详情明细模块）", "title": "支持多列点击查看详情列表页", "type": "page"}