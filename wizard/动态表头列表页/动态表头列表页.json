{"body": [{"api": {"data": {}, "method": "post", "url": "/lmsnew/admin/user/pieceCount/listFromEs"}, "formColumns": [{"endName": "signInTimeEnd", "fieldProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "id": "u:24d2ce15350f", "initialValue": "today,${DATETOSTR(ENDOF(DATEMODIFY(NOW(),7,'day')),'YYYY-MM-DD HH:mm:ss')}", "label": "签到时间", "name": "timeRange", "startName": "signInTimeStart", "type": "rangePicker"}], "id": "u:61aa813e8d33", "label": "搜索列表", "manualRequest": false, "name": "chainSearchPage", "tableColumns": [{"actions": [{"actionType": "ajax", "api": {"data": {"id": "${id}"}, "method": "post", "url": "/mock/form/saveForm"}, "confirmText": "确认删除：${userName} ？", "danger": true, "icon": "icon-delete", "id": "u:a31a09946e3c", "label": "删除", "level": "link", "size": "middle", "type": "button"}], "fixed": "right", "id": "u:dab958e43aee", "label": "操作", "type": "operation", "width": 100}], "tableColumnsConfig": {"data": "extra.headerConfig", "fieldNames": {"label": "label", "name": "prop"}}, "type": "chainSearchPage"}], "id": "u:7003827c792e", "subTitle": "通过搜索接口控制表头", "title": "动态表头列表页", "type": "page"}