{"body": [{"api": {"data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "body": [{"actions": [{"id": "u:12a01ebaa513", "label": "新增", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:809b2b5c8b5c", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:d0fc6f323c84", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"url": "/scp-cost/goods/bomLevel1Category/save"}, "body": [{"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomConfigTypeEnum}"}, "formItemProps": {"rules": [{"message": "请选择配置类型", "required": true}]}, "id": "u:01c99e3ae895", "label": "BOM 配置类型", "name": "bomConfigType", "type": "select"}, {"fieldProps": {"changeOnSelect": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "id": "u:a8b46c842bcf", "label": "类目选择", "level1Name": "level1CategoryId", "level2Name": "level2CategoryId", "level3Name": "level3CategoryId", "linkage": [{"event": "onChange", "setValues": {"level1CategoryName": "${data[0].categoryName}", "level2CategoryName": "${data[1].categoryName}", "level3CategoryName": "${data[2].categoryName}"}}], "name": "levelCategoryIds", "type": "scmCategory"}, {"formItemProps": {"hidden": true}, "id": "u:4f2e97d1eabf", "label": "一级类目名称", "name": "level1CategoryName", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:f03b7fbbda79", "label": "二级类目名称", "name": "level2CategoryName", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:a7e6ad13133a", "label": "三级类目名称", "linkage": [{"event": "onChange", "setValues": {"level1CategoryName": "${data[0].categoryName}", "level2CategoryName": "${data[1].categoryName}", "level3CategoryName": "${data[2].categoryName}"}}], "name": "level3CategoryName", "type": "text"}, {"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomSourceEnum}"}, "formItemProps": {"rules": [{"message": "请选择来源", "required": true}]}, "id": "u:7b7ec22f6cdb", "label": "BOM 来源", "name": "bomSource", "type": "select"}, {"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomTypeEnum}"}, "formItemProps": {"rules": [{"message": "请选择类型", "required": true}]}, "id": "u:815cfeb47979", "label": "BOM 类型", "name": "bomType", "type": "select"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "id": "u:3fb28ae54eff", "label": "BOM Code", "name": "bomCode", "type": "text"}, {"fieldProps": {"maxLength": 100, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "id": "u:686aa252d724", "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "textarea"}, {"fieldProps": {"precision": 10}, "formItemProps": {"rules": [{"message": "使用量不能为空", "required": true}]}, "id": "u:42491bb58ff6", "label": "BOM 使用量", "name": "bomUsage", "type": "digit"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "单位不能为空", "required": true}]}, "id": "u:866b6585e6c4", "label": "BOM 单位", "name": "bomUnit", "type": "text"}], "id": "u:364a302cc4e1", "type": "form"}], "id": "u:e13e4bea3a1b", "title": "新增", "type": "dialog", "width": 720}}]}}, "type": "button"}, {"fileName": "BOM配置表模板.xlsx", "fileNameLabel": "批量新增模板下载", "fileUrl": "https://h5static.dewucdn.com/node-common/44956ff9-75de-2e5d-2181-ea65fe0b7de8.xlsx", "id": "u:31844b1bf2a4", "importMod": "editBomCategoryImport", "label": "编辑导入", "type": "lmsImport"}, {"id": "u:234b5fd416ec", "importMod": "editBomCategoryImport", "label": "编辑导入结果查询", "type": "lmsImportResult"}, {"fileName": "BOM配置表模板.xlsx", "fileNameLabel": "批量新增模板下载", "fileUrl": "https://h5static.dewucdn.com/node-common/44956ff9-75de-2e5d-2181-ea65fe0b7de8.xlsx", "id": "u:94c0f8be74b2", "importMod": "addBomCategoryImport", "label": "新增导入", "type": "lmsImport"}, {"id": "u:b622befa34f8", "importMod": "addBomCategoryImport", "label": "新增导入结果查询", "type": "lmsImportResult"}, {"exportMod": "bomCategoryExport", "id": "u:4f0d3b1b5e57", "type": "lmsExport"}], "api": {"data": {"orderBy": "updated_time"}, "method": "post", "url": "/scp-cost/goods/bomCategory/list"}, "formColumns": [{"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomConfigTypeEnum}"}, "id": "u:4af9d9e03a4b", "label": "BOM 配置类型", "name": "bomConfigType", "type": "select"}, {"fieldProps": {"changeOnSelect": true}, "id": "u:5b849fec1623", "label": "类目选择", "level1Name": "level1CategoryId", "level2Name": "level2CategoryId", "level3Name": "level3CategoryId", "name": "levelCategoryIds", "type": "scmCategory"}, {"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomTypeEnum}"}, "id": "u:2b57f04fa644", "label": "BOM 类型", "name": "bomType", "type": "select"}, {"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomSourceEnum}"}, "id": "u:f3f567712aef", "label": "BOM 来源", "name": "bomSource", "type": "select"}, {"fieldProps": {"placeholder": "请输入完整 code ，精确搜索"}, "id": "u:89eb8f368c7d", "label": "BOM Code", "name": "bomCode", "type": "text"}, {"fieldProps": {"placeholder": "模糊搜索"}, "id": "u:067a5e539de7", "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "text"}], "id": "u:e8f22f6f83d1", "name": "chainSearchPage", "rowKey": "id", "search": {"defaultCollapsed": false, "labelWidth": 110}, "tableColumns": [{"fixed": "left", "id": "u:a8440dcd4693", "label": "序号", "name": "id", "type": "text", "width": 100}, {"id": "u:6c1bb1fa2e5c", "label": "BOM 配置类型", "name": "bomConfigTypeName", "type": "text", "width": 120}, {"id": "u:f60dc9692d81", "label": "一级类目", "name": "level1CategoryName", "type": "text"}, {"id": "u:441ee2f66773", "label": "二级类目", "name": "level2CategoryName", "type": "text"}, {"id": "u:94a6a73013ed", "label": "三级类目", "name": "level3CategoryName", "type": "text"}, {"id": "u:8270a7fe733c", "label": "BOM 类型", "name": "bomTypeName", "type": "text"}, {"id": "u:dac8737ec3ed", "label": "BOM 来源", "name": "bomSourceName", "type": "text"}, {"id": "u:b444e1058759", "label": "BOM Code", "name": "bomCode", "type": "text"}, {"id": "u:5f2f32b0dec1", "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "text", "width": 150}, {"align": "right", "id": "u:52adc2fdba49", "label": "BOM 使用量", "name": "bomUsage", "type": "text", "width": 100}, {"id": "u:109336eccd25", "label": "BOM 单位", "name": "bomUnit", "type": "text", "width": 80}, {"id": "u:7544c66279df", "label": "版本", "name": "version", "type": "text"}, {"actions": [{"icon": "icon-unordered-list", "id": "u:f729a7a491b2", "label": "查看详细列表", "onEvent": {"click": {"actions": [{"actionType": "drawer", "drawer": {"actions": [{"actionType": "confirm", "icon": "icon-arrow-right", "id": "u:3fd2c8b80bdc", "label": "收起", "level": "primary", "type": "button"}], "body": [{"api": {"data": {"_dataSource_": {"date": "20240101", "dateRange": ["20240101", "20240131"], "imageList": ["https://h5static.dewucdn.com/pink/2023/image/10485130_byte848408byte_1e81522f03aea4e2076ddf543dc0eef2_iOS_w3024h3024.jpg", "https://h5static.dewucdn.com/pink/2023/image/10485130_byte1468033byte_d962f961d691e962a2af7ad42199e964_iOS_w3024h3024.jpg"], "money": "7777777", "percent": "100", "progress": "60", "state": "all", "text": "这是一段文本", "time": "10:30:01"}}, "method": "post", "url": "/wizard/mock/detailOperateItem"}, "columns": [{"copyable": false, "id": "u:e9d52f6fc0ed", "label": "一级类目", "name": "level1CategoryName", "type": "text"}, {"fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:e5c22e0e6c3c", "label": "二级类目", "name": "level2CategoryName", "type": "select"}, {"copyable": false, "ellipsis": true, "id": "u:1d4192782765", "label": "三级类目", "name": "level3CategoryName", "type": "text", "width": 100}, {"copyable": true, "id": "u:1aa00d987638", "label": "BomCode", "name": "bomCode", "span": 2, "type": "text"}, {"copyable": true, "id": "u:f9a1535e4cbd", "label": "版本", "name": "version", "type": "text"}], "extra": [{"body": [{"icon": "icon-question-circle", "id": "u:bc6e55d90f06", "style": {"color": "#2b2c3c", "fontSize": 18}, "type": "icon"}], "id": "u:358e36daf334", "type": "wrapper"}], "id": "u:4d54c01716ca", "title": "${bomConfigTypeName}", "type": "descriptions"}, {"id": "u:e8ee9cdfa4c6", "plain": true, "type": "divider"}, {"actions": [{"actionType": "ajax", "api": {"data": {"key": "${selectedRowKeys}"}, "messages": {"failed": "修改失败", "success": "修改成功"}, "method": "post", "url": "/mock/form/saveForm"}, "id": "u:b656ee0417a8", "label": "批量删除", "level": "primary", "type": "button"}], "api": {"data": {"&": "$$"}, "method": "get", "url": "/wizard/mock/demoList"}, "formColumns": [{"id": "u:f77498897837", "label": "时效规则", "name": "ruleGroupName", "type": "text"}], "id": "u:98375a327100", "label": "列表", "name": "chainSearchPage", "rowKey": "id", "tableAlertRender": false, "tableColumns": [{"id": "u:e80f60fef0b6", "label": "名字", "name": "name", "type": "text"}, {"body": [{"height": 100, "id": "u:33dac2fc5709", "mode": "cover", "src": "${pic}", "type": "picture", "width": 100}], "id": "u:49b38f960af8", "label": "照片", "name": "pic", "type": "custom"}, {"actions": [{"icon": "icon-edit", "id": "u:18dbc3ee6498", "label": "编辑", "type": "button"}, {"confirmText": "你确定要删除？", "danger": true, "icon": "icon-delete", "id": "u:680985e5bfe9", "label": "删除", "type": "button"}, {"icon": "icon-eye", "id": "u:752e9645e9c4", "label": "查看", "type": "button"}, {"icon": "icon-unordered-list", "id": "u:03a0affc5d38", "label": "操作日志", "type": "button"}], "blank": false, "fixed": "right", "id": "u:0077b2e577f6", "label": "操作", "type": "operation", "width": 200}], "type": "chainSearchPage"}], "closeOnOutside": true, "confirmLoading": true, "id": "u:a9343a298964", "mask": true, "title": "${\"详情-（\" + id + \")\"}", "type": "drawer", "width": 800}}]}}, "type": "button"}, {"id": "u:e944637c29ac", "label": "编辑", "level": "link", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:c15c9d6881ac", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:0f3c49c79ebb", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$", "id": "${id}"}, "method": "post", "url": "/scp-cost/goods/bomCategory/edit"}, "body": [{"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomConfigTypeEnum}"}, "formItemProps": {"rules": [{"message": "请选择配置类型", "required": true}]}, "id": "u:01c99e3ae895", "label": "BOM 配置类型", "name": "bomConfigType", "type": "select"}, {"fieldProps": {"changeOnSelect": true}, "id": "u:a8b46c842bcf", "label": "类目选择", "level1Name": "level1CategoryId", "level2Name": "level2CategoryId", "level3Name": "level3CategoryId", "name": "levelCategoryIds", "proFieldProps": {"mode": "read"}, "type": "scmCategory"}, {"formItemProps": {"hidden": true}, "id": "u:de0dcca36bfc", "label": "一级类目名称", "name": "level1CategoryName", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:f03b7fbbda79", "label": "二级类目名称", "name": "level2CategoryName", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:a7e6ad13133a", "label": "三级类目名称", "name": "level3CategoryName", "type": "text"}, {"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomSourceEnum}"}, "formItemProps": {"rules": [{"message": "请选择来源", "required": true}]}, "id": "u:7b7ec22f6cdb", "label": "BOM 来源", "name": "bomSource", "type": "select"}, {"fieldProps": {"fieldNames": {"label": "label", "value": "code"}, "options": "${bomTypeEnum}"}, "formItemProps": {"rules": [{"message": "请选择类型", "required": true}]}, "id": "u:815cfeb47979", "label": "BOM 类型", "name": "bomType", "type": "select"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "id": "u:3fb28ae54eff", "label": "BOM Code", "name": "bomCode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"fieldProps": {"maxLength": 100, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "id": "u:686aa252d724", "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "textarea"}, {"fieldProps": {"precision": 10}, "formItemProps": {"rules": [{"message": "使用量不能为空", "required": true}]}, "id": "u:42491bb58ff6", "label": "BOM 使用量", "name": "bomUsage", "type": "digit"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "单位不能为空", "required": true}]}, "id": "u:866b6585e6c4", "label": "BOM 单位", "name": "bomUnit", "type": "text"}], "id": "u:364a302cc4e1", "type": "form"}], "id": "u:3888f8495625", "title": "编辑", "type": "dialog", "width": 720}}]}}, "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}"}, "messages": {"failed": "删除失败", "success": "删除成功"}, "method": "post", "url": "/scp-cost/goods/bomLevel1Category/remove"}, "confirmText": "是否确认删除", "danger": true, "id": "u:31d4784cea59", "label": "删除", "type": "button"}], "fixed": "right", "id": "u:7320249ca7cb", "label": "操作", "type": "operation", "width": 220}], "type": "chainSearchPage"}], "id": "u:f92c01a6dfac", "type": "service"}], "id": "u:001434419bdb", "subTitle": "数据格式化、下拉枚举查询、联动、新增、编辑、删除、多个导入、多个导出、多个导入结果查询，点击详情打开列表", "title": "复杂列表页", "type": "page"}