{"body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "label": "取消", "type": "button"}, {"actionType": "submit", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"messages": {"success": "新增成功"}, "requestAdaptor": "\n              if(api.data.levelCategoryIds && api.data.levelCategoryIds.length > 0){\n                api.data.level1CategoryId = api.data.levelCategoryIds[0];\n                delete api.data.levelCategoryIds;\n              }\n              return api;", "url": "/scp-cost/goods/bomLevel1Category/save"}, "body": [{"api": {"adaptor": "\n                    return {\n                      status: 200,\n                      data: response.data.bomConfigTypeEnum\n                    };\n                  ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择配置类型", "required": true}]}, "label": "BOM 配置类型", "name": "bomConfigType", "type": "select"}, {"fieldProps": {"level": 1}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "label": "类目选择", "linkage": [{"event": "onChange", "setValues": {"level1CategoryName": "${data[0].categoryName}"}}], "name": "levelCategoryIds", "type": "scmCategory"}, {"api": {"adaptor": "\n                    return {\n                      status: 200,\n                      data: response.data.bomSourceEnum\n                    };\n                  ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择来源", "required": true}]}, "label": "BOM 来源", "name": "bomSource", "type": "select"}, {"api": {"adaptor": "\n                    return {\n                      status: 200,\n                      data: response.data.bomTypeEnum\n                    };\n                  ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择类型", "required": true}]}, "label": "BOM 类型", "name": "bomType", "type": "select"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "label": "BOM Code", "name": "bomCode", "type": "text"}, {"fieldProps": {"maxLength": 100, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "textarea"}, {"fieldProps": {"precision": 10}, "formItemProps": {"rules": [{"message": "使用量不能为空", "required": true}]}, "label": "BOM 使用量", "name": "bomUsage", "type": "digit"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "单位不能为空", "required": true}]}, "label": "BOM 单位", "name": "bomUnit", "type": "text"}, {"formItemProps": {"hidden": true}, "label": "level1CategoryName", "name": "level1CategoryName", "type": "text"}], "type": "form"}, "title": "新增", "type": "dialog", "width": 700}, "id": "u:8c0a90f0f883", "label": "新增", "level": "primary", "type": "button"}, {"fileName": "BOM配置表模板 (一级类目兜底).xlsx", "fileNameLabel": "批量新增模板下载", "fileUrl": "https://h5static.dewucdn.com/node-common/63fab249-42a9-fcec-b9a6-da2440b9dda6.xlsx", "id": "u:a286b7d766dd", "importMod": "bomLevel1CategoryImport", "label": "新增导入", "type": "lmsImport"}, {"id": "u:6a6ba8ee03ed", "importMod": "bomLevel1CategoryImport", "label": "新增导入结果查询", "type": "lmsImportResult"}, {"exportMod": "bomLevel1CategoryExport", "id": "u:f7d842d78991", "type": "lmsExport"}], "api": {"data": {"orderBy": "updated_time"}, "method": "post", "requestAdaptor": "\n        if(api.data.levelCategoryIds && api.data.levelCategoryIds.length > 0){\n          api.data.level1CategoryId = api.data.levelCategoryIds[0];\n          delete api.data.levelCategoryIds;\n        }\n        return api;", "url": "/scp-cost/goods/bomLevel1Category/list"}, "formColumns": [{"api": {"adaptor": "\n            return {\n              status: 200,\n              data: response.data.bomConfigTypeEnum\n            };\n          ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "id": "u:b08f1f595bc3", "label": "BOM 配置类型", "name": "bomConfigType", "type": "select"}, {"fieldProps": {"level": 1}, "id": "u:6bf1ca61e1b2", "label": "类目选择", "name": "levelCategoryIds", "type": "scmCategory"}, {"api": {"adaptor": "\n            return {\n              status: 200,\n              data: response.data.bomTypeEnum\n            };\n          ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "id": "u:65e30b316580", "label": "BOM 类型", "name": "bomType", "type": "select"}, {"api": {"adaptor": "\n            return {\n              status: 200,\n              data: response.data.bomSourceEnum\n            };\n          ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "id": "u:a8fbc0c0e0b8", "label": "BOM 来源", "name": "bomSource", "type": "select"}, {"fieldProps": {"placeholder": "请输入完整 code ，精确搜索"}, "id": "u:2664df43f386", "label": "BOM Code", "name": "bomCode", "type": "text"}, {"fieldProps": {"placeholder": "模糊搜索"}, "id": "u:a17c756eb150", "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "text"}], "id": "u:bd16eed8687a", "name": "chainSearchPage", "rowKey": "id", "search": {"defaultCollapsed": false, "labelWidth": 110}, "tableColumns": [{"fixed": "left", "id": "u:30239a473b49", "label": "序号", "name": "id", "type": "text", "width": 120}, {"id": "u:dc7678b879c7", "label": "BOM 配置类型", "name": "bomConfigTypeName", "type": "text", "width": 120}, {"id": "u:952a5e7eb895", "label": "一级类目", "name": "level1CategoryName", "type": "text"}, {"id": "u:7aad1634ed6f", "label": "BOM 类型", "name": "bomTypeName", "type": "text"}, {"id": "u:c3e03df83c24", "label": "BOM 来源", "name": "bomSourceName", "type": "text"}, {"id": "u:b228c34647c3", "label": "BOM Code", "name": "bomCode", "type": "text"}, {"id": "u:0ad31b46b248", "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "text"}, {"align": "right", "id": "u:70d7728bb416", "label": "BOM 使用量", "name": "bomUsage", "type": "text", "width": 120}, {"body": [{"id": "u:1aa2652cddc0", "label": "${bomUsage}", "type": "tag"}], "id": "u:a0a8165498d1", "label": "BOM 单位", "name": "bomUnit", "type": "custom"}, {"id": "u:ec0c4987bea3", "label": "版本", "name": "version", "type": "text"}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "label": "取消", "type": "button"}, {"actionType": "confirm", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"messages": {"success": "保存成功"}, "requestAdaptor": "\n                  if(api.data.levelCategoryIds && api.data.levelCategoryIds.length > 0){\n                    api.data.level1CategoryId = api.data.levelCategoryIds[0];\n                    delete api.data.levelCategoryIds;\n                  }\n                  return api;", "url": "/scp-cost/goods/bomLevel1Category/save"}, "body": [{"api": {"adaptor": "\n                        return {\n                          status: 200,\n                          data: response.data.bomConfigTypeEnum\n                        };\n                      ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择配置类型", "required": true}]}, "label": "BOM 配置类型", "name": "bomConfigType", "type": "select"}, {"fieldProps": {"disabled": true, "level": 1}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "label": "类目选择", "linkage": [{"event": "onChange", "setValues": {"level1CategoryName": "${data[0].categoryName}"}}], "name": "levelCategoryIds", "proFieldProps": {"mode": "read"}, "type": "scmCategory"}, {"api": {"adaptor": "\n                        return {\n                          status: 200,\n                          data: response.data.bomSourceEnum\n                        };\n                      ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择来源", "required": true}]}, "label": "BOM 来源", "name": "bomSource", "type": "select"}, {"api": {"adaptor": "\n                        return {\n                          status: 200,\n                          data: response.data.bomTypeEnum\n                        };\n                      ", "data": {"optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"}, "method": "post", "url": "/scp-cost/ops/query/enums"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择类型", "required": true}]}, "label": "BOM 类型", "name": "bomType", "type": "select"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "label": "BOM Code", "name": "bomCode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"fieldProps": {"maxLength": 100, "showCount": true}, "formItemProps": {"rules": [{"message": "请选择类目", "required": true}]}, "label": "BOM 名称", "name": "bom<PERSON>ame", "type": "textarea"}, {"fieldProps": {"precision": 10}, "formItemProps": {"rules": [{"message": "使用量不能为空", "required": true}]}, "label": "BOM 使用量", "name": "bomUsage", "type": "digit"}, {"fieldProps": {"maxLength": 50, "showCount": true}, "formItemProps": {"rules": [{"message": "单位不能为空", "required": true}]}, "label": "BOM 单位", "name": "bomUnit", "type": "text"}, {"formItemProps": {"hidden": true}, "label": "level1CategoryName", "name": "level1CategoryName", "type": "text"}, {"formItemProps": {"hidden": true}, "label": "id", "name": "id", "type": "text"}], "onFormInit": "const { level1CategoryId, ...ret } = values; return { levelCategoryIds: [ level1CategoryId ], ...ret }", "type": "form"}, "title": "编辑", "type": "dialog", "width": 700}, "id": "u:bc570e18fcd2", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}"}, "messages": {"failed": "删除失败", "success": "删除成功"}, "method": "post", "url": "/scp-cost/goods/bomLevel1Category/remove"}, "confirmText": "是否确认删除", "danger": true, "id": "u:edd6fb7b2c33", "label": "删除", "type": "button"}], "fixed": "right", "id": "u:1f2fae537cd1", "label": "操作", "type": "operation", "width": 100}], "type": "chainSearchPage"}], "id": "u:4caa2a3b183a", "subTitle": "数据格式化、下拉枚举查询、联动、新增、编辑、删除、导入、导出、导入结果查询", "title": "普通列表页", "type": "page"}