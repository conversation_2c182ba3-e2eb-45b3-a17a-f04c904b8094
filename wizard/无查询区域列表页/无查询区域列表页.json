{"body": [{"actions": [], "api": {"method": "post", "url": "/tms/admin/transportCabin/page"}, "formColumns": [], "id": "u:c61a67ebea74", "name": "chainSearchPage", "options": false, "rowSelection": false, "search": false, "tableColumns": [{"id": "u:cab35d72826e", "label": "运输产品分层编码", "name": "cabinCode", "type": "text"}, {"id": "u:d96d67635ce1", "label": "运输产品分层名称", "name": "cabinName", "type": "text"}, {"id": "u:bc234fb1de1a", "label": "是否兜底", "name": "isDefaultDesc", "type": "text"}, {"id": "u:566b8e8b6d6f", "label": "是否触发成本倒挂判断", "name": "costInvertedDesc", "type": "text"}, {"id": "u:c37b0953ceee", "label": "品牌直发内部轮询", "name": "ppzfPollDesc", "type": "text"}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:3f3ef07ae84e", "label": "取消", "type": "button"}], "body": [{"actions": [], "api": {"method": "POST", "prefix": "https://t1-wizard-server.shizhuang-inc.net", "url": "/wizard/mock/add"}, "body": [{"id": "u:c2a6aeab9a4a", "label": "运输产品分层编码", "name": "cabinCode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:71d2354536c5", "label": "运输产品分层名称", "name": "cabinName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:3517bf9baabc", "label": "是否兜底", "name": "isDefaultDesc", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:dc2a30e01f2e", "label": "是否触发成本倒挂判断", "name": "costInvertedDesc", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:f3e00c0f20e6", "label": "品牌直发内部轮询", "name": "ppzfPollDesc", "proFieldProps": {"mode": "read"}, "type": "text"}], "id": "u:cdae69295188", "initApi": {"data": {"cabinCode": "${cabinCode}"}, "method": "get", "url": "/tms/admin/transportCabin/get"}, "labelCol": {"style": {"width": 200}}, "title": "表单", "type": "form"}], "id": "u:5b96a8ff4054", "title": "查看", "type": "dialog", "width": 600}, "id": "u:8eb56843f0ae", "label": "查看", "level": "link", "type": "button"}, {"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "label": "取消", "type": "button"}, {"actionType": "confirm", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"messages": {"success": "保存成功"}, "url": "/tms/admin/transportCabin/updateByCabinCode"}, "body": [{"fieldProps": {"disabled": false, "maxLength": 50}, "formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "label": "运输产品分层编码", "name": "cabinCode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"fieldProps": {"disabled": false, "maxLength": 50}, "formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "label": "运输产品分层名称", "name": "cabinName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"fieldProps": {"disabled": false, "options": [{"label": "是", "value": 1}, {"label": "否", "value": 0}]}, "formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "label": "是否兜底", "name": "isDefault", "proFieldProps": {"mode": "read"}, "type": "select"}, {"fieldProps": {"options": [{"label": "是", "value": 1}, {"label": "否", "value": 0}]}, "formItemProps": {"rules": [{"message": "请输入", "required": true}]}, "label": "是否触发成本倒挂判断", "name": "costInverted", "type": "select"}, {"fieldProps": {"options": [{"label": "支持", "value": 1}, {"label": "不支持", "value": 0}]}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "label": "品牌直发内部轮询", "name": "ppzfPoll", "type": "select"}], "initApi": {"data": {"cabinCode": "${cabinCode}"}, "url": "/tms/admin/transportCabin/get"}, "labelCol": {"style": {"width": "160px"}}, "type": "form"}, "title": "编辑", "type": "dialog", "width": 600}, "id": "u:b929f26710a2", "label": "编辑", "level": "link", "type": "button"}, {"label": "操作记录", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:22151fb028ec", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:f69d0d381721", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"id": "${id || 1}", "tableName": "des_transport_cabin"}, "method": "post", "url": "/tms/admin/log/getLogList"}, "formColumns": [], "id": "u:322707c55516", "label": "列表", "name": "chainSearchPage", "options": true, "rowKey": "id", "search": false, "tableAlertRender": false, "tableColumns": [{"label": "操作人", "name": "name", "type": "text", "width": 100}, {"label": "操作时间", "name": "modifyTime", "type": "text", "width": 170}, {"label": "操作类型", "name": "operationName", "type": "text", "width": 100}, {"label": "操作内容", "name": "operationContent", "type": "text", "width": 400}], "type": "chainSearchPage"}], "id": "u:1cf9cd086642", "title": "查看操作日志", "type": "dialog", "width": 900}}]}}, "type": "button"}], "fixed": "right", "id": "u:f952396fa636", "label": "操作", "type": "operation", "width": 100}], "type": "chainSearchPage"}], "id": "u:3700901b5652", "subTitle": "影藏了搜索区和工具栏", "title": "无查询区域列表页", "type": "page"}