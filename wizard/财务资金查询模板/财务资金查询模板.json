{"body": [{"api": {"adaptor": "const convertEnum = (key, list) => {\n  return list?.reduce(\n    (result, v) => ({\n      ...result,\n      [v?.code || v?.desc]: convertLabel(v, key),\n    }),\n    {},\n  );\n};\nconst convertLabel = (item, enumKey) => {\n  if (enumKey === 'CURRENCY') {\n    return `${item?.code} - ${item?.desc}`;\n  }\n  if (enumKey === 'COUNTRY') {\n    return `${item?.desc} - ${item?.ext}`;\n  }\n  return item?.desc || '';\n};\nconst res = payload;\nconst resultMap = res?.optionResultMap || {};\nconst enumData = {};\n\nObject.entries(resultMap).forEach(([key, value]) => {\n  // 特殊处理币种\n  if (key === 'CURRENCY') {\n    enumData.CURRENCY = {\n      CNY: 'CNY - 人民币',\n      USD: 'USD - 美元',\n      EUR: 'EUR - 欧元',\n      HKD: 'HKD - 港币',\n      CNH: 'CNH - 中国离岸人民币',\n      GBP: 'GBP - 英镑',\n      JPY: 'JPY - 日元',\n      CAD: 'CAD - 加元',\n      AUD: 'AUD - 澳元',\n      KRW: 'KRW - 韩元',\n      SGD: 'SGD - 新加坡元',\n      ...convertEnum(key, value),\n    };\n  } else if (key === 'INST') {\n    // 特殊处理渠道简称，拆分俩枚举\n    enumData.INST = convertEnum(key, value);\n    enumData.INST_B = convertEnum(\n      key,\n      (value || []).filter((v) => v.ext === 'B'),\n    );\n    enumData.INST_T = convertEnum(\n      key,\n      (value || []).filter((v) => v.ext === 'T'),\n    );\n  } else {\n    enumData[key] = convertEnum(key, value);\n  }\n});\nreturn {\n  status: 200, // 200 表示请求成功，否则按错误处理\n  msg: \"请求成功\",\n  data: enumData,\n};", "data": {"appId": "gauss"}, "method": "post", "requestAdaptor": "api.data.options = [\n  {\"optionKey\":\"SUBJECT\"},\n  {\"optionKey\":\"COUNTRY\"},\n  {\"optionKey\":\"CHANNEL_TYPE\"},\n  {\"optionKey\":\"INST\"},\n  {\"optionKey\":\"CURRENCY\"},\n  {\"optionKey\":\"YES_NO\"},\n  {\"optionKey\":\"ACCOUNT_PAYMENT\"},\n  {\"optionKey\":\"INST_ACCOUNT_STATUS\"},\n  {\"optionKey\":\"ALLOCATION_LEVEL\"},\n  {\"optionKey\":\"PAY_ACCESS_TYPE\"},\n  {\"optionKey\":\"PRODUCT_ACCESS_TYPE\"},\n  {\"optionKey\":\"REALTIME_BALANCE_ACCESS_TYPE\"},\n  {\"optionKey\":\"DAILY_BALANCE_ACCESS_TYPE\"},\n  {\"optionKey\":\"BALANCE\"},\n  {\"optionKey\":\"FLOW\"},\n  {\"optionKey\":\"PROOF_TYPE\"},\n  {\"optionKey\":\"OVER_SEA\"},\n  {\"optionKey\":\"VIRTUAL_FLAG\"},\n  {\"optionKey\":\"KEY_STATUS\"},\n  {\"optionKey\":\"INST_FULL_NAME\"},\n  {\"optionKey\":\"INST_PRODUCT\"},\n  {\"optionKey\":\"ACCOUNT_DEPOSIT\"},\n  {\"optionKey\":\"ACCOUNT_APPLY\"},\n  {\"optionKey\":\"ACCOUNT_APPROVAL_STATUS\"},\n  {\"optionKey\":\"BANK_BRANCH\"},\n  {\"optionKey\":\"ACCOUNT_LEVEL\"},\n  {\"optionKey\":\"RECON_PERIOD\"},\n  {\"optionKey\":\"INDIRECT_CHANNEL_NAME\"},\n  {\"optionKey\":\"ACCOUNT_KEY_TYPE\"},\n  {\"optionKey\":\"ACCOUNT_KEY_AUTHORITY\"}\n];\nreturn api;", "url": "/fund/finance-fund-base/base/option"}, "body": [{"actions": [], "api": {"data": {"appId": "gauss"}, "method": "post", "url": "/fund/file/operation/inst-account-flow/page"}, "formColumns": [{"fieldProps": {"fieldNames": {}, "options": "${SUBJECT|objectToArray}"}, "id": "u:4e3da0fbe7c1", "label": "公司主体", "name": "subjectCode", "type": "select"}, {"fieldProps": {"fieldNames": {}, "options": "${INST|objectToArray}"}, "id": "u:0836ea471e35", "label": "渠道简称", "name": "instCode", "type": "select"}, {"fieldProps": {"options": [{"label": "处理中", "value": "P"}, {"label": "处理成功", "value": "S"}, {"label": "处理失败", "value": "F"}, {"label": "待处理", "value": "W"}]}, "id": "u:5e66e3e9c440", "label": "处理状态", "name": "status", "type": "select"}, {"api": {"adaptor": "\n  // 初始化返回结果\n  let result = {\n    status: response.status || 200,\n    code: response.code,\n    msg: response.msg,\n    data: []\n  };\n  console.log(payload.contents, 'payload.contents')\n  // 判断payload是否有值\n  if (payload.contents.length > 0) {\n      // 遍历contents\n    result.data = payload.contents.map((v) => {\n        return {\n          label:\n            `${v.instAccountRespDTO.instAccountName}(${v.instAccountRespDTO.instAccountNo})` ||\n            '',\n          value: v.instAccountRespDTO.instAccountNo || '',\n          data: v,\n        };\n      });\n      console.log(payload.contents, 'payload.contents')\n  }\n\n  // 返回处理好的数据\n  return result;\n", "data": {"appId": "gauss", "instAccountNo": "${keyWords}"}, "method": "post", "sendOn": "${!!keyWords}", "url": "/fund/inst/account/pageQuery"}, "fieldProps": {"fetchDataOnSearch": true, "showSearch": true}, "id": "u:eac43475175c", "label": "账号", "name": "instAccountNo", "type": "select"}, {"colSize": 1, "endName": "endTime", "fieldProps": {"format": "YYYY-MM-DD HH:mm:ss", "ranges": {}}, "id": "u:a84978792e58", "initialValue": "${[DATET<PERSON>TR(DATEMODIFY(TODAY(), -1, 'month'), 'YYYY-MM-DD'), DATETOSTR(TODAY(), 'YYYY-MM-DD 23:59:59')]}", "label": "日期", "name": "confirmDate", "startName": "startTime", "type": "rangePicker"}], "id": "u:f19cb8478eb6", "name": "chainSearchPage", "tableColumns": [{"fieldProps": {"options": []}, "id": "u:9b787b03c015", "label": "机构账户名称", "name": "instAccountName", "type": "text"}, {"fieldProps": {"options": "${SUBJECT|objectToArray}"}, "id": "u:2239468bb6aa", "label": "主体编码", "name": "subjectCode", "type": "select"}, {"fieldProps": {"options": "${INST|objectToArray}"}, "id": "u:45d6dca1e2e6", "label": "银行（渠道简称）", "name": "instCode", "type": "select"}, {"id": "u:e656dbfa2293", "label": "账户", "name": "instAccountNo", "type": "text"}, {"fieldProps": {"options": [{"label": "境内", "value": "Y"}, {"label": "境外", "value": "N"}]}, "id": "u:0d009c062af7", "label": "境外标识", "name": "overseas", "type": "select"}, {"body": [{"id": "u:5eafa365a8fa", "label": "${fileName}", "level": "link", "onEvent": {"click": {"actions": [{"actionType": "saveAs", "api": "${fileUrl}", "fileName": "${fileName}"}]}}, "style": {"padding": "0"}, "type": "button"}], "id": "u:f19bcf839cb2", "label": "文件名", "name": "fileName", "type": "custom"}, {"fieldProps": {"options": [{"label": "处理中", "value": "P"}, {"label": "处理成功", "value": "S"}, {"label": "处理失败", "value": "F"}, {"label": "待处理", "value": "W"}]}, "id": "u:1497ecfd6858", "label": "处理状态", "name": "status", "type": "select"}, {"fieldProps": {"options": [{"label": "是", "value": "Y"}, {"label": "否", "value": "N"}]}, "id": "u:2077964738ff", "label": "是否空流水", "name": "emptyFlowTag", "type": "select"}, {"id": "u:46800723ea5d", "label": "创建人", "name": "creator", "type": "text"}, {"id": "u:e77a2c484ee6", "label": "修改人", "name": "modifier", "type": "text"}, {"id": "u:437ae1c01e5d", "label": "创建时间", "name": "gmtCreated", "type": "text"}, {"id": "u:d26bf5212188", "label": "修改时间", "name": "gmtModified", "type": "text"}], "type": "chainSearchPage"}], "id": "u:e8ad898d9f6a", "name": "optionEnumDataService", "type": "service"}], "id": "u:faad4a30cb25", "type": "page"}