{"body": [{"actions": [], "api": {"method": "post", "url": "/finance-promotion/fcm/backend/queryBudgetList"}, "expandable": {"body": {"dataSource": "${expandedInfo.record.budgetPurposes}", "id": "u:f4e7fb1b0a35", "pagination": false, "rowKey": "id", "search": false, "tableColumns": [{"id": "u:2d46cf178064", "label": "预算用途ID", "name": "purposeId", "type": "text"}, {"id": "u:f20f6d878382", "label": "申请人", "name": "<PERSON><PERSON><PERSON>", "type": "text"}, {"id": "u:91616522a424", "label": "预算用途名称", "name": "purposeName", "type": "text"}, {"id": "u:2f19412fd1e9", "label": "申请时间", "name": "applyTime", "type": "text"}, {"id": "u:894653025468", "label": "总预算金额", "name": "purposeAmount", "type": "text"}, {"id": "u:d795f51db3d7", "label": "已发放预算", "name": "sendAmount", "type": "text"}, {"id": "u:c6be96df66ea", "label": "剩余预算", "name": "leaveSendAmount", "type": "text"}, {"id": "u:e56be0cb9ab1", "label": "预核销预算", "name": "preVerifyAmount", "type": "text"}, {"id": "u:1ad2e3f587cd", "label": "剩余预核销预算", "name": "leavePreVerifyAmount", "type": "text"}, {"id": "u:ef8774d6d7b4", "label": "核销预算", "name": "realVerifyAmount", "type": "text"}, {"id": "u:cdbd0501fbc0", "label": "状态", "name": "purposeStatus", "type": "text"}, {"id": "u:20242019be5d", "label": "使用活动类型", "name": "applicationType", "type": "text"}, {"id": "u:ca4303b592d7", "label": "资产ID", "name": "awardIds", "type": "text", "width": 200}, {"actions": [{"id": "u:b907b7f6ea2e", "label": "详情", "type": "button"}, {"id": "u:c6fa6cfbf127", "label": "复制", "type": "button"}], "id": "u:4dad5f25499c", "label": "操作", "type": "operation"}], "toolBarRender": false, "type": "table"}}, "formColumns": [{"id": "u:d9155a03bd74", "label": "预算池ID", "name": "budgetId", "type": "text"}, {"id": "u:2334706522bb", "label": "预算池名称", "name": "budgetName", "type": "text"}, {"api": {"method": "get", "responseData": {"&": "$$", "data": "${budgetType|objectToArray}"}, "url": "/finance-promotion/fcm/backend/queryBudgetEnums"}, "id": "u:76019bf0193e", "label": "预算类型", "name": "budgetTypeList", "type": "select"}, {"api": {"method": "get", "responseData": {"&": "$$", "data": "${subsidyGoal|objectToArray}"}, "url": "/finance-promotion/fcm/backend/queryBudgetEnums"}, "id": "u:833531c867c5", "label": "补贴目的", "name": "subsidyPurpose", "type": "select"}, {"api": {"method": "get", "responseData": {"&": "$$", "data": "${budgetStatus|objectToArray}"}, "url": "/finance-promotion/fcm/backend/queryBudgetEnums"}, "id": "u:ea59f85de394", "label": "状态", "name": "status", "type": "select"}, {"api": {"method": "get", "responseData": {"&": "$$", "data": "${productType|objectToArray}"}, "url": "/finance-promotion/fcm/backend/queryBudgetEnums"}, "id": "u:959545b4511e", "label": "产品类型", "name": "productCode", "type": "select"}], "id": "u:a3963992197d", "name": "chainSearchPage", "rowKey": "id", "tableColumns": [{"copyable": false, "fixed": "left", "id": "u:b4a9bb715634", "label": "预算池ID", "name": "budgetId", "type": "text"}, {"id": "u:976ec902828b", "label": "预算池名称", "name": "budgetName", "type": "text"}, {"api": {"method": "get", "responseData": {"&": "$$", "data": "${budgetType|objectToArray}"}, "url": "/finance-promotion/fcm/backend/queryBudgetEnums"}, "id": "u:e6e0c8401b83", "label": "预算类型", "name": "budgetTypeList", "type": "select"}, {"id": "u:ef127ed42ec1", "label": "补贴目的", "name": "subsidyPurpose", "type": "text"}, {"api": {"method": "get", "responseData": {"&": "$$", "data": "${budgetStatus|objectToArray}"}, "url": "/finance-promotion/fcm/backend/queryBudgetEnums"}, "id": "u:0553c9395208", "label": "状态", "name": "status", "type": "select"}, {"id": "u:4731a702565e", "label": "「发放」总预算金额", "name": "topSendAmount", "tpl": "${topSendAmount|number}", "type": "text"}, {"id": "u:8756c2a64936", "label": "「预核销」总预算金额", "name": "topUseAmount", "tpl": "${topUseAmount|number}", "type": "text"}, {"api": {"method": "get", "responseData": {"&": "$$", "data": "${productType|objectToArray}"}, "url": "/finance-promotion/fcm/backend/queryBudgetEnums"}, "id": "u:e41c38ece6b2", "label": "产品类型", "name": "productCode", "type": "select"}, {"body": [{"hiddenOn": "${needBindTime}", "id": "u:e5c92089ff23", "style": {"textAlign": "left"}, "tpl": "无时效限制", "type": "tpl"}, {"hiddenOn": "${!needBindTime}", "id": "u:dba7b4501656", "style": {"textAlign": "left"}, "tpl": "${DATETOSTR(bindBegin, 'YYYY-MM-DD HH:mm:ss') + '~' + DATETOSTR(bindEnd, 'YYYY-MM-DD HH:mm:ss')}", "type": "tpl"}], "id": "u:7503150c1749", "label": "预算池时间", "name": "needBindTime", "type": "custom", "width": 240}], "type": "chainSearchPage"}], "id": "u:3700901b5652", "type": "page"}