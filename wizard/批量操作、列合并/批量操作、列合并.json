{"body": [{"actions": [{"actionType": "ajax", "api": {"data": {"effectType": 0, "idList": "${selectedRows}"}, "messages": {"failed": "修改失败", "success": "执行批量失效操作成功"}, "method": "post", "requestAdaptor": "const idList = api.data.idList.map(item => item.id)\napi.data.idList = idList\nreturn api", "url": "/lean/api/evaluate/standard/takeEffect"}, "confirmText": "确认将已选的  ${COUNT(selectedRowKeys)} 个检查项目状态变更为失效？", "danger": true, "disabled": "${ selectedRowKeys.length === 0}", "id": "u:357b4782df6e", "label": "批量失效", "level": "default", "reload": "chainSearchPage", "type": "button"}, {"actionType": "ajax", "api": {"data": {"effectType": 1, "idList": "${selectedRowKeys}"}, "messages": {"failed": "修改失败", "success": "执行批量生效操作成功"}, "method": "post", "requestAdaptor": "const idList = api.data.idList.map(item => item.id)\napi.data.idList = idList\nreturn api", "url": "/lean/api/evaluate/standard/takeEffect"}, "confirmText": "确认将已选的  ${COUNT(selectedRowKeys)} 个检查项目状态变更为生效？", "danger": false, "disabled": "${ selectedRowKeys.length === 0}", "id": "u:3e519b0b8798", "label": "批量生效", "level": "primary", "reload": "chainSearchPage", "type": "button"}, {"id": "u:821c1753fc5f", "label": "导 出", "onEvent": {"click": {"actions": [{"actionType": "download", "api": {"data": {"evaluateStandardId": "${event.data}"}, "method": "post", "requestAdaptor": "api.data.evaluateStandardId = api.data.evaluateStandardId?.evaluateStandardId?.queryParam?.evaluateStandardId\n\nreturn api", "url": "/lean/api/evaluate/standard/export"}}]}}, "type": "action"}], "api": {"adaptor": "const data = response?.data || []\nconst dataSource = []\ndata.forEach((info, index) => {\n  info.checked = false\n  if (info.items?.length) {\n    info.items.forEach((item, idx) => {\n      const obj = {\n        ...item,\n        key: item.id\n      }\n      delete obj.id;\n      dataSource.push({ ...info, ...obj, idx: idx + 1 })\n    })\n  } else {\n    const uniqueValue = Math.random().toString(36).substring(2) + Date.now().toString(36);\n    dataSource.push({ ...info, idx: index + 1, key: uniqueValue})\n  }\n})\nconst dataSourceN = dataSource.map(item => {\n  return {\n    ...item,\n    frequencyTypeDesc: item?.frequencyType?.desc,\n    issueStatusDesc: item?.issueStatus?.desc,\n    issueStatusValue: item?.issueStatus?.value,\n    statusDesc: item?.status?.desc,\n    statusValue: item?.status?.value,\n    taskFrequencyDesc: item?.taskFrequency?.desc,\n  }\n})\n\nreturn {\n  status: 200,\n  data: {\n    contents: dataSourceN,\n  }\n}", "method": "post", "url": "/lean/api/evaluate/standard/list"}, "bordered": true, "formColumns": [{"api": {"method": "get", "url": "/lean/api/evaluate/standard/queryPrj"}, "fieldProps": {"fieldNames": {"label": "evaluateProjectName", "value": "id"}, "mode": "single", "showSearch": true}, "id": "u:93d68b5844b4", "label": "评定项目", "name": "evaluateStandardId", "type": "select"}], "id": "u:c61a67ebea74", "name": "chainSearchPage", "onEvent": {"submit": {"actions": [{"actionType": "setValue", "args": {"value": {"evaluateStandardId": "${event.data}"}}, "componentId": "u:3700901b5652"}]}}, "pagination": false, "rowKey": "key", "rowSelection": {"rowSpan": "${id}", "type": "checkbox"}, "tableColumns": [{"id": "u:be59d1cb140c", "label": "评定项目", "name": "evaluateProjectName", "rowSpan": "${id}", "width": 140}, {"id": "u:00ea819bb07b", "label": "序号", "name": "idx", "type": "text", "width": 60}, {"id": "u:73cbf6acdb9f", "label": "评定内容", "name": "evaluateContent", "type": "text", "width": 380}, {"id": "u:b88824a4e4e0", "label": "检查频次", "name": "frequencyTypeDesc", "rowSpan": "${id}", "type": "text", "width": 100}, {"id": "u:29b8a661a625", "label": "任务下达频次", "name": "taskFrequencyDesc", "rowSpan": "${id}", "type": "text", "width": 120}, {"id": "u:b07af36b130b", "label": "总分", "name": "totalPoint", "rowSpan": "${id}", "type": "text", "width": 80}, {"id": "u:25ce63773904", "label": "扣分标准", "name": "point", "type": "text", "width": 100}, {"id": "u:f769beb9849f", "label": "备注", "name": "markDesc", "rowSpan": "${id}", "type": "text"}, {"id": "u:32f17b13d5ee", "label": "下达任务组织", "name": "orgNames", "rowSpan": "${id}", "type": "text"}, {"actions": [{"actionType": "switch", "active": "${statusValue === 1}", "api": {"adaptor": "const {issueStatus} = api.data\nconsole.log(1111, issueStatus)\nreturn {\n  status: 200,\n  msg: issueStatus === 0 ? '执行失效操作成功' : '执行失效操作成功'\n}", "data": {"effectType": "${statusValue === 1 ? 0 : 1}", "id": "${id}"}, "method": "post", "requestAdaptor": "api.data.idList=[api.data.id]\nreturn api", "url": "/lean/api/evaluate/standard/takeEffect"}, "checkedChildren": "生效", "confirmText": "${IF(statusValue === 1, '要把该检查项目状态设置为失效吗？','要把该检查项目状态设置为生效吗？')}", "id": "u:c3a2190d712c", "type": "button", "unCheckedChildren": "失效"}], "id": "u:41920fb1f5519", "label": "是否生效", "name": "statusValue", "rowSpan": "${id}", "type": "operation", "width": 120}], "type": "chainSearchPage"}], "id": "u:3700901b5652", "title": " table跟随指定列规则合并、批量操作选择框按指定列合并", "type": "page"}