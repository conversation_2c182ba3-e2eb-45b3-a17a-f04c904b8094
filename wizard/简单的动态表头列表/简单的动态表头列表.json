{"body": [{"actions": [{"exportMod": "afterSaleOrderSimulateDataExport", "id": "u:9faa6ecdad79", "label": "导出", "type": "lmsExport"}], "api": {"adaptor": "\nlet result = {\n  status: response.status || 200,\n  code: response.code,\n  msg: response.msg,\n  data: {\n    contents: []\n  }\n};\n\nif (payload) {\n  let headerConfig = payload.headerConfig;\n  let contentList = payload.contentList;\n\n  contentList.forEach(item => {\n    let content = {\n      warehouseName: item.warehouseName\n    };\n    headerConfig.forEach(header => {\n      content[header.prop] = item[header.prop];\n    });\n    result.data.contents.push(content);\n  });\n\n  if (payload.total) {\n    result.data.total = payload.total;\n  }\n  if (payload.pageNum) {\n    result.data.pageNum = payload.pageNum;\n  }\n}\n\nresult.data.tableColumns = [{\n  label: \"实际收货量\",\n  name: 1,\n  className: \"table-blue\",\n  children: payload.headerConfig.filter(item => item.type == 1).map(item => ({ \n    label: item.label, \n    className: \"table-blue\",\n    name: item.prop,\n    width: \"100px\"\n    }))\n}, {\n  label: \"预计收货量\",\n  name: 2,\n  className: \"table-yellow\",\n  children: payload.headerConfig.filter(item => item.type == 2).map(item => ({ \n    label: item.label,\n    className: \"table-yellow\",\n    name: item.prop,\n    width: \"100px\"\n    }))\n}];\n\nreturn result;\n", "method": "post", "silent": true, "url": "/sandop/admin/afterSale/futureOrder/simulate/list"}, "formColumns": [{"api": {"adaptor": "\n  // 初始化result对象\n  let result = {\n    status: 200,\n    code: response.code,\n    msg: response.msg,\n    data: []\n  };\n\n  // 容错处理，判断payload是否有值\n  if (payload && payload.length > 0) {\n    // 遍历payload\n    payload.forEach(item => {\n      // 判断item是否为数组\n       result.data.push({\n          label: item,\n          value: item\n        });\n    });\n  }\n\n  // 返回处理好的数据\n  return result;\n", "method": "get", "url": "/sandop/admin/afterSale/futureOrder/simulate/versions"}, "fieldProps": {}, "formItemProps": {"rules": [{"message": "必须选择仿真版本", "required": true}]}, "id": "u:ae15ef2200f3", "initialValue": "", "label": "单量仿真版本", "name": "forecastVersion", "type": "select"}], "id": "u:fdc692c19c38", "manualRequest": true, "name": "chainSearchPage", "rowKey": "id", "search": {"labelWidth": 120}, "tableAlertRender": false, "tableColumns": [{"id": "u:6531b1c281cd", "label": "仓库", "name": "warehouseName", "type": "text", "width": 60}], "tableColumnsConfig": {"addAfterName": "warehouseName", "data": "tableColumns"}, "toolBarRender": true, "type": "chainSearchPage"}], "id": "u:09d5a3dfcf51", "title": "简单的动态表头列表", "type": "page", "wrapperCustomStyle": {".table-blue": {"background": "#f2fafa"}, ".table-yellow": {"background": "#faf3ea"}}}