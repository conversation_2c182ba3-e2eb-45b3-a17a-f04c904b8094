{"body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:8f097adc80fe", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:b382051e7ac6", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$"}, "url": "/tms/admin/fulfillmentModeConfig/create"}, "body": [{"fieldProps": {"options": [{"label": "仓维度", "value": 0}, {"label": "商家维度", "value": 2}]}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:c61bb5ac6cec", "label": "配置维度", "name": "deliveryMode", "type": "radio"}, {"api": {"method": "get", "url": "/tms/admin/enum/repositories"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "labelInValue": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 0}", "id": "u:23a153df57d5", "label": "始发仓库", "labelName": "originWarehouseName", "name": "originWarehouse", "type": "select", "valueName": "originWarehouseCode"}, {"api": {"adaptor": "return {status: 200,data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))};", "method": "get", "requestAdaptor": "const data = api.data || {};\n  data.name = data?.keyWords || ''; \n  api.data = {...data} \n return api;", "sendOn": "${keyWords}", "url": "/merchant/merchant/getMerchantIdByNameEs"}, "fieldProps": {"fetchDataOnSearch": true, "fieldNames": {"label": "name", "value": "merchantId"}, "placeholder": "请输入后查询"}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:35a2ee435901", "label": "直发商家", "name": "merchantId", "type": "select"}, {"api": {"adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}", "data": {"lang": "zh", "maxDeep": 3}, "method": "post", "url": "/tms/admin/address/queryAddressTreeByDeep"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:5fb959eb1ca0", "label": "始发城市", "linkage": [{"event": "onChange", "setValues": {"originCityCode": "${data[1].code}", "originCityName": "${data[1].name}"}}], "name": "originCity", "type": "cascader"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode!= 2}", "id": "u:8ed20caf07dc", "label": "城市Code", "name": "originCityCode", "type": "text"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode !=2}", "id": "u:589bd5e2f99b", "label": "城市名称", "name": "originCityName", "type": "text"}, {"api": {"adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }", "data": {"enumTypes": "transportFulfillModeEnum"}, "method": "post", "url": "/tms/admin/enum/listEnums"}, "id": "u:8dd726d9f0ab", "label": "运输履约模式", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "fulfillmentMode", "type": "select"}, {"api": {"url": "/tms/admin/enum/getLogistics"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:3101383b3836", "label": "承运商", "linkage": [{"event": "onChange", "resetKeys": ["logisticsProductCode", "customerCodeId"], "setValues": {"customerCodeId": null, "logisticsProductCode": null}}], "name": "logisticsCode", "type": "select"}, {"api": {"data": {"logisticsName": "${logisticsCode}"}, "sendOn": "${logisticsCode}", "url": "/tms/admin/enum/getLogisticsProduct"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:ad4bb5a73494", "label": "运输产品", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "logisticsProductCode", "type": "select"}, {"api": {"adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }", "data": {"logisticsCode": "${logisticsCode}", "logisticsProductCode": "${logisticsProductCode}", "transportFulfillmentMode": "${fulfillmentMode}"}, "method": "post", "sendOn": "fulfillmentMode && logisticsProductCode", "url": "/tms/admin/customerCode/getCustomerCodesV2"}, "formItemProps": {"rules": [{"message": "请选择", "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"}]}, "id": "u:b5a2405c516f", "label": "月结卡号", "name": "customerCodeId", "params": {"t": "${Date.now()}"}, "type": "select"}], "id": "u:6bc905badd74", "type": "form"}], "id": "u:462f993511f3", "title": "新增", "type": "dialog", "width": 700}, "id": "u:3a46389b7e47", "label": "新建", "level": "primary", "type": "button"}, {"fileName": "导入模板", "fileNameLabel": "模板下载", "fileUrl": "https://cdn.poizon.com/node-common/3a2d7339-93f6-74ae-6450-d15753638127.xlsx", "id": "u:9e003c343b6a", "importMod": "importTransportFulfillMode", "label": "导入", "type": "lmsImport"}, {"id": "u:99e4688cf8ec", "importMod": "importTransportFulfillMode", "label": "导入结果查询", "type": "lmsImportResult"}, {"actionType": "export", "api": {"data": {"exportMod": "TRANSPORT_FULFILLMENT_MODE_CONFIG"}, "method": "post", "url": "/lmsnew/reportCenter/report/v1/export"}, "bizType": "lmsnew", "id": "u:61bfcbed8fb1", "label": "导出", "level": "primary", "type": "button"}], "api": {"adaptor": "return {\n  'status': 200,\n  'msg': '请求成功',\n  'data': {\n    'rows': response.data.list,\n    'count': response.data.total\n  }\n}", "method": "post", "requestAdaptor": "const { originWarehouseCode, merchantId, fulfillmentMode, pageNum, pageSize, merchantIdV2 } = api.data\napi.data = {\n  pageNum, pageSize,\n  originWarehouseCodes: originWarehouseCode ? [originWarehouseCode] : undefined,\n  merchantIds: merchantIdV2 || merchantId ? [merchantIdV2 || merchantId] : undefined,\n  fulfillmentModes: fulfillmentMode ? [fulfillmentMode] : undefined\n}\nreturn api", "url": "/tms/admin/fulfillmentModeConfig/page"}, "formColumns": [{"api": {"method": "get", "url": "/tms/admin/enum/repositories"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}}, "id": "u:787ebe451b98", "label": "始发仓库", "name": "originWarehouseCode", "type": "select"}, {"fieldProps": {"multiple": true}, "id": "u:420ffe2155c6", "label": "日期多选", "name": "date", "type": "date"}, {"api": {"adaptor": "return {\n  status: 200,\n  data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))\n};", "data": {"name": "${keyWords}"}, "method": "get", "sendOn": "${keyWords}", "url": "/merchant/merchant/getMerchantIdByNameEs"}, "fieldProps": {"fetchDataOnSearch": true, "fieldNames": {"label": "name", "value": "merchantId"}, "placeholder": "请输入后查询"}, "id": "u:ede536f16a2b", "label": "直发商家", "name": "merchantId", "type": "select"}, {"fieldProps": {"fetchDataOnSearch": true, "placeholder": "请输入后查询"}, "id": "u:cade04fc4f1b", "label": "直发商家ID", "name": "merchantIdV2", "type": "text"}, {"api": {"adaptor": "const mapData = response.mapData || {}\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }", "data": {"enumTypes": "transportFulfillModeEnum"}, "method": "post", "url": "/tms/admin/enum/listEnums"}, "id": "u:e3dd1d6b1ac2", "label": "履约模式", "name": "fulfillmentMode", "type": "select"}], "id": "u:c61a67ebea74", "name": "chainSearchPage", "revalidateOnFocus": true, "rowKey": "id", "search": true, "tableColumns": [{"id": "u:c44ce52946a2", "label": "始发仓库", "name": "originWarehouseName", "type": "text", "width": 160}, {"body": [{"body": [{"id": "u:c880a47168b5", "tpl": "${\"名称：\"+( sellerUserName || \"-\")}", "type": "tpl"}], "id": "u:452480073e84", "type": "container"}, {"body": [{"id": "u:220fd3928402", "tpl": "${\"ID：\"+ (sellerUserId || \"-\")}", "type": "tpl"}], "id": "u:25ddebd84582", "type": "container"}], "id": "u:bba80cf7ad52", "label": "直发商家", "name": "sellerUserName", "type": "custom", "width": 160}, {"id": "u:fce592c0a4f7", "label": "始发城市", "name": "originCityName", "type": "text"}, {"id": "u:b0790cc787d6", "label": "运输履约模式", "name": "fulfillmentModeName", "type": "text"}, {"id": "u:dd72e6c1d157", "label": "承运商", "name": "logisticsName", "type": "text"}, {"id": "u:71179d5c4f3e", "label": "运输产品", "name": "logisticsProductName", "type": "text"}, {"id": "u:1fb040dd3a05", "label": "月结卡号", "name": "customerCode", "type": "text"}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:675998f5b366", "label": "取消", "type": "button"}], "body": [{"actions": [], "body": [{"id": "u:5a1f5c197f2b", "label": "配置维度", "name": "deliveryModeDesc", "proFieldProps": {"mode": "read"}, "type": "text"}, {"hiddenOn": "${deliveryMode != 0}", "id": "u:4037673c4a68", "label": "始发仓库", "name": "originWarehouseName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"hiddenOn": "${deliveryMode != 2}", "id": "u:7b8ce38f35c3", "label": "直发商家", "name": "sellerUserName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"hiddenOn": "${deliveryMode != 2}", "id": "u:2f5c24da6728", "label": "始发城市", "name": "originCityName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:29ca5c766f5f", "label": "运输履约模式", "name": "fulfillmentModeName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:8335c1b01bc3", "label": "承运商", "name": "logisticsName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:efd3e704190b", "label": "运输产品", "name": "logisticsProductName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:43685a5759ea", "label": "月结卡号", "name": "customerCode", "proFieldProps": {"mode": "read"}, "type": "text"}], "id": "u:57f639732290", "initApi": {"data": {"id": "${id}"}, "method": "get", "url": "/tms/admin/fulfillmentModeConfig/detail"}, "title": "表单", "type": "form"}], "id": "u:66eda46202cb", "title": "查看", "type": "dialog", "width": 600}, "id": "u:8eb56843f0ae", "label": "查看", "level": "link", "type": "button"}, {"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:488f3f745723", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:5a3ab578c259", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$"}, "url": "/tms/admin/fulfillmentModeConfig/edit"}, "body": [{"formItemProps": {"hidden": true}, "id": "u:d97d9fb9d7ac", "label": "ID", "name": "id", "proFieldProps": {"mode": "read"}, "type": "text"}, {"fieldProps": {"disabled": false, "options": [{"label": "仓维度", "value": 0}, {"label": "商家维度", "value": 2}]}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:2885354c4f38", "label": "配置维度", "name": "deliveryMode", "proFieldProps": {"mode": "read"}, "type": "radio"}, {"api": {"method": "get", "url": "/tms/admin/enum/repositories"}, "fieldProps": {"disabled": false, "fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "labelInValue": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 0}", "id": "u:9f5910067d23", "label": "始发仓库", "labelName": "originWarehouseName", "name": "originWarehouse", "proFieldProps": {"mode": "read"}, "type": "select", "valueName": "originWarehouseCode"}, {"fieldProps": {"disabled": false, "placeholder": "请输入后查询"}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:286124b7250c", "label": "直发商家", "name": "sellerUserName", "proFieldProps": {"mode": "read"}, "type": "select"}, {"api": {"adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}", "data": {"lang": "zh", "maxDeep": 3}, "method": "post", "url": "/tms/admin/address/queryAddressTreeByDeep"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:6a4b66400afc", "label": "始发城市", "linkage": [{"event": "onChange", "setValues": {"originCityName": "${data[1].name}"}}], "name": "originCity", "proFieldProps": {"mode": "read"}, "type": "cascader"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode!= 2}", "id": "u:ec33373a704f", "label": "城市Code", "name": "originCityCode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode !=2}", "id": "u:94ae9a297094", "label": "城市名称", "name": "originCityName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"api": {"adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }", "data": {"enumTypes": "transportFulfillModeEnum"}, "method": "post", "url": "/tms/admin/enum/listEnums"}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:85e6986c3990", "label": "运输履约模式", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "fulfillmentMode", "type": "select"}, {"api": {"url": "/tms/admin/enum/getLogistics"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:bb64db5388be", "label": "承运商", "linkage": [{"event": "onChange", "resetKeys": ["logisticsProductCode", "customerCodeId"], "setValues": {"customerCodeId": null, "logisticsProductCode": null}}], "name": "logisticsCode", "type": "select"}, {"api": {"data": {"logisticsName": "${logisticsCode}"}, "sendOn": "${logisticsCode}", "url": "/tms/admin/enum/getLogisticsProduct"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:f714a92015b9", "label": "运输产品", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "logisticsProductCode", "type": "select"}, {"api": {"adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }", "data": {"logisticsCode": "${logisticsCode}", "logisticsProductCode": "${logisticsProductCode}", "transportFulfillmentMode": "${fulfillmentMode}"}, "method": "post", "sendOn": "fulfillmentMode && logisticsProductCode", "url": "/tms/admin/customerCode/getCustomerCodesV2"}, "formItemProps": {"rules": [{"message": "请选择", "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"}]}, "id": "u:2a69a4e4366e", "label": "月结卡号", "name": "customerCodeId", "params": {"t": "${Date.now()}"}, "type": "select"}], "id": "u:b7373d316c7c", "initApi": {"adaptor": "const {originWarehouseCode, originWarehouseName, originProvinceCode,originCityCode, deliveryMode, customerCodeId, ...rest}=response.data;\nconst data = { deliveryMode, customerCodeId: customerCodeId === 0 ? null : customerCodeId, ...rest }\nif (deliveryMode === 0) {\n  data.originWarehouse = {label: originWarehouseName, value: originWarehouseCode}\n}\nif (deliveryMode === 2) {\n  data.originCity = [originProvinceCode, originCityCode]\n}\nreturn {status: 0, data: data }", "data": {"id": "${id}"}, "url": "/tms/admin/fulfillmentModeConfig/detail"}, "type": "form"}], "id": "u:8617c397bda5", "title": "编辑", "type": "dialog", "width": 700}, "id": "u:b929f26710a2", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}"}, "messages": {"failed": "删除失败", "success": "删除成功"}, "method": "post", "url": "/tms/admin/fulfillmentModeConfig/delete"}, "confirmText": "是否确认删除", "danger": true, "id": "u:4a6bb394b3a8", "label": "删除", "type": "button"}, {"id": "u:aae9dfe8aa96", "label": "操作记录", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:5b4ca903f79f", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:4256391b6fab", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"id": "${id || 148}", "tableName": "des_transport_fulfillment_mode_config"}, "method": "post", "url": "/tms/admin/log/getLogList"}, "formColumns": [], "id": "u:6d121dbf2ced", "label": "列表", "name": "chainSearchPage", "options": true, "rowKey": "id", "search": false, "tableAlertRender": false, "tableColumns": [{"id": "u:af39355bb3e6", "label": "操作人", "name": "name", "type": "text", "width": 60}, {"id": "u:0b7a8e69e9b2", "label": "操作时间", "name": "modifyTime", "type": "text", "width": 80}, {"id": "u:e347e7c72d1d", "label": "操作类型", "name": "operationName", "type": "text", "width": 80}, {"id": "u:84501b7ff89d", "label": "操作内容", "name": "operationContent", "type": "text"}], "type": "chainSearchPage"}], "id": "u:8eda1238e1de", "title": "查看操作日志", "type": "dialog", "width": 900}}]}}, "type": "button"}, {"id": "u:cbbc908432f1", "label": "步骤详情", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:7ea2b83edc6e", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:88a9d7c17be0", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": "/mock/form/saveForm", "body": [{"current": 1, "direction": "vertical", "disableChange": false, "id": "u:fa89b2dc35da", "items": [{"body": [{"height": 80, "id": "u:d21d9970a0f1", "mode": "stretch", "src": "https://cdn.dewu.com/node-common/526843c0-1469-d7d8-24cf-4563efa6fe75-425-425.png", "type": "picture", "width": 80}], "description": "This is a description.", "id": "u:3b32199e3943", "title": "Finished"}, {"body": [{"height": 80, "id": "u:690d0916f520", "mode": "stretch", "src": "https://cdn.dewu.com/node-common/14a55158-3b2d-babe-9bb8-8b5192ae05fc-1920-817.png", "type": "picture"}], "description": "This is a description.", "id": "u:bf403f0d8dab", "subTitle": "Left 00:00:08", "title": "In Progress"}, {"body": [{"height": 80, "id": "u:b015169ba742", "mode": "stretch", "src": "https://cdn.dewu.com/node-common/4874d8b4-9f0d-cfe1-4080-1ac2dffdbf1c-1920-817.png", "type": "picture"}], "description": "This is a description.", "id": "u:f057573f203b", "title": "Waiting"}], "labelPlacement": "horizontal", "mode": "default", "size": "default", "type": "steps"}], "id": "u:5997b3f367cc", "type": "form"}], "id": "u:fecab86abadb", "title": "详情", "type": "dialog"}}]}}, "type": "button"}], "fixed": "right", "id": "u:dd06d7b208ec", "label": "操作", "type": "operation", "width": 200}], "type": "chainSearchPage"}], "id": "u:3700901b5652", "subTitle": "数据格式化、下拉枚举查询、下拉枚举远程搜索、新增（form表单内容根据配置维度动态变化）、编辑、删除、导入、导出", "title": "常规列表页", "type": "page"}