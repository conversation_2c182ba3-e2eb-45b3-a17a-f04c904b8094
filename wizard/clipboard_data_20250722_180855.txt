粘贴板内容汇总
==================================================

菜单项: 简单列表页
时间: 2025-07-22T17:59:54.892384
内容长度: 7726 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "dialog",
          "dialog": {
            "actions": [
              {
                "actionType": "cancel",
                "id": "u:43d435cd3cf0",
                "label": "取消",
                "type": "button"
              },
              {
                "actionType": "confirm",
                "id": "u:77b46569b1ed",
                "label": "确认",
                "level": "primary",
                "reload": "chainSearchPage",
                "type": "button"
              }
            ],
            "body": {
              "actions": [],
              "api": {
                "messages": {
                  "success": "新增配置项成功"
                },
                "url": "/qms/lab/v1/createConfig"
              },
              "body": [
                {
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请输入检测项名称",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:2985a94acf89",
                  "label": "检测项",
                  "name": "detectName",
                  "type": "text"
                },
                {
                  "id": "u:723f3e973c3d",
                  "label": "适用标准",
                  "name": "applyNorm",
                  "type": "text"
                },
                {
                  "id": "u:60c9df7c54e6",
                  "label": "描述",
                  "name": "detectDescribe",
                  "type": "textarea"
                }
              ],
              "id": "u:3504e8889600",
              "type": "form",
              "wrapperCol": {
                "style": {
                  "flex": 0.9
                }
              }
            },
            "id": "u:67c02dd390d0",
            "title": "新增",
            "type": "dialog",
            "width": 700
          },
          "id": "u:1ac7f07c86ac",
          "label": "新增",
          "level": "primary",
          "type": "button"
        },
        {
          "actionType": "import",
          "api": {
            "data": {
              "bizType": "4"
            },
            "method": "post",
            "url": "/qms/excel/import"
          },
          "fileName": "检测项导入模板",
          "fileNameLabel": "导入模版",
          "fileUrl": "https://h5static.dewucdn.com/node-common/01d5431c-90f1-217b-4e83-af6f9556d84a.xlsx",
          "id": "u:62b113ceece6",
          "label": "导入",
          "level": "primary",
          "type": "button"
        }
      ],
      "api": {
        "method": "post",
        "url": "/qms/lab/v1/queryConfigList"
      },
      "formColumns": [
        {
          "api": {
            "adaptor": "return {\n  status: 200,\n  data: response.data?.items || []\n};       ",
            "method": "post",
            "requestAdaptor": "api.data.pageSize = 100;\napi.data.detectName = api.data.keyWords;\ndelete api.data.keyWords;\nreturn api;",
            "url": "/qms/lab/v1/queryConfigList"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "detectName",
              "value": "detectName"
            }
          },
          "id": "u:d9155a03bd74",
          "label": "检测项",
          "name": "detectName",
          "type": "select"
        }
      ],
      "id": "u:a3963992197d",
      "name": "chainSearchPage",
      "rowKey": "id",
      "tableColumns": [
        {
          "copyable": true,
          "fixed": "left",
          "id": "u:b4a9bb715634",
          "label": "检测项ID",
          "name": "id",
          "type": "text"
        },
        {
          "id": "u:976ec902828b",
          "label": "检测项",
          "name": "detectName",
          "type": "text"
        },
        {
          "id": "u:5b0cfc063328",
          "label": "适用标准",
          "name": "applyNorm",
          "type": "text"
        },
        {
          "id": "u:c938fee8cc5a",
          "label": "描述",
          "name": "detectDescribe",
          "type": "text"
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:61bd16496566",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "submit",
                    "id": "u:b93bc81d8a2f",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": {
                  "actions": [],
                  "api": {
                    "url": "/qms/lab/v1/editConfig"
                  },
                  "body": [
                    {
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请输入检测项名称",
                            "required": true
                          }
                        ]
                      },
                      "id": "u:4395add20120",
                      "label": "检测项",
                      "name": "detectName",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "id": "u:e28ddb48babd",
                      "label": "适用标准",
                      "name": "applyNorm",
                      "type": "text"
                    },
                    {
                      "id": "u:d609f2d66a51",
                      "label": "描述",
                      "name": "detectDescribe",
                      "type": "textarea"
                    },
                    {
                      "formItemProps": {
                        "hidden": true
                      },
                      "id": "u:ea3e137a0e85",
                      "label": "id",
                      "name": "id",
                      "type": "text"
                    }
                  ],
                  "id": "u:771d7648c347",
                  "type": "form",
                  "wrapperCol": {
                    "style": {
                      "flex": 0.9
                    }
                  }
                },
                "id": "u:5475f8caa37d",
                "title": "编辑",
                "type": "dialog",
                "width": 700
              },
              "id": "u:33a5ab78d907",
              "label": "编辑",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "id": "${id}"
                },
                "messages": {
                  "success": "删除成功"
                },
                "method": "post",
                "url": "/qms/lab/v1/deleteConfig"
              },
              "confirmText": "是否确认删除",
              "danger": true,
              "id": "u:19e9f24a8266",
              "label": "删除",
              "reload": "chainSearchPage",
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:04f3880a8a88",
          "label": "操作",
          "type": "operation",
          "width": 150
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "数据格式化、下拉枚举查询、新增、编辑、删除、非lms导入",
  "title": "简单列表页",
  "type": "page"
}

==================================================

菜单项: 普通列表页
时间: 2025-07-22T18:00:19.676604
内容长度: 24150 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "dialog",
          "dialog": {
            "actions": [
              {
                "actionType": "cancel",
                "label": "取消",
                "type": "button"
              },
              {
                "actionType": "submit",
                "label": "确认",
                "level": "primary",
                "reload": "chainSearchPage",
                "type": "button"
              }
            ],
            "body": {
              "actions": [],
              "api": {
                "messages": {
                  "success": "新增成功"
                },
                "requestAdaptor": "\n              if(api.data.levelCategoryIds && api.data.levelCategoryIds.length > 0){\n                api.data.level1CategoryId = api.data.levelCategoryIds[0];\n                delete api.data.levelCategoryIds;\n              }\n              return api;",
                "url": "/scp-cost/goods/bomLevel1Category/save"
              },
              "body": [
                {
                  "api": {
                    "adaptor": "\n                    return {\n                      status: 200,\n                      data: response.data.bomConfigTypeEnum\n                    };\n                  ",
                    "data": {
                      "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
                    },
                    "method": "post",
                    "url": "/scp-cost/ops/query/enums"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "value",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择配置类型",
                        "required": true
                      }
                    ]
                  },
                  "label": "BOM 配置类型",
                  "name": "bomConfigType",
                  "type": "select"
                },
                {
                  "fieldProps": {
                    "level": 1
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择类目",
                        "required": true
                      }
                    ]
                  },
                  "label": "类目选择",
                  "linkage": [
                    {
                      "event": "onChange",
                      "setValues": {
                        "level1CategoryName": "${data[0].categoryName}"
                      }
                    }
                  ],
                  "name": "levelCategoryIds",
                  "type": "scmCategory"
                },
                {
                  "api": {
                    "adaptor": "\n                    return {\n                      status: 200,\n                      data: response.data.bomSourceEnum\n                    };\n                  ",
                    "data": {
                      "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
                    },
                    "method": "post",
                    "url": "/scp-cost/ops/query/enums"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "value",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择来源",
                        "required": true
                      }
                    ]
                  },
                  "label": "BOM 来源",
                  "name": "bomSource",
                  "type": "select"
                },
                {
                  "api": {
                    "adaptor": "\n                    return {\n                      status: 200,\n                      data: response.data.bomTypeEnum\n                    };\n                  ",
                    "data": {
                      "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
                    },
                    "method": "post",
                    "url": "/scp-cost/ops/query/enums"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "value",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择类型",
                        "required": true
                      }
                    ]
                  },
                  "label": "BOM 类型",
                  "name": "bomType",
                  "type": "select"
                },
                {
                  "fieldProps": {
                    "maxLength": 50,
                    "showCount": true
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择类目",
                        "required": true
                      }
                    ]
                  },
                  "label": "BOM Code",
                  "name": "bomCode",
                  "type": "text"
                },
                {
                  "fieldProps": {
                    "maxLength": 100,
                    "showCount": true
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择类目",
                        "required": true
                      }
                    ]
                  },
                  "label": "BOM 名称",
                  "name": "bomName",
                  "type": "textarea"
                },
                {
                  "fieldProps": {
                    "precision": 10
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "使用量不能为空",
                        "required": true
                      }
                    ]
                  },
                  "label": "BOM 使用量",
                  "name": "bomUsage",
                  "type": "digit"
                },
                {
                  "fieldProps": {
                    "maxLength": 50,
                    "showCount": true
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "单位不能为空",
                        "required": true
                      }
                    ]
                  },
                  "label": "BOM 单位",
                  "name": "bomUnit",
                  "type": "text"
                },
                {
                  "formItemProps": {
                    "hidden": true
                  },
                  "label": "level1CategoryName",
                  "name": "level1CategoryName",
                  "type": "text"
                }
              ],
              "type": "form"
            },
            "title": "新增",
            "type": "dialog",
            "width": 700
          },
          "id": "u:8c0a90f0f883",
          "label": "新增",
          "level": "primary",
          "type": "button"
        },
        {
          "fileName": "BOM配置表模板 (一级类目兜底).xlsx",
          "fileNameLabel": "批量新增模板下载",
          "fileUrl": "https://h5static.dewucdn.com/node-common/63fab249-42a9-fcec-b9a6-da2440b9dda6.xlsx",
          "id": "u:a286b7d766dd",
          "importMod": "bomLevel1CategoryImport",
          "label": "新增导入",
          "type": "lmsImport"
        },
        {
          "id": "u:6a6ba8ee03ed",
          "importMod": "bomLevel1CategoryImport",
          "label": "新增导入结果查询",
          "type": "lmsImportResult"
        },
        {
          "exportMod": "bomLevel1CategoryExport",
          "id": "u:f7d842d78991",
          "type": "lmsExport"
        }
      ],
      "api": {
        "data": {
          "orderBy": "updated_time"
        },
        "method": "post",
        "requestAdaptor": "\n        if(api.data.levelCategoryIds && api.data.levelCategoryIds.length > 0){\n          api.data.level1CategoryId = api.data.levelCategoryIds[0];\n          delete api.data.levelCategoryIds;\n        }\n        return api;",
        "url": "/scp-cost/goods/bomLevel1Category/list"
      },
      "formColumns": [
        {
          "api": {
            "adaptor": "\n            return {\n              status: 200,\n              data: response.data.bomConfigTypeEnum\n            };\n          ",
            "data": {
              "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
            },
            "method": "post",
            "url": "/scp-cost/ops/query/enums"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "value",
              "value": "code"
            }
          },
          "id": "u:b08f1f595bc3",
          "label": "BOM 配置类型",
          "name": "bomConfigType",
          "type": "select"
        },
        {
          "fieldProps": {
            "level": 1
          },
          "id": "u:6bf1ca61e1b2",
          "label": "类目选择",
          "name": "levelCategoryIds",
          "type": "scmCategory"
        },
        {
          "api": {
            "adaptor": "\n            return {\n              status: 200,\n              data: response.data.bomTypeEnum\n            };\n          ",
            "data": {
              "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
            },
            "method": "post",
            "url": "/scp-cost/ops/query/enums"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "value",
              "value": "code"
            }
          },
          "id": "u:65e30b316580",
          "label": "BOM 类型",
          "name": "bomType",
          "type": "select"
        },
        {
          "api": {
            "adaptor": "\n            return {\n              status: 200,\n              data: response.data.bomSourceEnum\n            };\n          ",
            "data": {
              "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
            },
            "method": "post",
            "url": "/scp-cost/ops/query/enums"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "value",
              "value": "code"
            }
          },
          "id": "u:a8fbc0c0e0b8",
          "label": "BOM 来源",
          "name": "bomSource",
          "type": "select"
        },
        {
          "fieldProps": {
            "placeholder": "请输入完整 code ，精确搜索"
          },
          "id": "u:2664df43f386",
          "label": "BOM Code",
          "name": "bomCode",
          "type": "text"
        },
        {
          "fieldProps": {
            "placeholder": "模糊搜索"
          },
          "id": "u:a17c756eb150",
          "label": "BOM 名称",
          "name": "bomName",
          "type": "text"
        }
      ],
      "id": "u:bd16eed8687a",
      "name": "chainSearchPage",
      "rowKey": "id",
      "search": {
        "defaultCollapsed": false,
        "labelWidth": 110
      },
      "tableColumns": [
        {
          "fixed": "left",
          "id": "u:30239a473b49",
          "label": "序号",
          "name": "id",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:dc7678b879c7",
          "label": "BOM 配置类型",
          "name": "bomConfigTypeName",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:952a5e7eb895",
          "label": "一级类目",
          "name": "level1CategoryName",
          "type": "text"
        },
        {
          "id": "u:7aad1634ed6f",
          "label": "BOM 类型",
          "name": "bomTypeName",
          "type": "text"
        },
        {
          "id": "u:c3e03df83c24",
          "label": "BOM 来源",
          "name": "bomSourceName",
          "type": "text"
        },
        {
          "id": "u:b228c34647c3",
          "label": "BOM Code",
          "name": "bomCode",
          "type": "text"
        },
        {
          "id": "u:0ad31b46b248",
          "label": "BOM 名称",
          "name": "bomName",
          "type": "text"
        },
        {
          "align": "right",
          "id": "u:70d7728bb416",
          "label": "BOM 使用量",
          "name": "bomUsage",
          "type": "text",
          "width": 120
        },
        {
          "body": [
            {
              "id": "u:1aa2652cddc0",
              "label": "${bomUsage}",
              "type": "tag"
            }
          ],
          "id": "u:a0a8165498d1",
          "label": "BOM 单位",
          "name": "bomUnit",
          "type": "custom"
        },
        {
          "id": "u:ec0c4987bea3",
          "label": "版本",
          "name": "version",
          "type": "text"
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "confirm",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": {
                  "actions": [],
                  "api": {
                    "messages": {
                      "success": "保存成功"
                    },
                    "requestAdaptor": "\n                  if(api.data.levelCategoryIds && api.data.levelCategoryIds.length > 0){\n                    api.data.level1CategoryId = api.data.levelCategoryIds[0];\n                    delete api.data.levelCategoryIds;\n                  }\n                  return api;",
                    "url": "/scp-cost/goods/bomLevel1Category/save"
                  },
                  "body": [
                    {
                      "api": {
                        "adaptor": "\n                        return {\n                          status: 200,\n                          data: response.data.bomConfigTypeEnum\n                        };\n                      ",
                        "data": {
                          "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
                        },
                        "method": "post",
                        "url": "/scp-cost/ops/query/enums"
                      },
                      "fieldProps": {
                        "fieldNames": {
                          "label": "value",
                          "value": "code"
                        }
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择配置类型",
                            "required": true
                          }
                        ]
                      },
                      "label": "BOM 配置类型",
                      "name": "bomConfigType",
                      "type": "select"
                    },
                    {
                      "fieldProps": {
                        "disabled": true,
                        "level": 1
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择类目",
                            "required": true
                          }
                        ]
                      },
                      "label": "类目选择",
                      "linkage": [
                        {
                          "event": "onChange",
                          "setValues": {
                            "level1CategoryName": "${data[0].categoryName}"
                          }
                        }
                      ],
                      "name": "levelCategoryIds",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "scmCategory"
                    },
                    {
                      "api": {
                        "adaptor": "\n                        return {\n                          status: 200,\n                          data: response.data.bomSourceEnum\n                        };\n                      ",
                        "data": {
                          "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
                        },
                        "method": "post",
                        "url": "/scp-cost/ops/query/enums"
                      },
                      "fieldProps": {
                        "fieldNames": {
                          "label": "value",
                          "value": "code"
                        }
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择来源",
                            "required": true
                          }
                        ]
                      },
                      "label": "BOM 来源",
                      "name": "bomSource",
                      "type": "select"
                    },
                    {
                      "api": {
                        "adaptor": "\n                        return {\n                          status: 200,\n                          data: response.data.bomTypeEnum\n                        };\n                      ",
                        "data": {
                          "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum,goodsItemEnum,warehouse,warehouseCenter,costLevel1Type,costLevel2TypeEnum,dataStatusEnum"
                        },
                        "method": "post",
                        "url": "/scp-cost/ops/query/enums"
                      },
                      "fieldProps": {
                        "fieldNames": {
                          "label": "value",
                          "value": "code"
                        }
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择类型",
                            "required": true
                          }
                        ]
                      },
                      "label": "BOM 类型",
                      "name": "bomType",
                      "type": "select"
                    },
                    {
                      "fieldProps": {
                        "maxLength": 50,
                        "showCount": true
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择类目",
                            "required": true
                          }
                        ]
                      },
                      "label": "BOM Code",
                      "name": "bomCode",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "fieldProps": {
                        "maxLength": 100,
                        "showCount": true
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择类目",
                            "required": true
                          }
                        ]
                      },
                      "label": "BOM 名称",
                      "name": "bomName",
                      "type": "textarea"
                    },
                    {
                      "fieldProps": {
                        "precision": 10
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "使用量不能为空",
                            "required": true
                          }
                        ]
                      },
                      "label": "BOM 使用量",
                      "name": "bomUsage",
                      "type": "digit"
                    },
                    {
                      "fieldProps": {
                        "maxLength": 50,
                        "showCount": true
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "单位不能为空",
                            "required": true
                          }
                        ]
                      },
                      "label": "BOM 单位",
                      "name": "bomUnit",
                      "type": "text"
                    },
                    {
                      "formItemProps": {
                        "hidden": true
                      },
                      "label": "level1CategoryName",
                      "name": "level1CategoryName",
                      "type": "text"
                    },
                    {
                      "formItemProps": {
                        "hidden": true
                      },
                      "label": "id",
                      "name": "id",
                      "type": "text"
                    }
                  ],
                  "onFormInit": "const { level1CategoryId, ...ret } = values; return { levelCategoryIds: [ level1CategoryId ], ...ret }",
                  "type": "form"
                },
                "title": "编辑",
                "type": "dialog",
                "width": 700
              },
              "id": "u:bc570e18fcd2",
              "label": "编辑",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "id": "${id}"
                },
                "messages": {
                  "failed": "删除失败",
                  "success": "删除成功"
                },
                "method": "post",
                "url": "/scp-cost/goods/bomLevel1Category/remove"
              },
              "confirmText": "是否确认删除",
              "danger": true,
              "id": "u:edd6fb7b2c33",
              "label": "删除",
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:1f2fae537cd1",
          "label": "操作",
          "type": "operation",
          "width": 100
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:4caa2a3b183a",
  "subTitle": "数据格式化、下拉枚举查询、联动、新增、编辑、删除、导入、导出、导入结果查询",
  "title": "普通列表页",
  "type": "page"
}

==================================================

菜单项: 常规列表页
时间: 2025-07-22T18:00:44.798125
内容长度: 45282 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "dialog",
          "dialog": {
            "actions": [
              {
                "actionType": "cancel",
                "id": "u:8f097adc80fe",
                "label": "取消",
                "type": "button"
              },
              {
                "actionType": "submit",
                "id": "u:b382051e7ac6",
                "label": "确认",
                "level": "primary",
                "reload": "chainSearchPage",
                "type": "button"
              }
            ],
            "body": [
              {
                "actions": [],
                "api": {
                  "data": {
                    "&": "$$"
                  },
                  "url": "/tms/admin/fulfillmentModeConfig/create"
                },
                "body": [
                  {
                    "fieldProps": {
                      "options": [
                        {
                          "label": "仓维度",
                          "value": 0
                        },
                        {
                          "label": "商家维度",
                          "value": 2
                        }
                      ]
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:c61bb5ac6cec",
                    "label": "配置维度",
                    "name": "deliveryMode",
                    "type": "radio"
                  },
                  {
                    "api": {
                      "method": "get",
                      "url": "/tms/admin/enum/repositories"
                    },
                    "fieldProps": {
                      "fetchDataOnSearch": false,
                      "fieldNames": {
                        "label": "name",
                        "value": "code"
                      },
                      "labelInValue": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择",
                          "required": true
                        }
                      ]
                    },
                    "hiddenOn": "${deliveryMode != 0}",
                    "id": "u:23a153df57d5",
                    "label": "始发仓库",
                    "labelName": "originWarehouseName",
                    "name": "originWarehouse",
                    "type": "select",
                    "valueName": "originWarehouseCode"
                  },
                  {
                    "api": {
                      "adaptor": "return {status: 200,data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))};",
                      "method": "get",
                      "requestAdaptor": "const data = api.data || {};\n  data.name = data?.keyWords || ''; \n  api.data = {...data} \n return api;",
                      "sendOn": "${keyWords}",
                      "url": "/merchant/merchant/getMerchantIdByNameEs"
                    },
                    "fieldProps": {
                      "fetchDataOnSearch": true,
                      "fieldNames": {
                        "label": "name",
                        "value": "merchantId"
                      },
                      "placeholder": "请输入后查询"
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择",
                          "required": true
                        }
                      ]
                    },
                    "hiddenOn": "${deliveryMode != 2}",
                    "id": "u:35a2ee435901",
                    "label": "直发商家",
                    "name": "merchantId",
                    "type": "select"
                  },
                  {
                    "api": {
                      "adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}",
                      "data": {
                        "lang": "zh",
                        "maxDeep": 3
                      },
                      "method": "post",
                      "url": "/tms/admin/address/queryAddressTreeByDeep"
                    },
                    "fieldProps": {
                      "fieldNames": {
                        "label": "name",
                        "value": "code"
                      }
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择",
                          "required": true
                        }
                      ]
                    },
                    "hiddenOn": "${deliveryMode != 2}",
                    "id": "u:5fb959eb1ca0",
                    "label": "始发城市",
                    "linkage": [
                      {
                        "event": "onChange",
                        "setValues": {
                          "originCityCode": "${data[1].code}",
                          "originCityName": "${data[1].name}"
                        }
                      }
                    ],
                    "name": "originCity",
                    "type": "cascader"
                  },
                  {
                    "formItemProps": {
                      "hidden": true
                    },
                    "hiddenOn": "${deliveryMode!= 2}",
                    "id": "u:8ed20caf07dc",
                    "label": "城市Code",
                    "name": "originCityCode",
                    "type": "text"
                  },
                  {
                    "formItemProps": {
                      "hidden": true
                    },
                    "hiddenOn": "${deliveryMode !=2}",
                    "id": "u:589bd5e2f99b",
                    "label": "城市名称",
                    "name": "originCityName",
                    "type": "text"
                  },
                  {
                    "api": {
                      "adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }",
                      "data": {
                        "enumTypes": "transportFulfillModeEnum"
                      },
                      "method": "post",
                      "url": "/tms/admin/enum/listEnums"
                    },
                    "id": "u:8dd726d9f0ab",
                    "label": "运输履约模式",
                    "linkage": [
                      {
                        "event": "onChange",
                        "resetKeys": [
                          "customerCodeId"
                        ],
                        "setValues": {
                          "customerCodeId": null
                        }
                      }
                    ],
                    "name": "fulfillmentMode",
                    "type": "select"
                  },
                  {
                    "api": {
                      "url": "/tms/admin/enum/getLogistics"
                    },
                    "fieldProps": {
                      "fetchDataOnSearch": false,
                      "fieldNames": {
                        "label": "name",
                        "value": "code"
                      },
                      "showSearch": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:3101383b3836",
                    "label": "承运商",
                    "linkage": [
                      {
                        "event": "onChange",
                        "resetKeys": [
                          "logisticsProductCode",
                          "customerCodeId"
                        ],
                        "setValues": {
                          "customerCodeId": null,
                          "logisticsProductCode": null
                        }
                      }
                    ],
                    "name": "logisticsCode",
                    "type": "select"
                  },
                  {
                    "api": {
                      "data": {
                        "logisticsName": "${logisticsCode}"
                      },
                      "sendOn": "${logisticsCode}",
                      "url": "/tms/admin/enum/getLogisticsProduct"
                    },
                    "fieldProps": {
                      "fetchDataOnSearch": false,
                      "fieldNames": {
                        "label": "name",
                        "value": "code"
                      },
                      "showSearch": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:ad4bb5a73494",
                    "label": "运输产品",
                    "linkage": [
                      {
                        "event": "onChange",
                        "resetKeys": [
                          "customerCodeId"
                        ],
                        "setValues": {
                          "customerCodeId": null
                        }
                      }
                    ],
                    "name": "logisticsProductCode",
                    "type": "select"
                  },
                  {
                    "api": {
                      "adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }",
                      "data": {
                        "logisticsCode": "${logisticsCode}",
                        "logisticsProductCode": "${logisticsProductCode}",
                        "transportFulfillmentMode": "${fulfillmentMode}"
                      },
                      "method": "post",
                      "sendOn": "fulfillmentMode && logisticsProductCode",
                      "url": "/tms/admin/customerCode/getCustomerCodesV2"
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择",
                          "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"
                        }
                      ]
                    },
                    "id": "u:b5a2405c516f",
                    "label": "月结卡号",
                    "name": "customerCodeId",
                    "params": {
                      "t": "${Date.now()}"
                    },
                    "type": "select"
                  }
                ],
                "id": "u:6bc905badd74",
                "type": "form"
              }
            ],
            "id": "u:462f993511f3",
            "title": "新增",
            "type": "dialog",
            "width": 700
          },
          "id": "u:3a46389b7e47",
          "label": "新建",
          "level": "primary",
          "type": "button"
        },
        {
          "fileName": "导入模板",
          "fileNameLabel": "模板下载",
          "fileUrl": "https://cdn.poizon.com/node-common/3a2d7339-93f6-74ae-6450-d15753638127.xlsx",
          "id": "u:9e003c343b6a",
          "importMod": "importTransportFulfillMode",
          "label": "导入",
          "type": "lmsImport"
        },
        {
          "id": "u:99e4688cf8ec",
          "importMod": "importTransportFulfillMode",
          "label": "导入结果查询",
          "type": "lmsImportResult"
        },
        {
          "actionType": "export",
          "api": {
            "data": {
              "exportMod": "TRANSPORT_FULFILLMENT_MODE_CONFIG"
            },
            "method": "post",
            "url": "/lmsnew/reportCenter/report/v1/export"
          },
          "bizType": "lmsnew",
          "id": "u:61bfcbed8fb1",
          "label": "导出",
          "level": "primary",
          "type": "button"
        }
      ],
      "api": {
        "adaptor": "return {\n  'status': 200,\n  'msg': '请求成功',\n  'data': {\n    'rows': response.data.list,\n    'count': response.data.total\n  }\n}",
        "method": "post",
        "requestAdaptor": "const { originWarehouseCode, merchantId, fulfillmentMode, pageNum, pageSize, merchantIdV2 } = api.data\napi.data = {\n  pageNum, pageSize,\n  originWarehouseCodes: originWarehouseCode ? [originWarehouseCode] : undefined,\n  merchantIds: merchantIdV2 || merchantId ? [merchantIdV2 || merchantId] : undefined,\n  fulfillmentModes: fulfillmentMode ? [fulfillmentMode] : undefined\n}\nreturn api",
        "url": "/tms/admin/fulfillmentModeConfig/page"
      },
      "formColumns": [
        {
          "api": {
            "method": "get",
            "url": "/tms/admin/enum/repositories"
          },
          "fieldProps": {
            "fetchDataOnSearch": false,
            "fieldNames": {
              "label": "name",
              "value": "code"
            }
          },
          "id": "u:787ebe451b98",
          "label": "始发仓库",
          "name": "originWarehouseCode",
          "type": "select"
        },
        {
          "fieldProps": {
            "multiple": true
          },
          "id": "u:420ffe2155c6",
          "label": "日期多选",
          "name": "date",
          "type": "date"
        },
        {
          "api": {
            "adaptor": "return {\n  status: 200,\n  data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))\n};",
            "data": {
              "name": "${keyWords}"
            },
            "method": "get",
            "sendOn": "${keyWords}",
            "url": "/merchant/merchant/getMerchantIdByNameEs"
          },
          "fieldProps": {
            "fetchDataOnSearch": true,
            "fieldNames": {
              "label": "name",
              "value": "merchantId"
            },
            "placeholder": "请输入后查询"
          },
          "id": "u:ede536f16a2b",
          "label": "直发商家",
          "name": "merchantId",
          "type": "select"
        },
        {
          "fieldProps": {
            "fetchDataOnSearch": true,
            "placeholder": "请输入后查询"
          },
          "id": "u:cade04fc4f1b",
          "label": "直发商家ID",
          "name": "merchantIdV2",
          "type": "text"
        },
        {
          "api": {
            "adaptor": "const mapData = response.mapData || {}\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }",
            "data": {
              "enumTypes": "transportFulfillModeEnum"
            },
            "method": "post",
            "url": "/tms/admin/enum/listEnums"
          },
          "id": "u:e3dd1d6b1ac2",
          "label": "履约模式",
          "name": "fulfillmentMode",
          "type": "select"
        }
      ],
      "id": "u:c61a67ebea74",
      "name": "chainSearchPage",
      "revalidateOnFocus": true,
      "rowKey": "id",
      "search": true,
      "tableColumns": [
        {
          "id": "u:c44ce52946a2",
          "label": "始发仓库",
          "name": "originWarehouseName",
          "type": "text",
          "width": 160
        },
        {
          "body": [
            {
              "body": [
                {
                  "id": "u:c880a47168b5",
                  "tpl": "${\"名称：\"+( sellerUserName || \"-\")}",
                  "type": "tpl"
                }
              ],
              "id": "u:452480073e84",
              "type": "container"
            },
            {
              "body": [
                {
                  "id": "u:220fd3928402",
                  "tpl": "${\"ID：\"+ (sellerUserId || \"-\")}",
                  "type": "tpl"
                }
              ],
              "id": "u:25ddebd84582",
              "type": "container"
            }
          ],
          "id": "u:bba80cf7ad52",
          "label": "直发商家",
          "name": "sellerUserName",
          "type": "custom",
          "width": 160
        },
        {
          "id": "u:fce592c0a4f7",
          "label": "始发城市",
          "name": "originCityName",
          "type": "text"
        },
        {
          "id": "u:b0790cc787d6",
          "label": "运输履约模式",
          "name": "fulfillmentModeName",
          "type": "text"
        },
        {
          "id": "u:dd72e6c1d157",
          "label": "承运商",
          "name": "logisticsName",
          "type": "text"
        },
        {
          "id": "u:71179d5c4f3e",
          "label": "运输产品",
          "name": "logisticsProductName",
          "type": "text"
        },
        {
          "id": "u:1fb040dd3a05",
          "label": "月结卡号",
          "name": "customerCode",
          "type": "text"
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:675998f5b366",
                    "label": "取消",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "body": [
                      {
                        "id": "u:5a1f5c197f2b",
                        "label": "配置维度",
                        "name": "deliveryModeDesc",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "hiddenOn": "${deliveryMode != 0}",
                        "id": "u:4037673c4a68",
                        "label": "始发仓库",
                        "name": "originWarehouseName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "hiddenOn": "${deliveryMode != 2}",
                        "id": "u:7b8ce38f35c3",
                        "label": "直发商家",
                        "name": "sellerUserName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "hiddenOn": "${deliveryMode != 2}",
                        "id": "u:2f5c24da6728",
                        "label": "始发城市",
                        "name": "originCityName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:29ca5c766f5f",
                        "label": "运输履约模式",
                        "name": "fulfillmentModeName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:8335c1b01bc3",
                        "label": "承运商",
                        "name": "logisticsName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:efd3e704190b",
                        "label": "运输产品",
                        "name": "logisticsProductName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:43685a5759ea",
                        "label": "月结卡号",
                        "name": "customerCode",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      }
                    ],
                    "id": "u:57f639732290",
                    "initApi": {
                      "data": {
                        "id": "${id}"
                      },
                      "method": "get",
                      "url": "/tms/admin/fulfillmentModeConfig/detail"
                    },
                    "title": "表单",
                    "type": "form"
                  }
                ],
                "id": "u:66eda46202cb",
                "title": "查看",
                "type": "dialog",
                "width": 600
              },
              "id": "u:8eb56843f0ae",
              "label": "查看",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:488f3f745723",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "confirm",
                    "id": "u:5a3ab578c259",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "api": {
                      "data": {
                        "&": "$$"
                      },
                      "url": "/tms/admin/fulfillmentModeConfig/edit"
                    },
                    "body": [
                      {
                        "formItemProps": {
                          "hidden": true
                        },
                        "id": "u:d97d9fb9d7ac",
                        "label": "ID",
                        "name": "id",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "fieldProps": {
                          "disabled": false,
                          "options": [
                            {
                              "label": "仓维度",
                              "value": 0
                            },
                            {
                              "label": "商家维度",
                              "value": 2
                            }
                          ]
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:2885354c4f38",
                        "label": "配置维度",
                        "name": "deliveryMode",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "radio"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/repositories"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fetchDataOnSearch": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          },
                          "labelInValue": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "hiddenOn": "${deliveryMode != 0}",
                        "id": "u:9f5910067d23",
                        "label": "始发仓库",
                        "labelName": "originWarehouseName",
                        "name": "originWarehouse",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select",
                        "valueName": "originWarehouseCode"
                      },
                      {
                        "fieldProps": {
                          "disabled": false,
                          "placeholder": "请输入后查询"
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "hiddenOn": "${deliveryMode != 2}",
                        "id": "u:286124b7250c",
                        "label": "直发商家",
                        "name": "sellerUserName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select"
                      },
                      {
                        "api": {
                          "adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}",
                          "data": {
                            "lang": "zh",
                            "maxDeep": 3
                          },
                          "method": "post",
                          "url": "/tms/admin/address/queryAddressTreeByDeep"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "hiddenOn": "${deliveryMode != 2}",
                        "id": "u:6a4b66400afc",
                        "label": "始发城市",
                        "linkage": [
                          {
                            "event": "onChange",
                            "setValues": {
                              "originCityName": "${data[1].name}"
                            }
                          }
                        ],
                        "name": "originCity",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "cascader"
                      },
                      {
                        "formItemProps": {
                          "hidden": true
                        },
                        "hiddenOn": "${deliveryMode!= 2}",
                        "id": "u:ec33373a704f",
                        "label": "城市Code",
                        "name": "originCityCode",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "formItemProps": {
                          "hidden": true
                        },
                        "hiddenOn": "${deliveryMode !=2}",
                        "id": "u:94ae9a297094",
                        "label": "城市名称",
                        "name": "originCityName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "api": {
                          "adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }",
                          "data": {
                            "enumTypes": "transportFulfillModeEnum"
                          },
                          "method": "post",
                          "url": "/tms/admin/enum/listEnums"
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:85e6986c3990",
                        "label": "运输履约模式",
                        "linkage": [
                          {
                            "event": "onChange",
                            "resetKeys": [
                              "customerCodeId"
                            ],
                            "setValues": {
                              "customerCodeId": null
                            }
                          }
                        ],
                        "name": "fulfillmentMode",
                        "type": "select"
                      },
                      {
                        "api": {
                          "url": "/tms/admin/enum/getLogistics"
                        },
                        "fieldProps": {
                          "fetchDataOnSearch": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          },
                          "showSearch": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:bb64db5388be",
                        "label": "承运商",
                        "linkage": [
                          {
                            "event": "onChange",
                            "resetKeys": [
                              "logisticsProductCode",
                              "customerCodeId"
                            ],
                            "setValues": {
                              "customerCodeId": null,
                              "logisticsProductCode": null
                            }
                          }
                        ],
                        "name": "logisticsCode",
                        "type": "select"
                      },
                      {
                        "api": {
                          "data": {
                            "logisticsName": "${logisticsCode}"
                          },
                          "sendOn": "${logisticsCode}",
                          "url": "/tms/admin/enum/getLogisticsProduct"
                        },
                        "fieldProps": {
                          "fetchDataOnSearch": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          },
                          "showSearch": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:f714a92015b9",
                        "label": "运输产品",
                        "linkage": [
                          {
                            "event": "onChange",
                            "resetKeys": [
                              "customerCodeId"
                            ],
                            "setValues": {
                              "customerCodeId": null
                            }
                          }
                        ],
                        "name": "logisticsProductCode",
                        "type": "select"
                      },
                      {
                        "api": {
                          "adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }",
                          "data": {
                            "logisticsCode": "${logisticsCode}",
                            "logisticsProductCode": "${logisticsProductCode}",
                            "transportFulfillmentMode": "${fulfillmentMode}"
                          },
                          "method": "post",
                          "sendOn": "fulfillmentMode && logisticsProductCode",
                          "url": "/tms/admin/customerCode/getCustomerCodesV2"
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"
                            }
                          ]
                        },
                        "id": "u:2a69a4e4366e",
                        "label": "月结卡号",
                        "name": "customerCodeId",
                        "params": {
                          "t": "${Date.now()}"
                        },
                        "type": "select"
                      }
                    ],
                    "id": "u:b7373d316c7c",
                    "initApi": {
                      "adaptor": "const {originWarehouseCode, originWarehouseName, originProvinceCode,originCityCode, deliveryMode, customerCodeId, ...rest}=response.data;\nconst data = { deliveryMode, customerCodeId: customerCodeId === 0 ? null : customerCodeId, ...rest }\nif (deliveryMode === 0) {\n  data.originWarehouse = {label: originWarehouseName, value: originWarehouseCode}\n}\nif (deliveryMode === 2) {\n  data.originCity = [originProvinceCode, originCityCode]\n}\nreturn {status: 0, data: data }",
                      "data": {
                        "id": "${id}"
                      },
                      "url": "/tms/admin/fulfillmentModeConfig/detail"
                    },
                    "type": "form"
                  }
                ],
                "id": "u:8617c397bda5",
                "title": "编辑",
                "type": "dialog",
                "width": 700
              },
              "id": "u:b929f26710a2",
              "label": "编辑",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "id": "${id}"
                },
                "messages": {
                  "failed": "删除失败",
                  "success": "删除成功"
                },
                "method": "post",
                "url": "/tms/admin/fulfillmentModeConfig/delete"
              },
              "confirmText": "是否确认删除",
              "danger": true,
              "id": "u:4a6bb394b3a8",
              "label": "删除",
              "type": "button"
            },
            {
              "id": "u:aae9dfe8aa96",
              "label": "操作记录",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:5b4ca903f79f",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "confirm",
                            "id": "u:4256391b6fab",
                            "label": "确认",
                            "level": "primary",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "api": {
                              "data": {
                                "id": "${id || 148}",
                                "tableName": "des_transport_fulfillment_mode_config"
                              },
                              "method": "post",
                              "url": "/tms/admin/log/getLogList"
                            },
                            "formColumns": [],
                            "id": "u:6d121dbf2ced",
                            "label": "列表",
                            "name": "chainSearchPage",
                            "options": true,
                            "rowKey": "id",
                            "search": false,
                            "tableAlertRender": false,
                            "tableColumns": [
                              {
                                "id": "u:af39355bb3e6",
                                "label": "操作人",
                                "name": "name",
                                "type": "text",
                                "width": 60
                              },
                              {
                                "id": "u:0b7a8e69e9b2",
                                "label": "操作时间",
                                "name": "modifyTime",
                                "type": "text",
                                "width": 80
                              },
                              {
                                "id": "u:e347e7c72d1d",
                                "label": "操作类型",
                                "name": "operationName",
                                "type": "text",
                                "width": 80
                              },
                              {
                                "id": "u:84501b7ff89d",
                                "label": "操作内容",
                                "name": "operationContent",
                                "type": "text"
                              }
                            ],
                            "type": "chainSearchPage"
                          }
                        ],
                        "id": "u:8eda1238e1de",
                        "title": "查看操作日志",
                        "type": "dialog",
                        "width": 900
                      }
                    }
                  ]
                }
              },
              "type": "button"
            },
            {
              "id": "u:cbbc908432f1",
              "label": "步骤详情",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:7ea2b83edc6e",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "confirm",
                            "id": "u:88a9d7c17be0",
                            "label": "确认",
                            "level": "primary",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "api": "/mock/form/saveForm",
                            "body": [
                              {
                                "current": 1,
                                "direction": "vertical",
                                "disableChange": false,
                                "id": "u:fa89b2dc35da",
                                "items": [
                                  {
                                    "body": [
                                      {
                                        "height": 80,
                                        "id": "u:d21d9970a0f1",
                                        "mode": "stretch",
                                        "src": "https://cdn.dewu.com/node-common/526843c0-1469-d7d8-24cf-4563efa6fe75-425-425.png",
                                        "type": "picture",
                                        "width": 80
                                      }
                                    ],
                                    "description": "This is a description.",
                                    "id": "u:3b32199e3943",
                                    "title": "Finished"
                                  },
                                  {
                                    "body": [
                                      {
                                        "height": 80,
                                        "id": "u:690d0916f520",
                                        "mode": "stretch",
                                        "src": "https://cdn.dewu.com/node-common/14a55158-3b2d-babe-9bb8-8b5192ae05fc-1920-817.png",
                                        "type": "picture"
                                      }
                                    ],
                                    "description": "This is a description.",
                                    "id": "u:bf403f0d8dab",
                                    "subTitle": "Left 00:00:08",
                                    "title": "In Progress"
                                  },
                                  {
                                    "body": [
                                      {
                                        "height": 80,
                                        "id": "u:b015169ba742",
                                        "mode": "stretch",
                                        "src": "https://cdn.dewu.com/node-common/4874d8b4-9f0d-cfe1-4080-1ac2dffdbf1c-1920-817.png",
                                        "type": "picture"
                                      }
                                    ],
                                    "description": "This is a description.",
                                    "id": "u:f057573f203b",
                                    "title": "Waiting"
                                  }
                                ],
                                "labelPlacement": "horizontal",
                                "mode": "default",
                                "size": "default",
                                "type": "steps"
                              }
                            ],
                            "id": "u:5997b3f367cc",
                            "type": "form"
                          }
                        ],
                        "id": "u:fecab86abadb",
                        "title": "详情",
                        "type": "dialog"
                      }
                    }
                  ]
                }
              },
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:dd06d7b208ec",
          "label": "操作",
          "type": "operation",
          "width": 200
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "数据格式化、下拉枚举查询、下拉枚举远程搜索、新增（form表单内容根据配置维度动态变化）、编辑、删除、导入、导出",
  "title": "常规列表页",
  "type": "page"
}

==================================================

菜单项: 复杂列表页
时间: 2025-07-22T18:01:09.660327
内容长度: 41767 字符
------------------------------
{
  "body": [
    {
      "api": {
        "data": {
          "optionName": "bomTypeEnum,bomConfigTypeEnum,bomSourceEnum"
        },
        "method": "post",
        "url": "/scp-cost/ops/query/enums"
      },
      "body": [
        {
          "actions": [
            {
              "id": "u:12a01ebaa513",
              "label": "新增",
              "level": "primary",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:809b2b5c8b5c",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "submit",
                            "id": "u:d0fc6f323c84",
                            "label": "确认",
                            "level": "primary",
                            "reload": "chainSearchPage",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "api": {
                              "url": "/scp-cost/goods/bomLevel1Category/save"
                            },
                            "body": [
                              {
                                "fieldProps": {
                                  "fieldNames": {
                                    "label": "label",
                                    "value": "code"
                                  },
                                  "options": "${bomConfigTypeEnum}"
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择配置类型",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:01c99e3ae895",
                                "label": "BOM 配置类型",
                                "name": "bomConfigType",
                                "type": "select"
                              },
                              {
                                "fieldProps": {
                                  "changeOnSelect": true
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择类目",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:a8b46c842bcf",
                                "label": "类目选择",
                                "level1Name": "level1CategoryId",
                                "level2Name": "level2CategoryId",
                                "level3Name": "level3CategoryId",
                                "linkage": [
                                  {
                                    "event": "onChange",
                                    "setValues": {
                                      "level1CategoryName": "${data[0].categoryName}",
                                      "level2CategoryName": "${data[1].categoryName}",
                                      "level3CategoryName": "${data[2].categoryName}"
                                    }
                                  }
                                ],
                                "name": "levelCategoryIds",
                                "type": "scmCategory"
                              },
                              {
                                "formItemProps": {
                                  "hidden": true
                                },
                                "id": "u:4f2e97d1eabf",
                                "label": "一级类目名称",
                                "name": "level1CategoryName",
                                "type": "text"
                              },
                              {
                                "formItemProps": {
                                  "hidden": true
                                },
                                "id": "u:f03b7fbbda79",
                                "label": "二级类目名称",
                                "name": "level2CategoryName",
                                "type": "text"
                              },
                              {
                                "formItemProps": {
                                  "hidden": true
                                },
                                "id": "u:a7e6ad13133a",
                                "label": "三级类目名称",
                                "linkage": [
                                  {
                                    "event": "onChange",
                                    "setValues": {
                                      "level1CategoryName": "${data[0].categoryName}",
                                      "level2CategoryName": "${data[1].categoryName}",
                                      "level3CategoryName": "${data[2].categoryName}"
                                    }
                                  }
                                ],
                                "name": "level3CategoryName",
                                "type": "text"
                              },
                              {
                                "fieldProps": {
                                  "fieldNames": {
                                    "label": "label",
                                    "value": "code"
                                  },
                                  "options": "${bomSourceEnum}"
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择来源",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:7b7ec22f6cdb",
                                "label": "BOM 来源",
                                "name": "bomSource",
                                "type": "select"
                              },
                              {
                                "fieldProps": {
                                  "fieldNames": {
                                    "label": "label",
                                    "value": "code"
                                  },
                                  "options": "${bomTypeEnum}"
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择类型",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:815cfeb47979",
                                "label": "BOM 类型",
                                "name": "bomType",
                                "type": "select"
                              },
                              {
                                "fieldProps": {
                                  "maxLength": 50,
                                  "showCount": true
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择类目",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:3fb28ae54eff",
                                "label": "BOM Code",
                                "name": "bomCode",
                                "type": "text"
                              },
                              {
                                "fieldProps": {
                                  "maxLength": 100,
                                  "showCount": true
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择类目",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:686aa252d724",
                                "label": "BOM 名称",
                                "name": "bomName",
                                "type": "textarea"
                              },
                              {
                                "fieldProps": {
                                  "precision": 10
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "使用量不能为空",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:42491bb58ff6",
                                "label": "BOM 使用量",
                                "name": "bomUsage",
                                "type": "digit"
                              },
                              {
                                "fieldProps": {
                                  "maxLength": 50,
                                  "showCount": true
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "单位不能为空",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:866b6585e6c4",
                                "label": "BOM 单位",
                                "name": "bomUnit",
                                "type": "text"
                              }
                            ],
                            "id": "u:364a302cc4e1",
                            "type": "form"
                          }
                        ],
                        "id": "u:e13e4bea3a1b",
                        "title": "新增",
                        "type": "dialog",
                        "width": 720
                      }
                    }
                  ]
                }
              },
              "type": "button"
            },
            {
              "fileName": "BOM配置表模板.xlsx",
              "fileNameLabel": "批量新增模板下载",
              "fileUrl": "https://h5static.dewucdn.com/node-common/44956ff9-75de-2e5d-2181-ea65fe0b7de8.xlsx",
              "id": "u:31844b1bf2a4",
              "importMod": "editBomCategoryImport",
              "label": "编辑导入",
              "type": "lmsImport"
            },
            {
              "id": "u:234b5fd416ec",
              "importMod": "editBomCategoryImport",
              "label": "编辑导入结果查询",
              "type": "lmsImportResult"
            },
            {
              "fileName": "BOM配置表模板.xlsx",
              "fileNameLabel": "批量新增模板下载",
              "fileUrl": "https://h5static.dewucdn.com/node-common/44956ff9-75de-2e5d-2181-ea65fe0b7de8.xlsx",
              "id": "u:94c0f8be74b2",
              "importMod": "addBomCategoryImport",
              "label": "新增导入",
              "type": "lmsImport"
            },
            {
              "id": "u:b622befa34f8",
              "importMod": "addBomCategoryImport",
              "label": "新增导入结果查询",
              "type": "lmsImportResult"
            },
            {
              "exportMod": "bomCategoryExport",
              "id": "u:4f0d3b1b5e57",
              "type": "lmsExport"
            }
          ],
          "api": {
            "data": {
              "orderBy": "updated_time"
            },
            "method": "post",
            "url": "/scp-cost/goods/bomCategory/list"
          },
          "formColumns": [
            {
              "fieldProps": {
                "fieldNames": {
                  "label": "label",
                  "value": "code"
                },
                "options": "${bomConfigTypeEnum}"
              },
              "id": "u:4af9d9e03a4b",
              "label": "BOM 配置类型",
              "name": "bomConfigType",
              "type": "select"
            },
            {
              "fieldProps": {
                "changeOnSelect": true
              },
              "id": "u:5b849fec1623",
              "label": "类目选择",
              "level1Name": "level1CategoryId",
              "level2Name": "level2CategoryId",
              "level3Name": "level3CategoryId",
              "name": "levelCategoryIds",
              "type": "scmCategory"
            },
            {
              "fieldProps": {
                "fieldNames": {
                  "label": "label",
                  "value": "code"
                },
                "options": "${bomTypeEnum}"
              },
              "id": "u:2b57f04fa644",
              "label": "BOM 类型",
              "name": "bomType",
              "type": "select"
            },
            {
              "fieldProps": {
                "fieldNames": {
                  "label": "label",
                  "value": "code"
                },
                "options": "${bomSourceEnum}"
              },
              "id": "u:f3f567712aef",
              "label": "BOM 来源",
              "name": "bomSource",
              "type": "select"
            },
            {
              "fieldProps": {
                "placeholder": "请输入完整 code ，精确搜索"
              },
              "id": "u:89eb8f368c7d",
              "label": "BOM Code",
              "name": "bomCode",
              "type": "text"
            },
            {
              "fieldProps": {
                "placeholder": "模糊搜索"
              },
              "id": "u:067a5e539de7",
              "label": "BOM 名称",
              "name": "bomName",
              "type": "text"
            }
          ],
          "id": "u:e8f22f6f83d1",
          "name": "chainSearchPage",
          "rowKey": "id",
          "search": {
            "defaultCollapsed": false,
            "labelWidth": 110
          },
          "tableColumns": [
            {
              "fixed": "left",
              "id": "u:a8440dcd4693",
              "label": "序号",
              "name": "id",
              "type": "text",
              "width": 100
            },
            {
              "id": "u:6c1bb1fa2e5c",
              "label": "BOM 配置类型",
              "name": "bomConfigTypeName",
              "type": "text",
              "width": 120
            },
            {
              "id": "u:f60dc9692d81",
              "label": "一级类目",
              "name": "level1CategoryName",
              "type": "text"
            },
            {
              "id": "u:441ee2f66773",
              "label": "二级类目",
              "name": "level2CategoryName",
              "type": "text"
            },
            {
              "id": "u:94a6a73013ed",
              "label": "三级类目",
              "name": "level3CategoryName",
              "type": "text"
            },
            {
              "id": "u:8270a7fe733c",
              "label": "BOM 类型",
              "name": "bomTypeName",
              "type": "text"
            },
            {
              "id": "u:dac8737ec3ed",
              "label": "BOM 来源",
              "name": "bomSourceName",
              "type": "text"
            },
            {
              "id": "u:b444e1058759",
              "label": "BOM Code",
              "name": "bomCode",
              "type": "text"
            },
            {
              "id": "u:5f2f32b0dec1",
              "label": "BOM 名称",
              "name": "bomName",
              "type": "text",
              "width": 150
            },
            {
              "align": "right",
              "id": "u:52adc2fdba49",
              "label": "BOM 使用量",
              "name": "bomUsage",
              "type": "text",
              "width": 100
            },
            {
              "id": "u:109336eccd25",
              "label": "BOM 单位",
              "name": "bomUnit",
              "type": "text",
              "width": 80
            },
            {
              "id": "u:7544c66279df",
              "label": "版本",
              "name": "version",
              "type": "text"
            },
            {
              "actions": [
                {
                  "icon": "icon-unordered-list",
                  "id": "u:f729a7a491b2",
                  "label": "查看详细列表",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "drawer",
                          "drawer": {
                            "actions": [
                              {
                                "actionType": "confirm",
                                "icon": "icon-arrow-right",
                                "id": "u:3fd2c8b80bdc",
                                "label": "收起",
                                "level": "primary",
                                "type": "button"
                              }
                            ],
                            "body": [
                              {
                                "api": {
                                  "data": {
                                    "_dataSource_": {
                                      "date": "20240101",
                                      "dateRange": [
                                        "20240101",
                                        "20240131"
                                      ],
                                      "imageList": [
                                        "https://h5static.dewucdn.com/pink/2023/image/10485130_byte848408byte_1e81522f03aea4e2076ddf543dc0eef2_iOS_w3024h3024.jpg",
                                        "https://h5static.dewucdn.com/pink/2023/image/10485130_byte1468033byte_d962f961d691e962a2af7ad42199e964_iOS_w3024h3024.jpg"
                                      ],
                                      "money": "7777777",
                                      "percent": "100",
                                      "progress": "60",
                                      "state": "all",
                                      "text": "这是一段文本",
                                      "time": "10:30:01"
                                    }
                                  },
                                  "method": "post",
                                  "url": "/wizard/mock/detailOperateItem"
                                },
                                "columns": [
                                  {
                                    "copyable": false,
                                    "id": "u:e9d52f6fc0ed",
                                    "label": "一级类目",
                                    "name": "level1CategoryName",
                                    "type": "text"
                                  },
                                  {
                                    "fieldProps": {
                                      "options": [
                                        {
                                          "label": "全部",
                                          "value": "all"
                                        },
                                        {
                                          "label": "解决中",
                                          "value": "pending"
                                        },
                                        {
                                          "label": "已解决",
                                          "value": "resolved"
                                        }
                                      ]
                                    },
                                    "id": "u:e5c22e0e6c3c",
                                    "label": "二级类目",
                                    "name": "level2CategoryName",
                                    "type": "select"
                                  },
                                  {
                                    "copyable": false,
                                    "ellipsis": true,
                                    "id": "u:1d4192782765",
                                    "label": "三级类目",
                                    "name": "level3CategoryName",
                                    "type": "text",
                                    "width": 100
                                  },
                                  {
                                    "copyable": true,
                                    "id": "u:1aa00d987638",
                                    "label": "BomCode",
                                    "name": "bomCode",
                                    "span": 2,
                                    "type": "text"
                                  },
                                  {
                                    "copyable": true,
                                    "id": "u:f9a1535e4cbd",
                                    "label": "版本",
                                    "name": "version",
                                    "type": "text"
                                  }
                                ],
                                "extra": [
                                  {
                                    "body": [
                                      {
                                        "icon": "icon-question-circle",
                                        "id": "u:bc6e55d90f06",
                                        "style": {
                                          "color": "#2b2c3c",
                                          "fontSize": 18
                                        },
                                        "type": "icon"
                                      }
                                    ],
                                    "id": "u:358e36daf334",
                                    "type": "wrapper"
                                  }
                                ],
                                "id": "u:4d54c01716ca",
                                "title": "${bomConfigTypeName}",
                                "type": "descriptions"
                              },
                              {
                                "id": "u:e8ee9cdfa4c6",
                                "plain": true,
                                "type": "divider"
                              },
                              {
                                "actions": [
                                  {
                                    "actionType": "ajax",
                                    "api": {
                                      "data": {
                                        "key": "${selectedRowKeys}"
                                      },
                                      "messages": {
                                        "failed": "修改失败",
                                        "success": "修改成功"
                                      },
                                      "method": "post",
                                      "url": "/mock/form/saveForm"
                                    },
                                    "id": "u:b656ee0417a8",
                                    "label": "批量删除",
                                    "level": "primary",
                                    "type": "button"
                                  }
                                ],
                                "api": {
                                  "data": {
                                    "&": "$$"
                                  },
                                  "method": "get",
                                  "url": "/wizard/mock/demoList"
                                },
                                "formColumns": [
                                  {
                                    "id": "u:f77498897837",
                                    "label": "时效规则",
                                    "name": "ruleGroupName",
                                    "type": "text"
                                  }
                                ],
                                "id": "u:98375a327100",
                                "label": "列表",
                                "name": "chainSearchPage",
                                "rowKey": "id",
                                "tableAlertRender": false,
                                "tableColumns": [
                                  {
                                    "id": "u:e80f60fef0b6",
                                    "label": "名字",
                                    "name": "name",
                                    "type": "text"
                                  },
                                  {
                                    "body": [
                                      {
                                        "height": 100,
                                        "id": "u:33dac2fc5709",
                                        "mode": "cover",
                                        "src": "${pic}",
                                        "type": "picture",
                                        "width": 100
                                      }
                                    ],
                                    "id": "u:49b38f960af8",
                                    "label": "照片",
                                    "name": "pic",
                                    "type": "custom"
                                  },
                                  {
                                    "actions": [
                                      {
                                        "icon": "icon-edit",
                                        "id": "u:18dbc3ee6498",
                                        "label": "编辑",
                                        "type": "button"
                                      },
                                      {
                                        "confirmText": "你确定要删除？",
                                        "danger": true,
                                        "icon": "icon-delete",
                                        "id": "u:680985e5bfe9",
                                        "label": "删除",
                                        "type": "button"
                                      },
                                      {
                                        "icon": "icon-eye",
                                        "id": "u:752e9645e9c4",
                                        "label": "查看",
                                        "type": "button"
                                      },
                                      {
                                        "icon": "icon-unordered-list",
                                        "id": "u:03a0affc5d38",
                                        "label": "操作日志",
                                        "type": "button"
                                      }
                                    ],
                                    "blank": false,
                                    "fixed": "right",
                                    "id": "u:0077b2e577f6",
                                    "label": "操作",
                                    "type": "operation",
                                    "width": 200
                                  }
                                ],
                                "type": "chainSearchPage"
                              }
                            ],
                            "closeOnOutside": true,
                            "confirmLoading": true,
                            "id": "u:a9343a298964",
                            "mask": true,
                            "title": "${\"详情-（\" + id + \")\"}",
                            "type": "drawer",
                            "width": 800
                          }
                        }
                      ]
                    }
                  },
                  "type": "button"
                },
                {
                  "id": "u:e944637c29ac",
                  "label": "编辑",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "dialog",
                          "dialog": {
                            "actions": [
                              {
                                "actionType": "cancel",
                                "id": "u:c15c9d6881ac",
                                "label": "取消",
                                "type": "button"
                              },
                              {
                                "actionType": "confirm",
                                "id": "u:0f3c49c79ebb",
                                "label": "确认",
                                "level": "primary",
                                "reload": "chainSearchPage",
                                "type": "button"
                              }
                            ],
                            "body": [
                              {
                                "actions": [],
                                "api": {
                                  "data": {
                                    "&": "$$",
                                    "id": "${id}"
                                  },
                                  "method": "post",
                                  "url": "/scp-cost/goods/bomCategory/edit"
                                },
                                "body": [
                                  {
                                    "fieldProps": {
                                      "fieldNames": {
                                        "label": "label",
                                        "value": "code"
                                      },
                                      "options": "${bomConfigTypeEnum}"
                                    },
                                    "formItemProps": {
                                      "rules": [
                                        {
                                          "message": "请选择配置类型",
                                          "required": true
                                        }
                                      ]
                                    },
                                    "id": "u:01c99e3ae895",
                                    "label": "BOM 配置类型",
                                    "name": "bomConfigType",
                                    "type": "select"
                                  },
                                  {
                                    "fieldProps": {
                                      "changeOnSelect": true
                                    },
                                    "id": "u:a8b46c842bcf",
                                    "label": "类目选择",
                                    "level1Name": "level1CategoryId",
                                    "level2Name": "level2CategoryId",
                                    "level3Name": "level3CategoryId",
                                    "name": "levelCategoryIds",
                                    "proFieldProps": {
                                      "mode": "read"
                                    },
                                    "type": "scmCategory"
                                  },
                                  {
                                    "formItemProps": {
                                      "hidden": true
                                    },
                                    "id": "u:de0dcca36bfc",
                                    "label": "一级类目名称",
                                    "name": "level1CategoryName",
                                    "type": "text"
                                  },
                                  {
                                    "formItemProps": {
                                      "hidden": true
                                    },
                                    "id": "u:f03b7fbbda79",
                                    "label": "二级类目名称",
                                    "name": "level2CategoryName",
                                    "type": "text"
                                  },
                                  {
                                    "formItemProps": {
                                      "hidden": true
                                    },
                                    "id": "u:a7e6ad13133a",
                                    "label": "三级类目名称",
                                    "name": "level3CategoryName",
                                    "type": "text"
                                  },
                                  {
                                    "fieldProps": {
                                      "fieldNames": {
                                        "label": "label",
                                        "value": "code"
                                      },
                                      "options": "${bomSourceEnum}"
                                    },
                                    "formItemProps": {
                                      "rules": [
                                        {
                                          "message": "请选择来源",
                                          "required": true
                                        }
                                      ]
                                    },
                                    "id": "u:7b7ec22f6cdb",
                                    "label": "BOM 来源",
                                    "name": "bomSource",
                                    "type": "select"
                                  },
                                  {
                                    "fieldProps": {
                                      "fieldNames": {
                                        "label": "label",
                                        "value": "code"
                                      },
                                      "options": "${bomTypeEnum}"
                                    },
                                    "formItemProps": {
                                      "rules": [
                                        {
                                          "message": "请选择类型",
                                          "required": true
                                        }
                                      ]
                                    },
                                    "id": "u:815cfeb47979",
                                    "label": "BOM 类型",
                                    "name": "bomType",
                                    "type": "select"
                                  },
                                  {
                                    "fieldProps": {
                                      "maxLength": 50,
                                      "showCount": true
                                    },
                                    "formItemProps": {
                                      "rules": [
                                        {
                                          "message": "请选择类目",
                                          "required": true
                                        }
                                      ]
                                    },
                                    "id": "u:3fb28ae54eff",
                                    "label": "BOM Code",
                                    "name": "bomCode",
                                    "proFieldProps": {
                                      "mode": "read"
                                    },
                                    "type": "text"
                                  },
                                  {
                                    "fieldProps": {
                                      "maxLength": 100,
                                      "showCount": true
                                    },
                                    "formItemProps": {
                                      "rules": [
                                        {
                                          "message": "请选择类目",
                                          "required": true
                                        }
                                      ]
                                    },
                                    "id": "u:686aa252d724",
                                    "label": "BOM 名称",
                                    "name": "bomName",
                                    "type": "textarea"
                                  },
                                  {
                                    "fieldProps": {
                                      "precision": 10
                                    },
                                    "formItemProps": {
                                      "rules": [
                                        {
                                          "message": "使用量不能为空",
                                          "required": true
                                        }
                                      ]
                                    },
                                    "id": "u:42491bb58ff6",
                                    "label": "BOM 使用量",
                                    "name": "bomUsage",
                                    "type": "digit"
                                  },
                                  {
                                    "fieldProps": {
                                      "maxLength": 50,
                                      "showCount": true
                                    },
                                    "formItemProps": {
                                      "rules": [
                                        {
                                          "message": "单位不能为空",
                                          "required": true
                                        }
                                      ]
                                    },
                                    "id": "u:866b6585e6c4",
                                    "label": "BOM 单位",
                                    "name": "bomUnit",
                                    "type": "text"
                                  }
                                ],
                                "id": "u:364a302cc4e1",
                                "type": "form"
                              }
                            ],
                            "id": "u:3888f8495625",
                            "title": "编辑",
                            "type": "dialog",
                            "width": 720
                          }
                        }
                      ]
                    }
                  },
                  "type": "button"
                },
                {
                  "actionType": "ajax",
                  "api": {
                    "data": {
                      "id": "${id}"
                    },
                    "messages": {
                      "failed": "删除失败",
                      "success": "删除成功"
                    },
                    "method": "post",
                    "url": "/scp-cost/goods/bomLevel1Category/remove"
                  },
                  "confirmText": "是否确认删除",
                  "danger": true,
                  "id": "u:31d4784cea59",
                  "label": "删除",
                  "type": "button"
                }
              ],
              "fixed": "right",
              "id": "u:7320249ca7cb",
              "label": "操作",
              "type": "operation",
              "width": 220
            }
          ],
          "type": "chainSearchPage"
        }
      ],
      "id": "u:f92c01a6dfac",
      "type": "service"
    }
  ],
  "id": "u:001434419bdb",
  "subTitle": "数据格式化、下拉枚举查询、联动、新增、编辑、删除、多个导入、多个导出、多个导入结果查询，点击详情打开列表",
  "title": "复杂列表页",
  "type": "page"
}

==================================================

菜单项: 功能齐全列表页
时间: 2025-07-22T18:01:34.920316
内容长度: 30318 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "disabled": "${ selectedRowKeys.length === 0}",
          "id": "u:7adb48d8da7b",
          "label": "批量操作",
          "level": "primary",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "dialog",
                  "dialog": {
                    "actions": [
                      {
                        "actionType": "cancel",
                        "id": "u:bd4cd6480d96",
                        "label": "取消",
                        "type": "button"
                      },
                      {
                        "actionType": "confirm",
                        "confirmText": "确定批量修改吗？",
                        "id": "u:80ec033801c8",
                        "label": "确认",
                        "level": "primary",
                        "type": "button"
                      }
                    ],
                    "body": [
                      {
                        "actions": [],
                        "api": {
                          "data": {
                            "&": "$$",
                            "ids": "${selectedRowKeys}"
                          },
                          "method": "post",
                          "url": "/mock/form/saveForm"
                        },
                        "body": [
                          {
                            "id": "u:cba4c2a75e22",
                            "label": "文本",
                            "name": "text",
                            "type": "text"
                          }
                        ],
                        "id": "u:cd961e113b09",
                        "type": "form"
                      }
                    ],
                    "id": "u:8e1dbc3c2d5a",
                    "title": "批量修改",
                    "type": "dialog"
                  }
                }
              ]
            }
          },
          "type": "button"
        },
        {
          "actionType": "dialog",
          "dialog": {
            "actions": [
              {
                "actionType": "cancel",
                "id": "u:a4c1aa593607",
                "label": "取消",
                "type": "button"
              },
              {
                "actionType": "submit",
                "id": "u:fbe9237dbfcd",
                "label": "确认",
                "level": "primary",
                "reload": "chainSearchPage",
                "type": "button"
              }
            ],
            "body": [
              {
                "actions": [],
                "api": {
                  "messages": {
                    "success": "新增成功"
                  },
                  "url": "/wcs/admin/device/tree/add"
                },
                "body": [
                  {
                    "api": {
                      "method": "get",
                      "url": "/wcs/admin/device/tree/queryAllDeviceNodeType"
                    },
                    "fieldProps": {
                      "fieldNames": {
                        "label": "deviceNodeTypeName",
                        "value": "deviceNodeTypeCode"
                      }
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择节点类型",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:1a52002c830f",
                    "label": "节点类型",
                    "name": "nodeType",
                    "type": "select"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 32
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入节点名称",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:ffc2907f453f",
                    "label": "节点名称",
                    "name": "nodeName",
                    "type": "text"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 32
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入节点编码",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:37821659b308",
                    "label": "节点编码",
                    "name": "barcode",
                    "type": "text"
                  },
                  {
                    "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                    "id": "u:5ceae3c17423",
                    "label": "设备位置",
                    "name": "location",
                    "type": "text"
                  },
                  {
                    "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                    "id": "u:f21e4a54e221",
                    "label": "设备区域",
                    "name": "region",
                    "type": "text"
                  },
                  {
                    "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                    "id": "u:693840b209d4",
                    "label": "所属电柜",
                    "name": "cabinet",
                    "type": "text"
                  },
                  {
                    "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                    "id": "u:b69b5d733ea3",
                    "label": "扩展信息",
                    "name": "feature",
                    "type": "textarea"
                  },
                  {
                    "id": "u:781c982de64b",
                    "label": "上级节点名称",
                    "name": "parentName",
                    "proFieldProps": {
                      "mode": "read"
                    },
                    "type": "text"
                  },
                  {
                    "id": "u:ede282e6fdb7",
                    "label": "上级节点编码",
                    "name": "parentId",
                    "proFieldProps": {
                      "mode": "read"
                    },
                    "type": "text"
                  }
                ],
                "id": "u:842df78b33b7",
                "onFormInit": "var parentObj = values.queryParam.parentObj;\nreturn {\n  parentId: parentObj.value,\n  parentName: parentObj.label\n}\n",
                "type": "form"
              }
            ],
            "id": "u:f1167ab898bd",
            "title": "添加节点",
            "type": "dialog",
            "width": 700
          },
          "id": "u:87954eb4a1b3",
          "label": "添加节点",
          "level": "primary",
          "type": "button"
        },
        {
          "fileName": "设备树节点批量导入模板.xlsx",
          "fileNameLabel": "设备树节点批量导入模板",
          "fileUrl": "https://t1-h5cdn.dewu.net/supply-chain/scm-cdn/10604095/20231212-8455de936a2c83f3.xlsx",
          "id": "u:32bc075fc56e",
          "importMod": "importDeviceTreeNode",
          "label": "导入",
          "type": "lmsImport"
        },
        {
          "id": "u:80011f60c80b",
          "importMod": "importDeviceTreeNode",
          "label": "导入结果查询",
          "type": "lmsImportResult"
        },
        {
          "exportMod": "exportDeviceTree",
          "id": "u:a454c28f8fb1",
          "type": "lmsExport"
        }
      ],
      "api": {
        "data": {},
        "method": "post",
        "requestAdaptor": "if(api.data.nodeId && api.data.nodeId.label) {\n  api.data.parentObj = api.data.nodeId;\n  api.data.nodeId = api.data.nodeId.value;\n}\nreturn api",
        "url": "/wcs/admin/device/tree/pageQuery"
      },
      "formColumns": [
        {
          "id": "u:9b3d965998ff",
          "label": "节点名称/编码",
          "labelWidth": "200",
          "name": "nodeNameOrCode",
          "type": "text"
        },
        {
          "api": {
            "data": {
              "nodeId": ""
            },
            "method": "post",
            "url": "/wcs/admin/device/tree/queryNextDeviceTree"
          },
          "fieldProps": {
            "allowClear": true,
            "fieldNames": {
              "label": "nodeName",
              "value": "id"
            },
            "labelInValue": true,
            "placeholder": "请选择"
          },
          "id": "u:358b80a00be5",
          "label": "节点树",
          "loadApi": {
            "data": {
              "nodeId": "${id}"
            },
            "method": "post",
            "url": "/wcs/admin/device/tree/queryNextDeviceTree"
          },
          "name": "nodeId",
          "type": "treeSelect"
        }
      ],
      "id": "u:644132d2ddcc",
      "name": "chainSearchPage",
      "rowKey": "id",
      "rowSelection": {
        "type": "checkbox"
      },
      "search": {
        "labelWidth": 110
      },
      "tableColumns": [
        {
          "fixed": "left",
          "id": "u:76d17b29da35",
          "label": "节点名称",
          "name": "nodeName",
          "type": "text"
        },
        {
          "id": "u:d74935c2c467",
          "label": "节点编码（文本展示）",
          "name": "nodeCode",
          "type": "text"
        },
        {
          "body": [
            {
              "body": [
                {
                  "id": "u:7a8aff8c3ade",
                  "style": {
                    "textAlign": "left"
                  },
                  "tpl": "${nodeName}",
                  "type": "tpl"
                }
              ],
              "content": [
                {
                  "body": [
                    {
                      "column": 2,
                      "columns": [
                        {
                          "copyable": true,
                          "id": "u:849238135780",
                          "label": "节点名称",
                          "name": "nodeName",
                          "type": "text"
                        },
                        {
                          "fieldProps": {
                            "options": [
                              {
                                "label": "全部",
                                "value": "all"
                              },
                              {
                                "label": "解决中",
                                "value": "pending"
                              },
                              {
                                "label": "已解决",
                                "value": "resolved"
                              }
                            ]
                          },
                          "id": "u:1c7ddcd3c422",
                          "label": "节点编码",
                          "name": "nodeCode",
                          "type": "select"
                        },
                        {
                          "body": [
                            {
                              "dataSource": [
                                "https://cdn.dewu.com/node-common/cbfbc33d-b829-c754-8b09-a855c557b235-960-960.png",
                                "https://cdn.dewu.com/node-common/1d099b0c-d215-e67c-9171-f010af4f51a4-960-960.png"
                              ],
                              "height": 100,
                              "id": "u:a2c613a594b9",
                              "type": "pictures",
                              "width": 100
                            }
                          ],
                          "id": "u:43f388b9f4ce",
                          "label": "图片",
                          "name": "imageList",
                          "span": 2,
                          "type": "custom"
                        }
                      ],
                      "id": "u:5591b7928576",
                      "layout": "vertical",
                      "size": "small",
                      "title": [
                        {
                          "id": "u:7b20af60ef03",
                          "style": {
                            "textAlign": "left"
                          },
                          "tpl": "详细信息",
                          "type": "tpl"
                        }
                      ],
                      "type": "descriptions"
                    }
                  ],
                  "id": "u:f35af0c9fb79",
                  "style": {
                    "width": "300px"
                  },
                  "type": "wrapper"
                }
              ],
              "id": "u:598b2b47afd9",
              "type": "popover"
            }
          ],
          "id": "u:35ec075d884c",
          "label": "浮层展示更多",
          "name": "test1",
          "type": "custom"
        },
        {
          "id": "u:262909ed6127",
          "label": "节点类型",
          "name": "nodeTypeDesc",
          "type": "text"
        },
        {
          "body": [
            {
              "color": "${hideDesc === '作废' ? 'orange': 'green'}",
              "id": "u:3b449f5b9859",
              "label": "${hideDesc}",
              "type": "tag"
            }
          ],
          "id": "u:ade7751b7edb",
          "label": "节点状态",
          "name": "hideDesc",
          "type": "custom"
        },
        {
          "id": "u:a03b915e9865",
          "label": "上级节点名称",
          "name": "parentNodeName",
          "type": "text"
        },
        {
          "id": "u:f7dd1cf51de9",
          "label": "上级节点编码",
          "name": "parentBarcode",
          "type": "text"
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:aa43185e33f2",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "confirm",
                    "id": "u:090c78b00fc4",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "api": {
                      "messages": {
                        "success": "保存成功"
                      },
                      "url": "/wcs/admin/device/tree/modify"
                    },
                    "body": [
                      {
                        "api": {
                          "method": "get",
                          "url": "/wcs/admin/device/tree/queryAllDeviceNodeType"
                        },
                        "fieldProps": {
                          "disabled": true,
                          "fieldNames": {
                            "label": "deviceNodeTypeName",
                            "value": "deviceNodeTypeCode"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择节点类型",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:00643ec12aee",
                        "label": "节点类型",
                        "name": "nodeType",
                        "type": "select"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 32
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入节点名称",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:b6235bedcdf2",
                        "label": "节点名称",
                        "name": "nodeName",
                        "type": "text"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 32
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入节点编码",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:bebe03bea9a1",
                        "label": "节点编码",
                        "name": "barcode",
                        "type": "text"
                      },
                      {
                        "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                        "id": "u:05351ffc84e6",
                        "label": "设备位置",
                        "name": "location",
                        "type": "text"
                      },
                      {
                        "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                        "id": "u:7ccf5ea7e2f0",
                        "label": "设备区域",
                        "name": "region",
                        "type": "text"
                      },
                      {
                        "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                        "id": "u:4ecbc87a0d44",
                        "label": "所属电柜",
                        "name": "cabinet",
                        "type": "text"
                      },
                      {
                        "hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}",
                        "id": "u:c74df1922a2b",
                        "label": "扩展信息",
                        "name": "feature",
                        "type": "textarea"
                      },
                      {
                        "id": "u:0defda7c1919",
                        "label": "上级节点名称",
                        "name": "parentNodeName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:50c5fc60d58f",
                        "label": "上级节点编码",
                        "name": "parentId",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "formItemProps": {
                          "hidden": true
                        },
                        "id": "u:4d36b526dbfb",
                        "label": "nodeId",
                        "name": "nodeId",
                        "type": "text"
                      }
                    ],
                    "id": "u:cc955a411288",
                    "initApi": {
                      "adaptor": "if(response.data.id) {\n  response.data.nodeId = response.data.id\n}\nreturn response",
                      "data": {
                        "nodeId": "${id}"
                      },
                      "method": "post",
                      "url": "/wcs/admin/device/tree/queryTreeDetailById"
                    },
                    "onFormInit": "if(values.id) {\n  values.nodeId = values.id\n}\nreturn values",
                    "type": "form"
                  }
                ],
                "id": "u:9f09a2dd1c15",
                "title": "编辑节点",
                "type": "dialog",
                "width": 700
              },
              "id": "u:b1e201c58b20",
              "label": "编辑",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "nodeId": "${id}"
                },
                "messages": {
                  "failed": "作废失败",
                  "success": "作废成功"
                },
                "method": "post",
                "url": "/wcs/admin/device/tree/hideOrShow"
              },
              "confirmText": "是否确认作废",
              "danger": true,
              "hiddenOn": "${hide != 0}",
              "id": "u:5f81c62318f9",
              "label": "作废",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "nodeId": "${id}"
                },
                "messages": {
                  "failed": "启用失败",
                  "success": "启用成功"
                },
                "method": "post",
                "url": "/wcs/admin/device/tree/hideOrShow"
              },
              "confirmText": "是否确认启用",
              "danger": true,
              "hiddenOn": "${hide != 1}",
              "id": "u:5f81c62318f3",
              "label": "启用",
              "type": "button"
            },
            {
              "actionType": "dialog",
              "dialog": {
                "body": {
                  "actions": [],
                  "api": {
                    "data": {
                      "nodeId": "${id}"
                    },
                    "method": "POST",
                    "url": "/wcs/admin/device/tree/queryTreeDetailById"
                  },
                  "body": [
                    {
                      "id": "u:e15e1402aacd",
                      "label": "节点编号",
                      "name": "barcode",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "id": "u:7e64801b0ed4",
                      "label": "节点类型",
                      "name": "nodeTypeDesc",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "id": "u:c4989894ee81",
                      "label": "节点名称",
                      "name": "nodeName",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "id": "u:108c1ee4adcd",
                      "label": "上级节点名称",
                      "name": "parentNodeName",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text",
                      "visibleOn": "${!nodeType === 'WAREHOUSE'}"
                    },
                    {
                      "id": "u:bfe47fd63eb0",
                      "label": "上级节点编号",
                      "name": "parentBarcode",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text",
                      "visibleOn": "${!nodeType === 'WAREHOUSE'}"
                    },
                    {
                      "id": "u:becc81231ec3",
                      "label": "所属仓库",
                      "name": "warehouseCode",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "id": "u:d1c864a1c271",
                      "label": "节点描述",
                      "name": "nodeDesc",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "id": "u:5f019001f528",
                      "label": "设备位置",
                      "name": "location",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text",
                      "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"
                    },
                    {
                      "id": "u:48a09acbfe05",
                      "label": "设备区域",
                      "name": "region",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text",
                      "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"
                    },
                    {
                      "id": "u:e04424c4ebc1",
                      "label": "所属电柜",
                      "name": "cabinet",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text",
                      "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"
                    },
                    {
                      "id": "u:54a75b6804df",
                      "label": "扩展信息",
                      "name": "feature",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text",
                      "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"
                    }
                  ],
                  "column": 2,
                  "id": "loadDetail",
                  "type": "form"
                },
                "id": "u:b7323b4b54fe",
                "title": "节点详情",
                "type": "dialog"
              },
              "id": "u:e4d5ff9413fa",
              "label": "详情",
              "level": "link",
              "type": "button"
            },
            {
              "id": "u:364efd916a48",
              "label": "页面跳转",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "link",
                      "args": {
                        "link": "/ww/bb/cc",
                        "params": {
                          "id": "${id}"
                        }
                      }
                    }
                  ]
                }
              },
              "type": "button"
            },
            {
              "id": "u:2ba301b67e75",
              "label": "操作记录",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:c83b5d2735a5",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "confirm",
                            "id": "u:b2f10786df75",
                            "label": "确认",
                            "level": "primary",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "api": {
                              "data": {
                                "targetId": "${id}",
                                "type": "11"
                              },
                              "method": "POST",
                              "url": "/wms/admin/wcs/base/new/operatelog/query"
                            },
                            "formColumns": [],
                            "id": "u:a3c54ad92cf7",
                            "name": "logList",
                            "rowKey": "id",
                            "search": false,
                            "tableAlertRender": false,
                            "tableColumns": [
                              {
                                "hideInSearch": true,
                                "id": "u:11db14a10f62",
                                "label": "操作人",
                                "name": "editor",
                                "width": 100
                              },
                              {
                                "hideInSearch": true,
                                "id": "u:722d34b2513d",
                                "label": "操作时间",
                                "name": "editTime",
                                "width": 120
                              },
                              {
                                "hideInSearch": true,
                                "id": "u:798b16276edf",
                                "label": "操作内容",
                                "name": "content"
                              }
                            ],
                            "type": "chainSearchPage"
                          }
                        ],
                        "id": "u:5fc83871868e",
                        "title": "操作记录",
                        "type": "dialog",
                        "width": 900
                      }
                    }
                  ]
                }
              },
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:91472d6e0fdd",
          "label": "操作",
          "type": "operation",
          "width": 200
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:7003827c792e",
  "name": "",
  "subTitle": "查询条件标签名宽度设置、查询结果透传给新增模块使用、动态获取编辑数据、初始逻辑处理、参数映射",
  "title": "功能齐全列表页",
  "type": "page"
}

==================================================

菜单项: 无查询区域列表页
时间: 2025-07-22T18:02:00.648630
内容长度: 13476 字符
------------------------------
{
  "body": [
    {
      "actions": [],
      "api": {
        "method": "post",
        "url": "/tms/admin/transportCabin/page"
      },
      "formColumns": [],
      "id": "u:c61a67ebea74",
      "name": "chainSearchPage",
      "options": false,
      "rowSelection": false,
      "search": false,
      "tableColumns": [
        {
          "id": "u:cab35d72826e",
          "label": "运输产品分层编码",
          "name": "cabinCode",
          "type": "text"
        },
        {
          "id": "u:d96d67635ce1",
          "label": "运输产品分层名称",
          "name": "cabinName",
          "type": "text"
        },
        {
          "id": "u:bc234fb1de1a",
          "label": "是否兜底",
          "name": "isDefaultDesc",
          "type": "text"
        },
        {
          "id": "u:566b8e8b6d6f",
          "label": "是否触发成本倒挂判断",
          "name": "costInvertedDesc",
          "type": "text"
        },
        {
          "id": "u:c37b0953ceee",
          "label": "品牌直发内部轮询",
          "name": "ppzfPollDesc",
          "type": "text"
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:3f3ef07ae84e",
                    "label": "取消",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "api": {
                      "method": "POST",
                      "prefix": "https://t1-wizard-server.shizhuang-inc.net",
                      "url": "/wizard/mock/add"
                    },
                    "body": [
                      {
                        "id": "u:c2a6aeab9a4a",
                        "label": "运输产品分层编码",
                        "name": "cabinCode",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:71d2354536c5",
                        "label": "运输产品分层名称",
                        "name": "cabinName",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:3517bf9baabc",
                        "label": "是否兜底",
                        "name": "isDefaultDesc",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:dc2a30e01f2e",
                        "label": "是否触发成本倒挂判断",
                        "name": "costInvertedDesc",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      },
                      {
                        "id": "u:f3e00c0f20e6",
                        "label": "品牌直发内部轮询",
                        "name": "ppzfPollDesc",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "text"
                      }
                    ],
                    "id": "u:cdae69295188",
                    "initApi": {
                      "data": {
                        "cabinCode": "${cabinCode}"
                      },
                      "method": "get",
                      "url": "/tms/admin/transportCabin/get"
                    },
                    "labelCol": {
                      "style": {
                        "width": 200
                      }
                    },
                    "title": "表单",
                    "type": "form"
                  }
                ],
                "id": "u:5b96a8ff4054",
                "title": "查看",
                "type": "dialog",
                "width": 600
              },
              "id": "u:8eb56843f0ae",
              "label": "查看",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "confirm",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": {
                  "actions": [],
                  "api": {
                    "messages": {
                      "success": "保存成功"
                    },
                    "url": "/tms/admin/transportCabin/updateByCabinCode"
                  },
                  "body": [
                    {
                      "fieldProps": {
                        "disabled": false,
                        "maxLength": 50
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请输入",
                            "required": true
                          }
                        ]
                      },
                      "label": "运输产品分层编码",
                      "name": "cabinCode",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "fieldProps": {
                        "disabled": false,
                        "maxLength": 50
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请输入",
                            "required": true
                          }
                        ]
                      },
                      "label": "运输产品分层名称",
                      "name": "cabinName",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "text"
                    },
                    {
                      "fieldProps": {
                        "disabled": false,
                        "options": [
                          {
                            "label": "是",
                            "value": 1
                          },
                          {
                            "label": "否",
                            "value": 0
                          }
                        ]
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请输入",
                            "required": true
                          }
                        ]
                      },
                      "label": "是否兜底",
                      "name": "isDefault",
                      "proFieldProps": {
                        "mode": "read"
                      },
                      "type": "select"
                    },
                    {
                      "fieldProps": {
                        "options": [
                          {
                            "label": "是",
                            "value": 1
                          },
                          {
                            "label": "否",
                            "value": 0
                          }
                        ]
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请输入",
                            "required": true
                          }
                        ]
                      },
                      "label": "是否触发成本倒挂判断",
                      "name": "costInverted",
                      "type": "select"
                    },
                    {
                      "fieldProps": {
                        "options": [
                          {
                            "label": "支持",
                            "value": 1
                          },
                          {
                            "label": "不支持",
                            "value": 0
                          }
                        ]
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择",
                            "required": true
                          }
                        ]
                      },
                      "label": "品牌直发内部轮询",
                      "name": "ppzfPoll",
                      "type": "select"
                    }
                  ],
                  "initApi": {
                    "data": {
                      "cabinCode": "${cabinCode}"
                    },
                    "url": "/tms/admin/transportCabin/get"
                  },
                  "labelCol": {
                    "style": {
                      "width": "160px"
                    }
                  },
                  "type": "form"
                },
                "title": "编辑",
                "type": "dialog",
                "width": 600
              },
              "id": "u:b929f26710a2",
              "label": "编辑",
              "level": "link",
              "type": "button"
            },
            {
              "label": "操作记录",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:22151fb028ec",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "confirm",
                            "id": "u:f69d0d381721",
                            "label": "确认",
                            "level": "primary",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "api": {
                              "data": {
                                "id": "${id || 1}",
                                "tableName": "des_transport_cabin"
                              },
                              "method": "post",
                              "url": "/tms/admin/log/getLogList"
                            },
                            "formColumns": [],
                            "id": "u:322707c55516",
                            "label": "列表",
                            "name": "chainSearchPage",
                            "options": true,
                            "rowKey": "id",
                            "search": false,
                            "tableAlertRender": false,
                            "tableColumns": [
                              {
                                "label": "操作人",
                                "name": "name",
                                "type": "text",
                                "width": 100
                              },
                              {
                                "label": "操作时间",
                                "name": "modifyTime",
                                "type": "text",
                                "width": 170
                              },
                              {
                                "label": "操作类型",
                                "name": "operationName",
                                "type": "text",
                                "width": 100
                              },
                              {
                                "label": "操作内容",
                                "name": "operationContent",
                                "type": "text",
                                "width": 400
                              }
                            ],
                            "type": "chainSearchPage"
                          }
                        ],
                        "id": "u:1cf9cd086642",
                        "title": "查看操作日志",
                        "type": "dialog",
                        "width": 900
                      }
                    }
                  ]
                }
              },
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:f952396fa636",
          "label": "操作",
          "type": "operation",
          "width": 100
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "影藏了搜索区和工具栏",
  "title": "无查询区域列表页",
  "type": "page"
}

==================================================

菜单项: 卡片展示复杂详情
时间: 2025-07-22T18:02:26.673944
内容长度: 31602 字符
------------------------------
{
  "body": [
    {
      "body": [
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${contents}"
            },
            "url": "/pink/admin/repositories/list"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "repositoryName",
              "value": "repositoryCode"
            },
            "mode": "multiple"
          },
          "id": "u:c29c7f0e9fb8",
          "label": "仓位",
          "name": "warehouseCodeList",
          "type": "select"
        },
        {
          "fieldProps": {
            "fetchDataOnSearch": false,
            "mode": "multiple",
            "options": [
              {
                "label": "待审核",
                "value": 0
              },
              {
                "label": "审核通过",
                "value": 1
              },
              {
                "label": "审核不通过",
                "value": 2
              }
            ],
            "showSearch": true
          },
          "formItemProps": {},
          "id": "u:57c5fa3e08f9",
          "label": "审核状态",
          "name": "verifyStatusList",
          "type": "select"
        },
        {
          "formItemProps": {
            "rules": [
              {
                "message": "请选择质检不通过时间",
                "required": true
              }
            ]
          },
          "id": "u:5d893758fddc",
          "initialValue": "${\"2022-07-01 00:00:00\"},${\"2024-07-01 00:00:00\"}",
          "label": "质检不通过时间",
          "name": "rangePicker",
          "type": "rangePicker"
        }
      ],
      "id": "u:4f99439dc91d",
      "labelCol": {
        "style": {
          "width": 160
        }
      },
      "layout": "vertical",
      "mode": "query",
      "omitNil": false,
      "submitText": "查询",
      "target": "dataSource",
      "type": "form"
    },
    {
      "api": {
        "data": {
          "categoryIdList": "${[]}",
          "createEndTime": "${DATETOSTR(rangePicker[1], 'YYYY-MM-DD HH:mm:ss')}",
          "createStartTime": "${DATETOSTR(rangePicker[0], 'YYYY-MM-DD HH:mm:ss')}",
          "pageNum": "${1}",
          "pageSize": "${10}",
          "verifyStatusList": "${verifyStatusList || []}",
          "warehouseCodeList": "${warehouseCodeList || []}"
        },
        "method": "post",
        "sendOn": "${IF(rangePicker, true, false)}",
        "trackExpression": "${rangePicker},${warehouseCodeList.length > 0},${verifyStatusList.length > 0}",
        "url": "/pink/admin/quality/secondReason/v1/list"
      },
      "body": [
        {
          "body": [
            {
              "icon": "icon-export",
              "id": "u:92353b13e5be",
              "label": "导出",
              "level": "primary",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "toast",
                      "args": {
                        "msg": "导出中，请稍等...",
                        "msgType": "info"
                      }
                    },
                    {
                      "actionType": "ajax",
                      "api": {
                        "data": {
                          "categoryIdList": "${[]}",
                          "createEndTime": "${DATETOSTR(rangePicker[1], 'YYYY-MM-DD HH:mm:ss')}",
                          "createStartTime": "${DATETOSTR(rangePicker[0], 'YYYY-MM-DD HH:mm:ss')}",
                          "pageNum": "${1}",
                          "pageSize": "${10}",
                          "verifyStatusList": "${verifyStatusList || []}",
                          "warehouseCodeList": "${warehouseCodeList || []}"
                        },
                        "method": "post",
                        "silent": true,
                        "url": "/pink/admin/quality/secondReason/v1/export"
                      },
                      "loopApi": {
                        "data": {
                          "taskId": "${taskId}"
                        },
                        "method": "get",
                        "sendOn": "${downloadStatus == 0}",
                        "silent": true,
                        "url": "/pink/admin/report/queryDownloadResult"
                      },
                      "outputVar": "exportResult"
                    },
                    {
                      "actionType": "url",
                      "args": {
                        "url": "${event.data.exportResult.responseData.downloadUrl}"
                      },
                      "expression": "${event.data.exportResult.responseData.downloadStatus == 1}"
                    },
                    {
                      "actionType": "toast",
                      "args": {
                        "msg": "导出成功，已自动下载",
                        "msgType": "success"
                      },
                      "expression": "${event.data.exportResult.responseData.downloadStatus == 1}"
                    }
                  ]
                }
              },
              "type": "button"
            }
          ],
          "id": "u:a46a1c05f0b3",
          "onEvent": {
            "click": {
              "actions": []
            }
          },
          "style": {
            "paddingBottom": 12,
            "paddingTop": 12,
            "textAlign": "right"
          },
          "type": "container"
        },
        {
          "id": "u:413b38a23e91",
          "items": {
            "body": [
              {
                "actions": [],
                "body": [
                  {
                    "alignItems": "flex-start",
                    "id": "u:1d42db36ec27",
                    "items": [
                      {
                        "body": [
                          {
                            "id": "u:89294556b1c2",
                            "style": {
                              "color": "#ff0000",
                              "marginTop": "12px",
                              "textAlign": "left"
                            },
                            "tpl": "${\"二级瑕疵原因：\"+secondReason}",
                            "type": "tpl",
                            "wrapperComponent": "h3"
                          },
                          {
                            "id": "u:47b1c508d6ae",
                            "items": [
                              {
                                "body": [
                                  {
                                    "id": "u:d95b6f085aae",
                                    "mode": "contain",
                                    "src": "${goodsLogo || \"https://cdn.dewu.com/node-common/344e8524-9fe8-c713-75c5-6c8fb75bc4c0-1440-1446.png\"}",
                                    "type": "picture",
                                    "width": 100
                                  }
                                ],
                                "id": "u:2a533c6c304c",
                                "style": {
                                  "display": "block",
                                  "flex": "0 0 120px",
                                  "flexBasis": "120px",
                                  "position": "static"
                                },
                                "type": "wrapper"
                              },
                              {
                                "body": [
                                  {
                                    "bordered": false,
                                    "column": 2,
                                    "columns": [
                                      {
                                        "body": [
                                          {
                                            "blank": true,
                                            "body": "${uniqueCode|| \"-\"}",
                                            "href": "${uniqueCode ? \"https://t1-scm.shizhuang-inc.net/#/scm-pink/operate/operate-in-store-detail?id=\" + itemId : null}",
                                            "id": "u:5d5f448c1f1d",
                                            "type": "link"
                                          }
                                        ],
                                        "copyable": false,
                                        "id": "u:7e60b051a688",
                                        "label": "唯一码",
                                        "name": "uniqueCode",
                                        "type": "custom"
                                      },
                                      {
                                        "body": [
                                          {
                                            "id": "u:13244e24ef72",
                                            "style": {
                                              "textAlign": "left"
                                            },
                                            "tpl": "${DATETOSTR(createTime, \"YYYY-MM-DD HH:mm:ss\")}",
                                            "type": "tpl"
                                          }
                                        ],
                                        "fieldProps": {
                                          "options": [
                                            {
                                              "label": "全部",
                                              "value": "all"
                                            },
                                            {
                                              "label": "解决中",
                                              "value": "pending"
                                            },
                                            {
                                              "label": "已解决",
                                              "value": "resolved"
                                            }
                                          ]
                                        },
                                        "id": "u:23b5571da66f",
                                        "label": "质检不通过时间",
                                        "name": "createTime",
                                        "type": "custom"
                                      },
                                      {
                                        "fieldProps": {
                                          "options": [
                                            {
                                              "label": "全部",
                                              "value": "all"
                                            },
                                            {
                                              "label": "解决中",
                                              "value": "pending"
                                            },
                                            {
                                              "label": "已解决",
                                              "value": "resolved"
                                            }
                                          ]
                                        },
                                        "id": "u:3203649cca34",
                                        "label": "商品名称",
                                        "name": "spuName",
                                        "type": "text"
                                      },
                                      {
                                        "fieldProps": {
                                          "options": [
                                            {
                                              "label": "全部",
                                              "value": "all"
                                            },
                                            {
                                              "label": "解决中",
                                              "value": "pending"
                                            },
                                            {
                                              "label": "已解决",
                                              "value": "resolved"
                                            }
                                          ]
                                        },
                                        "id": "u:d0b57819c5a7",
                                        "label": "瑕疵原因",
                                        "name": "firstReason",
                                        "type": "text"
                                      },
                                      {
                                        "fieldProps": {
                                          "options": [
                                            {
                                              "label": "全部",
                                              "value": "all"
                                            },
                                            {
                                              "label": "解决中",
                                              "value": "pending"
                                            },
                                            {
                                              "label": "已解决",
                                              "value": "resolved"
                                            }
                                          ]
                                        },
                                        "id": "u:fd19d59e35ab",
                                        "label": "规格",
                                        "name": "spec",
                                        "type": "text"
                                      },
                                      {
                                        "fieldProps": {
                                          "options": [
                                            {
                                              "label": "全部",
                                              "value": "all"
                                            },
                                            {
                                              "label": "解决中",
                                              "value": "pending"
                                            },
                                            {
                                              "label": "已解决",
                                              "value": "resolved"
                                            }
                                          ]
                                        },
                                        "id": "u:cc05bb94934f",
                                        "label": "审核人",
                                        "name": "verifyUserName",
                                        "type": "text"
                                      },
                                      {
                                        "copyable": true,
                                        "fieldProps": {
                                          "options": [
                                            {
                                              "label": "全部",
                                              "value": "all"
                                            },
                                            {
                                              "label": "解决中",
                                              "value": "pending"
                                            },
                                            {
                                              "label": "已解决",
                                              "value": "resolved"
                                            }
                                          ]
                                        },
                                        "id": "u:05acf84fc199",
                                        "label": "货号",
                                        "name": "artNo",
                                        "type": "text"
                                      },
                                      {
                                        "body": [
                                          {
                                            "id": "u:8cf0b889b65c",
                                            "style": {
                                              "textAlign": "left"
                                            },
                                            "tpl": "${DATETOSTR(verifyTime, \"YYYY-MM-DD HH:mm:ss\")}",
                                            "type": "tpl"
                                          }
                                        ],
                                        "id": "u:a5e3d094e166",
                                        "label": "审核时间",
                                        "name": "verifyTime",
                                        "type": "custom"
                                      },
                                      {
                                        "fieldProps": {
                                          "options": [
                                            {
                                              "label": "全部",
                                              "value": "all"
                                            },
                                            {
                                              "label": "解决中",
                                              "value": "pending"
                                            },
                                            {
                                              "label": "已解决",
                                              "value": "resolved"
                                            }
                                          ]
                                        },
                                        "id": "u:15aa5e646191",
                                        "label": "审核不通过原因",
                                        "name": "remark",
                                        "span": 2,
                                        "type": "text"
                                      }
                                    ],
                                    "extra": [
                                      {
                                        "body": [],
                                        "id": "u:92dba49805ae",
                                        "type": "wrapper"
                                      }
                                    ],
                                    "id": "u:315b92acae65",
                                    "layout": "horizontal",
                                    "size": "small",
                                    "style": {
                                      "margin-top": "-40px"
                                    },
                                    "type": "descriptions"
                                  }
                                ],
                                "id": "u:ffc9abde49d8",
                                "style": {
                                  "display": "block",
                                  "flex": "0 0 620px",
                                  "flexBasis": "620px",
                                  "position": "static"
                                },
                                "type": "wrapper"
                              },
                              {
                                "body": [
                                  {
                                    "hiddenOn": "${!flawImageList || (flawImageList && flawImageList.length === 0)}",
                                    "id": "u:03961d16de62",
                                    "style": {
                                      "color": "#7f7f8e",
                                      "textAlign": "left"
                                    },
                                    "tpl": "瑕疵图：",
                                    "type": "tpl",
                                    "wrapperComponent": "div"
                                  },
                                  {
                                    "dataSource": "${flawImageList}",
                                    "height": 80,
                                    "hiddenOn": "${!flawImageList || (flawImageList && flawImageList.length === 0)}",
                                    "id": "u:b3a99c620aae",
                                    "mode": "stretch",
                                    "name": "flawImageList",
                                    "type": "pictures",
                                    "width": 80
                                  },
                                  {
                                    "hiddenOn": "${!attachImages || (attachImages && attachImages.length === 0)}",
                                    "id": "u:39ef910bbbf2",
                                    "style": {
                                      "color": "#7f7f8e",
                                      "margin-top": "20px",
                                      "textAlign": "left"
                                    },
                                    "tpl": "配件图：",
                                    "type": "tpl",
                                    "wrapperComponent": "div"
                                  },
                                  {
                                    "dataSource": "${attachImages}",
                                    "height": 80,
                                    "hiddenOn": "${!attachImages || (attachImages && attachImages.length === 0)}",
                                    "id": "u:6ea6deed9910",
                                    "mode": "stretch",
                                    "name": "attachImages",
                                    "type": "pictures",
                                    "width": 80
                                  }
                                ],
                                "id": "u:d91a5a943ae9",
                                "style": {
                                  "display": "block",
                                  "flex": "1 1 auto",
                                  "flexBasis": "auto",
                                  "flexGrow": 1,
                                  "position": "static"
                                },
                                "type": "wrapper"
                              }
                            ],
                            "type": "flex"
                          },
                          {
                            "body": [
                              {
                                "confirmText": "确认审核通过？",
                                "id": "u:8e76e3d4d71a",
                                "label": "审核通过",
                                "level": "primary",
                                "onEvent": {
                                  "click": {
                                    "actions": [
                                      {
                                        "actionType": "ajax",
                                        "api": {
                                          "data": {
                                            "id": "${id}",
                                            "verifyStatus": "${1}"
                                          },
                                          "messages": {
                                            "failed": "审核通过失败",
                                            "success": "审核通过完成"
                                          },
                                          "method": "post",
                                          "url": "/pink/admin/quality/secondReason/v1/verify"
                                        },
                                        "outputVar": "verifyResult"
                                      },
                                      {
                                        "actionType": "reload",
                                        "componentId": "u:41d5ada7817c",
                                        "expression": "${event.data.verifyResult.responseStatus == 200}"
                                      }
                                    ]
                                  }
                                },
                                "type": "button"
                              },
                              {
                                "danger": true,
                                "id": "u:f24f7bfa88c5",
                                "label": "审核不通过",
                                "level": "primary",
                                "onEvent": {
                                  "click": {
                                    "actions": [
                                      {
                                        "actionType": "dialog",
                                        "dialog": {
                                          "actions": [
                                            {
                                              "actionType": "cancel",
                                              "id": "u:5a0e1f3fd49d",
                                              "label": "取消",
                                              "type": "button"
                                            },
                                            {
                                              "actionType": "confirm",
                                              "id": "u:d65d4a9eabd8",
                                              "label": "确认不通过",
                                              "level": "primary",
                                              "type": "button"
                                            }
                                          ],
                                          "body": [
                                            {
                                              "actions": [],
                                              "api": {
                                                "data": {
                                                  "id": "${id}",
                                                  "remark": "${remark}",
                                                  "verifyStatus": "${2}"
                                                },
                                                "method": "post",
                                                "url": "/pink/admin/quality/secondReason/v1/verify"
                                              },
                                              "body": [
                                                {
                                                  "fieldProps": {
                                                    "options": [
                                                      {
                                                        "label": "鞋子坏了",
                                                        "value": "鞋子坏了"
                                                      },
                                                      {
                                                        "label": "娃娃不叫",
                                                        "value": "娃娃不叫"
                                                      },
                                                      {
                                                        "label": "北京限行",
                                                        "value": "北京限行"
                                                      }
                                                    ]
                                                  },
                                                  "formItemProps": {
                                                    "rules": [
                                                      {
                                                        "required": true
                                                      }
                                                    ]
                                                  },
                                                  "id": "u:eb0144650652",
                                                  "label": "审核不通过原因",
                                                  "name": "remark",
                                                  "type": "select"
                                                }
                                              ],
                                              "id": "u:69eb7c12cbb5",
                                              "type": "form"
                                            }
                                          ],
                                          "id": "u:8c4b780fe69b",
                                          "title": "请选择审核不通过原因",
                                          "type": "dialog"
                                        }
                                      }
                                    ]
                                  }
                                },
                                "type": "button"
                              }
                            ],
                            "hiddenOn": "${verifyStatus != 0}",
                            "id": "u:0022b0b6b5aa",
                            "style": {
                              "margin-top": "12px"
                            },
                            "type": "space"
                          }
                        ],
                        "id": "u:af64464bde8e",
                        "style": {
                          "display": "block",
                          "flex": "1 1 auto",
                          "flexBasis": "auto",
                          "flexGrow": 1,
                          "position": "static"
                        },
                        "type": "wrapper"
                      }
                    ],
                    "type": "flex"
                  }
                ],
                "bordered": true,
                "hoverable": true,
                "id": "u:71f8ad86b205",
                "loading": false,
                "size": "default",
                "style": {
                  "margin-bottom": "20px",
                  "margin-left": "8px",
                  "width": "100%"
                },
                "type": "card"
              }
            ],
            "color": "${IFS(verifyStatus == 0, \"blue\", verifyStatus == 1, \"green\",verifyStatus == 2, \"red\")}",
            "id": "u:ca494146ee55",
            "placement": "start",
            "text": "${IFS(verifyStatus == 0, \"待审核\", verifyStatus == 1, \"审核通过\",verifyStatus == 2, \"审核不通过\")}",
            "type": "badgeRibbon"
          },
          "placeholder": "暂无数据",
          "source": "${contents}",
          "type": "each"
        }
      ],
      "data": {
        "rangePicker": "${['2022-07-01 00:00:00', '2024-07-01 23:59:59']}"
      },
      "id": "u:41d5ada7817c",
      "name": "dataSource",
      "type": "service"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "顶部设置搜索条件，列表区域用卡片展示多样化布局",
  "title": "卡片展示的搜索列表页",
  "type": "page"
}

==================================================

菜单项: 列表中展示复杂详情
时间: 2025-07-22T18:02:54.298360
内容长度: 29674 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "icon": "icon-export",
          "id": "u:92353b13e5be",
          "label": "导出",
          "level": "primary",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msg": "导出中，请稍等...",
                    "msgType": "info"
                  }
                },
                {
                  "actionType": "ajax",
                  "api": {
                    "data": {
                      "&": "$$",
                      "pageNum": "${1}",
                      "pageSize": "10000"
                    },
                    "method": "post",
                    "silent": true,
                    "url": "/pink/admin/quality/secondReason/v1/export"
                  },
                  "loopApi": {
                    "data": {
                      "taskId": "${taskId}"
                    },
                    "method": "get",
                    "sendOn": "${downloadStatus == 0}",
                    "silent": true,
                    "url": "/pink/admin/report/queryDownloadResult"
                  },
                  "outputVar": "exportResult"
                },
                {
                  "actionType": "url",
                  "args": {
                    "url": "${event.data.exportResult.responseData.downloadUrl}"
                  },
                  "expression": "${event.data.exportResult.responseData.downloadStatus == 1}"
                },
                {
                  "actionType": "toast",
                  "args": {
                    "msg": "导出成功，已自动下载",
                    "msgType": "success"
                  },
                  "expression": "${event.data.exportResult.responseData.downloadStatus == 1}"
                }
              ]
            }
          },
          "type": "button"
        }
      ],
      "api": {
        "method": "post",
        "trackExpression": "",
        "url": "/pink/admin/quality/secondReason/v1/list"
      },
      "formColumns": [
        {
          "endName": "createEndTime",
          "formItemProps": {
            "rules": [
              {
                "message": "请选择质检不通过时间",
                "required": true
              }
            ]
          },
          "id": "u:5d893758fddc",
          "initialValue": "${\"2022-07-01 00:00:00\"},${DATETOSTR(ENDOF(TODAY(), \"month\"), \"YYYY-MM-DD 23:59:59\")}",
          "label": "质检不通过时间",
          "name": "rangePicker",
          "startName": "createStartTime",
          "type": "rangePicker"
        },
        {
          "fieldProps": {
            "fetchDataOnSearch": false,
            "mode": "multiple",
            "options": [
              {
                "label": "待审核",
                "value": 0
              },
              {
                "label": "审核通过",
                "value": 1
              },
              {
                "label": "审核不通过",
                "value": 2
              }
            ],
            "showSearch": true
          },
          "formItemProps": {},
          "id": "u:57c5fa3e08f9",
          "label": "审核状态",
          "name": "verifyStatusList",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${contents}"
            },
            "url": "/pink/admin/repositories/list"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "repositoryName",
              "value": "repositoryCode"
            },
            "mode": "multiple"
          },
          "id": "u:c29c7f0e9fb8",
          "label": "仓位",
          "name": "warehouseCodeList",
          "type": "select"
        }
      ],
      "id": "u:caa06d0e3651",
      "label": "列表",
      "name": "chainSearchPage",
      "rowKey": "id",
      "search": {
        "labelWidth": 120
      },
      "tableAlertRender": false,
      "tableColumns": [
        {
          "body": [
            {
              "body": [
                {
                  "alignItems": "flex-start",
                  "id": "u:1d42db36ec27",
                  "items": [
                    {
                      "body": [
                        {
                          "id": "u:89294556b1c2",
                          "style": {
                            "color": "#ff0000",
                            "textAlign": "left"
                          },
                          "tpl": "${\"二级瑕疵原因：\"+secondReason}",
                          "type": "tpl",
                          "wrapperComponent": "h3"
                        },
                        {
                          "id": "u:47b1c508d6ae",
                          "items": [
                            {
                              "body": [
                                {
                                  "id": "u:d95b6f085aae",
                                  "mode": "contain",
                                  "src": "${goodsLogo || \"https://cdn.dewu.com/node-common/344e8524-9fe8-c713-75c5-6c8fb75bc4c0-1440-1446.png\"}",
                                  "type": "picture",
                                  "width": 100
                                }
                              ],
                              "id": "u:2a533c6c304c",
                              "style": {
                                "display": "block",
                                "flex": "0 0 120px",
                                "flexBasis": "120px",
                                "position": "static"
                              },
                              "type": "wrapper"
                            },
                            {
                              "body": [
                                {
                                  "bordered": false,
                                  "column": 2,
                                  "columns": [
                                    {
                                      "body": [
                                        {
                                          "blank": true,
                                          "body": "${uniqueCode|| \"-\"}",
                                          "href": "${uniqueCode ? \"https://t1-scm.shizhuang-inc.net/#/scm-pink/operate/operate-in-store-detail?id=\" + itemId : null}",
                                          "id": "u:5d5f448c1f1d",
                                          "type": "link"
                                        }
                                      ],
                                      "copyable": false,
                                      "id": "u:7e60b051a688",
                                      "label": "唯一码",
                                      "name": "uniqueCode",
                                      "type": "custom"
                                    },
                                    {
                                      "body": [
                                        {
                                          "id": "u:13244e24ef72",
                                          "style": {
                                            "textAlign": "left"
                                          },
                                          "tpl": "${DATETOSTR(createTime, \"YYYY-MM-DD HH:mm:ss\")}",
                                          "type": "tpl"
                                        }
                                      ],
                                      "fieldProps": {
                                        "options": [
                                          {
                                            "label": "全部",
                                            "value": "all"
                                          },
                                          {
                                            "label": "解决中",
                                            "value": "pending"
                                          },
                                          {
                                            "label": "已解决",
                                            "value": "resolved"
                                          }
                                        ]
                                      },
                                      "id": "u:23b5571da66f",
                                      "label": "质检不通过时间",
                                      "name": "createTime",
                                      "type": "custom"
                                    },
                                    {
                                      "fieldProps": {
                                        "options": [
                                          {
                                            "label": "全部",
                                            "value": "all"
                                          },
                                          {
                                            "label": "解决中",
                                            "value": "pending"
                                          },
                                          {
                                            "label": "已解决",
                                            "value": "resolved"
                                          }
                                        ]
                                      },
                                      "id": "u:3203649cca34",
                                      "label": "商品名称",
                                      "name": "spuName",
                                      "type": "text"
                                    },
                                    {
                                      "fieldProps": {
                                        "options": [
                                          {
                                            "label": "全部",
                                            "value": "all"
                                          },
                                          {
                                            "label": "解决中",
                                            "value": "pending"
                                          },
                                          {
                                            "label": "已解决",
                                            "value": "resolved"
                                          }
                                        ]
                                      },
                                      "id": "u:d0b57819c5a7",
                                      "label": "瑕疵原因",
                                      "name": "firstReason",
                                      "type": "text"
                                    },
                                    {
                                      "fieldProps": {
                                        "options": [
                                          {
                                            "label": "全部",
                                            "value": "all"
                                          },
                                          {
                                            "label": "解决中",
                                            "value": "pending"
                                          },
                                          {
                                            "label": "已解决",
                                            "value": "resolved"
                                          }
                                        ]
                                      },
                                      "id": "u:fd19d59e35ab",
                                      "label": "规格",
                                      "name": "spec",
                                      "type": "text"
                                    },
                                    {
                                      "fieldProps": {
                                        "options": [
                                          {
                                            "label": "全部",
                                            "value": "all"
                                          },
                                          {
                                            "label": "解决中",
                                            "value": "pending"
                                          },
                                          {
                                            "label": "已解决",
                                            "value": "resolved"
                                          }
                                        ]
                                      },
                                      "id": "u:cc05bb94934f",
                                      "label": "审核人",
                                      "name": "verifyUserName",
                                      "type": "text"
                                    },
                                    {
                                      "copyable": true,
                                      "fieldProps": {
                                        "options": [
                                          {
                                            "label": "全部",
                                            "value": "all"
                                          },
                                          {
                                            "label": "解决中",
                                            "value": "pending"
                                          },
                                          {
                                            "label": "已解决",
                                            "value": "resolved"
                                          }
                                        ]
                                      },
                                      "id": "u:05acf84fc199",
                                      "label": "货号",
                                      "name": "artNo",
                                      "type": "text"
                                    },
                                    {
                                      "body": [
                                        {
                                          "id": "u:8cf0b889b65c",
                                          "style": {
                                            "textAlign": "left"
                                          },
                                          "tpl": "${DATETOSTR(verifyTime, \"YYYY-MM-DD HH:mm:ss\")}",
                                          "type": "tpl"
                                        }
                                      ],
                                      "id": "u:a5e3d094e166",
                                      "label": "审核时间",
                                      "name": "verifyTime",
                                      "type": "custom"
                                    },
                                    {
                                      "fieldProps": {
                                        "options": [
                                          {
                                            "label": "全部",
                                            "value": "all"
                                          },
                                          {
                                            "label": "解决中",
                                            "value": "pending"
                                          },
                                          {
                                            "label": "已解决",
                                            "value": "resolved"
                                          }
                                        ]
                                      },
                                      "id": "u:15aa5e646191",
                                      "label": "审核不通过原因",
                                      "name": "remark",
                                      "span": 2,
                                      "type": "text"
                                    }
                                  ],
                                  "extra": [
                                    {
                                      "body": [],
                                      "id": "u:92dba49805ae",
                                      "type": "wrapper"
                                    }
                                  ],
                                  "id": "u:315b92acae65",
                                  "layout": "horizontal",
                                  "size": "small",
                                  "style": {
                                    "margin-top": "-40px"
                                  },
                                  "type": "descriptions"
                                },
                                {
                                  "body": [
                                    {
                                      "confirmText": "确认审核通过？",
                                      "id": "u:8e76e3d4d71a",
                                      "label": "审核通过",
                                      "level": "primary",
                                      "onEvent": {
                                        "click": {
                                          "actions": [
                                            {
                                              "actionType": "ajax",
                                              "api": {
                                                "data": {
                                                  "id": "${id}",
                                                  "verifyStatus": "${1}"
                                                },
                                                "messages": {
                                                  "failed": "审核通过失败",
                                                  "success": "审核通过完成"
                                                },
                                                "method": "post",
                                                "url": "/pink/admin/quality/secondReason/v1/verify"
                                              },
                                              "outputVar": "verifyResult"
                                            },
                                            {
                                              "actionType": "reload",
                                              "componentId": "u:caa06d0e3651",
                                              "expression": "${event.data.verifyResult.responseStatus == 200}"
                                            }
                                          ]
                                        }
                                      },
                                      "type": "button"
                                    },
                                    {
                                      "danger": true,
                                      "id": "u:f24f7bfa88c5",
                                      "label": "审核不通过",
                                      "level": "primary",
                                      "onEvent": {
                                        "click": {
                                          "actions": [
                                            {
                                              "actionType": "dialog",
                                              "dialog": {
                                                "actions": [
                                                  {
                                                    "actionType": "cancel",
                                                    "id": "u:5a0e1f3fd49d",
                                                    "label": "取消",
                                                    "type": "button"
                                                  },
                                                  {
                                                    "actionType": "confirm",
                                                    "id": "u:d65d4a9eabd8",
                                                    "label": "确认不通过",
                                                    "level": "primary",
                                                    "type": "button"
                                                  }
                                                ],
                                                "body": [
                                                  {
                                                    "actions": [],
                                                    "api": {
                                                      "data": {
                                                        "id": "${id}",
                                                        "remark": "${remark}",
                                                        "verifyStatus": "${2}"
                                                      },
                                                      "method": "post",
                                                      "url": "/pink/admin/quality/secondReason/v1/verify"
                                                    },
                                                    "body": [
                                                      {
                                                        "fieldProps": {
                                                          "options": [
                                                            {
                                                              "label": "鞋子坏了",
                                                              "value": "鞋子坏了"
                                                            },
                                                            {
                                                              "label": "娃娃不叫",
                                                              "value": "娃娃不叫"
                                                            },
                                                            {
                                                              "label": "北京限行",
                                                              "value": "北京限行"
                                                            }
                                                          ]
                                                        },
                                                        "formItemProps": {
                                                          "rules": [
                                                            {
                                                              "required": true
                                                            }
                                                          ]
                                                        },
                                                        "id": "u:eb0144650652",
                                                        "label": "审核不通过原因",
                                                        "name": "remark",
                                                        "type": "select"
                                                      }
                                                    ],
                                                    "id": "u:69eb7c12cbb5",
                                                    "type": "form"
                                                  }
                                                ],
                                                "id": "u:8c4b780fe69b",
                                                "title": "请选择审核不通过原因",
                                                "type": "dialog"
                                              }
                                            }
                                          ]
                                        }
                                      },
                                      "type": "button"
                                    }
                                  ],
                                  "hiddenOn": "${verifyStatus != 0}",
                                  "id": "u:0022b0b6b5aa",
                                  "style": {
                                    "margin-top": "12px"
                                  },
                                  "type": "space"
                                }
                              ],
                              "id": "u:ffc9abde49d8",
                              "style": {
                                "display": "block",
                                "flex": "0 0 620px",
                                "flexBasis": "620px",
                                "position": "static"
                              },
                              "type": "wrapper"
                            },
                            {
                              "body": [
                                {
                                  "hiddenOn": "${!flawImageList || (flawImageList && flawImageList.length === 0)}",
                                  "id": "u:03961d16de62",
                                  "style": {
                                    "color": "#7f7f8e",
                                    "textAlign": "left"
                                  },
                                  "tpl": "瑕疵图：",
                                  "type": "tpl",
                                  "wrapperComponent": "div"
                                },
                                {
                                  "dataSource": "${flawImageList}",
                                  "height": 80,
                                  "hiddenOn": "${!flawImageList || (flawImageList && flawImageList.length === 0)}",
                                  "id": "u:b3a99c620aae",
                                  "mode": "stretch",
                                  "name": "flawImageList",
                                  "type": "pictures",
                                  "width": 80
                                },
                                {
                                  "hiddenOn": "${!attachImages || (attachImages && attachImages.length === 0)}",
                                  "id": "u:39ef910bbbf2",
                                  "style": {
                                    "color": "#7f7f8e",
                                    "margin-top": "20px",
                                    "textAlign": "left"
                                  },
                                  "tpl": "配件图：",
                                  "type": "tpl",
                                  "wrapperComponent": "div"
                                },
                                {
                                  "dataSource": "${attachImages}",
                                  "height": 80,
                                  "hiddenOn": "${!attachImages || (attachImages && attachImages.length === 0)}",
                                  "id": "u:6ea6deed9910",
                                  "mode": "stretch",
                                  "name": "attachImages",
                                  "type": "pictures",
                                  "width": 80
                                }
                              ],
                              "id": "u:d91a5a943ae9",
                              "style": {
                                "display": "block",
                                "flex": "1 1 auto",
                                "flexBasis": "auto",
                                "flexGrow": 1,
                                "position": "static"
                              },
                              "type": "wrapper"
                            }
                          ],
                          "type": "flex"
                        }
                      ],
                      "id": "u:af64464bde8e",
                      "style": {
                        "display": "block",
                        "flex": "1 1 auto",
                        "flexBasis": "auto",
                        "flexGrow": 1,
                        "position": "static"
                      },
                      "type": "wrapper"
                    }
                  ],
                  "type": "flex"
                }
              ],
              "color": "${IFS(verifyStatus == 0, \"blue\", verifyStatus == 1, \"green\",verifyStatus == 2, \"red\")}",
              "id": "u:fd07f570f0e2",
              "placement": "end",
              "text": "${IFS(verifyStatus == 0, \"待审核\", verifyStatus == 1, \"审核通过\",verifyStatus == 2, \"审核不通过\")}",
              "type": "badgeRibbon"
            }
          ],
          "id": "u:a6ecc63a341a",
          "name": "name",
          "type": "custom"
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "通过自定义表格列定制多种详情展示方式",
  "title": "列表中展示复杂详情",
  "type": "page",
  "wrapperCustomStyle": {
    ".wizard-table-header": {
      "display": "none"
    }
  }
}

==================================================

菜单项: 带Tab切换的列表页
时间: 2025-07-22T18:03:26.240823
内容长度: 59134 字符
------------------------------
{
  "body": [
    {
      "id": "u:7ee583fdcc47",
      "items": [
        {
          "body": [
            {
              "actions": [
                {
                  "actionType": "dialog",
                  "dialog": {
                    "actions": [
                      {
                        "actionType": "cancel",
                        "id": "u:541d948cf926",
                        "label": "取消",
                        "type": "button"
                      },
                      {
                        "actionType": "confirm",
                        "id": "u:1c971cc36e90",
                        "label": "确认",
                        "level": "primary",
                        "reload": "chainSearchPage",
                        "type": "button"
                      }
                    ],
                    "body": {
                      "actions": [],
                      "api": {
                        "messages": {
                          "success": "新增配置项成功"
                        },
                        "url": "/qms/lab/v1/createConfig"
                      },
                      "body": [
                        {
                          "formItemProps": {
                            "rules": [
                              {
                                "message": "请输入检测项名称",
                                "required": true
                              }
                            ]
                          },
                          "id": "u:82674ea43861",
                          "label": "检测项",
                          "name": "detectName",
                          "type": "text"
                        },
                        {
                          "id": "u:98ce68a346af",
                          "label": "适用标准",
                          "name": "applyNorm",
                          "type": "text"
                        },
                        {
                          "id": "u:4b04301637b8",
                          "label": "描述",
                          "name": "detectDescribe",
                          "type": "textarea"
                        }
                      ],
                      "id": "u:b6bf83a60d89",
                      "type": "form",
                      "wrapperCol": {
                        "style": {
                          "flex": 0.9
                        }
                      }
                    },
                    "id": "u:ccf8195e1264",
                    "title": "新增",
                    "type": "dialog",
                    "width": 700
                  },
                  "id": "u:1ac7f07c86ac",
                  "label": "新增",
                  "level": "primary",
                  "type": "button"
                },
                {
                  "actionType": "import",
                  "api": {
                    "data": {
                      "bizType": "4"
                    },
                    "method": "post",
                    "url": "/qms/excel/import"
                  },
                  "fileName": "检测项导入模板",
                  "fileNameLabel": "导入模版",
                  "fileUrl": "https://h5static.dewucdn.com/node-common/01d5431c-90f1-217b-4e83-af6f9556d84a.xlsx",
                  "id": "u:62b113ceece6",
                  "label": "导入",
                  "level": "primary",
                  "type": "button"
                }
              ],
              "api": {
                "method": "post",
                "url": "/qms/lab/v1/queryConfigList"
              },
              "formColumns": [
                {
                  "api": {
                    "adaptor": "return {\n  status: 200,\n  data: response.data?.items || []\n};       ",
                    "method": "post",
                    "requestAdaptor": "api.data.pageSize = 100;\napi.data.detectName = api.data.keyWords;\ndelete api.data.keyWords;\nreturn api;",
                    "url": "/qms/lab/v1/queryConfigList"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "detectName",
                      "value": "detectName"
                    }
                  },
                  "id": "u:d9155a03bd74",
                  "label": "检测项",
                  "name": "detectName",
                  "type": "select"
                }
              ],
              "id": "u:a3963992197d",
              "name": "chainSearchPage",
              "rowKey": "id",
              "tableColumns": [
                {
                  "copyable": true,
                  "fixed": "left",
                  "id": "u:b4a9bb715634",
                  "label": "检测项ID",
                  "name": "id",
                  "type": "text"
                },
                {
                  "id": "u:976ec902828b",
                  "label": "检测项",
                  "name": "detectName",
                  "type": "text"
                },
                {
                  "id": "u:5b0cfc063328",
                  "label": "适用标准",
                  "name": "applyNorm",
                  "type": "text"
                },
                {
                  "id": "u:c938fee8cc5a",
                  "label": "描述",
                  "name": "detectDescribe",
                  "type": "text"
                },
                {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:66cb8b0eb628",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "submit",
                            "id": "u:b67362d13d11",
                            "label": "确认",
                            "level": "primary",
                            "reload": "chainSearchPage",
                            "type": "button"
                          }
                        ],
                        "body": {
                          "actions": [],
                          "api": {
                            "url": "/qms/lab/v1/editConfig"
                          },
                          "body": [
                            {
                              "formItemProps": {
                                "rules": [
                                  {
                                    "message": "请输入检测项名称",
                                    "required": true
                                  }
                                ]
                              },
                              "id": "u:6cba31ffa1d4",
                              "label": "检测项",
                              "name": "detectName",
                              "proFieldProps": {
                                "mode": "read"
                              },
                              "type": "text"
                            },
                            {
                              "id": "u:05416775c491",
                              "label": "适用标准",
                              "name": "applyNorm",
                              "type": "text"
                            },
                            {
                              "id": "u:bf39776a3682",
                              "label": "描述",
                              "name": "detectDescribe",
                              "type": "textarea"
                            },
                            {
                              "formItemProps": {
                                "hidden": true
                              },
                              "id": "u:16e760c0d792",
                              "label": "id",
                              "name": "id",
                              "type": "text"
                            }
                          ],
                          "id": "u:ac0df68f7cf5",
                          "type": "form",
                          "wrapperCol": {
                            "style": {
                              "flex": 0.9
                            }
                          }
                        },
                        "id": "u:f64322a45ace",
                        "title": "编辑",
                        "type": "dialog",
                        "width": 700
                      },
                      "id": "u:33a5ab78d907",
                      "label": "编辑",
                      "level": "link",
                      "type": "button"
                    },
                    {
                      "actionType": "ajax",
                      "api": {
                        "data": {
                          "id": "${id}"
                        },
                        "messages": {
                          "success": "删除成功"
                        },
                        "method": "post",
                        "url": "/qms/lab/v1/deleteConfig"
                      },
                      "confirmText": "是否确认删除",
                      "danger": true,
                      "id": "u:19e9f24a8266",
                      "label": "删除",
                      "type": "button"
                    }
                  ],
                  "fixed": "right",
                  "id": "u:04f3880a8a88",
                  "label": "操作",
                  "type": "operation",
                  "width": 150
                }
              ],
              "type": "chainSearchPage"
            }
          ],
          "id": "u:b2c02e7aa577",
          "key": "1",
          "label": "简单列表页"
        },
        {
          "body": [
            {
              "actions": [
                {
                  "actionType": "dialog",
                  "dialog": {
                    "actions": [
                      {
                        "actionType": "cancel",
                        "id": "u:8f097adc80fe",
                        "label": "取消",
                        "type": "button"
                      },
                      {
                        "actionType": "submit",
                        "id": "u:b382051e7ac6",
                        "label": "确认",
                        "level": "primary",
                        "reload": "chainSearchPage",
                        "type": "button"
                      }
                    ],
                    "body": [
                      {
                        "actions": [],
                        "api": {
                          "data": {
                            "&": "$$"
                          },
                          "url": "/tms/admin/fulfillmentModeConfig/create"
                        },
                        "body": [
                          {
                            "fieldProps": {
                              "options": [
                                {
                                  "label": "仓维度",
                                  "value": 0
                                },
                                {
                                  "label": "商家维度",
                                  "value": 2
                                }
                              ]
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "id": "u:c61bb5ac6cec",
                            "label": "配置维度",
                            "name": "deliveryMode",
                            "type": "radio"
                          },
                          {
                            "api": {
                              "method": "get",
                              "url": "/tms/admin/enum/repositories"
                            },
                            "fieldProps": {
                              "fetchDataOnSearch": false,
                              "fieldNames": {
                                "label": "name",
                                "value": "code"
                              },
                              "labelInValue": true
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "hiddenOn": "${deliveryMode != 0}",
                            "id": "u:23a153df57d5",
                            "label": "始发仓库",
                            "labelName": "originWarehouseName",
                            "name": "originWarehouse",
                            "type": "select",
                            "valueName": "originWarehouseCode"
                          },
                          {
                            "api": {
                              "adaptor": "return {status: 200,data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))};",
                              "method": "get",
                              "requestAdaptor": "const data = api.data || {};\n  data.name = data?.keyWords || ''; \n  api.data = {...data} \n return api;",
                              "sendOn": "${keyWords}",
                              "url": "/merchant/merchant/getMerchantIdByNameEs"
                            },
                            "fieldProps": {
                              "fetchDataOnSearch": true,
                              "fieldNames": {
                                "label": "name",
                                "value": "merchantId"
                              },
                              "placeholder": "请输入后查询"
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "hiddenOn": "${deliveryMode != 2}",
                            "id": "u:35a2ee435901",
                            "label": "直发商家",
                            "name": "merchantId",
                            "type": "select"
                          },
                          {
                            "api": {
                              "adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}",
                              "data": {
                                "lang": "zh",
                                "maxDeep": 3
                              },
                              "method": "post",
                              "url": "/tms/admin/address/queryAddressTreeByDeep"
                            },
                            "fieldProps": {
                              "fieldNames": {
                                "label": "name",
                                "value": "code"
                              }
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "hiddenOn": "${deliveryMode != 2}",
                            "id": "u:5fb959eb1ca0",
                            "label": "始发城市",
                            "linkage": [
                              {
                                "event": "onChange",
                                "setValues": {
                                  "originCityCode": "${data[1].code}",
                                  "originCityName": "${data[1].name}"
                                }
                              }
                            ],
                            "name": "originCity",
                            "type": "cascader"
                          },
                          {
                            "formItemProps": {
                              "hidden": true
                            },
                            "hiddenOn": "${deliveryMode!= 2}",
                            "id": "u:d7484fc82b40",
                            "label": "城市Code",
                            "name": "originCityCode",
                            "type": "text"
                          },
                          {
                            "formItemProps": {
                              "hidden": true
                            },
                            "hiddenOn": "${deliveryMode !=2}",
                            "id": "u:e4e38a956d8b",
                            "label": "城市名称",
                            "name": "originCityName",
                            "type": "text"
                          },
                          {
                            "api": {
                              "adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }",
                              "data": {
                                "enumTypes": "transportFulfillModeEnum"
                              },
                              "method": "post",
                              "url": "/tms/admin/enum/listEnums"
                            },
                            "id": "u:8dd726d9f0ab",
                            "label": "运输履约模式",
                            "linkage": [
                              {
                                "event": "onChange",
                                "resetKeys": [
                                  "customerCodeId"
                                ],
                                "setValues": {
                                  "customerCodeId": null
                                }
                              }
                            ],
                            "name": "fulfillmentMode",
                            "type": "select"
                          },
                          {
                            "api": {
                              "url": "/tms/admin/enum/getLogistics"
                            },
                            "fieldProps": {
                              "fetchDataOnSearch": false,
                              "fieldNames": {
                                "label": "name",
                                "value": "code"
                              },
                              "showSearch": true
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "id": "u:3101383b3836",
                            "label": "承运商",
                            "linkage": [
                              {
                                "event": "onChange",
                                "resetKeys": [
                                  "logisticsProductCode",
                                  "customerCodeId"
                                ],
                                "setValues": {
                                  "customerCodeId": null,
                                  "logisticsProductCode": null
                                }
                              }
                            ],
                            "name": "logisticsCode",
                            "type": "select"
                          },
                          {
                            "api": {
                              "data": {
                                "logisticsName": "${logisticsCode}"
                              },
                              "sendOn": "${logisticsCode}",
                              "url": "/tms/admin/enum/getLogisticsProduct"
                            },
                            "fieldProps": {
                              "fetchDataOnSearch": false,
                              "fieldNames": {
                                "label": "name",
                                "value": "code"
                              },
                              "showSearch": true
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "id": "u:ad4bb5a73494",
                            "label": "运输产品",
                            "linkage": [
                              {
                                "event": "onChange",
                                "resetKeys": [
                                  "customerCodeId"
                                ],
                                "setValues": {
                                  "customerCodeId": null
                                }
                              }
                            ],
                            "name": "logisticsProductCode",
                            "type": "select"
                          },
                          {
                            "api": {
                              "adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }",
                              "data": {
                                "logisticsCode": "${logisticsCode}",
                                "logisticsProductCode": "${logisticsProductCode}",
                                "transportFulfillmentMode": "${fulfillmentMode}"
                              },
                              "method": "post",
                              "sendOn": "fulfillmentMode && logisticsProductCode",
                              "url": "/tms/admin/customerCode/getCustomerCodesV2"
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"
                                }
                              ]
                            },
                            "id": "u:b5a2405c516f",
                            "label": "月结卡号",
                            "name": "customerCodeId",
                            "params": {
                              "t": "${Date.now()}"
                            },
                            "type": "select"
                          }
                        ],
                        "id": "u:6bc905badd74",
                        "type": "form"
                      }
                    ],
                    "id": "u:462f993511f3",
                    "title": "新增",
                    "type": "dialog",
                    "width": 700
                  },
                  "id": "u:3a46389b7e47",
                  "label": "新建",
                  "level": "primary",
                  "type": "button"
                },
                {
                  "fileName": "导入模板",
                  "fileNameLabel": "模板下载",
                  "fileUrl": "https://cdn.poizon.com/node-common/3a2d7339-93f6-74ae-6450-d15753638127.xlsx",
                  "id": "u:9e003c343b6a",
                  "importMod": "importTransportFulfillMode",
                  "label": "导入",
                  "type": "lmsImport"
                },
                {
                  "id": "u:99e4688cf8ec",
                  "importMod": "importTransportFulfillMode",
                  "label": "导入结果查询",
                  "type": "lmsImportResult"
                },
                {
                  "actionType": "export",
                  "api": {
                    "data": {
                      "exportMod": "TRANSPORT_FULFILLMENT_MODE_CONFIG"
                    },
                    "method": "post",
                    "url": "/lmsnew/reportCenter/report/v1/export"
                  },
                  "bizType": "lmsnew",
                  "id": "u:61bfcbed8fb1",
                  "label": "导出",
                  "level": "primary",
                  "type": "button"
                }
              ],
              "api": {
                "adaptor": "return {\n  'status': 200,\n  'msg': '请求成功',\n  'data': {\n    'rows': response.data.list,\n    'count': response.data.total\n  }\n}",
                "method": "post",
                "requestAdaptor": "const { originWarehouseCode, merchantId, fulfillmentMode, pageNum, pageSize, merchantIdV2 } = api.data\napi.data = {\n  pageNum, pageSize,\n  originWarehouseCodes: originWarehouseCode ? [originWarehouseCode] : undefined,\n  merchantIds: merchantIdV2 || merchantId ? [merchantIdV2 || merchantId] : undefined,\n  fulfillmentModes: fulfillmentMode ? [fulfillmentMode] : undefined\n}\nreturn api",
                "url": "/tms/admin/fulfillmentModeConfig/page"
              },
              "formColumns": [
                {
                  "api": {
                    "method": "get",
                    "url": "/tms/admin/enum/repositories"
                  },
                  "fieldProps": {
                    "fetchDataOnSearch": false,
                    "fieldNames": {
                      "label": "name",
                      "value": "code"
                    }
                  },
                  "id": "u:787ebe451b98",
                  "label": "始发仓库",
                  "name": "originWarehouseCode",
                  "type": "select"
                },
                {
                  "api": {
                    "adaptor": "return {\n  status: 200,\n  data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))\n};",
                    "data": {
                      "name": "${keyWords}"
                    },
                    "method": "get",
                    "sendOn": "${keyWords}",
                    "url": "/merchant/merchant/getMerchantIdByNameEs"
                  },
                  "fieldProps": {
                    "fetchDataOnSearch": true,
                    "fieldNames": {
                      "label": "name",
                      "value": "merchantId"
                    },
                    "placeholder": "请输入后查询"
                  },
                  "id": "u:ede536f16a2b",
                  "label": "直发商家",
                  "name": "merchantId",
                  "type": "select"
                },
                {
                  "fieldProps": {
                    "fetchDataOnSearch": true,
                    "placeholder": "请输入后查询"
                  },
                  "id": "u:cade04fc4f1b",
                  "label": "直发商家ID",
                  "name": "merchantIdV2",
                  "type": "text"
                },
                {
                  "api": {
                    "adaptor": "const mapData = response.mapData || {}\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }",
                    "data": {
                      "enumTypes": "transportFulfillModeEnum"
                    },
                    "method": "post",
                    "url": "/tms/admin/enum/listEnums"
                  },
                  "id": "u:e3dd1d6b1ac2",
                  "label": "运输履约模式",
                  "name": "fulfillmentMode",
                  "type": "select"
                }
              ],
              "id": "u:c61a67ebea74",
              "name": "chainSearchPage",
              "revalidateOnFocus": true,
              "rowKey": "id",
              "search": {
                "defaultCollapsed": false,
                "labelWidth": 110
              },
              "tableColumns": [
                {
                  "id": "u:c44ce52946a2",
                  "label": "始发仓库",
                  "name": "originWarehouseName",
                  "type": "text",
                  "width": 160
                },
                {
                  "body": [
                    {
                      "body": [
                        {
                          "id": "u:c880a47168b5",
                          "tpl": "${\"名称：\"+( sellerUserName || \"-\")}",
                          "type": "tpl"
                        }
                      ],
                      "id": "u:452480073e84",
                      "type": "container"
                    },
                    {
                      "body": [
                        {
                          "id": "u:220fd3928402",
                          "tpl": "${\"ID：\"+ (sellerUserId || \"-\")}",
                          "type": "tpl"
                        }
                      ],
                      "id": "u:25ddebd84582",
                      "type": "container"
                    }
                  ],
                  "id": "u:bba80cf7ad52",
                  "label": "直发商家",
                  "name": "sellerUserName",
                  "type": "custom",
                  "width": 160
                },
                {
                  "id": "u:fce592c0a4f7",
                  "label": "始发城市",
                  "name": "originCityName",
                  "type": "text"
                },
                {
                  "id": "u:b0790cc787d6",
                  "label": "运输履约模式",
                  "name": "fulfillmentModeName",
                  "type": "text"
                },
                {
                  "id": "u:dd72e6c1d157",
                  "label": "承运商",
                  "name": "logisticsName",
                  "type": "text"
                },
                {
                  "id": "u:71179d5c4f3e",
                  "label": "运输产品",
                  "name": "logisticsProductName",
                  "type": "text"
                },
                {
                  "id": "u:1fb040dd3a05",
                  "label": "月结卡号",
                  "name": "customerCode",
                  "type": "text"
                },
                {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:675998f5b366",
                            "label": "取消",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "body": [
                              {
                                "id": "u:44bb23bdcf86",
                                "label": "配置维度",
                                "name": "deliveryModeDesc",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "hiddenOn": "${deliveryMode != 0}",
                                "id": "u:44bb23bdcf86",
                                "label": "始发仓库",
                                "name": "originWarehouseName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "hiddenOn": "${deliveryMode != 2}",
                                "id": "u:44bb23bdcf86",
                                "label": "直发商家",
                                "name": "sellerUserName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "hiddenOn": "${deliveryMode != 2}",
                                "id": "u:44bb23bdcf86",
                                "label": "始发城市",
                                "name": "originCityName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "id": "u:44bb23bdcf86",
                                "label": "运输履约模式",
                                "name": "fulfillmentModeName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "id": "u:44bb23bdcf86",
                                "label": "承运商",
                                "name": "logisticsName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "id": "u:44bb23bdcf86",
                                "label": "运输产品",
                                "name": "logisticsProductName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "id": "u:44bb23bdcf86",
                                "label": "月结卡号",
                                "name": "customerCode",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              }
                            ],
                            "id": "u:57f639732290",
                            "initApi": {
                              "data": {
                                "id": "${id}"
                              },
                              "method": "get",
                              "url": "/tms/admin/fulfillmentModeConfig/detail"
                            },
                            "title": "表单",
                            "type": "form"
                          }
                        ],
                        "id": "u:66eda46202cb",
                        "title": "查看",
                        "type": "dialog",
                        "width": 600
                      },
                      "id": "u:8eb56843f0ae",
                      "label": "查看",
                      "level": "link",
                      "type": "button"
                    },
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:488f3f745723",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "confirm",
                            "id": "u:5a3ab578c259",
                            "label": "确认",
                            "level": "primary",
                            "reload": "chainSearchPage",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "api": {
                              "data": {
                                "&": "$$"
                              },
                              "url": "/tms/admin/fulfillmentModeConfig/edit"
                            },
                            "body": [
                              {
                                "formItemProps": {
                                  "hidden": true
                                },
                                "id": "u:d97d9fb9d7ac",
                                "label": "ID",
                                "name": "id",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "fieldProps": {
                                  "disabled": false,
                                  "options": [
                                    {
                                      "label": "仓维度",
                                      "value": 0
                                    },
                                    {
                                      "label": "商家维度",
                                      "value": 2
                                    }
                                  ]
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:2885354c4f38",
                                "label": "配置维度",
                                "name": "deliveryMode",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "radio"
                              },
                              {
                                "api": {
                                  "method": "get",
                                  "url": "/tms/admin/enum/repositories"
                                },
                                "fieldProps": {
                                  "disabled": false,
                                  "fetchDataOnSearch": false,
                                  "fieldNames": {
                                    "label": "name",
                                    "value": "code"
                                  },
                                  "labelInValue": true
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": true
                                    }
                                  ]
                                },
                                "hiddenOn": "${deliveryMode != 0}",
                                "id": "u:9f5910067d23",
                                "label": "始发仓库",
                                "labelName": "originWarehouseName",
                                "name": "originWarehouse",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "select",
                                "valueName": "originWarehouseCode"
                              },
                              {
                                "fieldProps": {
                                  "disabled": false,
                                  "placeholder": "请输入后查询"
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": true
                                    }
                                  ]
                                },
                                "hiddenOn": "${deliveryMode != 2}",
                                "id": "u:286124b7250c",
                                "label": "直发商家",
                                "name": "sellerUserName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "select"
                              },
                              {
                                "api": {
                                  "adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}",
                                  "data": {
                                    "lang": "zh",
                                    "maxDeep": 3
                                  },
                                  "method": "post",
                                  "url": "/tms/admin/address/queryAddressTreeByDeep"
                                },
                                "fieldProps": {
                                  "disabled": false,
                                  "fieldNames": {
                                    "label": "name",
                                    "value": "code"
                                  }
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": true
                                    }
                                  ]
                                },
                                "hiddenOn": "${deliveryMode != 2}",
                                "id": "u:6a4b66400afc",
                                "label": "始发城市",
                                "linkage": [
                                  {
                                    "event": "onChange",
                                    "setValues": {
                                      "originCityName": "${data[1].name}"
                                    }
                                  }
                                ],
                                "name": "originCity",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "cascader"
                              },
                              {
                                "formItemProps": {
                                  "hidden": true
                                },
                                "hiddenOn": "${deliveryMode!= 2}",
                                "id": "u:d7484fc82b40",
                                "label": "城市Code",
                                "name": "originCityCode",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "formItemProps": {
                                  "hidden": true
                                },
                                "hiddenOn": "${deliveryMode !=2}",
                                "id": "u:e4e38a956d8b",
                                "label": "城市名称",
                                "name": "originCityName",
                                "proFieldProps": {
                                  "mode": "read"
                                },
                                "type": "text"
                              },
                              {
                                "api": {
                                  "adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }",
                                  "data": {
                                    "enumTypes": "transportFulfillModeEnum"
                                  },
                                  "method": "post",
                                  "url": "/tms/admin/enum/listEnums"
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:85e6986c3990",
                                "label": "运输履约模式",
                                "linkage": [
                                  {
                                    "event": "onChange",
                                    "resetKeys": [
                                      "customerCodeId"
                                    ],
                                    "setValues": {
                                      "customerCodeId": null
                                    }
                                  }
                                ],
                                "name": "fulfillmentMode",
                                "type": "select"
                              },
                              {
                                "api": {
                                  "url": "/tms/admin/enum/getLogistics"
                                },
                                "fieldProps": {
                                  "fetchDataOnSearch": false,
                                  "fieldNames": {
                                    "label": "name",
                                    "value": "code"
                                  },
                                  "showSearch": true
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:bb64db5388be",
                                "label": "承运商",
                                "linkage": [
                                  {
                                    "event": "onChange",
                                    "resetKeys": [
                                      "logisticsProductCode",
                                      "customerCodeId"
                                    ],
                                    "setValues": {
                                      "customerCodeId": null,
                                      "logisticsProductCode": null
                                    }
                                  }
                                ],
                                "name": "logisticsCode",
                                "type": "select"
                              },
                              {
                                "api": {
                                  "data": {
                                    "logisticsName": "${logisticsCode}"
                                  },
                                  "sendOn": "${logisticsCode}",
                                  "url": "/tms/admin/enum/getLogisticsProduct"
                                },
                                "fieldProps": {
                                  "fetchDataOnSearch": false,
                                  "fieldNames": {
                                    "label": "name",
                                    "value": "code"
                                  },
                                  "showSearch": true
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": true
                                    }
                                  ]
                                },
                                "id": "u:f714a92015b9",
                                "label": "运输产品",
                                "linkage": [
                                  {
                                    "event": "onChange",
                                    "resetKeys": [
                                      "customerCodeId"
                                    ],
                                    "setValues": {
                                      "customerCodeId": null
                                    }
                                  }
                                ],
                                "name": "logisticsProductCode",
                                "type": "select"
                              },
                              {
                                "api": {
                                  "adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }",
                                  "data": {
                                    "logisticsCode": "${logisticsCode}",
                                    "logisticsProductCode": "${logisticsProductCode}",
                                    "transportFulfillmentMode": "${fulfillmentMode}"
                                  },
                                  "method": "post",
                                  "sendOn": "fulfillmentMode && logisticsProductCode",
                                  "url": "/tms/admin/customerCode/getCustomerCodesV2"
                                },
                                "formItemProps": {
                                  "rules": [
                                    {
                                      "message": "请选择",
                                      "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"
                                    }
                                  ]
                                },
                                "id": "u:2a69a4e4366e",
                                "label": "月结卡号",
                                "name": "customerCodeId",
                                "params": {
                                  "t": "${Date.now()}"
                                },
                                "type": "select"
                              }
                            ],
                            "id": "u:b7373d316c7c",
                            "initApi": {
                              "adaptor": "const {originWarehouseCode, originWarehouseName, originProvinceCode,originCityCode, deliveryMode, customerCodeId, ...rest}=response.data;\nconst data = { deliveryMode, customerCodeId: customerCodeId === 0 ? null : customerCodeId, ...rest }\nif (deliveryMode === 0) {\n  data.originWarehouse = {label: originWarehouseName, value: originWarehouseCode}\n}\nif (deliveryMode === 2) {\n  data.originCity = [originProvinceCode, originCityCode]\n}\nreturn {status: 0, data: data }",
                              "data": {
                                "id": "${id}"
                              },
                              "url": "/tms/admin/fulfillmentModeConfig/detail"
                            },
                            "type": "form"
                          }
                        ],
                        "id": "u:8617c397bda5",
                        "title": "编辑",
                        "type": "dialog",
                        "width": 700
                      },
                      "id": "u:b929f26710a2",
                      "label": "编辑",
                      "level": "link",
                      "type": "button"
                    },
                    {
                      "actionType": "ajax",
                      "api": {
                        "data": {
                          "id": "${id}"
                        },
                        "messages": {
                          "failed": "删除失败",
                          "success": "删除成功"
                        },
                        "method": "post",
                        "url": "/tms/admin/fulfillmentModeConfig/delete"
                      },
                      "confirmText": "是否确认删除",
                      "danger": true,
                      "id": "u:4a6bb394b3a8",
                      "label": "删除",
                      "type": "button"
                    },
                    {
                      "id": "u:aae9dfe8aa96",
                      "label": "操作记录",
                      "onEvent": {
                        "click": {
                          "actions": [
                            {
                              "actionType": "dialog",
                              "dialog": {
                                "actions": [
                                  {
                                    "actionType": "cancel",
                                    "id": "u:5b4ca903f79f",
                                    "label": "取消",
                                    "type": "button"
                                  },
                                  {
                                    "actionType": "confirm",
                                    "id": "u:4256391b6fab",
                                    "label": "确认",
                                    "level": "primary",
                                    "type": "button"
                                  }
                                ],
                                "body": [
                                  {
                                    "actions": [],
                                    "api": {
                                      "data": {
                                        "id": "${id || 148}",
                                        "tableName": "des_transport_fulfillment_mode_config"
                                      },
                                      "method": "post",
                                      "url": "/tms/admin/log/getLogList"
                                    },
                                    "formColumns": [],
                                    "id": "u:6d121dbf2ced",
                                    "label": "列表",
                                    "name": "chainSearchPage",
                                    "rowKey": "id",
                                    "search": false,
                                    "tableAlertRender": false,
                                    "tableColumns": [
                                      {
                                        "id": "u:af39355bb3e6",
                                        "label": "操作人",
                                        "name": "name",
                                        "type": "text",
                                        "width": 60
                                      },
                                      {
                                        "id": "u:0b7a8e69e9b2",
                                        "label": "操作时间",
                                        "name": "modifyTime",
                                        "type": "text",
                                        "width": 80
                                      },
                                      {
                                        "id": "u:e347e7c72d1d",
                                        "label": "操作类型",
                                        "name": "operationName",
                                        "type": "text",
                                        "width": 80
                                      },
                                      {
                                        "id": "u:84501b7ff89d",
                                        "label": "操作内容",
                                        "name": "operationContent",
                                        "type": "text"
                                      }
                                    ],
                                    "type": "chainSearchPage"
                                  }
                                ],
                                "id": "u:8eda1238e1de",
                                "title": "查看操作日志",
                                "type": "dialog",
                                "width": 900
                              }
                            }
                          ]
                        }
                      },
                      "type": "button"
                    }
                  ],
                  "fixed": "right",
                  "id": "u:dd06d7b208ec",
                  "label": "操作",
                  "type": "operation",
                  "width": 200
                }
              ],
              "type": "chainSearchPage"
            }
          ],
          "id": "u:c2b3b4a34c37",
          "key": "2",
          "label": "常规列表页"
        }
      ],
      "tabPosition": "top",
      "tabsMode": "line",
      "type": "tabs"
    }
  ],
  "id": "u:1439ae43043a",
  "title": "带Tab切换的列表页",
  "type": "page"
}

==================================================

菜单项: 常用下载中心列表页
时间: 2025-07-22T18:03:51.139044
内容长度: 3541 字符
------------------------------
{
  "body": [
    {
      "api": {
        "data": {
          "appCode": "scp-cost-interfaces"
        },
        "method": "post",
        "requestAdaptor": "\n        if(api.data.rangeTime) {\n          api.data.startTime = api.data.rangeTime[0];\n          api.data.endTime = api.data.rangeTime[1];\n        }\n        delete api.data.rangeTime;\n        return api;",
        "url": "/lmsnew/reportCenter/report/v1/downloadList"
      },
      "formColumns": [
        {
          "id": "u:3c3bc1f093f0",
          "initialValue": "-3days,today",
          "label": "时间范围",
          "name": "rangeTime",
          "type": "rangePicker"
        }
      ],
      "id": "u:bb52b76cba2e",
      "name": "chainSearchPage",
      "rowKey": "taskId",
      "search": {
        "span": 8
      },
      "tableColumns": [
        {
          "fixed": "left",
          "id": "u:b22b59a60e7c",
          "label": "模块名称",
          "name": "exportModName",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:7b348f0fa413",
          "label": "文件名称",
          "name": "fileName",
          "type": "text"
        },
        {
          "id": "u:375db104ffdc",
          "label": "任务状态",
          "name": "exportStatusDesc",
          "type": "text",
          "width": 80
        },
        {
          "fieldProps": {
            "format": "YYYY-MM-DD HH:mm:ss"
          },
          "id": "u:8f6e8899b359",
          "label": "创建时间",
          "name": "createTime",
          "type": "date"
        },
        {
          "id": "u:2f13584ea73d",
          "label": "报表生成时间",
          "name": "generatedTime",
          "type": "text"
        },
        {
          "id": "u:86f1768ce1a3",
          "label": "过期时间",
          "name": "expireDesc",
          "type": "text"
        },
        {
          "id": "u:61f2de311ec8",
          "label": "结果明细",
          "name": "exportMsg",
          "type": "text",
          "width": 80
        },
        {
          "actions": [
            {
              "actionType": "ajax",
              "api": {
                "adaptor": "\nwindow.open(response.data);\nreturn {\n  status: 200,\n  msg: '下载文件成功',\n  data: response.data\n};\n              ",
                "data": {
                  "taskId": "${taskId}"
                },
                "method": "post",
                "url": "/lmsnew/reportCenter/report/v1/download"
              },
              "hiddenOn": "${expired || exportStatus == 0}",
              "id": "u:d2dd43979328",
              "label": "下载",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "adaptor": "return {\n  status: 200,\n  msg: '重新导出完成',\n  data: response.data\n};\n              ",
                "data": {
                  "taskId": "${taskId}"
                },
                "method": "post",
                "url": "/lmsnew/reportCenter/report/v1/retry"
              },
              "hiddenOn": "${!expired}",
              "id": "u:bd4df7050abe",
              "label": "重新导出",
              "level": "link",
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:240351be7424",
          "label": "操作",
          "type": "operation",
          "width": 150
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:ae52653333e0",
  "subTitle": "数据格式化、下拉枚举查询、新增、编辑、删除、导入",
  "title": "常用下载中心列表页",
  "type": "page"
}

==================================================

菜单项: 表单中使用编辑列表
时间: 2025-07-22T18:04:16.548570
内容长度: 49408 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "dialog",
          "dialog": {
            "actions": [
              {
                "actionType": "cancel",
                "id": "u:3faef695dcf3",
                "label": "取消",
                "type": "button"
              },
              {
                "actionType": "submit",
                "id": "u:562071a05820",
                "label": "确认",
                "level": "primary",
                "reload": "chainSearchPage",
                "type": "button"
              }
            ],
            "body": {
              "actions": [],
              "api": {
                "messages": {
                  "success": "新增成功"
                },
                "method": "post",
                "requestAdaptor": "const {orderAttrCode, detailList = [], ...rest} = api.data\nconst detailListN = detailList.map(item => {\n  const {priority, cabinName} = item\n  return {\n    cabinCode: cabinName?.cabinCode,\n    cabinName: cabinName?.cabinName,\n    priority\n  }\n})\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode,\n  detailList: detailListN\n}\nif (detailListN.length === 0) {\n  api.mockResponse = {\n    data: {\n      status: 500,\n      msg: '运输履约分层未配置'\n    }\n  }\n}\n\nreturn api",
                "silent": false,
                "url": "/tms/admin/transportAbilityConfig/add"
              },
              "body": [
                {
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请输入",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:50ba1ba9b221",
                  "label": "履约分层方案名称",
                  "name": "configName",
                  "type": "text"
                },
                {
                  "api": {
                    "method": "get",
                    "url": "/tms/admin/enum/getOversizeTransportEnum"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "name",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:4173b4e421e0",
                  "initialValue": 0,
                  "label": "大件运输",
                  "name": "oversizeTransport",
                  "type": "select"
                },
                {
                  "api": {
                    "method": "get",
                    "url": "/tms/admin/enum/getTemperatureRequireEnum"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "name",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:428bd6ad724c",
                  "initialValue": 0,
                  "label": "温度要求",
                  "name": "temperatureRequire",
                  "type": "select"
                },
                {
                  "api": {
                    "method": "get",
                    "url": "/tms/admin/enum/getCommodityValueEnum"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "name",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:8a6b26598cbc",
                  "initialValue": 0,
                  "label": "商品价值",
                  "name": "commodityValue",
                  "type": "select"
                },
                {
                  "api": {
                    "method": "get",
                    "url": "/tms/admin/enum/getFragileProtectEnum"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "name",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:87ccb4e8eefd",
                  "initialValue": 0,
                  "label": "易碎防护",
                  "name": "fragileProtection",
                  "type": "select"
                },
                {
                  "api": {
                    "method": "get",
                    "url": "/tms/admin/enum/getAirEmbargoEnum"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "name",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:667deaf0cc60",
                  "initialValue": 0,
                  "label": "航空禁运",
                  "name": "airEmbargo",
                  "type": "select"
                },
                {
                  "api": {
                    "adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}",
                    "method": "get",
                    "url": "/tms/admin/enum/getOrderAttrCode"
                  },
                  "fieldProps": {
                    "fieldNames": {
                      "label": "name",
                      "value": "code"
                    }
                  },
                  "formItemProps": {
                    "rules": [
                      {
                        "message": "请选择",
                        "required": true
                      }
                    ]
                  },
                  "id": "u:54e93cd680d4",
                  "label": "订单属性",
                  "name": "orderAttrCode",
                  "type": "select"
                },
                {
                  "columns": [
                    {
                      "api": {
                        "adaptor": "return response?.data?.list",
                        "method": "post",
                        "url": "/tms/admin/transportCabin/page"
                      },
                      "fieldProps": {
                        "fieldNames": {
                          "label": "cabinName",
                          "value": "cabinCode"
                        },
                        "labelInValue": true
                      },
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请选择",
                            "required": true
                          }
                        ]
                      },
                      "id": "u:cf04a1df8a2b",
                      "label": "运输履约分层",
                      "name": "cabinName",
                      "type": "select"
                    },
                    {
                      "formItemProps": {
                        "rules": [
                          {
                            "message": "请输入",
                            "required": true
                          }
                        ]
                      },
                      "label": "优先级",
                      "name": "priority",
                      "valueType": "digit"
                    },
                    {
                      "actions": [
                        {
                          "actionType": "editTableDelete",
                          "confirmText": "是否确认删除",
                          "danger": true,
                          "id": "u:00acecabe7f0",
                          "label": "删除",
                          "type": "button"
                        }
                      ],
                      "id": "u:78e81b1e6903",
                      "label": "操作",
                      "type": "option",
                      "width": 90
                    }
                  ],
                  "editable": {
                    "actionRender": " \n  return [defaultDoms.delete];\n"
                  },
                  "id": "u:9eeafd946583",
                  "name": "detailList",
                  "scroll": {
                    "x": 270
                  },
                  "type": "editTable"
                }
              ],
              "id": "u:1736e7fd2b98",
              "labelCol": {
                "span": 5
              },
              "type": "form"
            },
            "id": "u:ee7039d9d87e",
            "title": "新增",
            "type": "dialog",
            "width": 800
          },
          "id": "u:3a46389b7e47",
          "label": "新增",
          "level": "primary",
          "type": "button"
        },
        {
          "fileName": "导入模板",
          "fileNameLabel": "模板下载",
          "fileUrl": "https://h5static.dewucdn.com/node-common/69b128b7-f1f4-65f6-23bf-9d0c4c129569.xlsx",
          "id": "u:04ecc8b92b8b",
          "importMod": "importTransportAbilityConfig",
          "label": "导入",
          "type": "lmsImport"
        },
        {
          "id": "u:f7cf0910e7c7",
          "importMod": "importTransportAbilityConfig",
          "label": "导入结果查询",
          "type": "lmsImportResult"
        },
        {
          "id": "u:de04edb5d785"
        }
      ],
      "api": {
        "adaptor": "const list = response?.data?.list || []\nconst listN = list.map(item => {\n  const {detailList = []} = item\n  const detailListText = detailList.map((v) => v.priority + '-' + v.cabinName).join('；')\n  return {\n    ...item,\n    detailListText,\n  }\n})\n\nreturn {\n  status: 200,\n  code: 200,\n  data: {\n    contents: listN,\n    total: response?.data?.total\n  }\n}",
        "method": "post",
        "requestAdaptor": "const {orderAttrCode, ...rest} = api.data || {};\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode\n}\nreturn api",
        "url": "/tms/admin/transportAbilityConfig/page"
      },
      "formColumns": [
        {
          "id": "u:348e14f6ac20",
          "label": "履约分层方案名称",
          "name": "configName",
          "type": "text"
        },
        {
          "api": {
            "method": "get",
            "url": "/tms/admin/enum/getOversizeTransportEnum"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "name",
              "value": "code"
            }
          },
          "id": "u:fa54a86c96d5",
          "label": "大件运输",
          "name": "oversizeTransport",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "url": "/tms/admin/enum/getTemperatureRequireEnum"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "name",
              "value": "code"
            }
          },
          "id": "u:720262541c13",
          "label": "温度要求",
          "name": "temperatureRequire",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "url": "/tms/admin/enum/getCommodityValueEnum"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "name",
              "value": "code"
            }
          },
          "id": "u:31e47a73f63e",
          "label": "商品价值",
          "name": "commodityValue",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "url": "/tms/admin/enum/getFragileProtectEnum"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "name",
              "value": "code"
            }
          },
          "id": "u:d3983b6db22d",
          "label": "易碎防护",
          "name": "fragileProtection",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "url": "/tms/admin/enum/getAirEmbargoEnum"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "name",
              "value": "code"
            }
          },
          "id": "u:42de5f825967",
          "label": "航空禁运",
          "name": "airEmbargo",
          "type": "select"
        },
        {
          "api": {
            "adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}",
            "method": "get",
            "url": "/tms/admin/enum/getOrderAttrCode"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "name",
              "value": "code"
            }
          },
          "id": "u:431e3ebd9d1e",
          "label": "订单属性",
          "name": "orderAttrCode",
          "type": "select"
        },
        {
          "api": {
            "adaptor": "return {\n              status: 200,\n              data: response?.mapData?.transportAbilityConfigStateEnum || []\n            };",
            "data": {
              "enumTypes": "transportAbilityConfigStateEnum"
            },
            "method": "post",
            "url": "/tms/admin/enum/listEnums"
          },
          "id": "u:de7f6b1b99b3",
          "initialValue": "ENABLED",
          "label": "生效状态",
          "name": "state",
          "type": "select"
        }
      ],
      "id": "u:c61a67ebea74",
      "name": "chainSearchPage",
      "rowKey": "id",
      "search": {
        "defaultCollapsed": false,
        "labelWidth": 120
      },
      "tableColumns": [
        {
          "id": "u:2f276b8f515e",
          "label": "履约分层方案名称",
          "name": "configName",
          "type": "text",
          "width": 200
        },
        {
          "id": "u:f0c35b3b6f84",
          "label": "大件运输",
          "name": "oversizeTransportDesc",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:f3486f1765a7",
          "label": "温度要求",
          "name": "temperatureRequireDesc",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:1a3932f59639",
          "label": "商品价值",
          "name": "commodityValueDesc",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:2efbec57affc",
          "label": "易碎防护",
          "name": "fragileProtectionDesc",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:0e5f80c5bdb9",
          "label": "航空禁运",
          "name": "airEmbargoDesc",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:423eb93ff757",
          "label": "订单属性",
          "name": "orderAttrCodeDesc",
          "type": "text",
          "width": 140
        },
        {
          "id": "u:806a9d47a248",
          "label": "运输履约分层",
          "name": "detailListText",
          "type": "text",
          "width": 250
        },
        {
          "body": [
            {
              "color": "${state === 'ENABLED' ? 'green' : 'orange'}",
              "id": "u:a01974330be5",
              "label": "${state === 'ENABLED' ? '生效中' : '已失效'}",
              "type": "tag"
            }
          ],
          "id": "u:3af61c69f090",
          "label": "生效状态",
          "name": "state",
          "type": "custom"
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "api": {
                "adaptor": "const {oldFormValues, id} = api.data\nconst {orderAttrCode, ...rest} = response.data\nlet data = {\n  ...rest,\n  orderAttrCode: orderAttrCode ? orderAttrCode : '-1',\n  id,\n  isInit: true\n}\nif (oldFormValues) {\n  data = {\n    ...oldFormValues,\n    id,\n    isInit: true,\n    detailList: response.data?.detailList || []\n  }\n}\nreturn {\n  status: 200,\n  data\n}",
                "requestAdaptor": "let oldFormValues = null\nif (api.data?.oldFormValues) {\n  const {detailList,queryParam, ...rest} = api.data?.oldFormValues || {}  \n  oldFormValues = {...rest}\n}\napi.data = {\n  ...api.data,\n  oldFormValues: oldFormValues\n}\nconsole.log('api.data11', api.data)\nreturn api",
                "url": "/tms/admin/transportAbilityConfig/add"
              },
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:1d913fba902c",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "submit",
                    "id": "u:9bef622fc2d3",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "api": {
                      "data": {
                        "&": "$$",
                        "id": "${id}",
                        "isInit": "${isInit}"
                      },
                      "method": "post",
                      "requestAdaptor": "\nconst {orderAttrCode, detailList = [], ...rest} = api.data\n\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode\n}\n\nreturn api",
                      "url": "/tms/admin/transportAbilityConfig/updateById"
                    },
                    "body": [
                      {
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:72db77048f16",
                        "label": "履约分层方案名称",
                        "name": "configName",
                        "type": "text"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getOversizeTransportEnum"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:0f446f98b374",
                        "initialValue": 0,
                        "label": "大件运输",
                        "name": "oversizeTransport",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getTemperatureRequireEnum"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:fe4422cf0ca4",
                        "initialValue": 0,
                        "label": "温度要求",
                        "name": "temperatureRequire",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getCommodityValueEnum"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:408762f76861",
                        "initialValue": 0,
                        "label": "商品价值",
                        "name": "commodityValue",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getFragileProtectEnum"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "hiddenOn": false,
                        "id": "u:ccb30f9b11ef",
                        "initialValue": 0,
                        "label": "易碎防护",
                        "name": "fragileProtection",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getAirEmbargoEnum"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:1244caef75ec",
                        "initialValue": 0,
                        "label": "航空禁运",
                        "name": "airEmbargo",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select"
                      },
                      {
                        "api": {
                          "adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}",
                          "method": "get",
                          "url": "/tms/admin/enum/getOrderAttrCode"
                        },
                        "fieldProps": {
                          "disabled": false,
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:57a6f2c5f442",
                        "label": "订单属性",
                        "name": "orderAttrCode",
                        "proFieldProps": {
                          "mode": "read"
                        },
                        "type": "select"
                      },
                      {
                        "columns": [
                          {
                            "api": {
                              "adaptor": "return response?.data?.list",
                              "method": "post",
                              "url": "/tms/admin/transportCabin/page"
                            },
                            "fieldProps": {
                              "fieldNames": {
                                "label": "cabinName",
                                "value": "cabinCode"
                              },
                              "labelInValue": true
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "id": "u:e50704469e25",
                            "label": "运输履约分层",
                            "name": "cabinName",
                            "type": "select"
                          },
                          {
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请输入",
                                  "required": true
                                }
                              ]
                            },
                            "label": "优先级",
                            "name": "priority",
                            "valueType": "digit"
                          },
                          {
                            "actions": [
                              {
                                "actionType": "editTableDelete",
                                "api": {
                                  "data": {
                                    "detailId": "${id}"
                                  },
                                  "messages": {
                                    "failed": "删除失败",
                                    "success": "删除成功"
                                  },
                                  "method": "get",
                                  "url": "/tms/admin/transportAbilityConfig/detail/delete"
                                },
                                "confirmText": "是否确认删除",
                                "danger": true,
                                "id": "u:0982b9286505",
                                "label": "删除",
                                "type": "button"
                              }
                            ],
                            "id": "u:b605c35cd410",
                            "label": "操作",
                            "type": "option",
                            "width": 90
                          }
                        ],
                        "editable": {
                          "actionRender": " \n  return [defaultDoms.save,defaultDoms.delete];\n"
                        },
                        "id": "u:b288950eb431",
                        "name": "detailList",
                        "onSaveApi": {
                          "method": "post",
                          "requestAdaptor": "\nconst {editableRow, id} = api.data\nconst {cabinName, priority} = editableRow\napi.data = {\n  cabinCode: cabinName.cabinCode,\n  cabinName: cabinName.cabinName,\n  priority,\n  transportAbilityConfigId: id\n}\n\nreturn api",
                          "url": "/tms/admin/transportAbilityConfig/detail/add"
                        },
                        "rowKey": "id",
                        "scroll": {
                          "x": 270
                        },
                        "target": "editDetailForm",
                        "type": "editTable"
                      }
                    ],
                    "id": "u:6df3902cb860",
                    "initApi": {
                      "adaptor": "const {oldFormValues, id} = api.data\nconst {orderAttrCode, ...rest} = response.data\nlet data = {\n  ...rest,\n  orderAttrCode: orderAttrCode ? orderAttrCode : '-1',\n  id,\n  isInit: true\n}\nif (oldFormValues) {\n  data = {\n    ...oldFormValues,\n    id,\n    isInit: true,\n    detailList: response.data?.detailList || []\n  }\n}\nreturn {\n  status: 200,\n  data\n}",
                      "data": {
                        "id": "${id}",
                        "oldFormValues": "${oldFormValues}"
                      },
                      "method": "get",
                      "requestAdaptor": "let oldFormValues = null\nif (api.data?.oldFormValues) {\n  const {detailList,queryParam, ...rest} = api.data?.oldFormValues || {}  \n  oldFormValues = {...rest}\n}\napi.data = {\n  ...api.data,\n  oldFormValues: oldFormValues\n}\nconsole.log('api.data11', api.data)\nreturn api",
                      "sendOn": "${!isInit || !!oldFormValues}",
                      "url": "/tms/admin/transportAbilityConfig/get?keywords=${target}"
                    },
                    "labelCol": {
                      "span": 5
                    },
                    "name": "editDetailForm",
                    "type": "form"
                  }
                ],
                "id": "u:47f046cc1213",
                "title": "编辑",
                "type": "dialog",
                "width": 800
              },
              "id": "u:b929f26710a2",
              "label": "编辑",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "dialog",
              "api": {
                "adaptor": "const {configName, detailList, ...rest} = response?.data\n\nreturn {\n  status: 200,\n  data: rest\n}",
                "requestAdaptor": "const {orderAttrCode, detailList = [], ...rest} = api.data\nconst detailListN = detailList.map(item => {\n  const {priority, cabinName} = item\n  return {\n    cabinCode: cabinName?.cabinCode,\n    cabinName: cabinName?.cabinName,\n    priority\n  }\n})\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode,\n  detailList: detailListN\n}\nif (detailListN.length === 0) {\n  api.mockResponse = {\n    data: {\n      status: 500,\n      msg: '运输履约分层未配置'\n    }\n  }\n}\n\nconsole.log('data', api)\n\nreturn api",
                "url": "/tms/admin/transportAbilityConfig/add"
              },
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:1d913fba903c",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "submit",
                    "id": "u:9bef622fc2d1",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "api": {
                      "messages": {
                        "success": "新增成功"
                      },
                      "method": "post",
                      "requestAdaptor": "console.log('api', api)\nconst {orderAttrCode, detailList = [], ...rest} = api.data\nconst detailListN = detailList.map(item => {\n  const {priority, cabinName} = item\n  return {\n    cabinCode: cabinName?.cabinCode,\n    cabinName: cabinName?.cabinName,\n    priority\n  }\n})\napi.data = {\n  ...rest,\n  orderAttrCode: orderAttrCode === '-1' ? '' : orderAttrCode,\n  detailList: detailListN\n}\nif (detailListN.length === 0) {\n  api.mockResponse = {\n    data: {\n      status: 500,\n      msg: '运输履约分层未配置'\n    }\n  }\n}\nconsole.log('detailListN', detailListN)\n\nconsole.log('data', api)\n\nreturn api",
                      "silent": false,
                      "url": "/tms/admin/transportAbilityConfig/add"
                    },
                    "body": [
                      {
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:72db77048f17",
                        "label": "履约分层方案名称",
                        "name": "configName",
                        "type": "text"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getOversizeTransportEnum"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:0f446f98b374",
                        "initialValue": 0,
                        "label": "大件运输",
                        "name": "oversizeTransport",
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getTemperatureRequireEnum"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:fe4422cf1ca4",
                        "initialValue": 0,
                        "label": "温度要求",
                        "name": "temperatureRequire",
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getCommodityValueEnum"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:408762f76881",
                        "initialValue": 0,
                        "label": "商品价值",
                        "name": "commodityValue",
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getFragileProtectEnum"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:ccb30f9b21ef",
                        "initialValue": 0,
                        "label": "易碎防护",
                        "name": "fragileProtection",
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/tms/admin/enum/getAirEmbargoEnum"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:1244caef76ec",
                        "initialValue": 0,
                        "label": "航空禁运",
                        "name": "airEmbargo",
                        "type": "select"
                      },
                      {
                        "api": {
                          "adaptor": "const data = response?.data || []\nconst dataN = [\n  ...data,\n  {\n    name: '无', \n    code: '-1'\n  }\n] \nreturn {\n  status: 200,\n  data: dataN\n}",
                          "method": "get",
                          "url": "/tms/admin/enum/getOrderAttrCode"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "name",
                            "value": "code"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:57a6f2c5f432",
                        "label": "订单属性",
                        "name": "orderAttrCode",
                        "type": "select"
                      },
                      {
                        "columns": [
                          {
                            "api": {
                              "adaptor": "return response?.data?.list",
                              "method": "post",
                              "url": "/tms/admin/transportCabin/page"
                            },
                            "fieldProps": {
                              "fieldNames": {
                                "label": "cabinName",
                                "value": "cabinCode"
                              },
                              "labelInValue": true
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "id": "u:4dfd0598ce96",
                            "label": "运输履约分层",
                            "name": "cabinName",
                            "type": "select"
                          },
                          {
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请输入",
                                  "required": true
                                }
                              ]
                            },
                            "label": "优先级",
                            "name": "priority",
                            "valueType": "digit"
                          },
                          {
                            "actions": [
                              {
                                "actionType": "editTableDelete",
                                "confirmText": "是否确认删除",
                                "danger": true,
                                "id": "u:35fb7d798f0b",
                                "label": "删除",
                                "type": "button"
                              }
                            ],
                            "id": "u:0f851c231b2a",
                            "label": "操作",
                            "type": "option",
                            "width": 90
                          }
                        ],
                        "editable": {
                          "actionRender": " \n  return [defaultDoms.delete];\n"
                        },
                        "id": "u:b288950eb231",
                        "name": "detailList",
                        "scroll": {
                          "x": 270
                        },
                        "type": "editTable"
                      }
                    ],
                    "id": "u:6df3902cb810",
                    "initApi": {
                      "adaptor": "const {configName, detailList, ...rest} = response?.data\n\nreturn {\n  status: 200,\n  data: rest\n}",
                      "data": {
                        "id": "${id}"
                      },
                      "method": "get",
                      "url": "/tms/admin/transportAbilityConfig/get"
                    },
                    "labelCol": {
                      "span": 5
                    },
                    "type": "form"
                  }
                ],
                "id": "u:47f046cc1233",
                "title": "复制",
                "type": "dialog",
                "width": 800
              },
              "id": "u:b929f26710a3",
              "label": "复制",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "id": "${id}",
                  "state": "UN_ENABLED"
                },
                "messages": {
                  "failed": "失效失败",
                  "success": "失效成功"
                },
                "method": "post",
                "url": "/tms/admin/transportAbilityConfig/stateUpdate"
              },
              "confirmText": "是否确认失效",
              "danger": true,
              "hiddenOn": "${state != 'ENABLED'}",
              "id": "u:5f81c62318f9",
              "label": "失效",
              "type": "button"
            },
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "id": "${id}",
                  "state": "ENABLED"
                },
                "messages": {
                  "failed": "生效失败",
                  "success": "生效成功"
                },
                "method": "post",
                "url": "/tms/admin/transportAbilityConfig/stateUpdate"
              },
              "confirmText": "是否确认生效",
              "hiddenOn": "${state != 'UN_ENABLED'}",
              "id": "u:5f81c62318f3",
              "label": "生效",
              "type": "button"
            },
            {
              "id": "u:37b02531dafe",
              "label": "操作记录",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [
                          {
                            "actionType": "cancel",
                            "id": "u:1b757cbf958e",
                            "label": "取消",
                            "type": "button"
                          },
                          {
                            "actionType": "confirm",
                            "id": "u:b1066095b496",
                            "label": "确认",
                            "level": "primary",
                            "type": "button"
                          }
                        ],
                        "body": [
                          {
                            "actions": [],
                            "api": {
                              "data": {
                                "id": "${id || 242}",
                                "tableName": "des_transport_ability_config"
                              },
                              "method": "post",
                              "url": "/tms/admin/log/getLogList"
                            },
                            "formColumns": [],
                            "id": "u:4594553c451d",
                            "label": "列表",
                            "name": "chainSearchPage",
                            "rowKey": "id",
                            "search": false,
                            "tableAlertRender": false,
                            "tableColumns": [
                              {
                                "id": "u:023774b7dfbc",
                                "label": "操作人",
                                "name": "name",
                                "type": "text",
                                "width": 40
                              },
                              {
                                "id": "u:4b054e34abfd",
                                "label": "操作时间",
                                "name": "modifyTime",
                                "type": "text",
                                "width": 100
                              },
                              {
                                "id": "u:9e3581f42dd0",
                                "label": "操作类型",
                                "name": "operationName",
                                "type": "text",
                                "width": 60
                              },
                              {
                                "id": "u:746be6b29190",
                                "label": "操作内容",
                                "name": "operationContent",
                                "type": "text"
                              }
                            ],
                            "type": "chainSearchPage"
                          }
                        ],
                        "id": "u:6ee1612c2e63",
                        "title": "查看操作记录",
                        "type": "dialog",
                        "width": 900
                      }
                    }
                  ]
                }
              },
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:613e023d2028",
          "label": "操作",
          "type": "operation",
          "width": 210
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "数据格式化、下拉枚举查询、新增（form表单中使用编辑表格）、编辑、删除、导入、导出",
  "title": "表单中使用编辑列表",
  "type": "page"
}

==================================================

菜单项: 展示详情的列表页
时间: 2025-07-22T18:04:41.413754
内容长度: 34866 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "dialog",
          "dialog": {
            "actions": [
              {
                "actionType": "cancel",
                "id": "u:fb8e73d128b3",
                "label": "取消",
                "type": "button"
              },
              {
                "actionType": "submit",
                "id": "u:28c36b477adf",
                "label": "确认",
                "level": "primary",
                "reload": "chainSearchPage",
                "type": "button"
              }
            ],
            "body": [
              {
                "actions": [],
                "api": {
                  "messages": {
                    "success": "新增成功"
                  },
                  "url": "/mms/admin/material/reserveDepot/v1/saveOrUpdate"
                },
                "body": [
                  {
                    "api": {
                      "adaptor": "return {\n  status: 200,\n  data: response.data.boxList\n};\n          ",
                      "method": "get",
                      "url": "/scp/mdm/admin/boxInfo/getBoxSupplierInfo"
                    },
                    "fieldProps": {
                      "fieldNames": {
                        "label": "code",
                        "value": "code"
                      }
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入包材编码",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:52cb985d1e51",
                    "label": "包材编码",
                    "name": "packingMaterialCode",
                    "type": "select"
                  },
                  {
                    "api": {
                      "method": "get",
                      "url": "/mms/admin/material/species/v1/materialTypeList"
                    },
                    "fieldProps": {
                      "fieldNames": {
                        "label": "materialTypeName",
                        "value": "materialTypeCode"
                      }
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择包材类型",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:9bc434738ead",
                    "label": "包材类型",
                    "name": "materialTypeCode",
                    "type": "select"
                  },
                  {
                    "api": {
                      "adaptor": "return {\n  status: 200,\n  data: response.data.firstCategoryList\n};\n          ",
                      "method": "get",
                      "url": "/mms/admin/bom/query/v1/bomMapCategoryCondition"
                    },
                    "fieldProps": {
                      "fieldNames": {
                        "label": "firstCategoryName",
                        "value": "firstCategoryCode"
                      },
                      "multiple": true,
                      "showSearch": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择适用类目",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:a9d1848701ff",
                    "label": "适用类目",
                    "name": "firstCategoryCodes",
                    "type": "select"
                  },
                  {
                    "fieldProps": {
                      "options": [
                        {
                          "label": "否",
                          "value": 0
                        },
                        {
                          "label": "是",
                          "value": 1
                        }
                      ]
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择是否完成测试",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:09065b2ce562",
                    "label": "是否完成测试",
                    "name": "completeTestFlag",
                    "type": "select"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 200,
                      "showCount": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入成本描述",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:fc9a974f24f9",
                    "label": "成本描述",
                    "name": "costDesc",
                    "type": "textarea"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 500,
                      "showCount": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入使用方式描述",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:829063bd13c2",
                    "label": "使用方式描述",
                    "name": "useMethodDesc",
                    "type": "textarea"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 500,
                      "showCount": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入使用范围",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:3cfe40e49776",
                    "label": "使用范围",
                    "name": "useRange",
                    "type": "textarea"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 500,
                      "showCount": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入特点",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:7d940554aafc",
                    "label": "特点",
                    "name": "feature",
                    "type": "textarea"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 500,
                      "showCount": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入物理性能",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:f0b1273fd7e0",
                    "label": "物理性能",
                    "name": "physicalProperty",
                    "type": "textarea"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 500,
                      "showCount": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入文档链接"
                        }
                      ]
                    },
                    "id": "u:1c5f1ca69597",
                    "label": "文档链接",
                    "name": "documentLink",
                    "type": "textarea"
                  },
                  {
                    "fieldProps": {
                      "accept": ".png,.jpg,.gif,.jpeg",
                      "copyable": true,
                      "maxCount": 6,
                      "multiple": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请上传实物图",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:a125c9cd5e6f",
                    "label": "实物图",
                    "name": "realPics",
                    "type": "scmUpload"
                  },
                  {
                    "fieldProps": {
                      "accept": ".png,.jpg,.gif,.jpeg",
                      "copyable": true,
                      "maxCount": 6,
                      "multiple": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请上传包装效果图",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:1d5a60cd1f88",
                    "label": "包装效果图",
                    "name": "packagingEffectPics",
                    "type": "scmUpload"
                  }
                ],
                "id": "u:ddc9acd9368e",
                "type": "form"
              }
            ],
            "id": "u:2e7a56e013c7",
            "title": "新建",
            "type": "dialog",
            "width": 900
          },
          "id": "u:e0cb3b6db9db",
          "label": "新建",
          "level": "primary",
          "type": "button"
        }
      ],
      "api": {
        "data": {
          "orderBy": "updated_time"
        },
        "method": "post",
        "url": "/mms/admin/material/reserveDepot/v1/list"
      },
      "formColumns": [
        {
          "id": "u:e3826fe828e4",
          "label": "包材编码",
          "name": "packingMaterialCode",
          "type": "text"
        },
        {
          "api": {
            "method": "get",
            "url": "/mms/admin/material/species/v1/speciesList"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "speciesName",
              "value": "speciesCode"
            }
          },
          "id": "u:507e6cfbb685",
          "label": "包材种类",
          "name": "speciesCode",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "url": "/mms/admin/material/species/v1/materialTypeList"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "materialTypeName",
              "value": "materialTypeCode"
            }
          },
          "id": "u:2a5857a4d81f",
          "label": "包材类型",
          "name": "materialTypeCode",
          "type": "select"
        },
        {
          "api": {
            "adaptor": "return {\n  status: 200,\n  data: response.data.firstCategoryList\n};\n          ",
            "method": "get",
            "url": "/mms/admin/bom/query/v1/bomMapCategoryCondition"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "firstCategoryName",
              "value": "firstCategoryCode"
            }
          },
          "id": "u:6f299818a74c",
          "label": "适用类目",
          "name": "firstCategoryCode",
          "type": "select"
        },
        {
          "fieldProps": {
            "options": [
              {
                "label": "否",
                "value": 0
              },
              {
                "label": "是",
                "value": 1
              }
            ]
          },
          "id": "u:f383930349b3",
          "label": "是否完成测试",
          "name": "completeTestFlag",
          "type": "select"
        }
      ],
      "id": "u:c61a67ebea74",
      "name": "chainSearchPage",
      "rowKey": "id",
      "search": {
        "defaultCollapsed": false,
        "labelWidth": 110
      },
      "tableColumns": [
        {
          "body": [
            {
              "height": 60,
              "id": "u:aabba63165bd",
              "mode": "cover",
              "src": "${realPic}",
              "type": "picture",
              "width": 60
            }
          ],
          "id": "u:ff2bfd2c4f1e",
          "label": "实物图",
          "name": "realPic",
          "type": "custom",
          "width": 50
        },
        {
          "body": [
            {
              "id": "u:82f10fa8f932",
              "label": "${packingMaterialCode}",
              "level": "link",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "dialog": {
                        "actions": [],
                        "body": [
                          {
                            "api": {
                              "adaptor": "const {realPics = [], packagingEffectPics = [], ...rest} = response.data\nconst realPicsUrls = realPics.map(item => item.url)\nconst packagingEffectPicsUrls = packagingEffectPics.map(item => item.url)\n\nreturn {\n  status: 200,\n  data: {\n    ...response.data,\n    realPicsUrls,\n    packagingEffectPicsUrls\n  }\n}\n",
                              "data": {
                                "id": "${id}"
                              },
                              "method": "get",
                              "url": "/mms/admin/material/reserveDepot/v1/detail"
                            },
                            "body": [
                              {
                                "body": [
                                  {
                                    "columns": [
                                      {
                                        "label": "包材编码",
                                        "name": "packingMaterialCode"
                                      },
                                      {
                                        "label": "包材名称",
                                        "name": "packingMaterialName"
                                      },
                                      {
                                        "label": "适用类目",
                                        "name": "firstCategoryNames"
                                      },
                                      {
                                        "label": "包材种类",
                                        "name": "speciesName"
                                      },
                                      {
                                        "label": "包材类型",
                                        "name": "materialTypeName"
                                      },
                                      {
                                        "label": "是否完成测试",
                                        "name": "completeTestFlagName"
                                      },
                                      {
                                        "label": "成本描述",
                                        "name": "costDesc"
                                      },
                                      {
                                        "label": "文档链接",
                                        "name": "documentLink"
                                      }
                                    ],
                                    "id": "u:8f83fd192d73",
                                    "type": "descriptions"
                                  }
                                ],
                                "id": "u:b9876de64780",
                                "renderType": "inner",
                                "size": "small",
                                "title": "基本信息",
                                "type": "card"
                              },
                              {
                                "body": [
                                  {
                                    "dataSource": "${realPicsUrls}",
                                    "height": 100,
                                    "id": "u:9104e48b893f",
                                    "mode": "cover",
                                    "name": "realPicsUrls",
                                    "type": "pictures",
                                    "width": 100
                                  }
                                ],
                                "id": "u:ab796df2e0c6",
                                "renderType": "inner",
                                "size": "small",
                                "title": "实物图",
                                "type": "card"
                              },
                              {
                                "body": [
                                  {
                                    "dataSource": "${packagingEffectPicsUrls}",
                                    "height": 100,
                                    "id": "u:b77f74f13fdd",
                                    "mode": "cover",
                                    "name": "packagingEffectPicsUrls",
                                    "type": "pictures",
                                    "width": 100
                                  }
                                ],
                                "id": "u:360ae067b182",
                                "renderType": "inner",
                                "size": "small",
                                "title": "包装效果图",
                                "type": "card"
                              },
                              {
                                "body": [
                                  {
                                    "columns": [
                                      {
                                        "name": "useMethodDesc"
                                      }
                                    ],
                                    "id": "u:d9207199ff84",
                                    "style": {
                                      "text-align": "left"
                                    },
                                    "type": "descriptions"
                                  }
                                ],
                                "id": "u:3a4085f14dcc",
                                "renderType": "inner",
                                "size": "small",
                                "title": "使用方式描述",
                                "type": "card"
                              },
                              {
                                "body": [
                                  {
                                    "columns": [
                                      {
                                        "name": "useRange"
                                      }
                                    ],
                                    "id": "u:cdda4fcab00d",
                                    "style": {
                                      "text-align": "left"
                                    },
                                    "type": "descriptions"
                                  }
                                ],
                                "id": "u:5a92754a831f",
                                "renderType": "inner",
                                "size": "small",
                                "title": "使用范围",
                                "type": "card"
                              },
                              {
                                "body": [
                                  {
                                    "columns": [
                                      {
                                        "name": "physicalProperty"
                                      }
                                    ],
                                    "id": "u:99a860eb7561",
                                    "style": {
                                      "text-align": "left"
                                    },
                                    "type": "descriptions"
                                  }
                                ],
                                "id": "u:270b4ae0eb16",
                                "renderType": "inner",
                                "size": "small",
                                "title": "物理性能",
                                "type": "card"
                              },
                              {
                                "body": [
                                  {
                                    "columns": [
                                      {
                                        "name": "feature"
                                      }
                                    ],
                                    "id": "u:01c7039ab2af",
                                    "style": {
                                      "text-align": "left"
                                    },
                                    "type": "descriptions"
                                  }
                                ],
                                "id": "u:53e92db68ec4",
                                "renderType": "inner",
                                "size": "small",
                                "title": "特点",
                                "type": "card"
                              }
                            ],
                            "data": {
                              "id": "${id || 13}"
                            },
                            "id": "u:38cd0de48f99",
                            "type": "service"
                          }
                        ],
                        "id": "u:15edce03a1c4",
                        "title": "查看详情",
                        "type": "dialog",
                        "width": 860
                      }
                    }
                  ]
                }
              },
              "type": "button"
            }
          ],
          "id": "u:28d108bafa1d",
          "label": "包材编码",
          "name": "packingMaterialCode",
          "type": "custom",
          "width": 100
        },
        {
          "id": "u:dc1fc3867d46",
          "label": "包材名称",
          "name": "packingMaterialName",
          "type": "text",
          "width": 100
        },
        {
          "id": "u:2d141404a4b7",
          "label": "包材类型（种类）",
          "name": "materialTypeName",
          "type": "text",
          "width": 140
        },
        {
          "id": "u:e02905540587",
          "label": "是否完成测试",
          "name": "completeTestFlagName",
          "type": "text",
          "width": 100
        },
        {
          "id": "u:06547b146600",
          "label": "创建人",
          "name": "createdUserRealName",
          "type": "text",
          "width": 100
        },
        {
          "id": "u:7fccdec9d57e",
          "label": "创建时间",
          "name": "createTime",
          "type": "text",
          "width": 120
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:93b8ffd38bf0",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "confirm",
                    "id": "u:112394b0c14b",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "api": {
                      "messages": {
                        "success": "修改成功"
                      },
                      "url": "/mms/admin/material/reserveDepot/v1/saveOrUpdate"
                    },
                    "body": [
                      {
                        "disabled": true,
                        "id": "u:4ecf2d85b8f4",
                        "label": "包材编码",
                        "name": "packingMaterialCode",
                        "type": "text"
                      },
                      {
                        "api": {
                          "method": "get",
                          "url": "/mms/admin/material/species/v1/materialTypeList"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "materialTypeName",
                            "value": "materialTypeCode"
                          }
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择包材类型",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:6d8957f5c581",
                        "label": "包材类型",
                        "name": "materialTypeCode",
                        "type": "select"
                      },
                      {
                        "api": {
                          "adaptor": "\n            return {\n              status: 200,\n              data: response.data.firstCategoryList\n            };\n          ",
                          "method": "get",
                          "url": "/mms/admin/bom/query/v1/bomMapCategoryCondition"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "firstCategoryName",
                            "value": "firstCategoryCode"
                          },
                          "multiple": true,
                          "showSearch": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择适用类目",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:58b1b7eee3b7",
                        "label": "适用类目",
                        "name": "firstCategoryCodes",
                        "type": "select"
                      },
                      {
                        "fieldProps": {
                          "options": [
                            {
                              "label": "否",
                              "value": 0
                            },
                            {
                              "label": "是",
                              "value": 1
                            }
                          ]
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择是否完成测试",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:7ba49e99e2a3",
                        "label": "是否完成测试",
                        "name": "completeTestFlag",
                        "type": "select"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 200,
                          "showCount": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入成本描述",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:5d3be25bf8d0",
                        "label": "成本描述",
                        "name": "costDesc",
                        "type": "textarea"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 500,
                          "showCount": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入使用方式描述",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:4c0598442bff",
                        "label": "使用方式描述",
                        "name": "useMethodDesc",
                        "type": "textarea"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 500,
                          "showCount": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入使用范围",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:c4ec8f745733",
                        "label": "使用范围",
                        "name": "useRange",
                        "type": "textarea"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 500,
                          "showCount": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入特点",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:dd89bbc6f70a",
                        "label": "特点",
                        "name": "feature",
                        "type": "textarea"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 500,
                          "showCount": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入物理性能",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:7376274cc410",
                        "label": "物理性能",
                        "name": "physicalProperty",
                        "type": "textarea"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 500,
                          "showCount": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入文档链接"
                            }
                          ]
                        },
                        "id": "u:f41528752365",
                        "label": "文档链接",
                        "name": "documentLink",
                        "type": "textarea"
                      },
                      {
                        "fieldProps": {
                          "accept": ".png,.jpg,.gif,.jpeg",
                          "copyable": true,
                          "maxCount": 6,
                          "multiple": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请上传实物图",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:022a387552a6",
                        "label": "实物图",
                        "name": "realPics",
                        "type": "scmUpload"
                      },
                      {
                        "fieldProps": {
                          "accept": ".png,.jpg,.gif,.jpeg",
                          "copyable": true,
                          "maxCount": 6,
                          "multiple": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请上传包装效果图",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:a548fddae8b8",
                        "label": "包装效果图",
                        "name": "packagingEffectPics",
                        "type": "scmUpload"
                      },
                      {
                        "formItemProps": {
                          "hidden": true
                        },
                        "id": "u:7d9d52c17133",
                        "label": "id",
                        "name": "id",
                        "type": "text"
                      }
                    ],
                    "id": "u:573f1ef5449a",
                    "initApi": {
                      "data": {
                        "id": "${id}"
                      },
                      "method": "get",
                      "url": "/mms/admin/material/reserveDepot/v1/detail"
                    },
                    "type": "form"
                  }
                ],
                "id": "u:40b2f3a68354",
                "title": "编辑",
                "type": "dialog",
                "width": 900
              },
              "id": "u:a84f3d65c108",
              "label": "编辑",
              "level": "link",
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:6dec1ead211d",
          "label": "操作",
          "type": "operation",
          "width": 100
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:7003827c792e",
  "name": "chainSearchPage",
  "subTitle": "新增、编辑、弹框中展示详情",
  "title": "展示详情的列表页",
  "type": "page"
}

==================================================

菜单项: 轮询查询列表页
时间: 2025-07-22T18:05:06.368048
内容长度: 1793 字符
------------------------------
{
  "body": [
    {
      "api": {
        "method": "post",
        "requestAdaptor": "const {pageNum, pageSize, ...rest} = api.data\napi.data = {\n  extendParam: {\n    page: pageNum,\n    limit: pageSize,\n    isUpcSorting: 0,\n    rmaRecallStatus: 2,\n    type: 'rma_request',\n    uniqueCodes: []\n  },\n  reportName: 'rmaAdminList'\n}\n\nreturn api",
        "url": "/pink/admin/report/query"
      },
      "formColumns": [
        {
          "id": "u:59a32ec0303e",
          "label": "示例字段",
          "name": "warehouseCode1",
          "type": "text"
        }
      ],
      "id": "u:8b3851074eb2",
      "loopApi": {
        "adaptor": "const data = JSON.parse(response.data)\nconst {queryStatus, queryResult = {}, ...rest} = data\nlet isNeedLoop = false\nif (queryStatus === 0) {\n  isNeedLoop = true\n}\nif (queryStatus === 1) {\n  isNeedLoop = false\n}\nconst body = { \n  ...rest,\n  ...queryResult,\n  isNeedLoop,\n}\nreturn {\n  status: 200,\n  data: body\n}",
        "data": {
          "taskId": "${taskId}"
        },
        "method": "get",
        "url": "/pink/admin/report/queryResult"
      },
      "manualRequest": true,
      "name": "chainSearchPage",
      "rowKey": "id",
      "search": {
        "searchText": "点击创建查询任务"
      },
      "tableColumns": [
        {
          "id": "u:e6ba95d4a941",
          "label": "预测日期",
          "name": "buyerDeliveryTime",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:85263292b138",
          "label": "类型",
          "name": "bizTypeText",
          "type": "text",
          "width": 100
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:7003827c792e",
  "subTitle": "数据查询任务化之后，支持轮询任务ID进行数据查询，并动态展示列表字段",
  "title": "轮询查询列表页",
  "type": "page"
}

==================================================

菜单项: 动态表头列表页
时间: 2025-07-22T18:05:31.657779
内容长度: 1777 字符
------------------------------
{
  "body": [
    {
      "api": {
        "data": {},
        "method": "post",
        "url": "/lmsnew/admin/user/pieceCount/listFromEs"
      },
      "formColumns": [
        {
          "endName": "signInTimeEnd",
          "fieldProps": {
            "format": "YYYY-MM-DD HH:mm:ss"
          },
          "id": "u:24d2ce15350f",
          "initialValue": "today,${DATETOSTR(ENDOF(DATEMODIFY(NOW(),7,'day')),'YYYY-MM-DD HH:mm:ss')}",
          "label": "签到时间",
          "name": "timeRange",
          "startName": "signInTimeStart",
          "type": "rangePicker"
        }
      ],
      "id": "u:61aa813e8d33",
      "label": "搜索列表",
      "manualRequest": false,
      "name": "chainSearchPage",
      "tableColumns": [
        {
          "actions": [
            {
              "actionType": "ajax",
              "api": {
                "data": {
                  "id": "${id}"
                },
                "method": "post",
                "url": "/mock/form/saveForm"
              },
              "confirmText": "确认删除：${userName} ？",
              "danger": true,
              "icon": "icon-delete",
              "id": "u:a31a09946e3c",
              "label": "删除",
              "level": "link",
              "size": "middle",
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:dab958e43aee",
          "label": "操作",
          "type": "operation",
          "width": 100
        }
      ],
      "tableColumnsConfig": {
        "data": "extra.headerConfig",
        "fieldNames": {
          "label": "label",
          "name": "prop"
        }
      },
      "type": "chainSearchPage"
    }
  ],
  "id": "u:7003827c792e",
  "subTitle": "通过搜索接口控制表头",
  "title": "动态表头列表页",
  "type": "page"
}

==================================================

菜单项: 支持多列点击查看详情列表页
时间: 2025-07-22T18:05:57.034553
内容长度: 20114 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "export",
          "api": {
            "data": {},
            "method": "get",
            "requestAdaptor": " const queryParam =  api.data?.queryParam || {} \n const data = {...queryParam} \n api.data = data \n return api;",
            "url": "/wmsreport/report/delivery/v1/createReportByTime"
          },
          "id": "u:b0306ac82ca9",
          "label": "导出",
          "level": "primary",
          "type": "button"
        }
      ],
      "api": {
        "adaptor": "const { deliveryType, orderTags } = api.data;\n\nconst data = {\n  contents: response.data.map(item => {\n    const startTimeStr = item.dateStr + \" 00:00:00\";\n    const endTimeStr = item.dateStr + \" 23:59:59\";\n    const startTime = Math.max(new Date(startTimeStr).getTime(), new Date(api.data.startTime).getTime());\n\n    return {\n      ...item,\n      deliveryType,\n      startTime,\n      endTime: new Date(endTimeStr).getTime(),\n      orderTags,\n      lastPickedTime: api.data.lastPickedTime\n    };\n  })\n};\n\nreturn {\n  msg: \"success\",\n  status: 200,\n  data\n};",
        "data": {},
        "method": "post",
        "requestAdaptor": "const { timeRange, warehouseCodeList, deliveryType, lastPickedTime, orderTags } = api.data;\nlet warehouseCodeStr = null;\nif (!!warehouseCodeList) {\n  warehouseCodeStr = warehouseCodeList.join(\",\");\n}\napi.data = {\n  startTime: timeRange?.[0],\n  endTime: timeRange?.[1],\n  warehouseCodeStr: warehouseCodeStr ? warehouseCodeStr : \"\",\n  deliveryType: deliveryType ? deliveryType : \"\",\n  lastPickedTime: lastPickedTime ? lastPickedTime : \"\",\n  orderTags: orderTags ? orderTags : \"\"\n};\nreturn api;",
        "url": "/wmsreport/report/delivery/v1/queryDeliveryStatisticalByDay"
      },
      "formColumns": [
        {
          "fieldProps": {
            "maxTagCount": 1,
            "multiple": true,
            "type": "wms"
          },
          "id": "u:0ab1dc5b83de",
          "label": "仓库名称",
          "name": "warehouseCodeList",
          "type": "scmWarehouse"
        },
        {
          "formItemProps": {
            "rules": [
              {
                "message": "请选择时间",
                "required": true
              }
            ]
          },
          "id": "u:eae48a240c57",
          "initialValue": "${DATETOSTR(DATEMODIFY(DATEMODIFY(STARTOF(NOW(), 'day'), -1, 'day'), 19, 'hour'), 'X') + ',' + DATETOSTR(DATEMODIFY(ENDOF(NOW(), 'day'), -5, 'hour'), 'X')}",
          "label": "时间范围",
          "name": "timeRange",
          "type": "rangePicker"
        },
        {
          "api": {
            "data": {
              "enumName": "WmsOutBoundTypeEnum"
            },
            "method": "GET",
            "url": "/wms/cfg/enum/v1/load"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "typeDesc",
              "value": "typeCode"
            },
            "showSearch": true
          },
          "id": "u:670fdf9c678b",
          "label": "发货单类型",
          "name": "deliveryType",
          "type": "select"
        },
        {
          "api": {
            "data": {
              "enumName": "DeliveryTagEnum"
            },
            "method": "GET",
            "url": "/wms/cfg/enum/v1/load"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "typeDesc",
              "value": "typeCode"
            },
            "showSearch": true
          },
          "id": "u:317cd06b2cb6",
          "label": "订单标记",
          "name": "orderTags",
          "type": "select"
        },
        {
          "api": {
            "data": {
              "enumName": "LatestPickupTimeEnum"
            },
            "method": "GET",
            "url": "/wms/cfg/enum/v1/load"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "typeDesc",
              "value": "typeCode"
            },
            "showSearch": true
          },
          "id": "u:c310d3ca90e7",
          "label": "最晚拣货时间",
          "name": "lastPickedTime",
          "type": "select"
        }
      ],
      "id": "u:c61a67ebea74",
      "name": "chainSearchPage",
      "rowKey": "id",
      "search": {
        "defaultCollapsed": false,
        "labelWidth": 120
      },
      "tableColumns": [
        {
          "align": "center",
          "id": "u:edae717fc990",
          "label": "日期",
          "name": "dateStr",
          "type": "text",
          "width": 180
        },
        {
          "align": "center",
          "id": "u:cc4742a4359f",
          "label": "仓库",
          "name": "warehouseName",
          "type": "text",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "INIT",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:2f509e402a43",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${initNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:f89e9695a757",
          "label": "初始",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "FAIL_ALLOCATE",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:02ff286d0028",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${failAllocateNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:aa6df3e9221e",
          "label": "分配失败",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "PART_ALLOCATE",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:a1a571574012",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${partAllocateNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:688805c66bac",
          "label": "部分分配",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "WHOLE_ALLOCATE",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:1ed30521b268",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${wholeAllocateNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:2db5059e13b7",
          "label": "完全分配",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "LAUNCH",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:17b74fd39d00",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${launchNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:7acd693f3d9f",
          "label": "创建波次",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "PICKING",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:4ace8d0de104",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${pickingNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:66af24f02901",
          "label": "拣货中",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "PICKED",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:4accc81cf578",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${pickedNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:07be9141354e",
          "label": "拣货完成",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "PACKING",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:3dbe71cff55d",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${packingNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:264bb55876c3",
          "label": "装箱中",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "PACK_FINISH",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:eb8dbec1776a",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${packFinishNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:fa3f3752b33d",
          "label": "装箱完成",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "OUTING",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:35a44d3358a7",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${outingNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:6b49bac43f13",
          "label": "发货中",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "OUTED",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:b6ba79ae8695",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${outedNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:5466bbcbfc14",
          "label": "发货完成",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "HANDOVER",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:e7ad13f2fa9e",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${handoverNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:8234a98bc4ac",
          "label": "完成交接",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "CANCEL",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:68cc64d52c57",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${cancelNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:3cb2399d9c00",
          "label": "取消",
          "type": "operation",
          "width": 180
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "$ref": "dialogCom",
                "data": {
                  "deliveryType": "${deliveryType}",
                  "endTime": "${endTime}",
                  "lastPickedTime": "${lastPickedTime}",
                  "orderTags": "${orderTags}",
                  "startTime": "${startTime}",
                  "status": "ALL",
                  "warehouseCode": "${warehouseCode}"
                },
                "id": "u:51e9f11e3b2f",
                "type": "dialog"
              },
              "id": "u:f60f64afb095",
              "label": " ${totalNum} ",
              "level": "link",
              "type": "button"
            }
          ],
          "align": "center",
          "id": "u:f45b33aabe25",
          "label": "汇总",
          "type": "operation",
          "width": 180
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "definitions": {
    "dialogCom": {
      "actions": [],
      "body": [
        {
          "actions": [
            {
              "actionType": "export",
              "api": {
                "data": {},
                "method": "post",
                "requestAdaptor": " const queryParam =  api.data?.queryParam || {} \n const data = {...queryParam} \n api.data = data \n return api;",
                "url": "/wmsreport/report/delivery/v1/createReportDetailByTime"
              },
              "id": "u:b5eadd982b92",
              "label": "导出",
              "level": "primary",
              "type": "button"
            }
          ],
          "api": {
            "data": {
              "deliveryType": "${deliveryType}",
              "endTime": "${endTime}",
              "lastPickedTime": "${lastPickedTime}",
              "orderTags": "${orderTags}",
              "startTime": "${startTime}",
              "status": "${status}",
              "warehouseCode": "${warehouseCode}"
            },
            "method": "post",
            "url": "/wmsreport/report/delivery/v1/queryDeliveryDetailByDay"
          },
          "id": "u:63495c70237f",
          "name": "table",
          "tableColumns": [
            {
              "align": "center",
              "label": "单据号",
              "name": "deliveryOrderCode"
            },
            {
              "align": "center",
              "label": "关联单号",
              "name": "relatedOrderCode"
            },
            {
              "align": "center",
              "label": "单据类型",
              "name": "orderTypeDesc"
            },
            {
              "align": "center",
              "label": "数量",
              "name": "totalPlanQty"
            }
          ],
          "type": "table"
        }
      ],
      "id": "u:2ce9fd6d98e4",
      "title": "明细",
      "type": "dialog",
      "width": "90%"
    }
  },
  "id": "u:7003827c792e",
  "subTitle": "点击表格中数据展示弹框详情明细（共用弹框详情明细模块）",
  "title": "支持多列点击查看详情列表页",
  "type": "page"
}

==================================================

菜单项: 多table共用一个查询项
时间: 2025-07-22T18:06:22.237651
内容长度: 6355 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "submit",
          "htmlType": "submit",
          "id": "u:f0ce5911606c",
          "label": "查询",
          "level": "primary",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "args": {
                    "value": {
                      "count": "${TIMESTAMP()}",
                      "id": "u:e3c77f045cfd",
                      "type": "${type}"
                    }
                  },
                  "componentId": "u:61853dec1386"
                }
              ]
            }
          },
          "type": "submit"
        },
        {
          "id": "u:92b20df66e2d",
          "label": "重置",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "args": {
                    "value": {
                      "type": ""
                    }
                  },
                  "componentId": "u:61853dec1386"
                }
              ]
            }
          },
          "type": "reset"
        }
      ],
      "body": [
        {
          "fieldProps": {
            "options": [
              {
                "label": "供应链",
                "value": 0
              },
              {
                "label": "非供应链",
                "value": 1
              }
            ]
          },
          "id": "u:bf1111f09bf4",
          "label": "舆情分类：",
          "name": "type",
          "type": "select",
          "width": "300px"
        },
        {
          "id": "u:d9196eb40626",
          "label": "编码",
          "name": "id",
          "type": "text"
        }
      ],
      "id": "u:4f99439dc91d",
      "layout": "horizontal",
      "mode": "query",
      "omitNil": false,
      "submitText": "查询",
      "type": "form"
    },
    {
      "body": [
        {
          "actions": [
            {
              "exportMod": "work-order-opinion-monitior-summary-export",
              "id": "u:42b491322ff1",
              "type": "lmsExport"
            }
          ],
          "api": {
            "adaptor": "return { status: 200, msg: 'ok', data: { contents: response?.data?.groupData || [], total: response?.data?.total || 0}};",
            "data": {
              "count": "${count}",
              "type": "${type}"
            },
            "method": "get",
            "url": "/scp-sop-work-order/api/user/query/opinion-monitor"
          },
          "formColumns": [],
          "headerTitle": "汇总数据列表",
          "id": "u:15ed5cc00a16",
          "name": "tableAll",
          "pagination": false,
          "rowKey": "pendProcess",
          "seach": false,
          "tableColumns": [
            {
              "id": "u:a56c4abae472",
              "label": "运营工单处理组",
              "name": "groupName"
            },
            {
              "id": "u:2b112cbe7e27",
              "label": "舆情分类",
              "name": "type"
            },
            {
              "id": "u:1fd6bc1132a6",
              "label": "待处理",
              "name": "pendProcess"
            },
            {
              "id": "u:aba6267dca0e",
              "label": "已领取",
              "name": "allReceiveOpinion",
              "type": "text"
            },
            {
              "id": "u:4c7569cc5f36",
              "label": "处理中",
              "name": "processOpinion",
              "type": "text"
            },
            {
              "id": "u:c20d20f4ce7e",
              "label": "处理完成",
              "name": "completeOpinion",
              "type": "text"
            },
            {
              "id": "u:1d077e4bd38d",
              "label": "当日已核实",
              "name": "verifiedOpinion",
              "type": "text"
            },
            {
              "id": "u:7005c76ca3a0",
              "label": "当日领取",
              "name": "receiveOpinion",
              "type": "text"
            }
          ],
          "type": "chainSearchPage"
        },
        {
          "actions": [
            {
              "exportMod": "work-order-opinion-monitior-export",
              "id": "u:9fbaaaf2e2d1",
              "type": "lmsExport"
            }
          ],
          "api": {
            "adaptor": "return { status: 200, msg: 'ok', data: { contents: response?.data?.content || [], total: response?.data?.totalElements || 0}};",
            "data": {
              "count": "${count}",
              "type": "${type}"
            },
            "method": "get",
            "url": "/scp-sop-work-order/api/user/query/opinion-monitor"
          },
          "formColumns": [],
          "headerTitle": "明细数据列表",
          "id": "u:66df027089d3",
          "name": "detailTable",
          "pagination": {
            "defaultPageSize": 20
          },
          "rowKey": "id",
          "seach": false,
          "tableColumns": [
            {
              "id": "u:2ac382341f70",
              "label": "处理人",
              "name": "name"
            },
            {
              "id": "u:8d0e6dac029b",
              "label": "舆情分类",
              "name": "type"
            },
            {
              "id": "u:5e99da99ccaa",
              "label": "已领取",
              "name": "allReceiveOpinion",
              "type": "text"
            },
            {
              "id": "u:64549539fdea",
              "label": "处理中",
              "name": "processOpinion",
              "type": "text"
            },
            {
              "id": "u:670637781b30",
              "label": "处理完成",
              "name": "completeOpinion",
              "type": "text"
            },
            {
              "id": "u:7e24b40e39da",
              "label": "当日已核实",
              "name": "verifiedOpinion",
              "type": "text"
            },
            {
              "id": "u:f14f967bd59c",
              "label": "当日领取",
              "name": "receiveOpinion",
              "type": "text"
            }
          ],
          "type": "chainSearchPage"
        }
      ],
      "id": "u:61853dec1386",
      "type": "service"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "多table共用一个查询项、数据格式化、下拉枚举查询、导出",
  "title": "多table共用一个查询项 ",
  "type": "page"
}

==================================================

菜单项: 动态表单列表
时间: 2025-07-22T18:06:47.149269
内容长度: 19215 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "dialog",
          "dialog": {
            "actions": [
              {
                "actionType": "cancel",
                "id": "u:adbc68305403",
                "label": "取消",
                "type": "button"
              },
              {
                "actionType": "submit",
                "id": "u:d846b8bf6fd2",
                "label": "确认",
                "level": "primary",
                "reload": "chainSearchPage",
                "type": "button"
              }
            ],
            "body": [
              {
                "actions": [],
                "api": {
                  "messages": {
                    "success": "新增成功"
                  },
                  "url": "/lmsprod/admin/standard/interval/edit"
                },
                "body": [
                  {
                    "api": {
                      "adaptor": "return {\n              status: 200,\n              data: response.data.wareHouse\n            };",
                      "data": {
                        "bizType": "STANDARD_INTERVAL"
                      },
                      "method": "get",
                      "url": "/lmsnew/admin/select/options"
                    },
                    "fieldProps": {
                      "fieldNames": {
                        "label": "value",
                        "value": "code"
                      },
                      "mode": "multiple",
                      "showSearch": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择仓库名称",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:e3e696cb60c9",
                    "label": "仓库名称",
                    "name": "warehouseCodeList",
                    "type": "select"
                  },
                  {
                    "api": {
                      "method": "post",
                      "url": "/lmsnew/admin/standard/worklink/link/name"
                    },
                    "fieldProps": {
                      "fieldNames": {
                        "label": "linkName",
                        "value": "linkCode"
                      },
                      "mode": "multiple",
                      "showSearch": true
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请选择环节名称",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:705dc0a3cf0e",
                    "label": "环节名称",
                    "name": "linkCodeList",
                    "type": "select"
                  },
                  {
                    "fieldProps": {
                      "addonAfter": "min",
                      "min": 0,
                      "precision": 0
                    },
                    "formItemProps": {
                      "rules": [
                        {
                          "message": "请输入弹框时间",
                          "required": true
                        }
                      ]
                    },
                    "id": "u:c2451f573cf8",
                    "label": "弹框时间",
                    "name": "popupIntervalDuration",
                    "type": "digit"
                  },
                  {
                    "fieldProps": {
                      "maxLength": 200,
                      "showCount": true
                    },
                    "id": "u:d32473fb430c",
                    "label": "备注",
                    "name": "remark",
                    "type": "textarea"
                  },
                  {
                    "col": 2,
                    "columns": [
                      {
                        "api": {
                          "method": "get",
                          "url": "/lmsprod/admin/standard/interval/getReasonList"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "reason",
                            "value": "code"
                          },
                          "mode": "single",
                          "showSearch": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:47b6784d8e21",
                        "label": "间歇原因",
                        "name": "reasonCode",
                        "type": "select"
                      },
                      {
                        "fieldProps": {
                          "addonAfter": "min",
                          "min": 1,
                          "precision": 0
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:fb920aa42f96",
                        "label": "合理最大值",
                        "name": "maxReasonable",
                        "type": "digit"
                      }
                    ],
                    "id": "u:b5b479e730da",
                    "label": "",
                    "min": 1,
                    "name": "intervalDetailList",
                    "type": "dynamicList"
                  }
                ],
                "id": "u:7929a8cde223",
                "onFormInit": "return {\n intervalDetailList: [{}] \n}",
                "type": "form"
              }
            ],
            "id": "u:d188e09ad03e",
            "title": "新增",
            "type": "dialog",
            "width": 660
          },
          "id": "u:3a46389b7e47",
          "label": "新增",
          "level": "primary",
          "type": "button"
        },
        {
          "data": {},
          "exportMod": "standardIntervalExport",
          "id": "u:deab7ec21505",
          "label": "导出",
          "type": "lmsExport"
        }
      ],
      "api": {
        "method": "post",
        "url": "/lmsprod/admin/standard/interval/list"
      },
      "formColumns": [
        {
          "api": {
            "adaptor": "return {\n              status: 200,\n              data: response.data.wareHouse\n            };",
            "data": {
              "bizType": "STANDARD_INTERVAL"
            },
            "method": "get",
            "url": "/lmsnew/admin/select/options"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "value",
              "value": "code"
            },
            "mode": "multiple",
            "showSearch": true
          },
          "id": "u:93d68b5844b4",
          "label": "仓库名称",
          "name": "warehouseCodeList",
          "type": "select"
        },
        {
          "api": {
            "method": "post",
            "url": "/lmsprod/admin/standard/worklink/link/name"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "linkName",
              "value": "linkCode"
            },
            "mode": "multiple",
            "showSearch": true
          },
          "id": "u:54ed10e932a2",
          "label": "环节名称",
          "name": "linkCodeList",
          "type": "select"
        },
        {
          "api": {
            "adaptor": "return {\n              status: 200,\n              data: response.data.yesOrNo\n            };",
            "data": {
              "bizType": "STANDARD_INTERVAL"
            },
            "method": "get",
            "url": "/lmsnew/admin/select/options"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "value",
              "value": "code"
            }
          },
          "id": "u:d6883a420793",
          "label": "是否开启",
          "name": "actived",
          "type": "select"
        }
      ],
      "id": "u:c61a67ebea74",
      "name": "chainSearchPage",
      "rowKey": "id",
      "tableColumns": [
        {
          "id": "u:ae08901715d8",
          "label": "序号",
          "type": "index",
          "width": 80
        },
        {
          "id": "u:be59d1cb140c",
          "label": "仓库名称",
          "name": "warehouseNames",
          "width": 120
        },
        {
          "id": "u:e5db865fc1d6",
          "label": "环节名称",
          "name": "linkNames",
          "width": 120
        },
        {
          "id": "u:5a00e4852f83",
          "label": "弹窗时间(min)",
          "name": "popupIntervalDuration",
          "width": 120
        },
        {
          "body": [
            {
              "id": "u:588ff27d5f48",
              "items": {
                "id": "u:7ae7117368bf",
                "tpl": "<div>${item.reasonCodeDesc}:${item.maxReasonable}</div>",
                "type": "tpl"
              },
              "name": "detailList",
              "placeholder": "-",
              "type": "each"
            }
          ],
          "id": "u:e85690f52921",
          "label": "间歇原因和合理最大值（min）",
          "name": "detailList",
          "type": "custom",
          "width": 210
        },
        {
          "actions": [
            {
              "actionType": "dialog",
              "dialog": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "id": "u:0da1db99c355",
                    "label": "取消",
                    "type": "button"
                  },
                  {
                    "actionType": "confirm",
                    "id": "u:94ed169b1118",
                    "label": "确认",
                    "level": "primary",
                    "reload": "chainSearchPage",
                    "type": "button"
                  }
                ],
                "body": [
                  {
                    "actions": [],
                    "api": {
                      "messages": {
                        "success": "保存成功"
                      },
                      "url": "/lmsprod/admin/standard/interval/edit"
                    },
                    "body": [
                      {
                        "formItemProps": {
                          "hidden": true
                        },
                        "id": "u:2adff6eb71b4",
                        "label": "id",
                        "name": "id",
                        "type": "text"
                      },
                      {
                        "api": {
                          "adaptor": "return {\n              status: 200,\n              data: response.data.wareHouse\n            };",
                          "data": {
                            "bizType": "STANDARD_INTERVAL"
                          },
                          "method": "get",
                          "url": "/lmsnew/admin/select/options"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "value",
                            "value": "code"
                          },
                          "mode": "multiple",
                          "showSearch": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择仓库名称",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:14f2dc8af8c6",
                        "label": "仓库名称",
                        "name": "warehouseCodeList",
                        "type": "select"
                      },
                      {
                        "api": {
                          "method": "post",
                          "url": "/lmsnew/admin/standard/worklink/link/name"
                        },
                        "fieldProps": {
                          "fieldNames": {
                            "label": "linkName",
                            "value": "linkCode"
                          },
                          "mode": "multiple",
                          "showSearch": true
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请选择环节名称",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:78c51da30fc3",
                        "label": "环节名称",
                        "name": "linkCodeList",
                        "type": "select"
                      },
                      {
                        "fieldProps": {
                          "addonAfter": "min",
                          "min": 0,
                          "precision": 0
                        },
                        "formItemProps": {
                          "rules": [
                            {
                              "message": "请输入弹框时间",
                              "required": true
                            }
                          ]
                        },
                        "id": "u:85acadfdd7c2",
                        "label": "弹框时间",
                        "name": "popupIntervalDuration",
                        "type": "digit"
                      },
                      {
                        "fieldProps": {
                          "maxLength": 200,
                          "showCount": true
                        },
                        "id": "u:94bdf8ba3d05",
                        "label": "备注",
                        "name": "remark",
                        "type": "textarea"
                      },
                      {
                        "col": 2,
                        "columns": [
                          {
                            "api": {
                              "method": "get",
                              "url": "/lmsprod/admin/standard/interval/getReasonList"
                            },
                            "fieldProps": {
                              "disabled": true,
                              "fieldNames": {
                                "label": "reason",
                                "value": "code"
                              },
                              "mode": "single",
                              "showSearch": true
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "id": "u:41548463aaa7",
                            "label": "间歇原因",
                            "name": "reasonCode",
                            "type": "select"
                          },
                          {
                            "fieldProps": {
                              "addonAfter": "min",
                              "min": 1,
                              "precision": 0
                            },
                            "formItemProps": {
                              "rules": [
                                {
                                  "message": "请选择",
                                  "required": true
                                }
                              ]
                            },
                            "id": "u:3af2047319e3",
                            "label": "合理最大值",
                            "name": "maxReasonable",
                            "type": "digit"
                          },
                          {
                            "hiddenOn": true,
                            "id": "u:3af20473129e3",
                            "label": "",
                            "name": "detailId",
                            "type": "text"
                          }
                        ],
                        "copyIconProps": false,
                        "deleteIconProps": false,
                        "id": "u:f3d235d4e7db",
                        "label": "",
                        "max": "${intervalDetailList.length}",
                        "name": "intervalDetailList",
                        "type": "dynamicList"
                      }
                    ],
                    "id": "u:d5a5a0ce936d",
                    "onFormInit": "return {\n  ...values,\n  intervalDetailList: values?.detailList || []\n}",
                    "type": "form"
                  }
                ],
                "id": "u:738665d9bec6",
                "title": "编辑",
                "type": "dialog"
              },
              "id": "u:b929f26710a2",
              "label": "编辑",
              "level": "link",
              "type": "button"
            },
            {
              "actionType": "operateLog",
              "api": {
                "adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": {\n    \"rows\": response.data.contents,\n    \"count\": response.data.total\n  }\n}",
                "data": {
                  "bizKey": "${id}",
                  "bizType": "INTERVAL_REASON_CONFIG"
                },
                "method": "get",
                "url": "/lmsprod/admin/oplog/list"
              },
              "columns": [
                {
                  "hideInSearch": true,
                  "label": "操作人",
                  "name": "userName",
                  "width": 100
                },
                {
                  "hideInSearch": true,
                  "label": "操作时间",
                  "name": "operationTime",
                  "width": 180
                },
                {
                  "hideInSearch": true,
                  "label": "操作内容",
                  "name": "opContent"
                }
              ],
              "id": "u:a7d5367ad9ab",
              "label": "操作记录",
              "rowKey": "id",
              "type": "button"
            }
          ],
          "fixed": "right",
          "id": "u:042369368177",
          "label": "操作",
          "type": "operation",
          "width": 100
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "subTitle": "动态表单在弹框中使用",
  "title": "动态表单在弹框中使用 ",
  "type": "page"
}

==================================================

菜单项: 批量操作、列合并
时间: 2025-07-22T18:07:11.909719
内容长度: 7489 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "actionType": "ajax",
          "api": {
            "data": {
              "effectType": 0,
              "idList": "${selectedRows}"
            },
            "messages": {
              "failed": "修改失败",
              "success": "执行批量失效操作成功"
            },
            "method": "post",
            "requestAdaptor": "const idList = api.data.idList.map(item => item.id)\napi.data.idList = idList\nreturn api",
            "url": "/lean/api/evaluate/standard/takeEffect"
          },
          "confirmText": "确认将已选的  ${COUNT(selectedRowKeys)} 个检查项目状态变更为失效？",
          "danger": true,
          "disabled": "${ selectedRowKeys.length === 0}",
          "id": "u:357b4782df6e",
          "label": "批量失效",
          "level": "default",
          "reload": "chainSearchPage",
          "type": "button"
        },
        {
          "actionType": "ajax",
          "api": {
            "data": {
              "effectType": 1,
              "idList": "${selectedRowKeys}"
            },
            "messages": {
              "failed": "修改失败",
              "success": "执行批量生效操作成功"
            },
            "method": "post",
            "requestAdaptor": "const idList = api.data.idList.map(item => item.id)\napi.data.idList = idList\nreturn api",
            "url": "/lean/api/evaluate/standard/takeEffect"
          },
          "confirmText": "确认将已选的  ${COUNT(selectedRowKeys)} 个检查项目状态变更为生效？",
          "danger": false,
          "disabled": "${ selectedRowKeys.length === 0}",
          "id": "u:3e519b0b8798",
          "label": "批量生效",
          "level": "primary",
          "reload": "chainSearchPage",
          "type": "button"
        },
        {
          "id": "u:821c1753fc5f",
          "label": "导 出",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "download",
                  "api": {
                    "data": {
                      "evaluateStandardId": "${event.data}"
                    },
                    "method": "post",
                    "requestAdaptor": "api.data.evaluateStandardId = api.data.evaluateStandardId?.evaluateStandardId?.queryParam?.evaluateStandardId\n\nreturn api",
                    "url": "/lean/api/evaluate/standard/export"
                  }
                }
              ]
            }
          },
          "type": "action"
        }
      ],
      "api": {
        "adaptor": "const data = response?.data || []\nconst dataSource = []\ndata.forEach((info, index) => {\n  info.checked = false\n  if (info.items?.length) {\n    info.items.forEach((item, idx) => {\n      const obj = {\n        ...item,\n        key: item.id\n      }\n      delete obj.id;\n      dataSource.push({ ...info, ...obj, idx: idx + 1 })\n    })\n  } else {\n    const uniqueValue = Math.random().toString(36).substring(2) + Date.now().toString(36);\n    dataSource.push({ ...info, idx: index + 1, key: uniqueValue})\n  }\n})\nconst dataSourceN = dataSource.map(item => {\n  return {\n    ...item,\n    frequencyTypeDesc: item?.frequencyType?.desc,\n    issueStatusDesc: item?.issueStatus?.desc,\n    issueStatusValue: item?.issueStatus?.value,\n    statusDesc: item?.status?.desc,\n    statusValue: item?.status?.value,\n    taskFrequencyDesc: item?.taskFrequency?.desc,\n  }\n})\n\nreturn {\n  status: 200,\n  data: {\n    contents: dataSourceN,\n  }\n}",
        "method": "post",
        "url": "/lean/api/evaluate/standard/list"
      },
      "bordered": true,
      "formColumns": [
        {
          "api": {
            "method": "get",
            "url": "/lean/api/evaluate/standard/queryPrj"
          },
          "fieldProps": {
            "fieldNames": {
              "label": "evaluateProjectName",
              "value": "id"
            },
            "mode": "single",
            "showSearch": true
          },
          "id": "u:93d68b5844b4",
          "label": "评定项目",
          "name": "evaluateStandardId",
          "type": "select"
        }
      ],
      "id": "u:c61a67ebea74",
      "name": "chainSearchPage",
      "onEvent": {
        "submit": {
          "actions": [
            {
              "actionType": "setValue",
              "args": {
                "value": {
                  "evaluateStandardId": "${event.data}"
                }
              },
              "componentId": "u:3700901b5652"
            }
          ]
        }
      },
      "pagination": false,
      "rowKey": "key",
      "rowSelection": {
        "rowSpan": "${id}",
        "type": "checkbox"
      },
      "tableColumns": [
        {
          "id": "u:be59d1cb140c",
          "label": "评定项目",
          "name": "evaluateProjectName",
          "rowSpan": "${id}",
          "width": 140
        },
        {
          "id": "u:00ea819bb07b",
          "label": "序号",
          "name": "idx",
          "type": "text",
          "width": 60
        },
        {
          "id": "u:73cbf6acdb9f",
          "label": "评定内容",
          "name": "evaluateContent",
          "type": "text",
          "width": 380
        },
        {
          "id": "u:b88824a4e4e0",
          "label": "检查频次",
          "name": "frequencyTypeDesc",
          "rowSpan": "${id}",
          "type": "text",
          "width": 100
        },
        {
          "id": "u:29b8a661a625",
          "label": "任务下达频次",
          "name": "taskFrequencyDesc",
          "rowSpan": "${id}",
          "type": "text",
          "width": 120
        },
        {
          "id": "u:b07af36b130b",
          "label": "总分",
          "name": "totalPoint",
          "rowSpan": "${id}",
          "type": "text",
          "width": 80
        },
        {
          "id": "u:25ce63773904",
          "label": "扣分标准",
          "name": "point",
          "type": "text",
          "width": 100
        },
        {
          "id": "u:f769beb9849f",
          "label": "备注",
          "name": "markDesc",
          "rowSpan": "${id}",
          "type": "text"
        },
        {
          "id": "u:32f17b13d5ee",
          "label": "下达任务组织",
          "name": "orgNames",
          "rowSpan": "${id}",
          "type": "text"
        },
        {
          "actions": [
            {
              "actionType": "switch",
              "active": "${statusValue === 1}",
              "api": {
                "adaptor": "const {issueStatus} = api.data\nconsole.log(1111, issueStatus)\nreturn {\n  status: 200,\n  msg: issueStatus === 0 ? '执行失效操作成功' : '执行失效操作成功'\n}",
                "data": {
                  "effectType": "${statusValue === 1 ? 0 : 1}",
                  "id": "${id}"
                },
                "method": "post",
                "requestAdaptor": "api.data.idList=[api.data.id]\nreturn api",
                "url": "/lean/api/evaluate/standard/takeEffect"
              },
              "checkedChildren": "生效",
              "confirmText": "${IF(statusValue === 1, '要把该检查项目状态设置为失效吗？','要把该检查项目状态设置为生效吗？')}",
              "id": "u:c3a2190d712c",
              "type": "button",
              "unCheckedChildren": "失效"
            }
          ],
          "id": "u:41920fb1f5519",
          "label": "是否生效",
          "name": "statusValue",
          "rowSpan": "${id}",
          "type": "operation",
          "width": 120
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "title": " table跟随指定列规则合并、批量操作选择框按指定列合并",
  "type": "page"
}

==================================================

菜单项: 快捷查询
时间: 2025-07-22T18:07:36.934661
内容长度: 4387 字符
------------------------------
{
  "body": [
    {
      "actions": [],
      "api": {
        "data": {
          "auditStatus": "${filterType}",
          "queryType": 1
        },
        "filertEmptyString": true,
        "method": "post",
        "requestAdaptor": "const { spuIds } = api.data;\nif (spuIds) {\n  api.data.spuIds = spuIds?.match(/[^,，\\s]+/gm) ?? []\n}\n\nreturn api;",
        "url": "/commodity-admin/admin/merchant/spu-audit/page-list"
      },
      "formColumns": [
        {
          "fieldProps": {
            "placeholder": "最多查询20个，用“,”隔开"
          },
          "id": "u:7e0cd6bedb21",
          "label": "商品 ID",
          "name": "spuIds",
          "type": "text"
        }
      ],
      "headerTitle": {
        "title": [
          {
            "body": [
              {
                "fieldProps": {
                  "buttonStyle": "solid",
                  "optionType": "button",
                  "options": [
                    {
                      "label": "全部",
                      "value": ""
                    },
                    {
                      "label": "待审核",
                      "value": 2
                    },
                    {
                      "label": "待风控审核",
                      "value": 6
                    },
                    {
                      "label": "待法务审核",
                      "value": 7
                    },
                    {
                      "label": "审核成功",
                      "value": 3
                    },
                    {
                      "label": "审核驳回",
                      "value": 4
                    }
                  ],
                  "value": "${filterType}"
                },
                "id": "u:38970ded7138",
                "ignoreFormItem": true,
                "label": "单选框",
                "name": "radio",
                "onEvent": {
                  "change": {
                    "actions": [
                      {
                        "actionType": "setValue",
                        "args": {
                          "value": {
                            "auditStatus": "${event.data.value}",
                            "filterType": "${event.data.value}"
                          }
                        },
                        "componentId": "u:7003827c792e"
                      }
                    ]
                  }
                },
                "type": "radio"
              },
              {
                "id": "u:f73f4d946d15",
                "style": {
                  "color": "#666666",
                  "font-size": "14px",
                  "fontWeight": "normal",
                  "marginLeft": 8,
                  "textAlign": "left",
                  "whiteSpace": "normal"
                },
                "tpl": "符合筛选结果的共 ${total} 条，当前列表最多展示10000条\n",
                "type": "tpl",
                "wrapperComponent": "default"
              }
            ],
            "id": "u:b39b4f60414d",
            "type": "wrapper"
          }
        ]
      },
      "id": "u:7a1eeee0efba",
      "name": "chainSearchPage",
      "onEvent": {
        "submit": {
          "actions": [
            {
              "actionType": "setValue",
              "args": {
                "value": {
                  "total": "${queryResult.total}"
                }
              },
              "componentId": "u:7003827c792e"
            }
          ]
        }
      },
      "rowKey": "id",
      "tableAlertRender": false,
      "tableColumns": [
        {
          "id": "u:0e672d11e320",
          "label": "商品ID",
          "name": "spuId",
          "type": "text"
        },
        {
          "id": "u:0ce84ed24fde",
          "label": "商品名称",
          "name": "title",
          "type": "text"
        },
        {
          "body": [
            {
              "height": 100,
              "id": "u:2027371d3931",
              "mode": "cover",
              "src": "${logoUrl}",
              "type": "picture",
              "width": 100
            }
          ],
          "id": "u:2e18af229a6a",
          "label": "商品图片",
          "name": "logoUrl",
          "type": "custom"
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "data": {
    "auditStatus": 2,
    "filterType": 2,
    "total": 0
  },
  "id": "u:7003827c792e",
  "type": "page"
}

==================================================

菜单项: 简单的动态表头列表
时间: 2025-07-22T18:08:02.022791
内容长度: 3369 字符
------------------------------
{
  "body": [
    {
      "actions": [
        {
          "exportMod": "afterSaleOrderSimulateDataExport",
          "id": "u:9faa6ecdad79",
          "label": "导出",
          "type": "lmsExport"
        }
      ],
      "api": {
        "adaptor": "\nlet result = {\n  status: response.status || 200,\n  code: response.code,\n  msg: response.msg,\n  data: {\n    contents: []\n  }\n};\n\nif (payload) {\n  let headerConfig = payload.headerConfig;\n  let contentList = payload.contentList;\n\n  contentList.forEach(item => {\n    let content = {\n      warehouseName: item.warehouseName\n    };\n    headerConfig.forEach(header => {\n      content[header.prop] = item[header.prop];\n    });\n    result.data.contents.push(content);\n  });\n\n  if (payload.total) {\n    result.data.total = payload.total;\n  }\n  if (payload.pageNum) {\n    result.data.pageNum = payload.pageNum;\n  }\n}\n\nresult.data.tableColumns = [{\n  label: \"实际收货量\",\n  name: 1,\n  className: \"table-blue\",\n  children: payload.headerConfig.filter(item => item.type == 1).map(item => ({ \n    label: item.label, \n    className: \"table-blue\",\n    name: item.prop,\n    width: \"100px\"\n    }))\n}, {\n  label: \"预计收货量\",\n  name: 2,\n  className: \"table-yellow\",\n  children: payload.headerConfig.filter(item => item.type == 2).map(item => ({ \n    label: item.label,\n    className: \"table-yellow\",\n    name: item.prop,\n    width: \"100px\"\n    }))\n}];\n\nreturn result;\n",
        "method": "post",
        "silent": true,
        "url": "/sandop/admin/afterSale/futureOrder/simulate/list"
      },
      "formColumns": [
        {
          "api": {
            "adaptor": "\n  // 初始化result对象\n  let result = {\n    status: 200,\n    code: response.code,\n    msg: response.msg,\n    data: []\n  };\n\n  // 容错处理，判断payload是否有值\n  if (payload && payload.length > 0) {\n    // 遍历payload\n    payload.forEach(item => {\n      // 判断item是否为数组\n       result.data.push({\n          label: item,\n          value: item\n        });\n    });\n  }\n\n  // 返回处理好的数据\n  return result;\n",
            "method": "get",
            "url": "/sandop/admin/afterSale/futureOrder/simulate/versions"
          },
          "fieldProps": {},
          "formItemProps": {
            "rules": [
              {
                "message": "必须选择仿真版本",
                "required": true
              }
            ]
          },
          "id": "u:ae15ef2200f3",
          "initialValue": "",
          "label": "单量仿真版本",
          "name": "forecastVersion",
          "type": "select"
        }
      ],
      "id": "u:fdc692c19c38",
      "manualRequest": true,
      "name": "chainSearchPage",
      "rowKey": "id",
      "search": {
        "labelWidth": 120
      },
      "tableAlertRender": false,
      "tableColumns": [
        {
          "id": "u:6531b1c281cd",
          "label": "仓库",
          "name": "warehouseName",
          "type": "text",
          "width": 60
        }
      ],
      "tableColumnsConfig": {
        "addAfterName": "warehouseName",
        "data": "tableColumns"
      },
      "toolBarRender": true,
      "type": "chainSearchPage"
    }
  ],
  "id": "u:09d5a3dfcf51",
  "title": "简单的动态表头列表",
  "type": "page",
  "wrapperCustomStyle": {
    ".table-blue": {
      "background": "#f2fafa"
    },
    ".table-yellow": {
      "background": "#faf3ea"
    }
  }
}

==================================================

菜单项: 带行展开的列表页
时间: 2025-07-22T18:08:27.367690
内容长度: 8074 字符
------------------------------
{
  "body": [
    {
      "actions": [],
      "api": {
        "method": "post",
        "url": "/finance-promotion/fcm/backend/queryBudgetList"
      },
      "expandable": {
        "body": {
          "dataSource": "${expandedInfo.record.budgetPurposes}",
          "id": "u:f4e7fb1b0a35",
          "pagination": false,
          "rowKey": "id",
          "search": false,
          "tableColumns": [
            {
              "id": "u:2d46cf178064",
              "label": "预算用途ID",
              "name": "purposeId",
              "type": "text"
            },
            {
              "id": "u:f20f6d878382",
              "label": "申请人",
              "name": "applyPerson",
              "type": "text"
            },
            {
              "id": "u:91616522a424",
              "label": "预算用途名称",
              "name": "purposeName",
              "type": "text"
            },
            {
              "id": "u:2f19412fd1e9",
              "label": "申请时间",
              "name": "applyTime",
              "type": "text"
            },
            {
              "id": "u:894653025468",
              "label": "总预算金额",
              "name": "purposeAmount",
              "type": "text"
            },
            {
              "id": "u:d795f51db3d7",
              "label": "已发放预算",
              "name": "sendAmount",
              "type": "text"
            },
            {
              "id": "u:c6be96df66ea",
              "label": "剩余预算",
              "name": "leaveSendAmount",
              "type": "text"
            },
            {
              "id": "u:e56be0cb9ab1",
              "label": "预核销预算",
              "name": "preVerifyAmount",
              "type": "text"
            },
            {
              "id": "u:1ad2e3f587cd",
              "label": "剩余预核销预算",
              "name": "leavePreVerifyAmount",
              "type": "text"
            },
            {
              "id": "u:ef8774d6d7b4",
              "label": "核销预算",
              "name": "realVerifyAmount",
              "type": "text"
            },
            {
              "id": "u:cdbd0501fbc0",
              "label": "状态",
              "name": "purposeStatus",
              "type": "text"
            },
            {
              "id": "u:20242019be5d",
              "label": "使用活动类型",
              "name": "applicationType",
              "type": "text"
            },
            {
              "id": "u:ca4303b592d7",
              "label": "资产ID",
              "name": "awardIds",
              "type": "text",
              "width": 200
            },
            {
              "actions": [
                {
                  "id": "u:b907b7f6ea2e",
                  "label": "详情",
                  "type": "button"
                },
                {
                  "id": "u:c6fa6cfbf127",
                  "label": "复制",
                  "type": "button"
                }
              ],
              "id": "u:4dad5f25499c",
              "label": "操作",
              "type": "operation"
            }
          ],
          "toolBarRender": false,
          "type": "table"
        }
      },
      "formColumns": [
        {
          "id": "u:d9155a03bd74",
          "label": "预算池ID",
          "name": "budgetId",
          "type": "text"
        },
        {
          "id": "u:2334706522bb",
          "label": "预算池名称",
          "name": "budgetName",
          "type": "text"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${budgetType|objectToArray}"
            },
            "url": "/finance-promotion/fcm/backend/queryBudgetEnums"
          },
          "id": "u:76019bf0193e",
          "label": "预算类型",
          "name": "budgetTypeList",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${subsidyGoal|objectToArray}"
            },
            "url": "/finance-promotion/fcm/backend/queryBudgetEnums"
          },
          "id": "u:833531c867c5",
          "label": "补贴目的",
          "name": "subsidyPurpose",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${budgetStatus|objectToArray}"
            },
            "url": "/finance-promotion/fcm/backend/queryBudgetEnums"
          },
          "id": "u:ea59f85de394",
          "label": "状态",
          "name": "status",
          "type": "select"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${productType|objectToArray}"
            },
            "url": "/finance-promotion/fcm/backend/queryBudgetEnums"
          },
          "id": "u:959545b4511e",
          "label": "产品类型",
          "name": "productCode",
          "type": "select"
        }
      ],
      "id": "u:a3963992197d",
      "name": "chainSearchPage",
      "rowKey": "id",
      "tableColumns": [
        {
          "copyable": false,
          "fixed": "left",
          "id": "u:b4a9bb715634",
          "label": "预算池ID",
          "name": "budgetId",
          "type": "text"
        },
        {
          "id": "u:976ec902828b",
          "label": "预算池名称",
          "name": "budgetName",
          "type": "text"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${budgetType|objectToArray}"
            },
            "url": "/finance-promotion/fcm/backend/queryBudgetEnums"
          },
          "id": "u:e6e0c8401b83",
          "label": "预算类型",
          "name": "budgetTypeList",
          "type": "select"
        },
        {
          "id": "u:ef127ed42ec1",
          "label": "补贴目的",
          "name": "subsidyPurpose",
          "type": "text"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${budgetStatus|objectToArray}"
            },
            "url": "/finance-promotion/fcm/backend/queryBudgetEnums"
          },
          "id": "u:0553c9395208",
          "label": "状态",
          "name": "status",
          "type": "select"
        },
        {
          "id": "u:4731a702565e",
          "label": "「发放」总预算金额",
          "name": "topSendAmount",
          "tpl": "${topSendAmount|number}",
          "type": "text"
        },
        {
          "id": "u:8756c2a64936",
          "label": "「预核销」总预算金额",
          "name": "topUseAmount",
          "tpl": "${topUseAmount|number}",
          "type": "text"
        },
        {
          "api": {
            "method": "get",
            "responseData": {
              "&": "$$",
              "data": "${productType|objectToArray}"
            },
            "url": "/finance-promotion/fcm/backend/queryBudgetEnums"
          },
          "id": "u:e41c38ece6b2",
          "label": "产品类型",
          "name": "productCode",
          "type": "select"
        },
        {
          "body": [
            {
              "hiddenOn": "${needBindTime}",
              "id": "u:e5c92089ff23",
              "style": {
                "textAlign": "left"
              },
              "tpl": "无时效限制",
              "type": "tpl"
            },
            {
              "hiddenOn": "${!needBindTime}",
              "id": "u:dba7b4501656",
              "style": {
                "textAlign": "left"
              },
              "tpl": "${DATETOSTR(bindBegin, 'YYYY-MM-DD HH:mm:ss') + '~' + DATETOSTR(bindEnd, 'YYYY-MM-DD HH:mm:ss')}",
              "type": "tpl"
            }
          ],
          "id": "u:7503150c1749",
          "label": "预算池时间",
          "name": "needBindTime",
          "type": "custom",
          "width": 240
        }
      ],
      "type": "chainSearchPage"
    }
  ],
  "id": "u:3700901b5652",
  "type": "page"
}

==================================================

菜单项: 财务资金查询模板
时间: 2025-07-22T18:08:52.461206
内容长度: 10646 字符
------------------------------
{
  "body": [
    {
      "api": {
        "adaptor": "const convertEnum = (key, list) => {\n  return list?.reduce(\n    (result, v) => ({\n      ...result,\n      [v?.code || v?.desc]: convertLabel(v, key),\n    }),\n    {},\n  );\n};\nconst convertLabel = (item, enumKey) => {\n  if (enumKey === 'CURRENCY') {\n    return `${item?.code} - ${item?.desc}`;\n  }\n  if (enumKey === 'COUNTRY') {\n    return `${item?.desc} - ${item?.ext}`;\n  }\n  return item?.desc || '';\n};\nconst res = payload;\nconst resultMap = res?.optionResultMap || {};\nconst enumData = {};\n\nObject.entries(resultMap).forEach(([key, value]) => {\n  // 特殊处理币种\n  if (key === 'CURRENCY') {\n    enumData.CURRENCY = {\n      CNY: 'CNY - 人民币',\n      USD: 'USD - 美元',\n      EUR: 'EUR - 欧元',\n      HKD: 'HKD - 港币',\n      CNH: 'CNH - 中国离岸人民币',\n      GBP: 'GBP - 英镑',\n      JPY: 'JPY - 日元',\n      CAD: 'CAD - 加元',\n      AUD: 'AUD - 澳元',\n      KRW: 'KRW - 韩元',\n      SGD: 'SGD - 新加坡元',\n      ...convertEnum(key, value),\n    };\n  } else if (key === 'INST') {\n    // 特殊处理渠道简称，拆分俩枚举\n    enumData.INST = convertEnum(key, value);\n    enumData.INST_B = convertEnum(\n      key,\n      (value || []).filter((v) => v.ext === 'B'),\n    );\n    enumData.INST_T = convertEnum(\n      key,\n      (value || []).filter((v) => v.ext === 'T'),\n    );\n  } else {\n    enumData[key] = convertEnum(key, value);\n  }\n});\nreturn {\n  status: 200, // 200 表示请求成功，否则按错误处理\n  msg: \"请求成功\",\n  data: enumData,\n};",
        "data": {
          "appId": "gauss"
        },
        "method": "post",
        "requestAdaptor": "api.data.options = [\n  {\"optionKey\":\"SUBJECT\"},\n  {\"optionKey\":\"COUNTRY\"},\n  {\"optionKey\":\"CHANNEL_TYPE\"},\n  {\"optionKey\":\"INST\"},\n  {\"optionKey\":\"CURRENCY\"},\n  {\"optionKey\":\"YES_NO\"},\n  {\"optionKey\":\"ACCOUNT_PAYMENT\"},\n  {\"optionKey\":\"INST_ACCOUNT_STATUS\"},\n  {\"optionKey\":\"ALLOCATION_LEVEL\"},\n  {\"optionKey\":\"PAY_ACCESS_TYPE\"},\n  {\"optionKey\":\"PRODUCT_ACCESS_TYPE\"},\n  {\"optionKey\":\"REALTIME_BALANCE_ACCESS_TYPE\"},\n  {\"optionKey\":\"DAILY_BALANCE_ACCESS_TYPE\"},\n  {\"optionKey\":\"BALANCE\"},\n  {\"optionKey\":\"FLOW\"},\n  {\"optionKey\":\"PROOF_TYPE\"},\n  {\"optionKey\":\"OVER_SEA\"},\n  {\"optionKey\":\"VIRTUAL_FLAG\"},\n  {\"optionKey\":\"KEY_STATUS\"},\n  {\"optionKey\":\"INST_FULL_NAME\"},\n  {\"optionKey\":\"INST_PRODUCT\"},\n  {\"optionKey\":\"ACCOUNT_DEPOSIT\"},\n  {\"optionKey\":\"ACCOUNT_APPLY\"},\n  {\"optionKey\":\"ACCOUNT_APPROVAL_STATUS\"},\n  {\"optionKey\":\"BANK_BRANCH\"},\n  {\"optionKey\":\"ACCOUNT_LEVEL\"},\n  {\"optionKey\":\"RECON_PERIOD\"},\n  {\"optionKey\":\"INDIRECT_CHANNEL_NAME\"},\n  {\"optionKey\":\"ACCOUNT_KEY_TYPE\"},\n  {\"optionKey\":\"ACCOUNT_KEY_AUTHORITY\"}\n];\nreturn api;",
        "url": "/fund/finance-fund-base/base/option"
      },
      "body": [
        {
          "actions": [],
          "api": {
            "data": {
              "appId": "gauss"
            },
            "method": "post",
            "url": "/fund/file/operation/inst-account-flow/page"
          },
          "formColumns": [
            {
              "fieldProps": {
                "fieldNames": {},
                "options": "${SUBJECT|objectToArray}"
              },
              "id": "u:4e3da0fbe7c1",
              "label": "公司主体",
              "name": "subjectCode",
              "type": "select"
            },
            {
              "fieldProps": {
                "fieldNames": {},
                "options": "${INST|objectToArray}"
              },
              "id": "u:0836ea471e35",
              "label": "渠道简称",
              "name": "instCode",
              "type": "select"
            },
            {
              "fieldProps": {
                "options": [
                  {
                    "label": "处理中",
                    "value": "P"
                  },
                  {
                    "label": "处理成功",
                    "value": "S"
                  },
                  {
                    "label": "处理失败",
                    "value": "F"
                  },
                  {
                    "label": "待处理",
                    "value": "W"
                  }
                ]
              },
              "id": "u:5e66e3e9c440",
              "label": "处理状态",
              "name": "status",
              "type": "select"
            },
            {
              "api": {
                "adaptor": "\n  // 初始化返回结果\n  let result = {\n    status: response.status || 200,\n    code: response.code,\n    msg: response.msg,\n    data: []\n  };\n  console.log(payload.contents, 'payload.contents')\n  // 判断payload是否有值\n  if (payload.contents.length > 0) {\n      // 遍历contents\n    result.data = payload.contents.map((v) => {\n        return {\n          label:\n            `${v.instAccountRespDTO.instAccountName}(${v.instAccountRespDTO.instAccountNo})` ||\n            '',\n          value: v.instAccountRespDTO.instAccountNo || '',\n          data: v,\n        };\n      });\n      console.log(payload.contents, 'payload.contents')\n  }\n\n  // 返回处理好的数据\n  return result;\n",
                "data": {
                  "appId": "gauss",
                  "instAccountNo": "${keyWords}"
                },
                "method": "post",
                "sendOn": "${!!keyWords}",
                "url": "/fund/inst/account/pageQuery"
              },
              "fieldProps": {
                "fetchDataOnSearch": true,
                "showSearch": true
              },
              "id": "u:eac43475175c",
              "label": "账号",
              "name": "instAccountNo",
              "type": "select"
            },
            {
              "colSize": 1,
              "endName": "endTime",
              "fieldProps": {
                "format": "YYYY-MM-DD HH:mm:ss",
                "ranges": {}
              },
              "id": "u:a84978792e58",
              "initialValue": "${[DATETOSTR(DATEMODIFY(TODAY(), -1, 'month'), 'YYYY-MM-DD'), DATETOSTR(TODAY(), 'YYYY-MM-DD 23:59:59')]}",
              "label": "日期",
              "name": "confirmDate",
              "startName": "startTime",
              "type": "rangePicker"
            }
          ],
          "id": "u:f19cb8478eb6",
          "name": "chainSearchPage",
          "tableColumns": [
            {
              "fieldProps": {
                "options": []
              },
              "id": "u:9b787b03c015",
              "label": "机构账户名称",
              "name": "instAccountName",
              "type": "text"
            },
            {
              "fieldProps": {
                "options": "${SUBJECT|objectToArray}"
              },
              "id": "u:2239468bb6aa",
              "label": "主体编码",
              "name": "subjectCode",
              "type": "select"
            },
            {
              "fieldProps": {
                "options": "${INST|objectToArray}"
              },
              "id": "u:45d6dca1e2e6",
              "label": "银行（渠道简称）",
              "name": "instCode",
              "type": "select"
            },
            {
              "id": "u:e656dbfa2293",
              "label": "账户",
              "name": "instAccountNo",
              "type": "text"
            },
            {
              "fieldProps": {
                "options": [
                  {
                    "label": "境内",
                    "value": "Y"
                  },
                  {
                    "label": "境外",
                    "value": "N"
                  }
                ]
              },
              "id": "u:0d009c062af7",
              "label": "境外标识",
              "name": "overseas",
              "type": "select"
            },
            {
              "body": [
                {
                  "id": "u:5eafa365a8fa",
                  "label": "${fileName}",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "saveAs",
                          "api": "${fileUrl}",
                          "fileName": "${fileName}"
                        }
                      ]
                    }
                  },
                  "style": {
                    "padding": "0"
                  },
                  "type": "button"
                }
              ],
              "id": "u:f19bcf839cb2",
              "label": "文件名",
              "name": "fileName",
              "type": "custom"
            },
            {
              "fieldProps": {
                "options": [
                  {
                    "label": "处理中",
                    "value": "P"
                  },
                  {
                    "label": "处理成功",
                    "value": "S"
                  },
                  {
                    "label": "处理失败",
                    "value": "F"
                  },
                  {
                    "label": "待处理",
                    "value": "W"
                  }
                ]
              },
              "id": "u:1497ecfd6858",
              "label": "处理状态",
              "name": "status",
              "type": "select"
            },
            {
              "fieldProps": {
                "options": [
                  {
                    "label": "是",
                    "value": "Y"
                  },
                  {
                    "label": "否",
                    "value": "N"
                  }
                ]
              },
              "id": "u:2077964738ff",
              "label": "是否空流水",
              "name": "emptyFlowTag",
              "type": "select"
            },
            {
              "id": "u:46800723ea5d",
              "label": "创建人",
              "name": "creator",
              "type": "text"
            },
            {
              "id": "u:e77a2c484ee6",
              "label": "修改人",
              "name": "modifier",
              "type": "text"
            },
            {
              "id": "u:437ae1c01e5d",
              "label": "创建时间",
              "name": "gmtCreated",
              "type": "text"
            },
            {
              "id": "u:d26bf5212188",
              "label": "修改时间",
              "name": "gmtModified",
              "type": "text"
            }
          ],
          "type": "chainSearchPage"
        }
      ],
      "id": "u:e8ad898d9f6a",
      "name": "optionEnumDataService",
      "type": "service"
    }
  ],
  "id": "u:faad4a30cb25",
  "type": "page"
}

==================================================

