{"body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:43d435cd3cf0", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:77b46569b1ed", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"messages": {"success": "新增配置项成功"}, "url": "/qms/lab/v1/createConfig"}, "body": [{"formItemProps": {"rules": [{"message": "请输入检测项名称", "required": true}]}, "id": "u:2985a94acf89", "label": "检测项", "name": "detectName", "type": "text"}, {"id": "u:723f3e973c3d", "label": "适用标准", "name": "applyNorm", "type": "text"}, {"id": "u:60c9df7c54e6", "label": "描述", "name": "detectDescribe", "type": "textarea"}], "id": "u:3504e8889600", "type": "form", "wrapperCol": {"style": {"flex": 0.9}}}, "id": "u:67c02dd390d0", "title": "新增", "type": "dialog", "width": 700}, "id": "u:1ac7f07c86ac", "label": "新增", "level": "primary", "type": "button"}, {"actionType": "import", "api": {"data": {"bizType": "4"}, "method": "post", "url": "/qms/excel/import"}, "fileName": "检测项导入模板", "fileNameLabel": "导入模版", "fileUrl": "https://h5static.dewucdn.com/node-common/01d5431c-90f1-217b-4e83-af6f9556d84a.xlsx", "id": "u:62b113ceece6", "label": "导入", "level": "primary", "type": "button"}], "api": {"method": "post", "url": "/qms/lab/v1/queryConfigList"}, "formColumns": [{"api": {"adaptor": "return {\n  status: 200,\n  data: response.data?.items || []\n};       ", "method": "post", "requestAdaptor": "api.data.pageSize = 100;\napi.data.detectName = api.data.keyWords;\ndelete api.data.keyWords;\nreturn api;", "url": "/qms/lab/v1/queryConfigList"}, "fieldProps": {"fieldNames": {"label": "detectName", "value": "detectName"}}, "id": "u:d9155a03bd74", "label": "检测项", "name": "detectName", "type": "select"}], "id": "u:a3963992197d", "name": "chainSearchPage", "rowKey": "id", "tableColumns": [{"copyable": true, "fixed": "left", "id": "u:b4a9bb715634", "label": "检测项ID", "name": "id", "type": "text"}, {"id": "u:976ec902828b", "label": "检测项", "name": "detectName", "type": "text"}, {"id": "u:5b0cfc063328", "label": "适用标准", "name": "applyNorm", "type": "text"}, {"id": "u:c938fee8cc5a", "label": "描述", "name": "detectDescribe", "type": "text"}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:61bd16496566", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:b93bc81d8a2f", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"url": "/qms/lab/v1/editConfig"}, "body": [{"formItemProps": {"rules": [{"message": "请输入检测项名称", "required": true}]}, "id": "u:4395add20120", "label": "检测项", "name": "detectName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:e28ddb48babd", "label": "适用标准", "name": "applyNorm", "type": "text"}, {"id": "u:d609f2d66a51", "label": "描述", "name": "detectDescribe", "type": "textarea"}, {"formItemProps": {"hidden": true}, "id": "u:ea3e137a0e85", "label": "id", "name": "id", "type": "text"}], "id": "u:771d7648c347", "type": "form", "wrapperCol": {"style": {"flex": 0.9}}}, "id": "u:5475f8caa37d", "title": "编辑", "type": "dialog", "width": 700}, "id": "u:33a5ab78d907", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}"}, "messages": {"success": "删除成功"}, "method": "post", "url": "/qms/lab/v1/deleteConfig"}, "confirmText": "是否确认删除", "danger": true, "id": "u:19e9f24a8266", "label": "删除", "reload": "chainSearchPage", "type": "button"}], "fixed": "right", "id": "u:04f3880a8a88", "label": "操作", "type": "operation", "width": 150}], "type": "chainSearchPage"}], "id": "u:3700901b5652", "subTitle": "数据格式化、下拉枚举查询、新增、编辑、删除、非lms导入", "title": "简单列表页", "type": "page"}