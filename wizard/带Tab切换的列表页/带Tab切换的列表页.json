{"body": [{"id": "u:7ee583fdcc47", "items": [{"body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:541d948cf926", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:1c971cc36e90", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"messages": {"success": "新增配置项成功"}, "url": "/qms/lab/v1/createConfig"}, "body": [{"formItemProps": {"rules": [{"message": "请输入检测项名称", "required": true}]}, "id": "u:82674ea43861", "label": "检测项", "name": "detectName", "type": "text"}, {"id": "u:98ce68a346af", "label": "适用标准", "name": "applyNorm", "type": "text"}, {"id": "u:4b04301637b8", "label": "描述", "name": "detectDescribe", "type": "textarea"}], "id": "u:b6bf83a60d89", "type": "form", "wrapperCol": {"style": {"flex": 0.9}}}, "id": "u:ccf8195e1264", "title": "新增", "type": "dialog", "width": 700}, "id": "u:1ac7f07c86ac", "label": "新增", "level": "primary", "type": "button"}, {"actionType": "import", "api": {"data": {"bizType": "4"}, "method": "post", "url": "/qms/excel/import"}, "fileName": "检测项导入模板", "fileNameLabel": "导入模版", "fileUrl": "https://h5static.dewucdn.com/node-common/01d5431c-90f1-217b-4e83-af6f9556d84a.xlsx", "id": "u:62b113ceece6", "label": "导入", "level": "primary", "type": "button"}], "api": {"method": "post", "url": "/qms/lab/v1/queryConfigList"}, "formColumns": [{"api": {"adaptor": "return {\n  status: 200,\n  data: response.data?.items || []\n};       ", "method": "post", "requestAdaptor": "api.data.pageSize = 100;\napi.data.detectName = api.data.keyWords;\ndelete api.data.keyWords;\nreturn api;", "url": "/qms/lab/v1/queryConfigList"}, "fieldProps": {"fieldNames": {"label": "detectName", "value": "detectName"}}, "id": "u:d9155a03bd74", "label": "检测项", "name": "detectName", "type": "select"}], "id": "u:a3963992197d", "name": "chainSearchPage", "rowKey": "id", "tableColumns": [{"copyable": true, "fixed": "left", "id": "u:b4a9bb715634", "label": "检测项ID", "name": "id", "type": "text"}, {"id": "u:976ec902828b", "label": "检测项", "name": "detectName", "type": "text"}, {"id": "u:5b0cfc063328", "label": "适用标准", "name": "applyNorm", "type": "text"}, {"id": "u:c938fee8cc5a", "label": "描述", "name": "detectDescribe", "type": "text"}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:66cb8b0eb628", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:b67362d13d11", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": {"actions": [], "api": {"url": "/qms/lab/v1/editConfig"}, "body": [{"formItemProps": {"rules": [{"message": "请输入检测项名称", "required": true}]}, "id": "u:6cba31ffa1d4", "label": "检测项", "name": "detectName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:05416775c491", "label": "适用标准", "name": "applyNorm", "type": "text"}, {"id": "u:bf39776a3682", "label": "描述", "name": "detectDescribe", "type": "textarea"}, {"formItemProps": {"hidden": true}, "id": "u:16e760c0d792", "label": "id", "name": "id", "type": "text"}], "id": "u:ac0df68f7cf5", "type": "form", "wrapperCol": {"style": {"flex": 0.9}}}, "id": "u:f64322a45ace", "title": "编辑", "type": "dialog", "width": 700}, "id": "u:33a5ab78d907", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}"}, "messages": {"success": "删除成功"}, "method": "post", "url": "/qms/lab/v1/deleteConfig"}, "confirmText": "是否确认删除", "danger": true, "id": "u:19e9f24a8266", "label": "删除", "type": "button"}], "fixed": "right", "id": "u:04f3880a8a88", "label": "操作", "type": "operation", "width": 150}], "type": "chainSearchPage"}], "id": "u:b2c02e7aa577", "key": "1", "label": "简单列表页"}, {"body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:8f097adc80fe", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:b382051e7ac6", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$"}, "url": "/tms/admin/fulfillmentModeConfig/create"}, "body": [{"fieldProps": {"options": [{"label": "仓维度", "value": 0}, {"label": "商家维度", "value": 2}]}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:c61bb5ac6cec", "label": "配置维度", "name": "deliveryMode", "type": "radio"}, {"api": {"method": "get", "url": "/tms/admin/enum/repositories"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "labelInValue": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 0}", "id": "u:23a153df57d5", "label": "始发仓库", "labelName": "originWarehouseName", "name": "originWarehouse", "type": "select", "valueName": "originWarehouseCode"}, {"api": {"adaptor": "return {status: 200,data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))};", "method": "get", "requestAdaptor": "const data = api.data || {};\n  data.name = data?.keyWords || ''; \n  api.data = {...data} \n return api;", "sendOn": "${keyWords}", "url": "/merchant/merchant/getMerchantIdByNameEs"}, "fieldProps": {"fetchDataOnSearch": true, "fieldNames": {"label": "name", "value": "merchantId"}, "placeholder": "请输入后查询"}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:35a2ee435901", "label": "直发商家", "name": "merchantId", "type": "select"}, {"api": {"adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}", "data": {"lang": "zh", "maxDeep": 3}, "method": "post", "url": "/tms/admin/address/queryAddressTreeByDeep"}, "fieldProps": {"fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:5fb959eb1ca0", "label": "始发城市", "linkage": [{"event": "onChange", "setValues": {"originCityCode": "${data[1].code}", "originCityName": "${data[1].name}"}}], "name": "originCity", "type": "cascader"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode!= 2}", "id": "u:d7484fc82b40", "label": "城市Code", "name": "originCityCode", "type": "text"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode !=2}", "id": "u:e4e38a956d8b", "label": "城市名称", "name": "originCityName", "type": "text"}, {"api": {"adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }", "data": {"enumTypes": "transportFulfillModeEnum"}, "method": "post", "url": "/tms/admin/enum/listEnums"}, "id": "u:8dd726d9f0ab", "label": "运输履约模式", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "fulfillmentMode", "type": "select"}, {"api": {"url": "/tms/admin/enum/getLogistics"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:3101383b3836", "label": "承运商", "linkage": [{"event": "onChange", "resetKeys": ["logisticsProductCode", "customerCodeId"], "setValues": {"customerCodeId": null, "logisticsProductCode": null}}], "name": "logisticsCode", "type": "select"}, {"api": {"data": {"logisticsName": "${logisticsCode}"}, "sendOn": "${logisticsCode}", "url": "/tms/admin/enum/getLogisticsProduct"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:ad4bb5a73494", "label": "运输产品", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "logisticsProductCode", "type": "select"}, {"api": {"adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }", "data": {"logisticsCode": "${logisticsCode}", "logisticsProductCode": "${logisticsProductCode}", "transportFulfillmentMode": "${fulfillmentMode}"}, "method": "post", "sendOn": "fulfillmentMode && logisticsProductCode", "url": "/tms/admin/customerCode/getCustomerCodesV2"}, "formItemProps": {"rules": [{"message": "请选择", "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"}]}, "id": "u:b5a2405c516f", "label": "月结卡号", "name": "customerCodeId", "params": {"t": "${Date.now()}"}, "type": "select"}], "id": "u:6bc905badd74", "type": "form"}], "id": "u:462f993511f3", "title": "新增", "type": "dialog", "width": 700}, "id": "u:3a46389b7e47", "label": "新建", "level": "primary", "type": "button"}, {"fileName": "导入模板", "fileNameLabel": "模板下载", "fileUrl": "https://cdn.poizon.com/node-common/3a2d7339-93f6-74ae-6450-d15753638127.xlsx", "id": "u:9e003c343b6a", "importMod": "importTransportFulfillMode", "label": "导入", "type": "lmsImport"}, {"id": "u:99e4688cf8ec", "importMod": "importTransportFulfillMode", "label": "导入结果查询", "type": "lmsImportResult"}, {"actionType": "export", "api": {"data": {"exportMod": "TRANSPORT_FULFILLMENT_MODE_CONFIG"}, "method": "post", "url": "/lmsnew/reportCenter/report/v1/export"}, "bizType": "lmsnew", "id": "u:61bfcbed8fb1", "label": "导出", "level": "primary", "type": "button"}], "api": {"adaptor": "return {\n  'status': 200,\n  'msg': '请求成功',\n  'data': {\n    'rows': response.data.list,\n    'count': response.data.total\n  }\n}", "method": "post", "requestAdaptor": "const { originWarehouseCode, merchantId, fulfillmentMode, pageNum, pageSize, merchantIdV2 } = api.data\napi.data = {\n  pageNum, pageSize,\n  originWarehouseCodes: originWarehouseCode ? [originWarehouseCode] : undefined,\n  merchantIds: merchantIdV2 || merchantId ? [merchantIdV2 || merchantId] : undefined,\n  fulfillmentModes: fulfillmentMode ? [fulfillmentMode] : undefined\n}\nreturn api", "url": "/tms/admin/fulfillmentModeConfig/page"}, "formColumns": [{"api": {"method": "get", "url": "/tms/admin/enum/repositories"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}}, "id": "u:787ebe451b98", "label": "始发仓库", "name": "originWarehouseCode", "type": "select"}, {"api": {"adaptor": "return {\n  status: 200,\n  data: (response.data.contents || []).map(v=>({...v,name:`${v.name}(${v.merchantId})`}))\n};", "data": {"name": "${keyWords}"}, "method": "get", "sendOn": "${keyWords}", "url": "/merchant/merchant/getMerchantIdByNameEs"}, "fieldProps": {"fetchDataOnSearch": true, "fieldNames": {"label": "name", "value": "merchantId"}, "placeholder": "请输入后查询"}, "id": "u:ede536f16a2b", "label": "直发商家", "name": "merchantId", "type": "select"}, {"fieldProps": {"fetchDataOnSearch": true, "placeholder": "请输入后查询"}, "id": "u:cade04fc4f1b", "label": "直发商家ID", "name": "merchantIdV2", "type": "text"}, {"api": {"adaptor": "const mapData = response.mapData || {}\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }", "data": {"enumTypes": "transportFulfillModeEnum"}, "method": "post", "url": "/tms/admin/enum/listEnums"}, "id": "u:e3dd1d6b1ac2", "label": "运输履约模式", "name": "fulfillmentMode", "type": "select"}], "id": "u:c61a67ebea74", "name": "chainSearchPage", "revalidateOnFocus": true, "rowKey": "id", "search": {"defaultCollapsed": false, "labelWidth": 110}, "tableColumns": [{"id": "u:c44ce52946a2", "label": "始发仓库", "name": "originWarehouseName", "type": "text", "width": 160}, {"body": [{"body": [{"id": "u:c880a47168b5", "tpl": "${\"名称：\"+( sellerUserName || \"-\")}", "type": "tpl"}], "id": "u:452480073e84", "type": "container"}, {"body": [{"id": "u:220fd3928402", "tpl": "${\"ID：\"+ (sellerUserId || \"-\")}", "type": "tpl"}], "id": "u:25ddebd84582", "type": "container"}], "id": "u:bba80cf7ad52", "label": "直发商家", "name": "sellerUserName", "type": "custom", "width": 160}, {"id": "u:fce592c0a4f7", "label": "始发城市", "name": "originCityName", "type": "text"}, {"id": "u:b0790cc787d6", "label": "运输履约模式", "name": "fulfillmentModeName", "type": "text"}, {"id": "u:dd72e6c1d157", "label": "承运商", "name": "logisticsName", "type": "text"}, {"id": "u:71179d5c4f3e", "label": "运输产品", "name": "logisticsProductName", "type": "text"}, {"id": "u:1fb040dd3a05", "label": "月结卡号", "name": "customerCode", "type": "text"}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:675998f5b366", "label": "取消", "type": "button"}], "body": [{"actions": [], "body": [{"id": "u:44bb23bdcf86", "label": "配置维度", "name": "deliveryModeDesc", "proFieldProps": {"mode": "read"}, "type": "text"}, {"hiddenOn": "${deliveryMode != 0}", "id": "u:44bb23bdcf86", "label": "始发仓库", "name": "originWarehouseName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"hiddenOn": "${deliveryMode != 2}", "id": "u:44bb23bdcf86", "label": "直发商家", "name": "sellerUserName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"hiddenOn": "${deliveryMode != 2}", "id": "u:44bb23bdcf86", "label": "始发城市", "name": "originCityName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:44bb23bdcf86", "label": "运输履约模式", "name": "fulfillmentModeName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:44bb23bdcf86", "label": "承运商", "name": "logisticsName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:44bb23bdcf86", "label": "运输产品", "name": "logisticsProductName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:44bb23bdcf86", "label": "月结卡号", "name": "customerCode", "proFieldProps": {"mode": "read"}, "type": "text"}], "id": "u:57f639732290", "initApi": {"data": {"id": "${id}"}, "method": "get", "url": "/tms/admin/fulfillmentModeConfig/detail"}, "title": "表单", "type": "form"}], "id": "u:66eda46202cb", "title": "查看", "type": "dialog", "width": 600}, "id": "u:8eb56843f0ae", "label": "查看", "level": "link", "type": "button"}, {"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:488f3f745723", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:5a3ab578c259", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$"}, "url": "/tms/admin/fulfillmentModeConfig/edit"}, "body": [{"formItemProps": {"hidden": true}, "id": "u:d97d9fb9d7ac", "label": "ID", "name": "id", "proFieldProps": {"mode": "read"}, "type": "text"}, {"fieldProps": {"disabled": false, "options": [{"label": "仓维度", "value": 0}, {"label": "商家维度", "value": 2}]}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:2885354c4f38", "label": "配置维度", "name": "deliveryMode", "proFieldProps": {"mode": "read"}, "type": "radio"}, {"api": {"method": "get", "url": "/tms/admin/enum/repositories"}, "fieldProps": {"disabled": false, "fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "labelInValue": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 0}", "id": "u:9f5910067d23", "label": "始发仓库", "labelName": "originWarehouseName", "name": "originWarehouse", "proFieldProps": {"mode": "read"}, "type": "select", "valueName": "originWarehouseCode"}, {"fieldProps": {"disabled": false, "placeholder": "请输入后查询"}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:286124b7250c", "label": "直发商家", "name": "sellerUserName", "proFieldProps": {"mode": "read"}, "type": "select"}, {"api": {"adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": response.data.children\n}", "data": {"lang": "zh", "maxDeep": 3}, "method": "post", "url": "/tms/admin/address/queryAddressTreeByDeep"}, "fieldProps": {"disabled": false, "fieldNames": {"label": "name", "value": "code"}}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "hiddenOn": "${deliveryMode != 2}", "id": "u:6a4b66400afc", "label": "始发城市", "linkage": [{"event": "onChange", "setValues": {"originCityName": "${data[1].name}"}}], "name": "originCity", "proFieldProps": {"mode": "read"}, "type": "cascader"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode!= 2}", "id": "u:d7484fc82b40", "label": "城市Code", "name": "originCityCode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"formItemProps": {"hidden": true}, "hiddenOn": "${deliveryMode !=2}", "id": "u:e4e38a956d8b", "label": "城市名称", "name": "originCityName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"api": {"adaptor": "const mapData = response.mapData || {};\nconst data = mapData.transportFulfillModeEnum || []\nreturn { status: 200, data: data.filter(v => v.value !== '2') }", "data": {"enumTypes": "transportFulfillModeEnum"}, "method": "post", "url": "/tms/admin/enum/listEnums"}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:85e6986c3990", "label": "运输履约模式", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "fulfillmentMode", "type": "select"}, {"api": {"url": "/tms/admin/enum/getLogistics"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:bb64db5388be", "label": "承运商", "linkage": [{"event": "onChange", "resetKeys": ["logisticsProductCode", "customerCodeId"], "setValues": {"customerCodeId": null, "logisticsProductCode": null}}], "name": "logisticsCode", "type": "select"}, {"api": {"data": {"logisticsName": "${logisticsCode}"}, "sendOn": "${logisticsCode}", "url": "/tms/admin/enum/getLogisticsProduct"}, "fieldProps": {"fetchDataOnSearch": false, "fieldNames": {"label": "name", "value": "code"}, "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:f714a92015b9", "label": "运输产品", "linkage": [{"event": "onChange", "resetKeys": ["customerCodeId"], "setValues": {"customerCodeId": null}}], "name": "logisticsProductCode", "type": "select"}, {"api": {"adaptor": "var list = response.data ? response.data || [] : []\nvar data = list.map((v) => ({ label: [v.customerCode, v.customerCodeTag].join('-'), value: v.customerId }))\nreturn { status: 0, msg: \"请求成功\", data: data }", "data": {"logisticsCode": "${logisticsCode}", "logisticsProductCode": "${logisticsProductCode}", "transportFulfillmentMode": "${fulfillmentMode}"}, "method": "post", "sendOn": "fulfillmentMode && logisticsProductCode", "url": "/tms/admin/customerCode/getCustomerCodesV2"}, "formItemProps": {"rules": [{"message": "请选择", "required": "${fulfillmentMode==='3' || fulfillmentMode === '4'}"}]}, "id": "u:2a69a4e4366e", "label": "月结卡号", "name": "customerCodeId", "params": {"t": "${Date.now()}"}, "type": "select"}], "id": "u:b7373d316c7c", "initApi": {"adaptor": "const {originWarehouseCode, originWarehouseName, originProvinceCode,originCityCode, deliveryMode, customerCodeId, ...rest}=response.data;\nconst data = { deliveryMode, customerCodeId: customerCodeId === 0 ? null : customerCodeId, ...rest }\nif (deliveryMode === 0) {\n  data.originWarehouse = {label: originWarehouseName, value: originWarehouseCode}\n}\nif (deliveryMode === 2) {\n  data.originCity = [originProvinceCode, originCityCode]\n}\nreturn {status: 0, data: data }", "data": {"id": "${id}"}, "url": "/tms/admin/fulfillmentModeConfig/detail"}, "type": "form"}], "id": "u:8617c397bda5", "title": "编辑", "type": "dialog", "width": 700}, "id": "u:b929f26710a2", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"data": {"id": "${id}"}, "messages": {"failed": "删除失败", "success": "删除成功"}, "method": "post", "url": "/tms/admin/fulfillmentModeConfig/delete"}, "confirmText": "是否确认删除", "danger": true, "id": "u:4a6bb394b3a8", "label": "删除", "type": "button"}, {"id": "u:aae9dfe8aa96", "label": "操作记录", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:5b4ca903f79f", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:4256391b6fab", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"id": "${id || 148}", "tableName": "des_transport_fulfillment_mode_config"}, "method": "post", "url": "/tms/admin/log/getLogList"}, "formColumns": [], "id": "u:6d121dbf2ced", "label": "列表", "name": "chainSearchPage", "rowKey": "id", "search": false, "tableAlertRender": false, "tableColumns": [{"id": "u:af39355bb3e6", "label": "操作人", "name": "name", "type": "text", "width": 60}, {"id": "u:0b7a8e69e9b2", "label": "操作时间", "name": "modifyTime", "type": "text", "width": 80}, {"id": "u:e347e7c72d1d", "label": "操作类型", "name": "operationName", "type": "text", "width": 80}, {"id": "u:84501b7ff89d", "label": "操作内容", "name": "operationContent", "type": "text"}], "type": "chainSearchPage"}], "id": "u:8eda1238e1de", "title": "查看操作日志", "type": "dialog", "width": 900}}]}}, "type": "button"}], "fixed": "right", "id": "u:dd06d7b208ec", "label": "操作", "type": "operation", "width": 200}], "type": "chainSearchPage"}], "id": "u:c2b3b4a34c37", "key": "2", "label": "常规列表页"}], "tabPosition": "top", "tabsMode": "line", "type": "tabs"}], "id": "u:1439ae43043a", "title": "带Tab切换的列表页", "type": "page"}