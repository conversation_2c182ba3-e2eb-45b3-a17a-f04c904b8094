{"body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:adbc68305403", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:d846b8bf6fd2", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "新增成功"}, "url": "/lmsprod/admin/standard/interval/edit"}, "body": [{"api": {"adaptor": "return {\n              status: 200,\n              data: response.data.wareHouse\n            };", "data": {"bizType": "STANDARD_INTERVAL"}, "method": "get", "url": "/lmsnew/admin/select/options"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}, "mode": "multiple", "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择仓库名称", "required": true}]}, "id": "u:e3e696cb60c9", "label": "仓库名称", "name": "warehouseCodeList", "type": "select"}, {"api": {"method": "post", "url": "/lmsnew/admin/standard/worklink/link/name"}, "fieldProps": {"fieldNames": {"label": "linkName", "value": "linkCode"}, "mode": "multiple", "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择环节名称", "required": true}]}, "id": "u:705dc0a3cf0e", "label": "环节名称", "name": "linkCodeList", "type": "select"}, {"fieldProps": {"addonAfter": "min", "min": 0, "precision": 0}, "formItemProps": {"rules": [{"message": "请输入弹框时间", "required": true}]}, "id": "u:c2451f573cf8", "label": "弹框时间", "name": "popupIntervalDuration", "type": "digit"}, {"fieldProps": {"maxLength": 200, "showCount": true}, "id": "u:d32473fb430c", "label": "备注", "name": "remark", "type": "textarea"}, {"col": 2, "columns": [{"api": {"method": "get", "url": "/lmsprod/admin/standard/interval/getReasonList"}, "fieldProps": {"fieldNames": {"label": "reason", "value": "code"}, "mode": "single", "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:47b6784d8e21", "label": "间歇原因", "name": "reasonCode", "type": "select"}, {"fieldProps": {"addonAfter": "min", "min": 1, "precision": 0}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:fb920aa42f96", "label": "合理最大值", "name": "maxReasonable", "type": "digit"}], "id": "u:b5b479e730da", "label": "", "min": 1, "name": "intervalDetailList", "type": "dynamicList"}], "id": "u:7929a8cde223", "onFormInit": "return {\n intervalDetailList: [{}] \n}", "type": "form"}], "id": "u:d188e09ad03e", "title": "新增", "type": "dialog", "width": 660}, "id": "u:3a46389b7e47", "label": "新增", "level": "primary", "type": "button"}, {"data": {}, "exportMod": "standardIntervalExport", "id": "u:deab7ec21505", "label": "导出", "type": "lmsExport"}], "api": {"method": "post", "url": "/lmsprod/admin/standard/interval/list"}, "formColumns": [{"api": {"adaptor": "return {\n              status: 200,\n              data: response.data.wareHouse\n            };", "data": {"bizType": "STANDARD_INTERVAL"}, "method": "get", "url": "/lmsnew/admin/select/options"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}, "mode": "multiple", "showSearch": true}, "id": "u:93d68b5844b4", "label": "仓库名称", "name": "warehouseCodeList", "type": "select"}, {"api": {"method": "post", "url": "/lmsprod/admin/standard/worklink/link/name"}, "fieldProps": {"fieldNames": {"label": "linkName", "value": "linkCode"}, "mode": "multiple", "showSearch": true}, "id": "u:54ed10e932a2", "label": "环节名称", "name": "linkCodeList", "type": "select"}, {"api": {"adaptor": "return {\n              status: 200,\n              data: response.data.yesOrNo\n            };", "data": {"bizType": "STANDARD_INTERVAL"}, "method": "get", "url": "/lmsnew/admin/select/options"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}}, "id": "u:d6883a420793", "label": "是否开启", "name": "actived", "type": "select"}], "id": "u:c61a67ebea74", "name": "chainSearchPage", "rowKey": "id", "tableColumns": [{"id": "u:ae08901715d8", "label": "序号", "type": "index", "width": 80}, {"id": "u:be59d1cb140c", "label": "仓库名称", "name": "warehouseNames", "width": 120}, {"id": "u:e5db865fc1d6", "label": "环节名称", "name": "linkNames", "width": 120}, {"id": "u:5a00e4852f83", "label": "弹窗时间(min)", "name": "popupIntervalDuration", "width": 120}, {"body": [{"id": "u:588ff27d5f48", "items": {"id": "u:7ae7117368bf", "tpl": "<div>${item.reasonCodeDesc}:${item.maxReasonable}</div>", "type": "tpl"}, "name": "detailList", "placeholder": "-", "type": "each"}], "id": "u:e85690f52921", "label": "间歇原因和合理最大值（min）", "name": "detailList", "type": "custom", "width": 210}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:0da1db99c355", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:94ed169b1118", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "保存成功"}, "url": "/lmsprod/admin/standard/interval/edit"}, "body": [{"formItemProps": {"hidden": true}, "id": "u:2adff6eb71b4", "label": "id", "name": "id", "type": "text"}, {"api": {"adaptor": "return {\n              status: 200,\n              data: response.data.wareHouse\n            };", "data": {"bizType": "STANDARD_INTERVAL"}, "method": "get", "url": "/lmsnew/admin/select/options"}, "fieldProps": {"fieldNames": {"label": "value", "value": "code"}, "mode": "multiple", "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择仓库名称", "required": true}]}, "id": "u:14f2dc8af8c6", "label": "仓库名称", "name": "warehouseCodeList", "type": "select"}, {"api": {"method": "post", "url": "/lmsnew/admin/standard/worklink/link/name"}, "fieldProps": {"fieldNames": {"label": "linkName", "value": "linkCode"}, "mode": "multiple", "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择环节名称", "required": true}]}, "id": "u:78c51da30fc3", "label": "环节名称", "name": "linkCodeList", "type": "select"}, {"fieldProps": {"addonAfter": "min", "min": 0, "precision": 0}, "formItemProps": {"rules": [{"message": "请输入弹框时间", "required": true}]}, "id": "u:85acadfdd7c2", "label": "弹框时间", "name": "popupIntervalDuration", "type": "digit"}, {"fieldProps": {"maxLength": 200, "showCount": true}, "id": "u:94bdf8ba3d05", "label": "备注", "name": "remark", "type": "textarea"}, {"col": 2, "columns": [{"api": {"method": "get", "url": "/lmsprod/admin/standard/interval/getReasonList"}, "fieldProps": {"disabled": true, "fieldNames": {"label": "reason", "value": "code"}, "mode": "single", "showSearch": true}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:41548463aaa7", "label": "间歇原因", "name": "reasonCode", "type": "select"}, {"fieldProps": {"addonAfter": "min", "min": 1, "precision": 0}, "formItemProps": {"rules": [{"message": "请选择", "required": true}]}, "id": "u:3af2047319e3", "label": "合理最大值", "name": "maxReasonable", "type": "digit"}, {"hiddenOn": true, "id": "u:3af20473129e3", "label": "", "name": "detailId", "type": "text"}], "copyIconProps": false, "deleteIconProps": false, "id": "u:f3d235d4e7db", "label": "", "max": "${intervalDetailList.length}", "name": "intervalDetailList", "type": "dynamicList"}], "id": "u:d5a5a0ce936d", "onFormInit": "return {\n  ...values,\n  intervalDetailList: values?.detailList || []\n}", "type": "form"}], "id": "u:738665d9bec6", "title": "编辑", "type": "dialog"}, "id": "u:b929f26710a2", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "operateLog", "api": {"adaptor": "return {\n  \"status\": 0,\n  \"msg\": \"请求成功\",\n  \"data\": {\n    \"rows\": response.data.contents,\n    \"count\": response.data.total\n  }\n}", "data": {"bizKey": "${id}", "bizType": "INTERVAL_REASON_CONFIG"}, "method": "get", "url": "/lmsprod/admin/oplog/list"}, "columns": [{"hideInSearch": true, "label": "操作人", "name": "userName", "width": 100}, {"hideInSearch": true, "label": "操作时间", "name": "operationTime", "width": 180}, {"hideInSearch": true, "label": "操作内容", "name": "opContent"}], "id": "u:a7d5367ad9ab", "label": "操作记录", "rowKey": "id", "type": "button"}], "fixed": "right", "id": "u:042369368177", "label": "操作", "type": "operation", "width": 100}], "type": "chainSearchPage"}], "id": "u:3700901b5652", "subTitle": "动态表单在弹框中使用", "title": "动态表单在弹框中使用 ", "type": "page"}