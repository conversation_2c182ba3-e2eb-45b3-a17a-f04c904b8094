{"body": [{"api": {"method": "post", "requestAdaptor": "const {pageNum, pageSize, ...rest} = api.data\napi.data = {\n  extendParam: {\n    page: pageNum,\n    limit: pageSize,\n    isUpcSorting: 0,\n    rmaRecallStatus: 2,\n    type: 'rma_request',\n    uniqueCodes: []\n  },\n  reportName: 'rmaAdminList'\n}\n\nreturn api", "url": "/pink/admin/report/query"}, "formColumns": [{"id": "u:59a32ec0303e", "label": "示例字段", "name": "warehouseCode1", "type": "text"}], "id": "u:8b3851074eb2", "loopApi": {"adaptor": "const data = JSON.parse(response.data)\nconst {queryStatus, queryResult = {}, ...rest} = data\nlet isNeedLoop = false\nif (queryStatus === 0) {\n  isNeedLoop = true\n}\nif (queryStatus === 1) {\n  isNeedLoop = false\n}\nconst body = { \n  ...rest,\n  ...queryResult,\n  isNeedLoop,\n}\nreturn {\n  status: 200,\n  data: body\n}", "data": {"taskId": "${taskId}"}, "method": "get", "url": "/pink/admin/report/queryResult"}, "manualRequest": true, "name": "chainSearchPage", "rowKey": "id", "search": {"searchText": "点击创建查询任务"}, "tableColumns": [{"id": "u:e6ba95d4a941", "label": "预测日期", "name": "buyerDeliveryTime", "type": "text", "width": 120}, {"id": "u:85263292b138", "label": "类型", "name": "bizTypeText", "type": "text", "width": 100}], "type": "chainSearchPage"}], "id": "u:7003827c792e", "subTitle": "数据查询任务化之后，支持轮询任务ID进行数据查询，并动态展示列表字段", "title": "轮询查询列表页", "type": "page"}