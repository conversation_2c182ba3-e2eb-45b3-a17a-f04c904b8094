{"body": [{"actions": [{"disabled": "${ selectedRowKeys.length === 0}", "id": "u:7adb48d8da7b", "label": "批量操作", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:bd4cd6480d96", "label": "取消", "type": "button"}, {"actionType": "confirm", "confirmText": "确定批量修改吗？", "id": "u:80ec033801c8", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$", "ids": "${selectedRowKeys}"}, "method": "post", "url": "/mock/form/saveForm"}, "body": [{"id": "u:cba4c2a75e22", "label": "文本", "name": "text", "type": "text"}], "id": "u:cd961e113b09", "type": "form"}], "id": "u:8e1dbc3c2d5a", "title": "批量修改", "type": "dialog"}}]}}, "type": "button"}, {"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:a4c1aa593607", "label": "取消", "type": "button"}, {"actionType": "submit", "id": "u:fbe9237dbfcd", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "新增成功"}, "url": "/wcs/admin/device/tree/add"}, "body": [{"api": {"method": "get", "url": "/wcs/admin/device/tree/queryAllDeviceNodeType"}, "fieldProps": {"fieldNames": {"label": "deviceNodeTypeName", "value": "deviceNodeTypeCode"}}, "formItemProps": {"rules": [{"message": "请选择节点类型", "required": true}]}, "id": "u:1a52002c830f", "label": "节点类型", "name": "nodeType", "type": "select"}, {"fieldProps": {"maxLength": 32}, "formItemProps": {"rules": [{"message": "请输入节点名称", "required": true}]}, "id": "u:ffc2907f453f", "label": "节点名称", "name": "nodeName", "type": "text"}, {"fieldProps": {"maxLength": 32}, "formItemProps": {"rules": [{"message": "请输入节点编码", "required": true}]}, "id": "u:37821659b308", "label": "节点编码", "name": "barcode", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:5ceae3c17423", "label": "设备位置", "name": "location", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:f21e4a54e221", "label": "设备区域", "name": "region", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:693840b209d4", "label": "所属电柜", "name": "cabinet", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:b69b5d733ea3", "label": "扩展信息", "name": "feature", "type": "textarea"}, {"id": "u:781c982de64b", "label": "上级节点名称", "name": "parentName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:ede282e6fdb7", "label": "上级节点编码", "name": "parentId", "proFieldProps": {"mode": "read"}, "type": "text"}], "id": "u:842df78b33b7", "onFormInit": "var parentObj = values.queryParam.parentObj;\nreturn {\n  parentId: parentObj.value,\n  parentName: parentObj.label\n}\n", "type": "form"}], "id": "u:f1167ab898bd", "title": "添加节点", "type": "dialog", "width": 700}, "id": "u:87954eb4a1b3", "label": "添加节点", "level": "primary", "type": "button"}, {"fileName": "设备树节点批量导入模板.xlsx", "fileNameLabel": "设备树节点批量导入模板", "fileUrl": "https://t1-h5cdn.dewu.net/supply-chain/scm-cdn/10604095/20231212-8455de936a2c83f3.xlsx", "id": "u:32bc075fc56e", "importMod": "importDeviceTreeNode", "label": "导入", "type": "lmsImport"}, {"id": "u:80011f60c80b", "importMod": "importDeviceTreeNode", "label": "导入结果查询", "type": "lmsImportResult"}, {"exportMod": "exportDeviceTree", "id": "u:a454c28f8fb1", "type": "lmsExport"}], "api": {"data": {}, "method": "post", "requestAdaptor": "if(api.data.nodeId && api.data.nodeId.label) {\n  api.data.parentObj = api.data.nodeId;\n  api.data.nodeId = api.data.nodeId.value;\n}\nreturn api", "url": "/wcs/admin/device/tree/pageQuery"}, "formColumns": [{"id": "u:9b3d965998ff", "label": "节点名称/编码", "labelWidth": "200", "name": "nodeNameOrCode", "type": "text"}, {"api": {"data": {"nodeId": ""}, "method": "post", "url": "/wcs/admin/device/tree/queryNextDeviceTree"}, "fieldProps": {"allowClear": true, "fieldNames": {"label": "nodeName", "value": "id"}, "labelInValue": true, "placeholder": "请选择"}, "id": "u:358b80a00be5", "label": "节点树", "loadApi": {"data": {"nodeId": "${id}"}, "method": "post", "url": "/wcs/admin/device/tree/queryNextDeviceTree"}, "name": "nodeId", "type": "treeSelect"}], "id": "u:644132d2ddcc", "name": "chainSearchPage", "rowKey": "id", "rowSelection": {"type": "checkbox"}, "search": {"labelWidth": 110}, "tableColumns": [{"fixed": "left", "id": "u:76d17b29da35", "label": "节点名称", "name": "nodeName", "type": "text"}, {"id": "u:d74935c2c467", "label": "节点编码（文本展示）", "name": "nodeCode", "type": "text"}, {"body": [{"body": [{"id": "u:7a8aff8c3ade", "style": {"textAlign": "left"}, "tpl": "${nodeName}", "type": "tpl"}], "content": [{"body": [{"column": 2, "columns": [{"copyable": true, "id": "u:849238135780", "label": "节点名称", "name": "nodeName", "type": "text"}, {"fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:1c7ddcd3c422", "label": "节点编码", "name": "nodeCode", "type": "select"}, {"body": [{"dataSource": ["https://cdn.dewu.com/node-common/cbfbc33d-b829-c754-8b09-a855c557b235-960-960.png", "https://cdn.dewu.com/node-common/1d099b0c-d215-e67c-9171-f010af4f51a4-960-960.png"], "height": 100, "id": "u:a2c613a594b9", "type": "pictures", "width": 100}], "id": "u:43f388b9f4ce", "label": "图片", "name": "imageList", "span": 2, "type": "custom"}], "id": "u:5591b7928576", "layout": "vertical", "size": "small", "title": [{"id": "u:7b20af60ef03", "style": {"textAlign": "left"}, "tpl": "详细信息", "type": "tpl"}], "type": "descriptions"}], "id": "u:f35af0c9fb79", "style": {"width": "300px"}, "type": "wrapper"}], "id": "u:598b2b47afd9", "type": "popover"}], "id": "u:35ec075d884c", "label": "浮层展示更多", "name": "test1", "type": "custom"}, {"id": "u:262909ed6127", "label": "节点类型", "name": "nodeTypeDesc", "type": "text"}, {"body": [{"color": "${hideDesc === '作废' ? 'orange': 'green'}", "id": "u:3b449f5b9859", "label": "${hideDesc}", "type": "tag"}], "id": "u:ade7751b7edb", "label": "节点状态", "name": "hideDesc", "type": "custom"}, {"id": "u:a03b915e9865", "label": "上级节点名称", "name": "parentNodeName", "type": "text"}, {"id": "u:f7dd1cf51de9", "label": "上级节点编码", "name": "parentBarcode", "type": "text"}, {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:aa43185e33f2", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:090c78b00fc4", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "保存成功"}, "url": "/wcs/admin/device/tree/modify"}, "body": [{"api": {"method": "get", "url": "/wcs/admin/device/tree/queryAllDeviceNodeType"}, "fieldProps": {"disabled": true, "fieldNames": {"label": "deviceNodeTypeName", "value": "deviceNodeTypeCode"}}, "formItemProps": {"rules": [{"message": "请选择节点类型", "required": true}]}, "id": "u:00643ec12aee", "label": "节点类型", "name": "nodeType", "type": "select"}, {"fieldProps": {"maxLength": 32}, "formItemProps": {"rules": [{"message": "请输入节点名称", "required": true}]}, "id": "u:b6235bedcdf2", "label": "节点名称", "name": "nodeName", "type": "text"}, {"fieldProps": {"maxLength": 32}, "formItemProps": {"rules": [{"message": "请输入节点编码", "required": true}]}, "id": "u:bebe03bea9a1", "label": "节点编码", "name": "barcode", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:05351ffc84e6", "label": "设备位置", "name": "location", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:7ccf5ea7e2f0", "label": "设备区域", "name": "region", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:4ecbc87a0d44", "label": "所属电柜", "name": "cabinet", "type": "text"}, {"hiddenOn": "${nodeType != 'DEVICE' && nodeType != 'SUB_DEVICE'}", "id": "u:c74df1922a2b", "label": "扩展信息", "name": "feature", "type": "textarea"}, {"id": "u:0defda7c1919", "label": "上级节点名称", "name": "parentNodeName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:50c5fc60d58f", "label": "上级节点编码", "name": "parentId", "proFieldProps": {"mode": "read"}, "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:4d36b526dbfb", "label": "nodeId", "name": "nodeId", "type": "text"}], "id": "u:cc955a411288", "initApi": {"adaptor": "if(response.data.id) {\n  response.data.nodeId = response.data.id\n}\nreturn response", "data": {"nodeId": "${id}"}, "method": "post", "url": "/wcs/admin/device/tree/queryTreeDetailById"}, "onFormInit": "if(values.id) {\n  values.nodeId = values.id\n}\nreturn values", "type": "form"}], "id": "u:9f09a2dd1c15", "title": "编辑节点", "type": "dialog", "width": 700}, "id": "u:b1e201c58b20", "label": "编辑", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"data": {"nodeId": "${id}"}, "messages": {"failed": "作废失败", "success": "作废成功"}, "method": "post", "url": "/wcs/admin/device/tree/hideOrShow"}, "confirmText": "是否确认作废", "danger": true, "hiddenOn": "${hide != 0}", "id": "u:5f81c62318f9", "label": "作废", "type": "button"}, {"actionType": "ajax", "api": {"data": {"nodeId": "${id}"}, "messages": {"failed": "启用失败", "success": "启用成功"}, "method": "post", "url": "/wcs/admin/device/tree/hideOrShow"}, "confirmText": "是否确认启用", "danger": true, "hiddenOn": "${hide != 1}", "id": "u:5f81c62318f3", "label": "启用", "type": "button"}, {"actionType": "dialog", "dialog": {"body": {"actions": [], "api": {"data": {"nodeId": "${id}"}, "method": "POST", "url": "/wcs/admin/device/tree/queryTreeDetailById"}, "body": [{"id": "u:e15e1402aacd", "label": "节点编号", "name": "barcode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:7e64801b0ed4", "label": "节点类型", "name": "nodeTypeDesc", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:c4989894ee81", "label": "节点名称", "name": "nodeName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:108c1ee4adcd", "label": "上级节点名称", "name": "parentNodeName", "proFieldProps": {"mode": "read"}, "type": "text", "visibleOn": "${!nodeType === 'WAREHOUSE'}"}, {"id": "u:bfe47fd63eb0", "label": "上级节点编号", "name": "parentBarcode", "proFieldProps": {"mode": "read"}, "type": "text", "visibleOn": "${!nodeType === 'WAREHOUSE'}"}, {"id": "u:becc81231ec3", "label": "所属仓库", "name": "warehouseCode", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:d1c864a1c271", "label": "节点描述", "name": "nodeDesc", "proFieldProps": {"mode": "read"}, "type": "text"}, {"id": "u:5f019001f528", "label": "设备位置", "name": "location", "proFieldProps": {"mode": "read"}, "type": "text", "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"}, {"id": "u:48a09acbfe05", "label": "设备区域", "name": "region", "proFieldProps": {"mode": "read"}, "type": "text", "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"}, {"id": "u:e04424c4ebc1", "label": "所属电柜", "name": "cabinet", "proFieldProps": {"mode": "read"}, "type": "text", "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"}, {"id": "u:54a75b6804df", "label": "扩展信息", "name": "feature", "proFieldProps": {"mode": "read"}, "type": "text", "visibleOn": "${nodeType === 'DEVICE' || nodeType === 'SUB_DEVICE'}"}], "column": 2, "id": "loadDetail", "type": "form"}, "id": "u:b7323b4b54fe", "title": "节点详情", "type": "dialog"}, "id": "u:e4d5ff9413fa", "label": "详情", "level": "link", "type": "button"}, {"id": "u:364efd916a48", "label": "页面跳转", "onEvent": {"click": {"actions": [{"actionType": "link", "args": {"link": "/ww/bb/cc", "params": {"id": "${id}"}}}]}}, "type": "button"}, {"id": "u:2ba301b67e75", "label": "操作记录", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:c83b5d2735a5", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:b2f10786df75", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"targetId": "${id}", "type": "11"}, "method": "POST", "url": "/wms/admin/wcs/base/new/operatelog/query"}, "formColumns": [], "id": "u:a3c54ad92cf7", "name": "logList", "rowKey": "id", "search": false, "tableAlertRender": false, "tableColumns": [{"hideInSearch": true, "id": "u:11db14a10f62", "label": "操作人", "name": "editor", "width": 100}, {"hideInSearch": true, "id": "u:722d34b2513d", "label": "操作时间", "name": "editTime", "width": 120}, {"hideInSearch": true, "id": "u:798b16276edf", "label": "操作内容", "name": "content"}], "type": "chainSearchPage"}], "id": "u:5fc83871868e", "title": "操作记录", "type": "dialog", "width": 900}}]}}, "type": "button"}], "fixed": "right", "id": "u:91472d6e0fdd", "label": "操作", "type": "operation", "width": 200}], "type": "chainSearchPage"}], "id": "u:7003827c792e", "name": "", "subTitle": "查询条件标签名宽度设置、查询结果透传给新增模块使用、动态获取编辑数据、初始逻辑处理、参数映射", "title": "功能齐全列表页", "type": "page"}