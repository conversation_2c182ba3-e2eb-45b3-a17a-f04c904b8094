{"body": [{"api": {"data": {"appCode": "scp-cost-interfaces"}, "method": "post", "requestAdaptor": "\n        if(api.data.rangeTime) {\n          api.data.startTime = api.data.rangeTime[0];\n          api.data.endTime = api.data.rangeTime[1];\n        }\n        delete api.data.rangeTime;\n        return api;", "url": "/lmsnew/reportCenter/report/v1/downloadList"}, "formColumns": [{"id": "u:3c3bc1f093f0", "initialValue": "-3days,today", "label": "时间范围", "name": "rangeTime", "type": "rangePicker"}], "id": "u:bb52b76cba2e", "name": "chainSearchPage", "rowKey": "taskId", "search": {"span": 8}, "tableColumns": [{"fixed": "left", "id": "u:b22b59a60e7c", "label": "模块名称", "name": "exportModName", "type": "text", "width": 120}, {"id": "u:7b348f0fa413", "label": "文件名称", "name": "fileName", "type": "text"}, {"id": "u:375db104ffdc", "label": "任务状态", "name": "exportStatusDesc", "type": "text", "width": 80}, {"fieldProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "id": "u:8f6e8899b359", "label": "创建时间", "name": "createTime", "type": "date"}, {"id": "u:2f13584ea73d", "label": "报表生成时间", "name": "generatedTime", "type": "text"}, {"id": "u:86f1768ce1a3", "label": "过期时间", "name": "expireDesc", "type": "text"}, {"id": "u:61f2de311ec8", "label": "结果明细", "name": "exportMsg", "type": "text", "width": 80}, {"actions": [{"actionType": "ajax", "api": {"adaptor": "\nwindow.open(response.data);\nreturn {\n  status: 200,\n  msg: '下载文件成功',\n  data: response.data\n};\n              ", "data": {"taskId": "${taskId}"}, "method": "post", "url": "/lmsnew/reportCenter/report/v1/download"}, "hiddenOn": "${expired || exportStatus == 0}", "id": "u:d2dd43979328", "label": "下载", "level": "link", "type": "button"}, {"actionType": "ajax", "api": {"adaptor": "return {\n  status: 200,\n  msg: '重新导出完成',\n  data: response.data\n};\n              ", "data": {"taskId": "${taskId}"}, "method": "post", "url": "/lmsnew/reportCenter/report/v1/retry"}, "hiddenOn": "${!expired}", "id": "u:bd4df7050abe", "label": "重新导出", "level": "link", "type": "button"}], "fixed": "right", "id": "u:240351be7424", "label": "操作", "type": "operation", "width": 150}], "type": "chainSearchPage"}], "id": "u:ae52653333e0", "subTitle": "数据格式化、下拉枚举查询、新增、编辑、删除、导入", "title": "常用下载中心列表页", "type": "page"}