{"type": "drawer", "actions": [], "body": [{"body": [{"column": 2, "columns": [{"copyable": true, "id": "u:23028f25dbe9", "label": "表名", "name": "tableName", "type": "text"}, {"copyable": true, "id": "u:datasource_name", "label": "数据源名称", "name": "datasourceName", "type": "text"}, {"copyable": true, "id": "u:database_name", "label": "数据库名称", "name": "databaseName", "type": "text"}, {"body": [{"id": "u:3dab5ae16f4e", "language": "sql", "type": "code", "value": "${condition || ''}"}], "copyable": true, "ellipsis": false, "fieldProps": {"options": [{"label": "全部", "value": "all"}, {"label": "解决中", "value": "pending"}, {"label": "已解决", "value": "resolved"}]}, "id": "u:248826665e5a", "label": "查询条件", "name": "conditions", "type": "custom"}, {"copyable": false, "ellipsis": true, "hiddenOn": "${!root}", "id": "u:2306da3b22f4", "label": "索引列", "name": "indexColumn", "type": "text", "width": 100}, {"fieldProps": {"options": [{"label": "升序", "value": "ASC"}, {"label": "降序", "value": "DESC"}]}, "hiddenOn": "${!root}", "id": "u:c34205813ee1", "label": "排序", "name": "orderBy", "type": "select"}, {"copyable": true, "hiddenOn": "${!root}", "id": "u:836d2854e392", "label": "索引值范围", "name": "indexValue", "tooltip": "索引值用于扫表，限制索引字段扫描的范围", "type": "text"}, {"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "id": "u:3bde315d0cd3", "label": "是否归档", "name": "isArchive", "type": "select"}, {"id": "u:8056f38f1a73", "label": "主键", "name": "primaryKeyColumn", "type": "text"}, {"fieldProps": {"options": [{"label": "不过滤", "value": 0}, {"label": "过滤", "value": 1}]}, "id": "u:54ffc818a743", "label": "默认值过滤", "name": "filterDefaultValue", "span": 2, "type": "select"}, {"copyable": true, "id": "u:node_id", "label": "节点ID", "name": "nodeId", "type": "text"}, {"copyable": true, "id": "u:parent_node_id", "label": "父节点ID", "name": "parentNodeId", "type": "text"}, {"fieldProps": {"options": [{"label": "先归档子节点", "value": 1}, {"label": "先归档父节点", "value": 2}]}, "hiddenOn": "${root}", "id": "u:8c3e4ad8b5f7", "initialValue": 1, "label": "归档顺序", "name": "archiveType", "type": "select"}, {"hiddenOn": "${root}", "id": "u:427eb1483e48", "label": "关联关系", "name": "relations", "span": 3, "type": "textarea"}, {"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "hiddenOn": "${!root}", "id": "u:7cd672982bc4", "label": "索引扫描", "name": "enableScan", "type": "select"}, {"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "id": "u:0e5b58473e25", "label": "启用分表", "name": "shardingEnabled", "type": "select"}, {"id": "u:e56afb3a12a8", "label": "分表表达式", "name": "shardingExpression", "type": "text"}, {"id": "u:6e0e7edefba4", "label": "分表插件类", "name": "shardingPluginClass", "type": "text"}, {"id": "u:221188873957", "label": "分表字段", "name": "shardingField", "type": "text"}, {"id": "u:23541a125764", "label": "保留天数", "name": "reserveDays", "type": "text"}, {"copyable": true, "id": "u:task_id", "label": "任务ID", "name": "taskId", "type": "text"}, {"fieldProps": {"options": [{"label": "运行中", "value": 1}, {"label": "已停止", "value": 0}]}, "id": "u:status", "label": "状态", "name": "status", "type": "select"}, {"copyable": true, "id": "u:create_time", "label": "创建时间", "name": "createTime", "type": "text"}, {"copyable": true, "id": "u:update_time", "label": "更新时间", "name": "updateTime", "type": "text"}], "dataSource": "${modifiedItem || item}", "extra": [{"body": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:cancel_btn", "label": "取消", "type": "button"}, {"id": "u:submit_btn", "label": "确认", "level": "primary", "type": "button", "actionType": "submit", "componentId": "u:archive_node_form"}], "body": [{"actions": [], "api": {"method": "post", "url": "/scm/rulecenter/archive-management/create-node", "messages": {"success": "创建成功"}}, "body": [{"id": "u:33bda0b60f07", "type": "grid", "columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data == null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}"}, "method": "post", "sendOn": "${databaseName!=null && databaseName!=''}", "url": "/scm/rulecenter/archive/fetch-tables"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择表名"}, "formItemProps": {"rules": [{"message": "请选择表名", "required": true}]}, "id": "u:table_name", "label": "表名", "linkage": [{"event": "onChange", "resetKeys": ["indexData"]}], "name": "tableName", "type": "select"}], "id": "u:5e12c4020389"}, {"body": [], "id": "u:47e33681bd30"}]}, {"id": "u:c5ea54c5280e", "type": "grid", "columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data !=null && response.data.length > 0){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${appName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-sources"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据源"}, "formItemProps": {"rules": [{"message": "请选择数据源", "required": false}]}, "id": "u:datasource_name", "label": "数据源名称", "linkage": [{"event": "onChange", "resetKeys": ["databaseName"], "setValues": {}}], "name": "datasourceName", "type": "select"}], "id": "u:e5bd8203b822"}, {"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data==null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "datasource": "${datasourceName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-bases"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据库"}, "formItemProps": {"rules": [{"message": "请选择数据库", "required": false}]}, "id": "u:database_name", "label": "数据库名称", "linkage": [{"event": "onChange", "resetKeys": ["tableName"]}], "name": "databaseName", "type": "select"}], "id": "u:fce32884ee30"}]}, {"id": "u:8ddc03b89d06", "type": "grid", "columns": [{"body": [{"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否归档", "required": true}]}, "id": "u:is_archive", "label": "是否归档", "name": "isArchive", "type": "select"}], "id": "u:86c33c47dee2"}, {"body": [{"fieldProps": {"options": [{"label": "先归档子节点", "value": 1}, {"label": "先归档父节点", "value": 2}]}, "hiddenOn": true, "id": "u:archive_type", "label": "归档顺序", "name": "archiveType", "type": "select"}], "id": "u:73e0ffc4ab60"}]}, {"id": "u:filter_section", "type": "grid", "columns": [{"body": [{"fieldProps": {"options": [{"label": "不过滤", "value": 0}, {"label": "过滤", "value": 1}]}, "hiddenOn": "${!isArchive}", "id": "u:filter_default_value", "label": "默认值过滤", "name": "filterDefaultValue", "type": "select"}], "id": "u:181da80b6b7c"}]}, {"id": "u:79a568220011", "type": "divider", "title": "分表配置"}, {"fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "id": "u:sharding_enabled", "label": "启用分片", "name": "shardingEnabled", "type": "select", "formItemProps": {"rules": [{"message": "请选择是否启用分片", "required": true}]}, "initialValue": false}, {"id": "u:c3bbac2791bb", "type": "grid", "columns": [{"body": [{"fieldProps": {"placeholder": "${\"pink_operate_item_${0-128}\"}"}, "formItemProps": {"rules": [{"message": "分表表达式格式错误，支持格式：字段名 % 数值 或 前缀${字段名%数值+偏移量}", "test": "function(value) {\n  if (!value) return true;\n  const simplePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\\s*[%]\\s*\\d+$/;\n  const templatePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\\$\\{[a-zA-Z_][a-zA-Z0-9_]*[%]\\d+([+\\-]\\d+)?\\}$/;\n  return simplePattern.test(value.trim()) || templatePattern.test(value.trim());\n}"}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:sharding_expression", "label": "分表表达式", "name": "shardingExpression", "type": "text"}], "id": "u:08e689831d09"}, {"body": [{"fieldProps": {"placeholder": "请输入分表插件全类名，如：com.example.ShardingPlugin"}, "formItemProps": {"hidden": true, "rules": [{"message": "插件类名格式错误，请输入完整的类名", "pattern": "^[a-zA-Z_][a-zA-Z0-9_]*(\\.[a-zA-Z_][a-zA-Z0-9_]*)*$", "required": false}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:sharding_plugin_class", "label": "分表插件类", "name": "shardingPluginClass", "type": "text"}], "id": "u:0ab1b1f5cf48"}]}, {"id": "u:f5b722a03b9f", "type": "grid", "columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data!=null){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}", "tableName": "${tableName}"}, "method": "post", "sendOn": "${tableName!=null && tableName != ''}", "url": "/scm/rulecenter/archive/fetch-columns"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择分表字段"}, "formItemProps": {"rules": [{"message": "请选择分表字段", "required": "${shardingEnabled}"}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:ee94adca43c4", "label": "分片字段", "name": "shardingField", "type": "select"}], "id": "u:4ae0aa0b3512"}, {"body": [], "id": "u:eb9253efbe69"}]}, {"id": "u:index_related_config", "type": "divider", "hiddenOn": "${!enableScan}", "title": "索引相关配置"}, {"id": "u:41b5c2871456", "label": "索引扫描", "name": "enableScan", "type": "select", "fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否启用索引扫描", "required": true}]}, "initialValue": false}, {"id": "u:index_column_section", "type": "grid", "columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data!=null){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name,type:v.type}})\n}\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}", "tableName": "${tableName}"}, "method": "post", "sendOn": "${tableName!=null && tableName != ''}", "url": "/scm/rulecenter/archive/fetch-columns"}, "fieldProps": {"labelInValue": true, "placeholder": "请选择索引字段"}, "formItemProps": {"rules": [{"message": "请选择索引字段", "required": false}]}, "id": "u:index_column", "label": "索引字段", "linkage": [{"event": "onChange", "resetKeys": ["indexSpan", "reserveDays"], "setValues": {"indexColumType": "${indexData.type}", "indexColumn": "${indexData.value}"}}], "name": "indexData", "type": "select"}], "id": "u:cca33985cab3"}, {"body": [{"fieldProps": {"disabled": false}, "formItemProps": {"hidden": false}, "id": "u:index_column_type", "initialValue": "", "label": "索引字段类型", "name": "indexColumType", "proFieldProps": {"mode": "read"}, "trim": true, "type": "text"}], "id": "u:b92e627e1c58"}]}, {"id": "u:order_by_section", "type": "grid", "columns": [{"body": [{"fieldProps": {"options": [{"label": "升序", "value": "ASC"}, {"label": "降序", "value": "DESC"}]}, "id": "u:order_by", "initialValue": "ASC", "label": "排序方式", "name": "orderBy", "type": "select"}], "id": "u:54f1dec34f4e"}]}, {"fieldProps": {"max": 36500, "min": 1, "placeholder": "请输入保留天数，如：30、90、365"}, "id": "u:reserve_days", "label": "保留天数", "name": "reserveDays", "type": "digit", "formItemProps": {"rules": [{"required": true}]}, "hiddenOn": "${!(CONTAINS(indexColumType,\"date\")||CONTAINS(indexColumType,\"datetime\")||CONTAINS(indexColumType,\"timestamp\"))}"}, {"fieldProps": {"placeholder": ["开始值", "结束值"], "tooltip": "可只填写开始或结束值，如：1000-9999 或 1000- 或 -9999"}, "formItemProps": {"rules": [{"message": "索引值范围格式错误，请输入有效的数字范围", "test": "function(value) {\n  if (!value || !Array.isArray(value)) return true;\n  const [start, end] = value;\n  if (start !== undefined && (isNaN(start) || start < 0)) return false;\n  if (end !== undefined && (isNaN(end) || end < 0)) return false;\n  if (start !== undefined && end !== undefined && start >= end) return false;\n  return true;\n}"}]}, "id": "u:index_span", "label": "索引值范围", "name": "indexSpan", "type": "digitRange", "endName": "indexEnd", "hiddenOn": "${!(STARTSWITH(indexColumType,\"bigint\")||STARTSWITH(indexColumType,\"int\")||STARTSWITH(indexColumType,\"tinyint\")||STARTSWITH(indexColumType,\"smallint\")||STARTSWITH(indexColumType,\"mediumint\")||STARTSWITH(indexColumType,\"decimal\")||STARTSWITH(indexColumType,\"float\")||STARTSWITH(indexColumType,\"double\")||STARTSWITH(indexColumType,\"varchar\"))}", "startName": "indexStart"}, {"id": "u:relations", "label": "关联关系", "name": "relations", "type": "textarea", "fieldProps": {"placeholder": "如：a.id = b.aid，仅支持等值连接，支持多个字段，如：a.id = b.aid and a.type = b.type", "rows": 3}, "formItemProps": {"hidden": false, "rules": [{"message": "关联关系格式错误，请使用等值连接格式：表别名.字段 = 表别名.字段", "test": "function(value) {\n  if (!value) return true;\n  const relationPattern = /^([a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*(\\s+and\\s+[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*)*)$/i;\n  return relationPattern.test(value.trim());\n}"}]}, "hiddenOn": true}, {"formItemProps": {"rules": [{"message": "查询条件格式错误，请检查SQL语法", "test": "function(value) {\n  if (!value) return true;\n  // 简单的SQL条件格式校验\n  const sqlPattern = /^(\\s*[a-zA-Z_][a-zA-Z0-9_]*\\s*[=<>!]+\\s*['\"\\w\\s\\-\\.]+\\s*(and|or)?\\s*)*$/i;\n  return sqlPattern.test(value.trim());\n}"}]}, "id": "u:condition", "label": "查询条件", "name": "condition", "type": "textarea", "fieldProps": {"placeholder": "请输入SQL查询条件，如：id > 0 and status = 1 and create_time < '2024-01-01'", "rows": 3}}, {"id": "u:task_id", "label": "taskId", "name": "taskId", "type": "text", "formItemProps": {"hidden": true}}, {"id": "u:node_id", "label": "nodeId", "name": "nodeId", "type": "text", "formItemProps": {"hidden": true}}, {"formItemProps": {"hidden": true}, "id": "u:parent_node_id", "label": "parentNodeId", "name": "parentNodeId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:e13385e814c8", "label": "rootNode", "name": "rootNode", "type": "text", "initialValue": true}, {"formItemProps": {"hidden": true}, "id": "u:769df234fa38", "label": "indexColumn", "name": "indexColumn", "type": "text"}], "id": "u:archive_node_form", "type": "form", "onSubmit": "// 表单提交前的数据处理\nconst formData = values;\n\n// 处理分片配置\nif (!formData.shardingEnabled) {\n  formData.shardingExpression = null;\n  formData.shardingPluginClass = null;\n  formData.shardingField = null;\n} else {\n  // 确保分片表达式和插件类只填写一个\n  if (formData.shardingExpression && formData.shardingPluginClass) {\n    const confirmed = confirm('分片表达式和分片插件类只能填写一个，请选择保留哪个？');\n    if (confirmed) {\n      formData.shardingPluginClass = null;\n    } else {\n      formData.shardingExpression = null;\n    }\n  }\n}\n\n// 处理索引范围\nif (formData.indexSpan) {\n  formData.indexStart = formData.indexSpan[0];\n  formData.indexEnd = formData.indexSpan[1];\n  delete formData.indexSpan;\n}\n\n// 设置默认值\nif (formData.status === undefined) {\n  formData.status = 1; // 默认运行状态\n}\nif (formData.isArchive === undefined) {\n  formData.isArchive = true; // 默认归档\n}\nif (formData.debugMode === undefined) {\n  formData.debugMode = false; // 默认关闭调试\n}\nif (formData.archiveType === undefined) {\n  formData.archiveType = 1; // 默认先归档子节点\n}\nif (formData.filterDefaultValue === undefined) {\n  formData.filterDefaultValue = 0; // 默认不过滤\n}\nif (formData.shardingEnabled === undefined) {\n  formData.shardingEnabled = false; // 默认关闭分片\n}\nif (formData.enableScan === undefined) {\n  formData.enableScan = false; // 默认关闭索引扫描\n}\n\nreturn formData;"}], "id": "u:40a8a52c5eba", "title": "归档节点配置", "type": "dialog", "size": "lg", "width": "1150px"}, "id": "u:79fd13ba06f1", "label": "编辑", "level": "link", "onEvent": {"click": {"actions": []}}, "size": "small", "type": "button"}, {"confirmText": "将删除当前表和子表，删除后无法恢复，确认删除？", "danger": true, "ghost": false, "id": "u:bcbaef01e259", "label": "删除", "level": "link", "popConfirmPlacement": "left", "size": "small", "type": "button"}], "id": "u:46ed2b1ced15", "type": "wrapper"}], "id": "u:35c6cd6ed16b", "style": {"paddingLeft": "1em", "paddingTop": "1em"}, "title": [], "type": "descriptions"}, {"block": true, "hiddenOn": true, "icon": "icon-eye", "id": "u:d9bde69e957d", "label": "预览", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "ajax", "api": {"data": {"appName": "${appName}", "currentKey": "${item.key}", "databaseName": "${databaseName}", "datasourceName": "${dataSourceName}", "taskConfig": "${list}", "taskName": "${taskName}"}, "method": "post", "requestAdaptor": "// api.data.count = api.data.count + 1; \nconst data = api.data\napi.data = {\n  taskDetails:context.list,\n  appName:data.appName,\n  databaseName:data.databaseName,\n  datasourceName:data.datasourceName,\n  taskName:data.taskName,\n  currentKey:data.currentKey\n}\nreturn api;", "url": "/scm/rulecenter/archive/preview"}, "outputVar": "previewResult"}, {"actionType": "setValue", "args": {"value": {"previewData": "${event.data.previewResult.responseData}"}}, "componentId": "u:091537134056", "dataMergeMode": "override"}]}}, "size": "middle", "style": {"marginTop": 24}, "title": "生成sql", "type": "button"}, {"body": [{"id": "u:ebb186ccc37a", "language": "sql", "type": "code", "value": "${previewData.selectSql || '-'}", "wordWrap": true}, {"hiddenOn": "${previewData.selectSql}", "id": "u:4cdc861938cb", "tpl": "暂无数据", "type": "tpl"}], "hiddenOn": "${!previewData || !previewData.selectSql}", "id": "u:782c873d6bac", "style": {"marginTop": 24}, "title": [{"id": "u:dcfb2e301901", "tpl": "结果预览", "type": "tpl"}], "type": "card"}], "id": "u:091537134056", "type": "service"}], "closeOnEsc": true, "closeOnOutside": true, "id": "u:933dc2dbb42b", "mask": true, "title": "详情", "width": 880}