{"type": "dialog", "actions": [{"actionType": "cancel", "id": "u:cancel_btn", "label": "取消", "type": "button"}, {"actionType": "submit", "componentId": "u:archive_node_form", "id": "u:submit_btn", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "创建成功"}, "method": "post", "url": "/scm/rulecenter/archive-management/create-node"}, "body": [{"columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data !=null && response.data.length > 0){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${appName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-sources"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据源"}, "formItemProps": {"rules": [{"message": "请选择数据源", "required": true}]}, "id": "u:datasource_name", "label": "数据源名称", "linkage": [{"event": "onChange", "resetKeys": ["databaseName"], "setValues": {}}], "name": "datasourceName", "type": "select"}], "id": "u:e5bd8203b822"}, {"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data==null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "datasource": "${datasourceName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-bases"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据库"}, "formItemProps": {"rules": [{"message": "请选择数据库", "required": true}]}, "id": "u:database_name", "label": "数据库名称", "linkage": [{"event": "onChange", "resetKeys": ["tableName"]}], "name": "databaseName", "type": "select"}], "id": "u:fce32884ee30"}], "id": "u:c5ea54c5280e", "type": "grid"}, {"columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data == null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}"}, "method": "post", "sendOn": "${databaseName!=null && databaseName!=''}", "url": "/scm/rulecenter/archive/fetch-tables"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择表名"}, "formItemProps": {"rules": [{"message": "请选择表名", "required": true}]}, "id": "u:ce7ae97fe716", "label": "表名", "linkage": [{"event": "onChange", "resetKeys": ["indexData"]}], "name": "tableName", "type": "select"}], "id": "u:29258173dba1"}, {"body": [{"fieldProps": {"placeholder": "请输入父节点ID"}, "formItemProps": {"rules": [{"message": "请输入父节点ID", "required": true}]}, "id": "u:parent_node_select", "label": "父节点ID", "name": "parentNodeId", "type": "text"}], "id": "u:parent_node_column"}], "id": "u:d2a3940543aa", "type": "grid"}, {"columns": [{"body": [{"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否归档", "required": true}]}, "id": "u:is_archive", "label": "是否归档", "name": "isArchive", "type": "select"}], "id": "u:86c33c47dee2"}, {"body": [{"fieldProps": {"options": [{"label": "先归档子节点", "value": 1}, {"label": "先归档父节点", "value": 2}]}, "id": "u:archive_type", "label": "归档顺序", "name": "archiveType", "type": "select"}], "id": "u:73e0ffc4ab60"}], "id": "u:8ddc03b89d06", "type": "grid"}, {"columns": [{"body": [{"fieldProps": {"options": [{"label": "不过滤", "value": 0}, {"label": "过滤", "value": 1}]}, "hiddenOn": "${!isArchive}", "id": "u:filter_default_value", "label": "默认值过滤", "name": "filterDefaultValue", "type": "select"}], "id": "u:181da80b6b7c"}], "id": "u:filter_section", "type": "grid"}, {"id": "u:relations_section", "title": "关联关系配置", "type": "divider"}, {"fieldProps": {"placeholder": "如：a.id = b.aid，仅支持等值连接，支持多个字段，如：a.id = b.aid and a.type = b.type", "rows": 3}, "formItemProps": {"rules": [{"message": "关联关系格式错误，请使用等值连接格式：表别名.字段 = 表别名.字段", "test": "function(value) {\n  if (!value) return true;\n  const relationPattern = /^([a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*(\\s+and\\s+[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*)*)$/i;\n  return relationPattern.test(value.trim());\n}"}]}, "id": "u:relations", "label": "关联关系", "name": "relations", "type": "textarea"}, {"fieldProps": {"placeholder": "请输入SQL查询条件，如：id > 0 and status = 1 and create_time < '2024-01-01'", "rows": 3}, "formItemProps": {"rules": [{"message": "查询条件格式错误，请检查SQL语法", "test": "function(value) {\n  if (!value) return true;\n  // 简单的SQL条件格式校验\n  const sqlPattern = /^(\\s*[a-zA-Z_][a-zA-Z0-9_]*\\s*[=<>!]+\\s*['\"\\w\\s\\-\\.]+\\s*(and|or)?\\s*)*$/i;\n  return sqlPattern.test(value.trim());\n}"}]}, "id": "u:condition", "label": "查询条件", "name": "condition", "type": "textarea"}, {"formItemProps": {"hidden": true}, "id": "u:task_id", "label": "taskId", "name": "taskId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:node_id", "label": "nodeId", "name": "nodeId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:e13385e814c8", "initialValue": false, "label": "rootNode", "name": "rootNode", "type": "text"}], "id": "u:archive_node_form", "onSubmit": "// 表单提交前的数据处理\nconst formData = values;\n\n// 设置默认值\nif (formData.status === undefined) {\n  formData.status = 1; // 默认运行状态\n}\nif (formData.isArchive === undefined) {\n  formData.isArchive = true; // 默认归档\n}\nif (formData.archiveType === undefined) {\n  formData.archiveType = 1; // 默认先归档子节点\n}\nif (formData.filterDefaultValue === undefined) {\n  formData.filterDefaultValue = 0; // 默认不过滤\n}\nif (formData.rootNode === undefined) {\n  formData.rootNode = false; // 非根节点\n}\n\nreturn formData;", "type": "form"}], "id": "u:40a8a52c5eba", "size": "lg", "title": "添加子节点", "width": "1150px"}