---
alwaysApply: true
---


# Project Module Structure Rule

## scm-rulecenter 目录结构

- `scm-rulecenter/` 目录下包含规则中心的核心模块，采用分层架构：
  - `scm-rulecenter-api/`：对外API接口层
  - `scm-rulecenter-application/`：应用服务层
  - `scm-rulecenter-domain/`：领域层，核心业务逻辑
  - `scm-rulecenter-infrastructure/`：基础设施层，第三方资源适配
  - `scm-rulecenter-interfaces/`：接口适配层
  - `scm-rulecenter-sdk/`、`scm-rulecenter-sdk-od/`：SDK相关模块
  - `scm-rulecenter-common/`：通用工具与基础代码

## scp-framework 目录结构（仅关注 archive 相关模块）

- `scp-framework1/scp-archive/`：归档主模块
  - `scp-archive-common/`：归档通用工具与基础代码
  - `scp-archive-sdk/`：归档SDK实现
  - `scp-archive-sdk-test/`：归档SDK测试模块

## 组织约定
- 各模块应根据其功能放置在对应目录下。
- 通用/共享代码应放在 common 或相关工具模块。
- 新功能或服务应作为新模块或在现有模块下扩展，遵循既有结构。

## Conventions
- Each module should be placed in its appropriate directory according to its function.
- Shared or common code should go into the relevant common or utility modules.
- New features or services should be added as new modules or within the correct existing module, following the established structure.

# Project Module Structure Rule

## scm-rulecenter 目录结构

- `scm-rulecenter/` 目录下包含规则中心的核心模块，采用分层架构：
  - `scm-rulecenter-api/`：对外API接口层
  - `scm-rulecenter-application/`：应用服务层
  - `scm-rulecenter-domain/`：领域层，核心业务逻辑
  - `scm-rulecenter-infrastructure/`：基础设施层，第三方资源适配
  - `scm-rulecenter-interfaces/`：接口适配层
  - `scm-rulecenter-sdk/`、`scm-rulecenter-sdk-od/`：SDK相关模块
  - `scm-rulecenter-common/`：通用工具与基础代码

## scp-framework 目录结构（仅关注 archive 相关模块）

- `scp-framework1/scp-archive/`：归档主模块
  - `scp-archive-common/`：归档通用工具与基础代码
  - `scp-archive-sdk/`：归档SDK实现
  - `scp-archive-sdk-test/`：归档SDK测试模块

## 组织约定
- 各模块应根据其功能放置在对应目录下。
- 通用/共享代码应放在 common 或相关工具模块。
- 新功能或服务应作为新模块或在现有模块下扩展，遵循既有结构。

## Conventions
- Each module should be placed in its appropriate directory according to its function.
- Shared or common code should go into the relevant common or utility modules.
- New features or services should be added as new modules or within the correct existing module, following the established structure.
