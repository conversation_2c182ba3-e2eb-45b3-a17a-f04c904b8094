{"type": "page", "body": [{"actions": [{"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:1fe1621896cc", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:530888dfa6eb", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$", "appName": "${queryParam.appName}"}, "method": "post", "url": "/scm/rulecenter/archive-management/save-app-config"}, "body": [{"id": "u:c5ed51489783", "label": "线程数", "name": "threads", "tooltip": "归档的线程数量，不要设置过大，可能会导致业务数据库压力", "type": "text"}, {"id": "u:41b2d052d769", "label": "全局开关", "name": "enable", "tooltip": "可一键暂停所有任务", "type": "switch"}, {"id": "u:d94ef774a0f4", "label": "扫描间隔", "name": "interval", "tooltip": "归档平台任务扫描间隔，归档SDK将以这个频率扫描是否有需要归档的任务", "type": "digit"}], "id": "u:fb54298a656a", "initApi": {"data": {"appName": "${queryParam.appName}"}, "method": "get", "url": "/scm/rulecenter/archive-management/get-app-config"}, "type": "form"}], "id": "u:5f5cfc6cfa3f", "title": "全局配置", "type": "dialog"}, "id": "u:87954eb4a1b3", "label": "全局配置", "level": "primary", "type": "button"}, {"actionType": "dialog", "dialog": {"actions": [{"actionType": "cancel", "id": "u:645b80faf9b0", "label": "取消", "type": "button"}, {"actionType": "confirm", "id": "u:84e4365bdc35", "label": "确认", "level": "primary", "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"data": {"&": "$$", "appName": "${queryParam.appName}"}, "messages": {"success": "新增配置项成功"}, "method": "post", "requestAdaptor": "// api.data.count = api.data.count + 1; \nconsole.log('api',api,context)\nreturn api;", "url": "/scm/rulecenter/archive-management/create-task"}, "body": [{"formItemProps": {"rules": [{"message": "请输入任务名称", "required": true}]}, "id": "u:573608204528", "initialValue": "${queryParam.appName}", "label": "应用名称", "name": "appName", "proFieldProps": {"mode": "read"}, "type": "text"}, {"formItemProps": {"rules": [{"message": "请输入任务名称", "required": true}]}, "id": "u:573601204528", "label": "任务名称", "name": "taskName", "type": "text"}, {"api": {"adaptor": "// 自定义修改 API 返回结果格式，如：\n// return {\n//   status: 200, // 200 表示请求成功，否则按错误处理\n//   msg: \"请求成功\",\n//   data: {\n//     text: \"world\",\n//     options: [\n//       {\n//         label: \"A\",\n//         value: \"a\",\n//       },\n//     ],\n//   },\n// };\nif(response.data !=null && response.data.length > 0){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${queryParam.appName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-sources"}, "fieldProps": {"labelInValue": false}, "formItemProps": {"rules": [{"message": "数据源必选", "required": true}]}, "id": "u:42c1610667ef", "initApi": {"adaptor": "// 自定义修改 API 返回结果格式，如：\n// return {\n//   status: 200, // 200 表示请求成功，否则按错误处理\n//   msg: \"请求成功\",\n//   data: {\n//     text: \"world\",\n//     options: [\n//       {\n//         label: \"A\",\n//         value: \"a\",\n//       },\n//     ],\n//   },\n// };\nconst initialData = response.data==null?null:response.data[0]\nif(initialData!=null){  \n  return {\n    data: {\n      datasource:initialData.name\n    }\n  }\n}\nreturn response;", "data": {"appName": "${queryParam.appName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-sources"}, "label": "数据源", "linkage": [{"event": "onChange", "resetKeys": ["database"], "setValues": {}}], "name": "datasourceName", "type": "select"}, {"api": {"adaptor": "// 自定义修改 API 返回结果格式，如：\n// return {\n//   status: 200, // 200 表示请求成功，否则按错误处理\n//   msg: \"请求成功\",\n//   data: {\n//     text: \"world\",\n//     options: [\n//       {\n//         label: \"A\",\n//         value: \"a\",\n//       },\n//     ],\n//   },\n// };\nresponse.data = response.data==null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${queryParam.appName}", "datasource": "${datasourceName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-bases"}, "fieldProps": {"labelInValue": false}, "formItemProps": {"rules": [{"message": "数据库必选", "required": true}]}, "id": "u:1a97a8cbea6f", "label": "数据库", "linkage": [{"event": "onChange", "resetKeys": ["tableName"]}], "name": "databaseName", "type": "select"}, {"fieldProps": {"options": [{"label": "全天", "value": 1}, {"label": "指定时间段", "value": 2}]}, "formItemProps": {"rules": [{"required": true}]}, "id": "u:69481269f4d4", "label": "归档时间段", "linkage": [{"event": "onChange", "resetKeys": ["timeSpan"]}], "name": "executionType", "type": "select"}, {"fieldProps": {"placeholder": "格式: 21:00-6:00(当前21:00运行到第二天6:00)"}, "formItemProps": {"rules": [{"message": "格式错误，示例:22:00-7:00", "pattern": "^(?:[01]?[0-9]|2[0-3]):[0-5][0-9]-(?:[01]?[0-9]|2[0-3]):[0-5][0-9]$"}, {"required": true}]}, "hiddenOn": "${executionType !=2}", "id": "u:4d4975944b70", "label": "指定时间", "name": "timeSpan", "trim": true, "type": "text"}, {"fieldProps": {"max": 10000}, "id": "u:3fe246ff5108", "initialValue": 100, "label": "批次数量", "name": "limit", "type": "digit"}, {"api": {"adaptor": "// 自定义修改 API 返回结果格式，如：\n// return {\n//   status: 200, // 200 表示请求成功，否则按错误处理\n//   msg: \"请求成功\",\n//   data: {\n//     text: \"world\",\n//     options: [\n//       {\n//         label: \"A\",\n//         value: \"a\",\n//       },\n//     ],\n//   },\n// };\nresponse.data = response.data == null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${queryParam.appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}"}, "method": "post", "sendOn": "${databaseName!=null && databaseName!=''}", "url": "/scm/rulecenter/archive/fetch-tables"}, "fieldProps": {}, "formItemProps": {"rules": [{"required": true}]}, "id": "u:cdabe0669d30", "label": "基础表", "linkage": [{"event": "onChange", "resetKeys": ["rootIndexColumn", "tableName", "indexData"]}], "name": "rootTableName", "type": "select"}, {"formItemProps": {"rules": []}, "id": "u:c3186e116846", "label": "基础表过滤条件", "name": "rootConditions", "trim": true, "type": "textarea"}, {"api": {"adaptor": "// 自定义修改 API 返回结果格式，如：\n// return {\n//   status: 200, // 200 表示请求成功，否则按错误处理\n//   msg: \"请求成功\",\n//   data: {\n//     text: \"world\",\n//     options: [\n//       {\n//         label: \"A\",\n//         value: \"a\",\n//       },\n//     ],\n//   },\n// };\nif(response.data!=null){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name,type:v.type}})\n}\nreturn response;", "data": {"appName": "${queryParam.appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}", "tableName": "${rootTableName}"}, "method": "post", "sendOn": "${rootTableName!=null && rootTableName != ''}", "silent": false, "url": "/scm/rulecenter/archive/fetch-columns"}, "fieldProps": {"labelInValue": true}, "formItemProps": {"rules": [{"required": true}]}, "id": "u:d60ebefa1b72", "label": "索引字段", "linkage": [{"event": "onChange", "resetKeys": ["rootReserveDays", "indexSpan", "indexColumType"], "setValues": {"indexColumType": "${indexData.type}", "rootIndexColumn": "${indexData.value}"}}], "name": "indexData", "onEvent": {"change": {"actions": []}}, "type": "select"}, {"fieldProps": {"labelInValue": true}, "formItemProps": {"extra": "当前任务的执行频率，取全局配置和任务维度的最大值,单位:秒", "rules": [{"required": true}]}, "id": "u:88f4811a8216", "initialValue": 60, "label": "任务执行频率", "linkage": [], "name": "interval", "onEvent": {"change": {"actions": []}}, "type": "digit"}, {"fieldProps": {"disabled": true}, "formItemProps": {"hidden": false}, "id": "u:448c1b69c68e", "initialValue": "", "label": "索引字段类型", "name": "indexColumType", "trim": true, "type": "text"}, {"endName": "rootIndexEnd", "formItemProps": {"rules": []}, "hiddenOn": "${!(STARTSWITH(indexData.type,\"bigint\")||STARTSWITH(indexData.type,\"int\")||STARTSWITH(indexData.type,\"tinyint\")||STARTSWITH(indexData.type,\"smallint\")||STARTSWITH(indexData.type,\"mediumint\")||STARTSWITH(indexData.type,\"decimal\")||STARTSWITH(indexData.type,\"float\")||STARTSWITH(indexData.type,\"double\")||STARTSWITH(indexData.type,\"varchar\"))}", "id": "u:4e6a84ce6e3b", "label": "索引值范围", "name": "indexSpan", "startName": "rootIndexStart", "tooltip": "可只填写开始或者结束值", "type": "digitRange"}, {"formItemProps": {"rules": []}, "hiddenOn": "${!(CONTAINS(indexData.type,\"date\")||CONTAINS(indexData.type,\"datetime\")||CONTAINS(indexData.type,\"timestamp\"))}", "id": "u:075327ab589a", "label": "保留天数", "name": "rootReserveDays", "type": "digit"}, {"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "formItemProps": {"rules": [{"required": true}]}, "id": "u:f331274f7043", "initialValue": true, "label": "是否归档", "name": "rootArchive", "tooltip": "基准表是否做归档", "type": "select"}, {"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "formItemProps": {"rules": []}, "hiddenOn": "${indexColumType==null||indexColumType==''}", "id": "u:5cde3e6025c5", "initialValue": true, "label": "索引扫描", "name": "rootEnableScan", "tooltip": "开启后，查询将分为两步1.通过索引字段扫描一定量数据 2 用扫描结果的数据匹配过滤条件。", "type": "select"}, {"fieldProps": {"options": [{"label": "是", "value": 1}, {"label": "否", "value": "0"}]}, "id": "u:8b24a2aca615", "label": "过滤数据库默认值", "labelCol": 2, "name": "rootFilterDefaultValue", "tooltip": "查询或者删除的时候，不考虑0或者空字符串", "type": "radio"}, {"formItemProps": {"hidden": true}, "id": "u:fa793a7535c6", "label": "数据源名称", "name": "datasourceName", "trim": true, "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:e093abc64bf2", "initialValue": true, "label": "root", "name": "root", "trim": true, "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:0d5a2ba1a1c3", "initialValue": true, "label": "rootIndexColumn", "name": "rootIndexColumn", "trim": true, "type": "text"}], "id": "u:daf300074416", "type": "form", "wrapperCol": {"style": {"flex": 0.9}}}], "id": "u:f8e003d13faa", "title": "新增", "type": "dialog", "width": 700}, "id": "u:1ac7f07c86ac", "label": "新增", "level": "primary", "type": "button"}], "api": {"adaptor": "\n  // 初始化result对象\n  let result = {\n    status: 200,\n    code: response.code,\n    msg: response.msg,\n    data: {\n      contents: []\n    }\n  };\n\n  // 容错处理：判断payload是否有值\n  if (payload && payload.length > 0) {\n    // 将payload处理后的数据赋值给contents\n    for(var data of payload){      \n      if(data.executionType == 1){\n        data.executionTime = '全天'\n      }else{\n        data.executionTime = data.timeSpan\n      }\n    }\n    result.data.contents = payload;\n  }\n  // 返回处理好的数据\n  return result;\n", "method": "post", "silent": true, "url": "/scm/rulecenter/archive-management/list-task-configs"}, "id": "u:a3963992197d", "manualRequest": false, "name": "chainSearchPage", "rowKey": "id", "search": true, "toolBarRender": true, "type": "chainSearchPage", "formColumns": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式，如：\n// return {\n//   status: 200, // 200 表示请求成功，否则按错误处理\n//   msg: \"请求成功\",\n//   data: {\n//     text: \"world\",\n//     options: [\n//       {\n//         label: \"A\",\n//         value: \"a\",\n//       },\n//     ],\n//   },\n// };\nconst data = response.data==null?[] :response.data.map(value=> {return {label:value,value:value}})\nresponse.data = data;\ncontext.initialValue = data.length >0?data[0]:null\nreturn response;", "method": "get", "silent": false, "url": "/scm/rulecenter/archive/app-list"}, "fieldProps": {}, "formItemProps": {"rules": [{"message": "请选择应用", "required": true}]}, "id": "u:0873f812274f", "initApi": {"adaptor": "// 自定义修改 API 返回结果格式，如：\n// return {\n//   status: 200, // 200 表示请求成功，否则按错误处理\n//   msg: \"请求成功\",\n//   data: {\n//     text: \"world\",\n//     options: [\n//       {\n//         label: \"A\",\n//         value: \"a\",\n//       },\n//     ],\n//   },\n// };\nconst initialData = response.data==null?null:response.data[0]\nif(initialData != null){\n  return {\n    data: {\n      appName:initialData\n    }\n  }\n}\nreturn response;", "cache": 3000, "method": "get", "url": "/scm/rulecenter/archive/app-list"}, "initialValue": "", "label": "应用列表", "linkage": [], "name": "appName", "onEvent": {"change": {"actions": [{"actionType": "refresh"}]}}, "proFieldProps": {}, "type": "select"}], "tableColumns": [{"copyable": true, "fixed": "left", "id": "u:b4a9bb715634", "label": "任务名称", "name": "taskName", "type": "text"}, {"id": "u:976ec902828b", "label": "数据源", "name": "datasourceName", "type": "text"}, {"id": "u:976ec302828b", "label": "数据库", "name": "databaseName", "type": "text"}, {"id": "u:5b0cfc063328", "label": "批次数量", "name": "limit", "type": "text"}, {"fieldProps": {"options": [{"label": "调试", "value": 1}, {"label": "生产", "value": 2}]}, "id": "u:c938fee8cc5a", "label": "运行模式", "name": "mode", "type": "select"}, {"fieldProps": {"options": [{"label": "暂停", "value": 0}, {"label": "运行", "value": 1}]}, "id": "u:1ff80c01abe7", "label": "状态", "name": "status", "type": "select"}, {"fieldProps": {"options": []}, "id": "u:a9f766079ad8", "label": "执行时间", "name": "executionTime", "type": "text"}, {"actions": [{"actionType": "ajax", "api": {"data": {"status": "${status==1?0:1}", "taskId": "${taskId}"}, "method": "post", "url": "/scm/rulecenter/archive-management/update-task-status"}, "confirmText": "是否确认${status==1?'暂停':'开启'}", "id": "u:33a5ab78d907", "label": "${status==0?'开启':'暂停'}", "level": "link", "type": "button"}, {"actionType": "link", "id": "u:1a6e7e8ad35c", "label": "详情", "level": "link", "link": "/scm-360/j2p/archive-platform/archive-management-detail?taskId=${taskId}", "type": "button"}, {"actionType": "ajax", "api": {"data": {"appName": "${queryParam.appName}", "delete": "true", "taskName": "${taskName}"}, "messages": {"success": "删除成功"}, "method": "post", "url": "/scm/rulecenter/archive/update-status"}, "confirmText": "是否确认删除", "danger": true, "id": "u:19e9f24a8266", "label": "删除", "reload": "chainSearchPage", "type": "button"}], "fixed": "right", "id": "u:04f3880a8a88", "label": "操作", "type": "operation", "width": 150}]}], "id": "u:3700901b5652", "toolbar": []}