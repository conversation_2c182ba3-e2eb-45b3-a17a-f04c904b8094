# 分片表达式校验说明

## 概述

在 `ArchiveManagementController#createNodeConfig` 和 `ArchiveManagementController#updateNodeConfig` 的共同逻辑中，我们实现了对 `shardingExpression` 的校验功能。校验逻辑位于 `ArchiveNodeConfigConverter#validateAndParseRelations` 方法中。

## 校验要求

### 基本要求
1. **只允许一个数据库字段名**：表达式必须包含且仅包含一个数据库字段名
2. **合法的数学运算**：除了字段名外，只能包含合法的数学运算符和数字
3. **SpEL表达式验证**：使用Java的SpEL表达式执行一次确保语法正确性

### 支持的格式
- `item_id%128` - 取模运算
- `item_id/128+1` - 除法加常数
- `item_id*2+1` - 乘法加常数
- `item_id-100` - 减法运算
- `item_id+50` - 加法运算
- `(item_id+1000)%128` - 带括号的复杂表达式
- `user_id%256` - 其他字段名
- `order_id/64+1` - 其他字段名
- `product_id*3%64` - 复杂运算

## 校验逻辑

### 1. 字段名提取
使用正则表达式 `[a-zA-Z_][a-zA-Z0-9_]*` 提取所有可能的字段名，排除数学函数名和常量。

### 2. 字段名数量校验
确保只有一个数据库字段名，多个字段名会被拒绝。

### 3. 字符合法性校验
只允许以下字符：
- 字母、数字、下划线（用于字段名）
- 运算符：`+`、`-`、`*`、`/`、`%`
- 括号：`(`、`)`
- 空格
- 小数点

### 4. 语法结构校验
- 括号匹配检查
- 连续运算符检查（除了负号）
- 表达式不能以运算符结尾

### 5. SpEL表达式验证
使用Spring Expression Language验证表达式的语法正确性，确保表达式可以被正确解析和执行。

## 实现位置

### 主要方法
```java
// ArchiveNodeConfigConverter.java
public void validateAndParseRelations(ArchiveNodeConfigDO nodeConfig)
private void validateShardingExpression(ArchiveNodeConfigDO nodeConfig)
private void validateShardingExpressionFormat(String expression)
private void validateShardingExpressionWithSpEL(String expression)
private void validateExpressionStructure(String expression)
```

### 调用位置
```java
// ArchiveManagementController.java
@PostMapping("create-node")
public Result<ArchiveNodeConfigDTO> createNodeConfig(@RequestBody @Valid ArchiveNodeConfigDTO archiveNodeConfigDDTO) {
    // ...
    archiveNodeConfigConverter.validateAndParseRelations(archiveNodeConfigDO);
    // ...
}

@PostMapping("update-node")
public Result<ArchiveNodeConfigDTO> updateNodeConfig(@RequestBody @Valid ArchiveNodeConfigDTO archiveNodeConfigDDTO) {
    // ...
    archiveNodeConfigConverter.validateAndParseRelations(archiveNodeConfigDO);
    // ...
}
```

## 错误处理

### 常见错误信息
1. **启用分片时，分片表达式不能为空** - 当 `shardingEnabled=true` 但表达式为空时
2. **分片表达式必须包含且仅包含一个数据库字段名** - 当表达式包含多个或没有字段名时
3. **分片表达式包含非法字符** - 当表达式包含不允许的字符时
4. **分片表达式括号不匹配** - 当括号数量不匹配时
5. **分片表达式包含连续的运算符** - 当出现连续运算符时
6. **分片表达式不能以运算符结尾** - 当表达式以运算符结尾时
7. **分片表达式格式错误** - 当SpEL解析失败时

## 测试用例

### 有效表达式示例
```java
String[] validExpressions = {
    "item_id%128",
    "item_id/128+1",
    "item_id*2+1",
    "item_id-100",
    "item_id+50",
    "user_id%256",
    "order_id/64+1",
    "(item_id+1000)%128",
    "product_id*3%64"
};
```

### 无效表达式示例
```java
String[] invalidExpressions = {
    "", // 空表达式
    "item_id%128+user_id", // 多个字段名
    "item_id%128;", // 包含分号
    "item_id%128+", // 以运算符结尾
    "(item_id%128", // 括号不匹配
    "item_id%128@", // 非法字符
    "123%128", // 没有字段名
    "item_id" // 没有运算符
};
```

## 依赖要求

需要在 `pom.xml` 中添加 Spring Expression 依赖：

```xml
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-expression</artifactId>
</dependency>
```

## 使用示例

```java
// 创建节点配置时自动校验
ArchiveNodeConfigDTO nodeConfig = new ArchiveNodeConfigDTO();
nodeConfig.setShardingEnabled(true);
nodeConfig.setShardingExpression("item_id%128");

// 调用创建接口，会自动进行校验
Result<ArchiveNodeConfigDTO> result = archiveManagementController.createNodeConfig(nodeConfig);
```

## 注意事项

1. **校验时机**：只有在 `shardingEnabled=true` 时才会进行校验
2. **性能考虑**：SpEL表达式验证会执行一次表达式，确保语法正确性
3. **错误信息**：提供详细的错误信息，帮助用户快速定位问题
4. **扩展性**：校验逻辑可以轻松扩展，支持更多的表达式格式 