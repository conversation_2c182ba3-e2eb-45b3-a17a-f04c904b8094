package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceVO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
class InvokeCmd{

    ElementInterfaceVO elementInterfaceVO;

    String replaceVariable;

    String colorEnv;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InvokeCmd)) return false;
        InvokeCmd invokeCmd = (InvokeCmd) o;
        return Objects.equals(elementInterfaceVO, invokeCmd.elementInterfaceVO) && Objects.equals(replaceVariable, invokeCmd.replaceVariable);
    }

    @Override
    public int hashCode() {
        return Objects.hash(elementInterfaceVO, replaceVariable);
    }
}
