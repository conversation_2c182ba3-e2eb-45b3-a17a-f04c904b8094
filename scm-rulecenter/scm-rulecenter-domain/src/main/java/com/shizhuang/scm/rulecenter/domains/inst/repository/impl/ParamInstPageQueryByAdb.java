package com.shizhuang.scm.rulecenter.domains.inst.repository.impl;

import cn.hutool.db.handler.BeanListHandler;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONValidator;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shizhuang.duapp.mdm.api.enums.YesOrNoEnum;
import com.shizhuang.scm.rulecenter.api.constant.ElementTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.SystemFieldConditionEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.domains.inst.assembler.ParamInstAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ParamInstanceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.DATABASE_ERROR;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.SCHEMA_NOT_EXIST;

@Slf4j
@Component
public class ParamInstPageQueryByAdb implements ParamInstPageQueryStrategy, InitializingBean {

    @Resource
    SchemaRepository schemaRepository;

    @Resource
    ParamInstanceMapper paramInstanceMapper;

    @Resource
    ParamInstAssembler paramInstAssembler;

    @Resource
    private ThreadPoolTaskExecutor asyncServiceExecutor;

    @Resource
    ElementRepository elementRepository;

    private final static Byte MULTI_PARAM_TEXT = (byte) -1;

    @Override
    public PageInfo<InstDimPageResponse> queryPage(InstPageQuery instPageQuery) {
        String schemaCode = instPageQuery.getSchemaCode();
        Integer pageNum = instPageQuery.getPageNum();
        Integer pageSize = instPageQuery.getPageSize();
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        String inputOrderBy = instPageQuery.getOrderBy();

        SchemaMetaAggregate schemaAggregateByCode = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLargestVersion());
        List<SchemaElementEntity> allElements = schemaAggregateByCode.getAllElements();
        Map<String, String> elementFieldMap = schemaAggregateByCode.getElementFieldMap();
        Map<String, Byte> elementValueRangeTypeMap = allElements.stream().collect(Collectors.toMap(SchemaElementEntity::getElementCode, e -> {
            if (ElementTypeEnum.PARAM.getType().equals(e.getElementType())
                    && Objects.equals(e.getIsMulti(), 1)) {
                return MULTI_PARAM_TEXT;
            }
            return e.getValueRangeType();
        }));

        for (SystemFieldConditionEnum systemFieldConditionEnum : SystemFieldConditionEnum.values()) {
            elementValueRangeTypeMap.put(systemFieldConditionEnum.getConditionCode(), ValueRangeTypeEnum.SYSTEM.getType());
        }
        //where后的条件
        String whereStr = getWhereSql(queryParams, elementFieldMap, elementValueRangeTypeMap, instPageQuery);
        String selectSql = getSelectSql(whereStr, pageNum, pageSize, inputOrderBy);
        String countSql = getCountSql(whereStr);

        //这里执行完ADB为什么要再查一次mysql？因为mysql->ADB的同步是有延迟的，直接用ADB的数据，可能会导致数据不准（比如生效\失效，删除未删除等），
        // 所以ADB适合用于复杂情况的搜索，拿到dimensionKey之后，再回mysql查表，能够保障数据一致性
        List<ParamInstanceDOWithBLOBs> paramInstanceDOList = null;
        try {
            List<String> parameters = new ArrayList<>();
            parameters.add(schemaCode);
            if (StringUtils.isNotBlank(inputOrderBy)) {
                parameters.add(inputOrderBy);
            }
            paramInstanceDOList = executeBySelectSql(selectSql, parameters);
            if (CollectionUtils.isNotEmpty(paramInstanceDOList)) {
                List<String> dimensionList = new ArrayList<>();
                List<String> dimensionKeyList = new ArrayList<>();
                paramInstanceDOList.forEach(p -> {
                    dimensionList.add(p.getDimension());
                    dimensionKeyList.add(p.getDimensionKey());
                });

                List<String> aggIds = paramInstanceDOList.stream().map(ParamInstanceDOWithBLOBs::getAggId).collect(Collectors.toList());
                ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
                String orderByClause = " modify_time desc ";
                if (StringUtils.isNotBlank(inputOrderBy)) {
                    orderByClause = " " + inputOrderBy + " ";
                }
                paramInstanceDOExample.setOrderByClause(orderByClause);
                paramInstanceDOExample.createCriteria()
                        .andSchemaCodeEqualTo(schemaCode).andIsDelEqualTo(CommonConstant.IS_NOT_DELETE).andAggIdIn(aggIds);
                paramInstanceDOList = paramInstanceMapper.selectByExampleWithBLOBs(paramInstanceDOExample);
            }

        } catch (SQLException e) {
            log.error("ParamInstRepositoryImpl.queryParamInstPage database error instPageQuery:{} selectSql:{} "
                    , JSON.toJSONString(instPageQuery), selectSql, e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }
        Long countBySql = null;
        try {
            countBySql = executeByCountSql(countSql, Lists.newArrayList(schemaCode));
        } catch (SQLException e) {
            log.error("ParamInstRepositoryImpl.queryParamInstPage database error instPageQuery:{} countSql:{} "
                    , JSON.toJSONString(instPageQuery), countSql, e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }

        InstDimPageResponse[] instDimResponses;
        if (CollectionUtils.isNotEmpty(paramInstanceDOList)) {
            instDimResponses = new InstDimPageResponse[paramInstanceDOList.size()];
            CountDownLatch countDownLatch = new CountDownLatch(paramInstanceDOList.size());
            List<ParamInstanceDOWithBLOBs> finalParamInstanceDOList = paramInstanceDOList;

            //本地缓存预热，避免大量调用redis
            allElements.forEach(e -> elementRepository.getByCodeSimple(e.getSchemaCode(), e.getElementCode()));
            for (int i = 0; i < paramInstanceDOList.size(); i++) {
                int finalI = i;
                asyncServiceExecutor.execute(() -> {
                    InstDimPageResponse pageResponse = paramInstAssembler.toPageResponse(finalParamInstanceDOList.get(finalI), allElements, elementFieldMap);
                    instDimResponses[finalI] = pageResponse;
                    countDownLatch.countDown();
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } else {
            instDimResponses = null;
        }

        PageInfo<InstDimPageResponse> paramInstanceDOPageInfo = PageInfo.of(instDimResponses == null ? new ArrayList<>() : Lists.newArrayList(instDimResponses));
        paramInstanceDOPageInfo.setPageNum(pageNum);
        paramInstanceDOPageInfo.setPageSize(pageSize);
        paramInstanceDOPageInfo.setTotal(countBySql);
        return paramInstanceDOPageInfo;
    }

    private static String getCountSql(String whereStr) {
        String countSql = "select count(1) "
                + " from param_instance "
                + " where is_del = 0 and schema_code =  ? " + whereStr;
        return countSql;
    }


    private static String getSelectSql(String whereStr, Integer pageNum, Integer pageSize, String orderBy) {

        String orderByStr = " order by create_time desc";
        if (StringUtils.isNotBlank(orderBy)) {
            orderByStr = " order by ? ";
        }

        String selectSql = "select schema_code,dimension,dimension_key,agg_id "
                + " from param_instance "
                + " where is_del = 0 and  schema_code = ? " + whereStr
                + orderByStr
                + " limit " + (pageNum - 1) * pageSize + "," + pageSize;
        return selectSql;
    }

    private static String getWhereSql(List<ElementInstDTO> queryParams, Map<String, String> elementFieldMap, Map<String, Byte> elementValueRangeTypeMap, InstPageQuery instPageQuery) {
        if (CollectionUtils.isEmpty(queryParams)) {
            return StringUtils.EMPTY;
        }
        Map<String/*adsField*/, String/*value*/> adsFieldValue = queryParams.stream().collect(Collectors.toMap(k -> elementFieldMap.get(k.getElementCode()), ElementInstDTO::getElementValue, (o, n) -> n));
        Map<String/*adsField*/, Byte/*valueRangeType*/> adsFieldValueType = queryParams.stream()
                .collect(Collectors.toMap(k -> elementFieldMap.get(k.getElementCode()), k -> elementValueRangeTypeMap.get(k.getElementCode()), (o, n) -> n));
        return " and " + adsFieldValue.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getValue())).map(entry -> {
            String value = entry.getValue();
            ValueRangeTypeEnum valueRangeTypeEnum = ValueRangeTypeEnum.of(adsFieldValueType.get(entry.getKey()));
            if (SystemFieldConditionEnum.isSystemCondition(entry.getKey())) {
                SystemFieldConditionEnum conditionEnum = SystemFieldConditionEnum.of(entry.getKey());
                assert conditionEnum != null;
                return conditionEnum.getFieldCode() + " " + conditionEnum.getCondition() + " " + String.format(conditionEnum.getStringFormat(), entry.getValue());
            } else if (value.contains("[")
                    && JSONValidator.from(value).validate() && entry.getKey().startsWith("dimension")) {
                JSONArray array = JSON.parseObject(value, JSONArray.class);
                String collection = array.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
                if (YesOrNoEnum.YES.getCode().equals(instPageQuery.getNeedDefaultParam())) {
                    return entry.getKey() + " in " + "(" + collection + ",'')";
                } else {
                    return entry.getKey() + " in " + "(" + collection + ")";
                }
            } else if (value.contains("[")
                    && JSONValidator.from(value).validate()
                    && entry.getKey().startsWith("param")) {
                JSONArray array = JSON.parseObject(value, JSONArray.class);
                Iterator<Object> iterator = array.stream().iterator();
                List<String> subConditionStrs = new ArrayList<>();
                while (iterator.hasNext()) {
                    String sub = String.valueOf(iterator.next());
                    subConditionStrs.add(entry.getKey() + " like '%" + sub + "%'");
                }
                String join = String.join(" or ", subConditionStrs);
                return "(" + join + ")";
            } else if (ValueRangeTypeEnum.CUSTOM_TEXT.equals(valueRangeTypeEnum)) {
                return entry.getKey() + " like " + "'%" + entry.getValue() + "%'";
            } else if (Objects.equals(MULTI_PARAM_TEXT, adsFieldValueType.get(entry.getKey()))) {
                return entry.getKey() + " like " + "'%\"" + entry.getValue() + "\"%'";
            } else {
                if (YesOrNoEnum.YES.getCode().equals(instPageQuery.getNeedDefaultParam())) {
                    return "(" + entry.getKey() + "=" + "'" + entry.getValue() + "' or " + entry.getKey() + "=" + "''";
                } else {
                    return entry.getKey() + "=" + "'" + entry.getValue() + "'";
                }
            }
        }).collect(Collectors.joining(" and "));
    }


    DruidDataSource adbDataSource;


    public List<ParamInstanceDOWithBLOBs> executeBySelectSql(String selectSql, List<String> parameters) throws SQLException {
        log.info("ParamInstRepositoryImpl.executeSelectSql : {}", selectSql);
        Connection connection = null;
        Statement statement = null;
        ResultSet rs = null;
        try {
            connection = adbDataSource.getConnection();
            PreparedStatement preparedStatement = connection.prepareStatement(selectSql);
            for (int i = 0; i < parameters.size(); i++) {
                preparedStatement.setString(i + 1, parameters.get(i));
            }
            rs = preparedStatement.executeQuery();
            BeanListHandler<ParamInstanceDOWithBLOBs> beanListHandler = new BeanListHandler<>(ParamInstanceDOWithBLOBs.class);
            List<ParamInstanceDOWithBLOBs> handle = beanListHandler.handle(rs);
            return handle;
        } catch (SQLException e) {
            log.error("ParamInstRepository.executeBySelectSql database error selectSql:{} ", selectSql, e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        } finally {
            if (Objects.nonNull(connection)) {
                connection.close();
            }
            if (Objects.nonNull(statement)) {
                statement.close();
            }
            if (Objects.nonNull(rs)) {
                rs.close();
            }
        }
    }

    public Long executeByCountSql(String countSql, List<String> parameters) throws SQLException {
        log.info("ParamInstRepositoryImpl.executeCountSql : {}", countSql);
        Connection connection = null;
        Statement statement = null;
        ResultSet countRs = null;
        try {
            connection = adbDataSource.getConnection();
            PreparedStatement preparedStatement = connection.prepareStatement(countSql);
            for (int i = 0; i < parameters.size(); i++) {
                preparedStatement.setString(i + 1, parameters.get(i));
            }
            countRs = preparedStatement.executeQuery();
            if (countRs.next()) {
                return (Long) countRs.getObject(1);
            }
        } catch (SQLException e) {
            log.error("ParamInstRepository.executeByCountSql database error countSql:{} ", countSql, e);
            throw new RuntimeException(e);
        } finally {
            if (Objects.nonNull(connection)) {
                connection.close();
            }
            if (Objects.nonNull(statement)) {
                statement.close();
            }
            if (Objects.nonNull(countRs)) {
                countRs.close();
            }
        }
        return 0L;
    }

    @Value("${sharding.jdbc.datasource.adb.jdbcUrl}")
    private String url;

    @Value("${sharding.jdbc.datasource.adb.username}")
    private String user;

    @Value("${sharding.jdbc.datasource.adb.password}")
    private String password;

    @Value("${sharding.jdbc.datasource.adb.driverClassName}")
    private String driverClass;

    @Override
    public void afterPropertiesSet() {
        adbDataSource = new DruidDataSource();
        adbDataSource.setDriverClassName(driverClass);
        adbDataSource.setUrl(url);
        adbDataSource.setUsername(user);
        adbDataSource.setPassword(password);
        adbDataSource.setMinIdle(10);
        adbDataSource.setMaxActive(100);
        adbDataSource.setMaxWait(100);
        adbDataSource.setInitialSize(10);
        adbDataSource.setRemoveAbandoned(true);
    }

}
