package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class CompareIssueVO {

    /**
     * 修改字段
     */
    String modifyField;

    /**
     * 旧内容
     */
    String oldContent;

    /**
     * 新内容
     */
    String newContent;


    public String toLog(){
        return String.format(CommonConstant.COMPARE_ISSUE_TEMPLATE,modifyField,oldContent,newContent);
    }

}
