package com.shizhuang.scm.rulecenter.domains.inst.assembler;

import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.google.common.collect.Lists;
import com.poizon.fusion.utils.DateUtils;
import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.constant.ElementTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.IsDelEnum;
import com.shizhuang.scm.rulecenter.api.constant.ParamInstEnableEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.dto.TableHeadDTO;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.InstDimResponse;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstAggEntity;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ElementInstVO;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ParamInstImportExcelVO;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstImportContext;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode;
import com.shizhuang.scm.rulecenter.infrastructure.common.utils.LetterUtil;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Component
public class ParamInstAssembler {

    @Resource
    MetaDomainService metaDomainService;

    @Resource
    private ThreadPoolTaskExecutor asyncElementServiceExecutor;


    public InstDimResponse toResponse(ParamInstanceDOWithBLOBs paramInstanceDO, List<SchemaElementEntity> allElements, Map<String/*elementCode*/, String/*adsField*/> elementFieldMap) {

        List<ElementInstDTO> paramInstanceList = new ArrayList<>();
        Map<String/*elementCode*/, Object/*elementValue*/> elementValueMap = new HashMap<>();
        for (SchemaElementEntity paramElement : allElements) {
            String fieldCode = elementFieldMap.get(paramElement.getElementCode());
            fieldCode = fieldCode.replace("_", "");
            try {
                Method method = ParamInstanceDOWithBLOBs.class.getMethod("get" + LetterUtil.getMethodName(fieldCode));
                String elementValue = (String) method.invoke(paramInstanceDO);
                if (StringUtils.isBlank(elementValue)) {
                    continue;
                }
                elementValueMap.put(paramElement.getElementCode(), elementValue);

            } catch (Exception e) {
                throw new BizRuntimeException(ErrorCode.REFLECT_ERROR.getCode(), ErrorCode.REFLECT_ERROR.getErrMsg());
            }
        }

        for (SchemaElementEntity paramElement : allElements) {
            try {
                ElementInstDTO elementInstDTO = parseElementInstDTO(paramElement, elementValueMap);
                if (elementInstDTO == null) continue;
                paramInstanceList.add(elementInstDTO);
            } catch (Exception e) {
                throw new BizRuntimeException(ErrorCode.REFLECT_ERROR.getCode(), ErrorCode.REFLECT_ERROR.getErrMsg());
            }
        }

        addBaseField(paramInstanceDO, paramInstanceList);

        InstDimResponse instDimResponse = new InstDimResponse();
        instDimResponse.setSchemaCode(paramInstanceDO.getSchemaCode());
        instDimResponse.setDimension(paramInstanceDO.getDimension());
        instDimResponse.setDimensionKey(paramInstanceDO.getDimensionKey());
        instDimResponse.setElements(paramInstanceList);
        instDimResponse.setCreateTime(paramInstanceDO.getCreateTime());
        instDimResponse.setModifyTime(paramInstanceDO.getModifyTime());
        return instDimResponse;
    }

    public InstDimPageResponse toPageResponse(ParamInstanceDOWithBLOBs paramInstanceDO, List<SchemaElementEntity> allElements, Map<String/*elementCode*/, String/*adsField*/> elementFieldMap) {

        List<ElementInstDTO> paramInstanceList = new ArrayList<>();
        Map<String, Object> elementValueMap = new HashMap<>();
        Map<String, Object> elementDescMap = new HashMap<>();


        CountDownLatch countDownLatch = new CountDownLatch(allElements.size());
        for (SchemaElementEntity schemaElement : allElements) {
            String fieldCode = elementFieldMap.get(schemaElement.getElementCode());
            fieldCode = fieldCode.replace("_", "");
            try {
                Method method = ParamInstanceDOWithBLOBs.class.getMethod("get" + LetterUtil.getMethodName(fieldCode));
                String elementValue = (String) method.invoke(paramInstanceDO);
                if (StringUtils.isBlank(elementValue)) {
                    continue;
                }
                elementValueMap.put(schemaElement.getElementCode(), elementValue);

            } catch (Exception e) {
                throw new BizRuntimeException(ErrorCode.REFLECT_ERROR.getCode(), ErrorCode.REFLECT_ERROR.getErrMsg());
            }
        }
        for (SchemaElementEntity schemaElement : allElements) {
            asyncElementServiceExecutor.execute(() -> {
                extracted(schemaElement, paramInstanceList, elementValueMap, elementDescMap);
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        addBaseField(elementValueMap, elementDescMap, paramInstanceDO, paramInstanceList);

        InstDimPageResponse instDimPageResponse = new InstDimPageResponse();
        instDimPageResponse.setSchemaCode(paramInstanceDO.getSchemaCode());
        instDimPageResponse.setDimension(paramInstanceDO.getDimension());
        instDimPageResponse.setDimensionKey(paramInstanceDO.getDimensionKey());
        instDimPageResponse.setElementValueMap(elementValueMap);
        instDimPageResponse.setElementDescMap(elementDescMap);
        instDimPageResponse.setVersion(paramInstanceDO.getVersion());
        instDimPageResponse.setAggId(paramInstanceDO.getAggId());
        return instDimPageResponse;
    }

    private void extracted(SchemaElementEntity paramElement, List<ElementInstDTO> paramInstanceList, Map<String, Object> elementValueMap, Map<String, Object> elementDescMap) {
        String elementCode = paramElement.getElementCode();
        try {
            ElementInstDTO elementInstDTO = parseElementInstDTO(paramElement, elementValueMap);
            if (elementInstDTO == null) return;
            paramInstanceList.add(elementInstDTO);
            elementDescMap.put(elementCode, elementInstDTO.getElementDesc());
            afterProcess(paramElement, elementValueMap, elementDescMap, elementInstDTO, elementCode);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void afterProcess(SchemaElementEntity paramElement, Map<String, Object> elementValueMap, Map<String, Object> elementDescMap, ElementInstDTO elementInstDTO, String elementCode) {
        if (Objects.equals(paramElement.getFrontComponent(), "rmbDigit")) {
            Object elementValue = elementInstDTO.getElementValue();
            if (elementValue != null && StringUtils.isNotBlank(elementValue.toString())) {
                BigDecimal fen = new BigDecimal(elementValue.toString());
                BigDecimal divide = fen.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                elementValueMap.put(elementCode, String.valueOf(fen.intValue()));
                elementDescMap.put(elementCode, divide.toString());
            }
        }
        if (Objects.equals(paramElement.getFrontComponent(), "picture")) {
            Object elementValue = elementInstDTO.getElementValue();
            if (elementValue != null && StringUtils.isNotBlank(elementValue.toString())) {
                List<String> strings = JSON.parseArray(String.valueOf(elementValue), String.class);
                elementValueMap.put(elementCode, strings);
                elementDescMap.put(elementCode, strings);
            }
        }
    }

    private ElementInstDTO parseElementInstDTO(SchemaElementEntity paramElement, Map<String, Object> elementValueMap) {
        Object obj = elementValueMap.get(paramElement.getElementCode());
        if (Objects.isNull(obj) || StringUtils.isBlank(String.valueOf(obj))) {
            return null;
        }
        String elementValue = String.valueOf(obj);
        ElementInstDTO elementInstDTO = new ElementInstDTO();
        elementInstDTO.setElementCode(paramElement.getElementCode());
        elementInstDTO.setElementValue(elementValue);
        if (ValueRangeTypeEnum.CUSTOM_TEXT.getType().equals(paramElement.getValueRangeType())) {
            elementInstDTO.setElementDesc(elementValue);
        } else {
            if (Objects.equals(paramElement.getIsMulti(), 1)
                    && ElementTypeEnum.PARAM.getType().equals(paramElement.getElementType())
                    && StringUtils.isNotBlank(elementValue) && StringUtils.startsWith(elementValue, "[")) {
                List<String> subValues = JSON.parseArray(elementValue, String.class);
                if (CollectionUtils.isNotEmpty(subValues)) {
                    List<String> elemValueDescList = subValues.stream().map(v -> metaDomainService.getFirstElemDesc(paramElement.getSchemaCode(), paramElement.getElementCode(), v, elementValueMap)).collect(Collectors.toList());
                    elementInstDTO.setElementDesc(String.join(",", elemValueDescList));
                    elementValueMap.put(paramElement.getElementCode(), subValues);
                }
            } else {
                String elemValueDesc = metaDomainService.getFirstElemDesc(paramElement.getSchemaCode(), paramElement.getElementCode(), elementValue, elementValueMap);
                elementInstDTO.setElementDesc(elemValueDesc);
            }
        }
        if (Objects.equals(paramElement.getFrontComponent(), "rmbDigit")) {
            if (StringUtils.isNotBlank(elementValue)) {
                BigDecimal fen = new BigDecimal(elementValue);
                BigDecimal divide = fen.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                elementInstDTO.setElementValue(String.valueOf(fen.intValue()));
                elementInstDTO.setElementDesc(divide.toString());
            }
        }

        return elementInstDTO;
    }

    private void addBaseField(Map<String, Object> elementValueMap, Map<String, Object> elementDescMap, ParamInstanceDOWithBLOBs paramInstanceDO, List<ElementInstDTO> paramInstanceList) {
        ElementInstDTO enabled = new ElementInstDTO();
        enabled.setElementCode("enabled");
        enabled.setElementName("生效状态");
        enabled.setElementValue(String.valueOf(paramInstanceDO.getEnabled()));
        ParamInstEnableEnum paramInstEnableEnum = ParamInstEnableEnum.of(paramInstanceDO.getEnabled());
        enabled.setElementDesc(paramInstEnableEnum.getDesc());
        paramInstanceList.add(enabled);
        elementValueMap.put("enabled", paramInstanceDO.getEnabled());
        elementDescMap.put("enabled", paramInstEnableEnum.getDesc());

        ElementInstDTO isDel = new ElementInstDTO();
        isDel.setElementCode("isDel");
        isDel.setElementName("是否删除");
        isDel.setElementValue(String.valueOf(paramInstanceDO.getIsDel()));
        IsDelEnum isDelEnum = IsDelEnum.of(paramInstanceDO.getIsDel());
        isDel.setElementDesc(isDelEnum.getDesc());
        paramInstanceList.add(isDel);
        elementValueMap.put("isDel", paramInstanceDO.getIsDel());
        elementDescMap.put("isDel", isDelEnum.getDesc());

        ElementInstDTO lastModifier = new ElementInstDTO();
        lastModifier.setElementCode("lastModifier");
        lastModifier.setElementName("最后修改人");
        lastModifier.setElementValue(paramInstanceDO.getModifier());
        lastModifier.setElementDesc(paramInstanceDO.getModifier());
        paramInstanceList.add(lastModifier);
        elementValueMap.put("lastModifier", paramInstanceDO.getModifier());
        elementDescMap.put("lastModifier", paramInstanceDO.getModifier());

        ElementInstDTO lastModifyTime = new ElementInstDTO();
        lastModifyTime.setElementCode("lastModifyTime");
        lastModifyTime.setElementName("最后修改时间");
        lastModifyTime.setElementValue(DateUtils.parseDateToStr(paramInstanceDO.getMtime()));
        lastModifyTime.setElementDesc(DateUtils.parseDateToStr(paramInstanceDO.getMtime()));
        paramInstanceList.add(lastModifyTime);
        elementValueMap.put("lastModifyTime", DateUtils.parseDateToStr(paramInstanceDO.getMtime()));
        elementDescMap.put("lastModifyTime", DateUtils.parseDateToStr(paramInstanceDO.getMtime()));

        ElementInstDTO instCreator = new ElementInstDTO();
        instCreator.setElementCode("instCreator");
        instCreator.setElementName("创建人");
        instCreator.setElementValue(paramInstanceDO.getCreator());
        instCreator.setElementDesc(paramInstanceDO.getCreator());
        paramInstanceList.add(instCreator);
        elementValueMap.put("instCreator", paramInstanceDO.getCreator());
        elementDescMap.put("instCreator", paramInstanceDO.getCreator());

        ElementInstDTO instCreateTime = new ElementInstDTO();
        instCreateTime.setElementCode("instCreateTime");
        instCreateTime.setElementName("创建时间");
        instCreateTime.setElementValue(DateUtils.parseDateToStr(paramInstanceDO.getCtime()));
        instCreateTime.setElementDesc(DateUtils.parseDateToStr(paramInstanceDO.getCtime()));
        paramInstanceList.add(instCreateTime);
        elementValueMap.put("instCreateTime", DateUtils.parseDateToStr(paramInstanceDO.getCtime()));
        elementDescMap.put("instCreateTime", DateUtils.parseDateToStr(paramInstanceDO.getCtime()));

    }

    private void addBaseField(ParamInstanceDOWithBLOBs paramInstanceDO, List<ElementInstDTO> paramInstanceList) {
        ElementInstDTO enabled = new ElementInstDTO();
        enabled.setElementCode("enabled");
        enabled.setElementName("生效状态");
        enabled.setElementValue(String.valueOf(paramInstanceDO.getEnabled()));
        ParamInstEnableEnum paramInstEnableEnum = ParamInstEnableEnum.of(paramInstanceDO.getEnabled());
        enabled.setElementDesc(paramInstEnableEnum.getDesc());
        paramInstanceList.add(enabled);

        ElementInstDTO isDel = new ElementInstDTO();
        isDel.setElementCode("isDel");
        isDel.setElementName("是否删除");
        isDel.setElementValue(String.valueOf(paramInstanceDO.getIsDel()));
        IsDelEnum isDelEnum = IsDelEnum.of(paramInstanceDO.getIsDel());
        isDel.setElementDesc(isDelEnum.getDesc());
        paramInstanceList.add(isDel);
    }

    public static ParamInstImportExcelVO transToExcelVO(BaseImportExcel baseImportExcel, ParamInstImportContext ctx) {
        List<Integer> allElementIndex = ctx.getAllElementIndex();
        List<SchemaElementEntity> existAllElement = ctx.getExistAllElement();
        List<Integer> dimensionIndex = ctx.getDimensionIndex();
        List<SchemaElementEntity> existDimensionElement = ctx.getExistDimensionElement();

        ParamInstImportExcelVO paramInstImportExcelVO = new ParamInstImportExcelVO();
        paramInstImportExcelVO.setTenantId(baseImportExcel.getTenantId());
        paramInstImportExcelVO.setTaskId(baseImportExcel.getTaskId());
        paramInstImportExcelVO.setRowId(baseImportExcel.getRowId());
        paramInstImportExcelVO.setBaseImportExcel(baseImportExcel);
        Map<Integer, Object> columnValueMap = baseImportExcel.getColumnValueMap();
        List<ElementInstVO> elementInstVOList = buildElementInstList(allElementIndex, existAllElement, columnValueMap);
        List<ElementInstVO> dimensionVOList = buildElementInstList(dimensionIndex, existDimensionElement, columnValueMap);
        String dimension = dimensionVOList.stream().map(ElementInstVO::getElementCode).collect(Collectors.joining("#"));
        paramInstImportExcelVO.setDimension(dimension);
        paramInstImportExcelVO.setElementInstVOList(elementInstVOList);
        Map<String, String> descMap = elementInstVOList.stream().collect(Collectors.toMap(ElementInstVO::getElementCode, ElementInstVO::getElementDesc));
        paramInstImportExcelVO.setDescMap(descMap);
        return paramInstImportExcelVO;
    }

    private static List<ElementInstVO> buildElementInstList(List<Integer> elementIndex, List<SchemaElementEntity> existElements, Map<Integer, Object> columnValueMap) {
        List<ElementInstVO> elementInstVOS = new ArrayList<>();
        for (int k = 0; k < elementIndex.size(); k++) {
            SchemaElementEntity schemaElement = existElements.get(k);
            String elementCode = schemaElement.getElementCode();
            String elementName = schemaElement.getElementName();
            Integer index = elementIndex.get(k);
            Object descObject = columnValueMap.get(index);
            if (Objects.isNull(descObject) || StringUtils.isBlank(String.valueOf(descObject))) {
                continue;
            }
            ElementInstVO elementInstVO = ElementInstVO.builder().elementCode(elementCode).elementDesc(String.valueOf(descObject)).elementName(elementName).build();
            elementInstVOS.add(elementInstVO);
        }
        return elementInstVOS;
    }


    public static List<ParamInstEntity> getParamInstEntities(List<ElementInstAddCmd> paramMap, SchemaMetaAggregate schemaAggregate, String aggId) {
        String schemaCode = schemaAggregate.getSchemaEntity().getSchemaCode();
        Map<String, Object> paramValueMap = paramMap.stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        List<SchemaElementEntity> schemaDimensions = schemaAggregate.getDimensions();
        List<List<String>> dimensionCollection = extractDimensionSet(schemaDimensions, paramValueMap);
        List<List<String>> dimensionKeySets = cartesianProduct(dimensionCollection);

        List<String> existDimension = schemaDimensions.stream().map(SchemaElementEntity::getElementCode).filter(paramValueMap::containsKey).collect(Collectors.toList());
        List<SchemaElementEntity> schemaParams = schemaAggregate.getParams();
        List<String> paramCollection = new ArrayList<>();
        List<String> existParams = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(schemaParams)) {
            paramCollection = extractParamSet(schemaParams, paramValueMap);
            existParams = schemaParams.stream().map(SchemaElementEntity::getElementCode).filter(paramValueMap::containsKey).collect(Collectors.toList());
        }

        String dimension = String.join("#", existDimension);
        List<String> finalExistParams = existParams;
        List<String> finalParamCollection = paramCollection;
        List<ElementInstVO> params = new ArrayList<>();
        for (int i = 0; i < finalExistParams.size(); i++) {
            ElementInstVO elementInstVO = ElementInstVO.builder().elementCode(finalExistParams.get(i)).elementValue(finalParamCollection.get(i)).build();
            params.add(elementInstVO);
        }

        return dimensionKeySets.stream().map(d -> {
            String dimensionKey = String.join("#", d);
            ParamInstEntity paramInstEntity = new ParamInstEntity();
            paramInstEntity.setSchemaCode(schemaCode);
            paramInstEntity.setDimension(dimension);
            paramInstEntity.setDimensionKey(dimensionKey);
            paramInstEntity.setVersion(CommonConstant.ONE);
            paramInstEntity.setEnabled(CommonConstant.ONE);
            paramInstEntity.setIsDel(CommonConstant.IS_NOT_DELETE);
            List<ElementInstVO> dimensions = new ArrayList<>();
            for (int i = 0; i < d.size(); i++) {
                ElementInstVO elementInstVO = ElementInstVO.builder().elementCode(existDimension.get(i)).elementValue(d.get(i)).build();
                dimensions.add(elementInstVO);
            }
            paramInstEntity.setDimensions(dimensions);
            paramInstEntity.setParams(params);
            paramInstEntity.setAggId(aggId);
            return paramInstEntity;
        }).collect(Collectors.toList());
    }

    private static List<List<String>> extractDimensionSet(List<SchemaElementEntity> element, Map<String, Object> paramValueMap) {
        List<List<String>> elementValueList = new ArrayList<>();
        for (SchemaElementEntity schemaElement : element) {
            String elementCode = schemaElement.getElementCode();
            if (!paramValueMap.containsKey(elementCode)) {
                continue;
            }
            Object elementValueObj = paramValueMap.get(elementCode);
            if (elementValueObj instanceof List) {
                List<String> list = ((List<?>) elementValueObj).stream().map(String::valueOf).collect(Collectors.toList());
                elementValueList.add(list);
            } else if (elementValueObj instanceof String) {
                elementValueList.add(Lists.newArrayList(String.valueOf(elementValueObj)));
            }
        }
        return elementValueList;
    }

    private static List<String> extractParamSet(List<SchemaElementEntity> element, Map<String, Object> paramValueMap) {
        List<String> elementValueList = new ArrayList<>();
        for (SchemaElementEntity schemaElement : element) {
            String elementCode = schemaElement.getElementCode();
            if (!paramValueMap.containsKey(elementCode)) {
                continue;
            }
            Object elementValueObj = paramValueMap.get(elementCode);
            if (elementValueObj instanceof List) {
                elementValueList.add(JSON.toJSONString(elementValueObj));
            } else if (Objects.equals(schemaElement.getFrontComponent(), "rmbDigit")) {
                String valueStr = String.valueOf(elementValueObj);
                if (StringUtils.isNotBlank(valueStr)) {
                    BigDecimal bigDecimal = new BigDecimal(valueStr);
                    BigDecimal multiply = bigDecimal.multiply(new BigDecimal(100));
                    elementValueList.add(multiply.toString());
                }
            } else {
                elementValueList.add(String.valueOf(elementValueObj));
            }
        }
        return elementValueList;
    }

    public static <T> List<List<T>> cartesianProduct(List<List<T>> sets) {
        if (sets.isEmpty()) {
            return new ArrayList<>(); // Empty product for empty input
        }

        List<List<T>> product = new ArrayList<>();
        backtrack(sets, 0, new ArrayList<>(), product);
        return product;
    }

    private static <T> void backtrack(List<List<T>> sets, int index, List<T> current, List<List<T>> product) {
        if (index == sets.size()) {
            product.add(new ArrayList<>(current));
            return;
        }

        for (T element : sets.get(index)) {
            current.add(element);
            backtrack(sets, index + 1, current, product);
            current.remove(current.size() - 1);
        }
    }

    public ParamInstAggEntity getParamAggInstEntityByDO(SchemaMetaAggregate schemaMetaAggregate, List<ParamInstanceDOWithBLOBs> paramInstanceDOS) {
        ParamInstAggEntity paramInstAggEntity = new ParamInstAggEntity();
        paramInstAggEntity.setAggId(paramInstanceDOS.get(0).getAggId());
        String schemaCode = schemaMetaAggregate.getSchemaEntity().getSchemaCode();
        List<SchemaElementEntity> allElements = schemaMetaAggregate.getAllElements();
        Map<String, String> elementFieldMap = schemaMetaAggregate.getElementFieldMap();

        Map<String, Object> extParam = new HashMap<>();
        allElements.forEach(schemaElement -> {
            List<String> fieldValues = new ArrayList<>();
            String fieldCode = elementFieldMap.get(schemaElement.getElementCode());
            fieldCode = fieldCode.replace("_", "");
            try {
                Method method = ParamInstanceDOWithBLOBs.class.getMethod("get" + LetterUtil.getMethodName(fieldCode));
                for (ParamInstanceDOWithBLOBs paramInstanceDO : paramInstanceDOS) {
                    String elementValue = (String) method.invoke(paramInstanceDO);
                    if (StringUtils.isBlank(elementValue) || fieldValues.contains(elementValue)) {
                        continue;
                    }
                    fieldValues.add(elementValue);
                }
                if (CollectionUtils.isEmpty(fieldValues)) {
                    return;
                }
                fieldValues = fieldValues.stream().distinct().collect(Collectors.toList());
                if (fieldValues.size() == 1) {
                    if (Objects.equals(schemaElement.getIsMulti(), 1)
                            && ElementTypeEnum.PARAM.getType().equals(schemaElement.getElementType()) &&
                            StringUtils.startsWith(fieldValues.get(0), "[")) {
                        List<String> subValues = JSON.parseArray(fieldValues.get(0), String.class);
                        if (CollectionUtils.isNotEmpty(subValues)) {
                            extParam.put(schemaElement.getElementCode(), subValues);
                        }
                    } else {
                        extParam.put(schemaElement.getElementCode(), fieldValues.get(0));
                    }
                } else {
                    extParam.put(schemaElement.getElementCode(), fieldValues);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        extParam.forEach((varName, varValue) -> {
            try {
                String fieldCode = elementFieldMap.get(varName);
                fieldCode = fieldCode.replace("_", "");
                Method aggEntityMethod = ParamInstAggEntity.class.getMethod("set" + LetterUtil.getMethodName(fieldCode), String.class);
                if (varValue instanceof List) {
                    List<String> list = new ArrayList<>();
                    for (String s : (List<String>) varValue) {
                        String firstElemDesc = metaDomainService.getFirstElemDesc(schemaCode, varName, String.valueOf(s), extParam);
                        list.add(firstElemDesc);
                    }
                    list = list.stream().sorted().collect(Collectors.toList());
                    aggEntityMethod.invoke(paramInstAggEntity, JSON.toJSONString(list));
                } else {
                    String desc = metaDomainService.getFirstElemDesc(schemaCode, varName, (String) varValue, extParam);
                    aggEntityMethod.invoke(paramInstAggEntity, desc);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        return paramInstAggEntity;
    }

    public ParamInstAggEntity getParamAggInstEntity(SchemaMetaAggregate schemaMetaAggregate, List<ElementInstAddCmd> paramMap) {

        Map<String, String> elementFieldMap = schemaMetaAggregate.getElementFieldMap();
        ParamInstAggEntity paramInstAggEntity = new ParamInstAggEntity();
        String schemaCode = schemaMetaAggregate.getSchemaEntity().getSchemaCode();
        Map<String, Object> extParam = paramMap.stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        paramMap.forEach(param -> {
            String fieldCode = elementFieldMap.get(param.getElementCode());
            fieldCode = fieldCode.replace("_", "");
            try {
                Method aggEntityMethod = ParamInstAggEntity.class.getMethod("set" + LetterUtil.getMethodName(fieldCode), String.class);
                if (param.getElementValue() instanceof String) {
                    String firstElemDesc = metaDomainService.getFirstElemDesc(schemaCode, param.getElementCode(), String.valueOf(param.getElementValue()), extParam);
                    aggEntityMethod.invoke(paramInstAggEntity, firstElemDesc);
                } else if (param.getElementValue() instanceof List) {
                    List<String> list = new ArrayList<>();
                    List<?> elementValues = ((List<?>) param.getElementValue()).stream().sorted(Comparator.comparing(JSON::toJSONString)).collect(Collectors.toList());
                    for (Object s : elementValues) {
                        String firstElemDesc = metaDomainService.getFirstElemDesc(schemaCode, param.getElementCode(), String.valueOf(s), extParam);
                        list.add(firstElemDesc);
                    }
                    list = list.stream().sorted().collect(Collectors.toList());
                    aggEntityMethod.invoke(paramInstAggEntity, JSON.toJSONString(list));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return paramInstAggEntity;
    }

}
