package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.dto.ElementEnumDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementCreateContext;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.DubboInterfaceValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.ElementValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.ValueRepeatableValidator;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 元素创建校验逻辑
 */
@Slf4j
@Component
public class ElementCreateValidatorChain implements IBasicHandler<ElementCreateContext>, ApplicationContextAware {
    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(ElementCreateContext elementCreateContext) {
        ComplexResult result = FluentValidator.checkAll()
                .putAttribute2Context(CommonConstant.APPLICATION_CONTEXT, this.applicationContext)
                .on(elementCreateContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            //打印相关日志，抛出相关异常,用户输入的错误打info日志即可
            log.info("ElementCreateValidator validate has errRes : errCode:{}, errMsg:{}", validationError.getErrorCode(), validationError.getErrorMsg());
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(
                // 1、同方案下（common或者指定方案），输入的元素code与现有的元素code不能相同
                ValidatorHandlerFactory.build(ecc -> validateElementRepeatable((ElementCreateContext) ecc)),

                // 2、元素code需要以驼峰形式命名，
                ValidatorHandlerFactory.build(ecc -> validateElementCodeHump((ElementCreateContext) ecc)),

                // 3、创建的元素code不能与系统code相冲突
                ValidatorHandlerFactory.build( ecc -> validateSystemFieldCode((ElementCreateContext) ecc)),

                // 4、枚举类型里的值域 值或者值描述不能为空
                ValidatorHandlerFactory.build(ecc -> acceptEnumElement((ElementCreateContext) ecc)
                        , ecc -> validateValueEmpty((ElementCreateContext) ecc)),

                // 5、枚举类型里的值域 值或者值描述不能有相同的情况，比如品牌id不能有重复，品牌名称不能有重复
                ValidatorHandlerFactory.build(ecc -> acceptEnumElement((ElementCreateContext) ecc)
                        , ecc -> validateValueRepeatable((ElementCreateContext) ecc)),

                // 6、如果是dubbo查询接口类型，需要做基本的字段判空校验
                ValidatorHandlerFactory.build(ecc -> acceptDubboElement((ElementCreateContext) ecc)
                        , ecc -> validateDubboInterface((ElementCreateContext) ecc)),

                // 7、如果是全量dubbo全量接口，会校验当前接口是否能调通
//                ValidatorHandlerFactory.build(ecc -> acceptFullDubboElement((ElementCreateContext) ecc)
//                        , ecc -> validateDubboInvokeSuccess((ElementCreateContext) ecc)),

                // 8、需要确定检查如果是dubbo单点查询，需要强制设置有且仅有一个#{var}的变量
                ValidatorHandlerFactory.build(ecc -> acceptSearchDubboElement((ElementCreateContext) ecc)
                        , ecc -> validateSearchDubboElement((ElementCreateContext) ecc))
        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }

    private ValidationError validateSystemFieldCode(ElementCreateContext ecc) {
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        String elementCode = elementCreateCmd.getElementCode();
        ElementValidator validator = applicationContext.getBean(ElementValidator.class);
        return validator.validateSystemFieldCode(elementCode);
    }

    private ValidationError validateElementCodeHump(ElementCreateContext ecc) {
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        String elementCode = elementCreateCmd.getElementCode();
        ElementValidator validator = applicationContext.getBean(ElementValidator.class);
        return validator.validateHump(elementCode);
    }

    private ValidationError validateDubboInvokeSuccess(ElementCreateContext ecc) {
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        ElementInterfaceDTO fullInterface = elementCreateCmd.getFullInterface();
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        return dubboInterfaceValidator.validateDubboInterfaceConnect(fullInterface);
    }

    private Boolean acceptFullDubboElement(ElementCreateContext ecc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        return dubboInterfaceValidator.acceptFullDubboInterface(elementCreateCmd.getValueRangeType());
    }

    private ValidationError validateElementRepeatable(ElementCreateContext elementCreateContext) {
        ElementValidator validator = applicationContext.getBean(ElementValidator.class);
        ElementCreateCmd elementCreateCmd = elementCreateContext.getElementCreateCmd();
        return validator.validateRepeatable(elementCreateCmd.getSchemaCode(), elementCreateCmd.getElementCode());
    }

    private Boolean acceptEnumElement(ElementCreateContext ecc) {
        ValueRepeatableValidator valueRepeatableValidator = applicationContext.getBean(ValueRepeatableValidator.class);
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        return valueRepeatableValidator.accept(elementCreateCmd.getValueRangeType());
    }

    private ValidationError validateValueEmpty(ElementCreateContext ecc) {
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        List<ElementEnumDTO> enums = elementCreateCmd.getEnums();
        if(CollectionUtils.isEmpty(enums)){
            return ValidationErrorFactory.create(ErrorCode.ELEMENT_VALUE_IS_EMPTY);
        }
        return null;
    }

    private ValidationError validateValueRepeatable(ElementCreateContext ecc) {
        ValueRepeatableValidator valueRepeatableValidator = applicationContext.getBean(ValueRepeatableValidator.class);
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        return valueRepeatableValidator.validate(elementCreateCmd.getEnums());
    }

    private Boolean acceptDubboElement(ElementCreateContext ecc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        return dubboInterfaceValidator.acceptBaseDubboInterface(ecc.getElementCreateCmd().getValueRangeType());
    }

    private ValidationError validateDubboInterface(ElementCreateContext ecc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        Byte valueRangeType = elementCreateCmd.getValueRangeType();
        return dubboInterfaceValidator.validate(valueRangeType, elementCreateCmd.getFullInterface(), elementCreateCmd.getValueToDescInterface(), elementCreateCmd.getDescToValueInterface());
    }


    private Boolean acceptSearchDubboElement(ElementCreateContext ecc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        ElementCreateCmd elementCreateCmd = ecc.getElementCreateCmd();
        return dubboInterfaceValidator.acceptSearchDubboInterface(elementCreateCmd.getValueRangeType());
    }

    private ValidationError validateSearchDubboElement(ElementCreateContext ecc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        return dubboInterfaceValidator.validateSearchDubboInterface(ecc.getElementCreateCmd().getValueToDescInterface(), ecc.getElementCreateCmd().getDescToValueInterface());
    }

}
