package com.shizhuang.scm.rulecenter.domains.inst.context;

import com.dewu.scm.lms.api.report.dto.request.ReportPageRequest;
import com.dewu.scm.lms.api.report.dto.response.ReportPageResponse;
import com.github.pagehelper.PageInfo;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.query.ParamInstExportRequest;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.InstDimResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class ParamInstExportContext {

    /**
     * 导出请求
     */
    ReportPageRequest<ParamInstExportRequest> pageRequest;

    /**
     * 内部分页查询请求
     */
    InstPageQuery instPageQuery;

    /**
     * 方案聚合
     */
    SchemaMetaAggregate schemaMetaAggregate;

    /**
     * 分页查询返回值
     */
    PageInfoResponse<InstDimPageResponse> instDimResponsePageInfo;

    /**
     * 返回给导入导出框架的response
     */
    ReportPageResponse reportPageResponse;

}
