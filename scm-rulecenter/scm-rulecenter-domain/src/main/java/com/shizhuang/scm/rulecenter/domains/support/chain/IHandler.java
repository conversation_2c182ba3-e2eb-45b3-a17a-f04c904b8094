package com.shizhuang.scm.rulecenter.domains.support.chain;

import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;

/**
 * 责任链
 */
public interface IHandler<CONTEXT> extends IBasicHandler<CONTEXT>{

    /**
     * 设置下一个处理节点
     * @param iHandler
     */
    void setNextHandler(IHandler<CONTEXT> iHandler);

    /**
     * 是否需要处理该请求
     * @param context
     * @return
     */
    boolean shouldHandle(CONTEXT context);

    /**
     * 处理业务逻辑，包含捕获业务异常以及链式调用
     * @param context
     */
    void handle(CONTEXT context);


    void handleException(CONTEXT context, BizRuntimeException e);

}
