package com.shizhuang.scm.rulecenter.domains.meta.service.validator;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import com.shizhuang.scm.rulecenter.infrastructure.dubbo.DubboGenericServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.*;

/**
 * 元素Dubbo接口
 */
@Slf4j
@Component
public class DubboInterfaceValidator {

    /**
     * 选择dubbo接口的情况下需要填写对应的dubbo接口信息
     *
     * @param valueRangeType
     * @param fullInterface
     * @param valueToDescInterface
     * @param descToValueInterface
     * @return
     */
    public ValidationError validate(Byte valueRangeType, ElementInterfaceDTO fullInterface, ElementInterfaceDTO valueToDescInterface, ElementInterfaceDTO descToValueInterface) {
        log.info("DubboInterfaceValidator.validate param valueRangeType:{}  fullInterface: {} , valueToDescInterface:{} , descToValueInterface:{} "
                , valueRangeType, JSON.toJSONString(fullInterface), JSON.toJSONString(valueToDescInterface), JSON.toJSONString(descToValueInterface));
        if (ValueRangeTypeEnum.DUBBO_FULL.getType().equals(valueRangeType)) {
            return validate(fullInterface);
        }
        if (ValueRangeTypeEnum.DUBBO_SEARCH.getType().equals(valueRangeType)) {
            ValidationError valueToDescValidate = validate(valueToDescInterface);
            if (Objects.nonNull(valueToDescValidate)) {
                return valueToDescValidate;
            }
            ValidationError descToValueValidate = validate(descToValueInterface);
            if (Objects.nonNull(descToValueValidate)) {
                return descToValueValidate;
            }
        }
        return null;
    }

    public ValidationError validate(ElementInterfaceDTO dubboInterface) {
        if (Objects.isNull(dubboInterface)) {
            return ValidationErrorFactory.create(DUBBO_INTERFACE_IS_NULL);
        }
        String dubboInterfacePath = dubboInterface.getDubboInterface();
        String dubboMethod = dubboInterface.getDubboMethod();
        String valueCodePath = dubboInterface.getValueCodePath();
        String valueDescPath = dubboInterface.getValueDescPath();

        if (StringUtils.isBlank(dubboInterfacePath)
                || StringUtils.isBlank(dubboMethod)
                || StringUtils.isBlank(valueCodePath)
                || StringUtils.isBlank(valueDescPath)) {
            return ValidationErrorFactory.create(DUBBO_INTERFACE_FIELD_IS_BLANK);
        }
        return null;
    }

    public Boolean acceptBaseDubboInterface(Byte valueRangeType) {
        return ValueRangeTypeEnum.isDubboInterface(valueRangeType);
    }

    public Boolean acceptSearchDubboInterface(Byte valueRangeType) {
        return ValueRangeTypeEnum.DUBBO_SEARCH.getType().equals(valueRangeType);
    }

    public Boolean acceptFullDubboInterface(Byte valueRangeType) {
        return ValueRangeTypeEnum.DUBBO_FULL.getType().equals(valueRangeType);
    }

    public ValidationError validateSearchDubboInterface(ElementInterfaceDTO valueToDescInterface, ElementInterfaceDTO descToValueInterface) {
        log.info("DubboInterfaceValidator.validateSearchDubboInterface param , valueToDescInterface:{} , descToValueInterface:{} "
                , JSON.toJSONString(valueToDescInterface), JSON.toJSONString(descToValueInterface));
        List<String> valueToDescParamValue = valueToDescInterface.getDubboParamValue();
        if (!valueToDescParamValue.contains(CommonConstant.VAR)) {
            return ValidationErrorFactory.create(DUBBO_INTERFACE_MISSING_VAR);
        }
        List<String> descToValueParamValue = descToValueInterface.getDubboParamValue();
        if (!descToValueParamValue.contains(CommonConstant.VAR)) {
            return ValidationErrorFactory.create(DUBBO_INTERFACE_MISSING_VAR);
        }
        return null;
    }

    @Resource
    DubboGenericServiceUtil dubboGenericServiceUtil;

    public ValidationError validateDubboInterfaceConnect(ElementInterfaceDTO fullInterface) {
        log.info("DubboInterfaceValidator.validateDubboInterfaceConnect param fullInterface: {} ", JSON.toJSONString(fullInterface));
        String dubboInterface = fullInterface.getDubboInterface();
        String dubboMethod = fullInterface.getDubboMethod();
        String[] dubboParamTypeArray = MetaDomainService.listToArray(fullInterface.getDubboParamType());
        String[] dubboParamValueArray = MetaDomainService.listToArray(fullInterface.getDubboParamValue());
        try {
            dubboGenericServiceUtil.invokeWhitMultiParam(dubboInterface, dubboMethod, dubboParamTypeArray, dubboParamValueArray);
        } catch (Exception e) {
            return ValidationErrorFactory.create(DUBBO_INTERFACE_CONNECT_ERROR);
        }
        return null;
    }
}
