package com.shizhuang.scm.rulecenter.domains.meta.assembler;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.SchemaFieldMappingDO;

public class SchemaFieldMappingAssembler {

    public static SchemaFieldMappingDO toDO(FieldMappingVO fieldMappingVO){
        SchemaFieldMappingDO schemaFieldMappingDO = new SchemaFieldMappingDO();
        schemaFieldMappingDO.setSchemaCode(fieldMappingVO.getSchemaCode());
        schemaFieldMappingDO.setElementCode(fieldMappingVO.getElementCode());
        schemaFieldMappingDO.setAdsFieldCode(fieldMappingVO.getFieldCode());
        schemaFieldMappingDO.setIsDel(CommonConstant.ZERO.byteValue());
        schemaFieldMappingDO.setVersion(fieldMappingVO.getVersion());
        return schemaFieldMappingDO;
    }

}
