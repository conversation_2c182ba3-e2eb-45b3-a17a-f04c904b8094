package com.shizhuang.scm.rulecenter.domains.meta.repository.transfer;

import com.alibaba.fastjson.JSON;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceSet;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementDO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(componentModel = "spring")
public interface ElementTransfer extends BaseTransfer<ElementDO, ElementMetaEntity> {

    ElementTransfer INSTANCE = Mappers.getMapper(ElementTransfer.class);

    @Override
    default ElementDO dToS(ElementMetaEntity data) {
        if ( data == null ) {
            return null;
        }

        ElementDO elementDO = new ElementDO();
        elementDO.setBelongToSchemaCode(data.getSchemaCode());
        elementDO.setElementCode( data.getElementCode() );
        elementDO.setElementName( data.getElementName() );
        elementDO.setValueRangeType( data.getValueRangeType() );
        elementDO.setElementSourceType( data.getElementSourceType() );
        elementDO.setFrontComponent(data.getFrontComponent());
        ElementInterfaceSet elementInterfaceSet = data.getElementInterfaceSet();
        elementDO.setInterfaceFeature(JSON.toJSONString(elementInterfaceSet));
        elementDO.setElementNote(data.getElementNote());

        return elementDO;
    }

    @Override
    default ElementMetaEntity sToT(ElementDO elementDO) {
        if ( elementDO == null ) {
            return null;
        }

        ElementMetaEntity elementMetaEntity = new ElementMetaEntity();
        elementMetaEntity.setSchemaCode(elementDO.getBelongToSchemaCode());
        elementMetaEntity.setElementCode( elementDO.getElementCode() );
        elementMetaEntity.setElementName( elementDO.getElementName() );
        elementMetaEntity.setElementSourceType( elementDO.getElementSourceType() );
        elementMetaEntity.setValueRangeType( elementDO.getValueRangeType() );
        elementMetaEntity.setFrontComponent(elementDO.getFrontComponent());
        String interfaceFeature = elementDO.getInterfaceFeature();
        ElementInterfaceSet elementInterfaceVO = new ElementInterfaceSet();
        if(StringUtils.isNotBlank(interfaceFeature)){
            try {
                elementInterfaceVO = JSON.parseObject(interfaceFeature, ElementInterfaceSet.class);
            } catch (Exception e) {

            }
        }
        elementMetaEntity.setElementInterfaceSet(elementInterfaceVO);
        elementMetaEntity.setElementNote(elementDO.getElementNote());
        return elementMetaEntity;
    }

}
