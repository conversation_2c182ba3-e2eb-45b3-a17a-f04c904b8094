package com.shizhuang.scm.rulecenter.domains.meta.repository.transfer;

import com.alibaba.fastjson.JSON;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementUIAttributeVO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.SchemaDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface SchemaElementTransfer extends BaseTransfer<SchemaDetailDO, SchemaElementEntity>{

    SchemaElementTransfer INSTANCE = Mappers.getMapper(SchemaElementTransfer.class);

    @Override
    default SchemaElementEntity sToT(SchemaDetailDO schemaDetailDO) {
        if ( schemaDetailDO == null ) {
            return null;
        }

        SchemaElementEntity schemaElementEntity = new SchemaElementEntity();

        schemaElementEntity.setSchemaCode( schemaDetailDO.getSchemaCode() );
        schemaElementEntity.setElementCode( schemaDetailDO.getElementCode() );
        schemaElementEntity.setElementName( schemaDetailDO.getElementName() );
        schemaElementEntity.setVersion( schemaDetailDO.getVersion() );
        schemaElementEntity.setIsDel( schemaDetailDO.getIsDel() );
        schemaElementEntity.setElementType( schemaDetailDO.getElementType() );
        String uiAttributes = schemaDetailDO.getUiAttributes();
        ElementUIAttributeVO uiAttributeVO = JSON.parseObject(uiAttributes, ElementUIAttributeVO.class);
        schemaElementEntity.setIsRequired(uiAttributeVO.getIsRequired());
        schemaElementEntity.setIsMulti(uiAttributeVO.getIsMulti());
        schemaElementEntity.setQueryCondition(uiAttributeVO.getQueryCondition());
        schemaElementEntity.setPriority(schemaDetailDO.getPriority());

        return schemaElementEntity;
    }

}
