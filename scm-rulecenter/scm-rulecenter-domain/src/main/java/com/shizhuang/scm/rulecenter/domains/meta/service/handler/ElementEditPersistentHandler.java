package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ElementEditPersistentHandler implements IBasicHandler<ElementEditContext> {

    @Resource
    ElementRepository elementRepository;

    @Override
    public void doHandle(ElementEditContext elementEditContext) {
        ElementMetaEntity editElementMetaEntity = elementEditContext.getEditElementMetaEntity();
        elementRepository.update(editElementMetaEntity);
    }
}
