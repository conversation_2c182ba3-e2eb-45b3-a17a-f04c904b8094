package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.constant.ElementSourceTypeEnum;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaCreateContext;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.SchemaElementValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.SchemaValidator;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

//校验逻辑
//  1.1、输入的维度与参数个数不能超过方案所有的字段个数
//  1.2、方案名称不要重复
//  1.3、不能有
@Component
public class SchemaCreateValidatorChain implements IBasicHandler<SchemaCreateContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(SchemaCreateContext schemaCreateContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(schemaCreateContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、方案code不能重复
                ValidatorHandlerFactory.build(ctx -> validateSchemaRepeatable((SchemaCreateContext) ctx)),

                // 2、方案下不能有重复元素
                ValidatorHandlerFactory.build(ctx -> validateSchemaElementRepeatable((SchemaCreateContext) ctx)),

                // 3、校验元素与方案是否匹配
                ValidatorHandlerFactory.build(ctx -> validateSchemaElementConsistency((SchemaCreateContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }

    private ValidationError validateSchemaElementConsistency(SchemaCreateContext ctx) {
        SchemaCreateCmd schemaCreateCmd = ctx.getSchemaCreateCmd();
        String schemaCode = schemaCreateCmd.getSchemaCode();
        List<SchemaElementCreateCmd> dimension = schemaCreateCmd.getDimension();
        List<SchemaElementCreateCmd> params = Optional.ofNullable(schemaCreateCmd.getParams()).orElseGet(ArrayList::new);
        List<String> elementCodes = Stream.concat(dimension.stream(), params.stream())
                .filter(e-> ElementSourceTypeEnum.CUSTOM.getType().equals(e.getElementSourceType().byteValue()))
                .map(SchemaElementCreateCmd::getElementCode).collect(Collectors.toList());
        SchemaElementValidator validator = applicationContext.getBean(SchemaElementValidator.class);
        return validator.validateConsistency(schemaCode, elementCodes);
    }

    private ValidationError validateSchemaElementRepeatable(SchemaCreateContext ctx) {
        SchemaValidator validator = applicationContext.getBean(SchemaValidator.class);
        SchemaCreateCmd schemaCreateCmd = ctx.getSchemaCreateCmd();
        List<SchemaElementCreateCmd> dimension = schemaCreateCmd.getDimension();
        List<SchemaElementCreateCmd> params = Optional.ofNullable(schemaCreateCmd.getParams()).orElse(new ArrayList<>());
        List<SchemaElementCreateCmd> allElements = Stream.concat(dimension.stream(), params.stream()).collect(Collectors.toList());
        return validator.validateElementRepeatable(allElements);
    }

    private ValidationError validateSchemaRepeatable(SchemaCreateContext ctx) {
        SchemaValidator validator = applicationContext.getBean(SchemaValidator.class);
        SchemaCreateCmd schemaCreateCmd = ctx.getSchemaCreateCmd();
        return validator.validateSchemaRepeatable(schemaCreateCmd.getSchemaCode());
    }
}
