package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.dewu.scm.lms.api.report.dto.response.ImportExcelResponse;
import com.dw.scp.bizlog.gateway.BizOperationLogGateway;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.constant.ElementTypeEnum;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ElementInstVO;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ParamInstImportExcelVO;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstImportContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 参数导入持久化逻辑
 */
@Component
public class ParamInstImportPersistentHandler implements IBasicHandler<ParamInstImportContext> {

    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    MetaDomainService metaDomainService;

    @Override
    public void doHandle(ParamInstImportContext paramInstImportContext) {
        String schemaCode = paramInstImportContext.getSchemaCode();
        SchemaMetaAggregate schemaMetaAggregate = paramInstImportContext.getSchemaMetaAggregate();
        List<SchemaElementEntity> dimensions = schemaMetaAggregate.getDimensions();
        List<Integer> dimensionIndex = paramInstImportContext.getDimensionIndex();
        List<ParamInstImportExcelVO> paramInstImportExcelVOS = paramInstImportContext.getParamInstImportExcelVOS();
        List<ImportExcelResponse> importExcelResponseList = Optional.ofNullable(paramInstImportContext.getImportExcelResponseList()).orElseGet(ArrayList::new);
        Map<String, String> fieldElementNameMap = schemaMetaAggregate.getFieldElementNameMap();

        paramInstImportExcelVOS.forEach(vo -> {
            BaseImportExcel row = vo.getBaseImportExcel();
            Map<Integer, Object> columnValueMap = row.getColumnValueMap();
            Map<String, String> paramInstMap = extractParamInstMap(paramInstImportContext, columnValueMap, schemaCode);
            Map<String, String> elementCodeInstMap = new HashMap<>();
            List<ElementInstVO> elementInstVOS = dimensionIndex.stream()
                    .filter(index -> Objects.nonNull(columnValueMap.get(index))
                            && StringUtils.isNotBlank(String.valueOf(columnValueMap.get(index))))
                    .map(index -> {
                        SchemaElementEntity schemaElement = dimensions.get(dimensionIndex.indexOf(index));
                        String elementCode = schemaElement.getElementCode();
                        String elementInstDesc = String.valueOf(columnValueMap.get(index)).trim();
                        String elementValue = metaDomainService.getElemValue(schemaCode, elementCode, elementInstDesc, elementCodeInstMap);
                        if (StringUtils.isBlank(elementValue)) {
                            elementValue = elementInstDesc;
                            // metaDomainService.getFirstElemDesc(schemaCode,elementCode,elementInstDesc,paramInstMap);
                        }
                        elementCodeInstMap.put(elementCode,elementValue);
                        return ElementInstVO.builder().elementCode(elementCode).elementValue(elementValue).build();
                    }).collect(Collectors.toList());
            String dimensionKey = elementInstVOS.stream().map(ElementInstVO::getElementValue).collect(Collectors.joining("#"));
            String dimension = elementInstVOS.stream().map(ElementInstVO::getElementCode).collect(Collectors.joining("#"));
            String res = paramInstRepository.saveOrUpdateInstForImport(schemaMetaAggregate, dimension, dimensionKey, paramInstMap, row, fieldElementNameMap);
            ImportExcelResponse importExcelResponse = ParamInstDomainService.buildRowRes(row, res);
            importExcelResponseList.add(importExcelResponse);
        });
        paramInstImportContext.setImportExcelResponseList(importExcelResponseList);
    }


    /**
     * 提取抽象列->参数值的map，用于后续落库
     *
     * @param paramInstImportContext
     * @param columnValueMap
     * @param schemaCode
     * @return
     */
    private Map<String/*adsFieldCode*/, String/*value*/> extractParamInstMap(ParamInstImportContext paramInstImportContext, Map<Integer, Object> columnValueMap, String schemaCode) {
        Map<String, SchemaElementEntity> allElementMap = paramInstImportContext.getAllElementMap();
        Map<String, String> paramInstMap = new HashMap<>();
        Map<String, String> elementCodeInstMap = new HashMap<>();
        for (Map.Entry<Integer, Object> entry : columnValueMap.entrySet()) {
            Integer index = entry.getKey();
            String desc = String.valueOf(entry.getValue()).trim();
            String elementCode = paramInstImportContext.getElementCodeByIndex(index);
            String value = metaDomainService.getElemValue(schemaCode, elementCode, desc, elementCodeInstMap);
            if (StringUtils.isBlank(value)) {
                value = desc;
            }
            SchemaElementEntity schemaElement = allElementMap.get(elementCode);
            if (Objects.equals(schemaElement.getFrontComponent(), "rmbDigit") && StringUtils.isNotBlank(value)) {
                BigDecimal bigDecimal = new BigDecimal(value);
                BigDecimal multiply = bigDecimal.multiply(new BigDecimal(100));
                value = multiply.toPlainString();
            }
            if (Objects.equals(schemaElement.getIsMulti(), 1) && Objects.equals(ElementTypeEnum.PARAM.getType(), schemaElement.getElementType())) {
                value = JSON.toJSONString(Lists.newArrayList(value));
            }
            String adsField = paramInstImportContext.getAdsFieldByIndex(index);
            paramInstMap.put(adsField, value);
            elementCodeInstMap.put(elementCode,value);
        }
        return paramInstMap;
    }


}
