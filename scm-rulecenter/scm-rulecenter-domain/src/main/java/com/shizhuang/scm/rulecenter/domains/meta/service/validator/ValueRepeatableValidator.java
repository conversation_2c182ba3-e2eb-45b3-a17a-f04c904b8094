package com.shizhuang.scm.rulecenter.domains.meta.service.validator;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.ElementEnumDTO;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class ValueRepeatableValidator {

    public ValidationError validate(List<ElementEnumDTO> list) {
        log.info("ValueRepeatableValidator.validate param list: {} ", JSON.toJSONString(list));
        Set<String> valueSet = new HashSet<>();
        Set<String> descSet = new HashSet<>();
        for (ElementEnumDTO enumDTO : list) {
            String value = enumDTO.getValue();
            String desc = enumDTO.getDesc();
            if (valueSet.contains(value)) {

                return ValidationError.create(String.format(ErrorCode.ELEMENT_VALUE_IS_REPEATABLE.getErrMsg(), value + ":" + desc))
                        .setErrorCode(ErrorCode.ELEMENT_VALUE_IS_REPEATABLE.getCode());
            }
            valueSet.add(value);

            if (descSet.contains(desc)) {
                return ValidationError.create(String.format(ErrorCode.ELEMENT_DESC_IS_REPEATABLE.getErrMsg(), value + ":" + desc))
                        .setErrorCode(ErrorCode.ELEMENT_DESC_IS_REPEATABLE.getCode());
            }
            descSet.add(desc);
        }
        return null;
    }

    public Boolean accept(Byte valueRangeType) {
        return ValueRangeTypeEnum.ENUM.getType().equals(valueRangeType);
    }
}
