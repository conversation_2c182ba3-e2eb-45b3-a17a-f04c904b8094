package com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SchemaElementEntity {

    /**
     * 方案code 非必填，跟着方案一起创建的元素需要带上schemaCode
     */
    String schemaCode;

    /**
     * 元素code
     */
    String elementCode;

    /**
     * 元素名称
     */
    String elementName;

    /**
     * 元素来源类型 1公共元素 2自定义方案元素
     */
    Integer elementSourceType;

    /**
     * 元素取值类型 1枚举，2url，3自定义文本
     */
    Byte valueRangeType;

    /**
     * 是否为搜索条件 0否 1是
     */
    Integer queryCondition;

    /**
     * 是否为必填 0否 1是
     */
    Integer isRequired;

    /**
     * 是否为多选 0否 1是
     */
    Integer isMulti;

    /**
     * 版本
     */
    Integer version;


    /**
     * 是否删除 0 否 1 是
     */
    Byte isDel;

    /**
     * 元素类型 1维度 2参数
     */
    Byte elementType;


    /**
     * 元素描述
     */
    String elementNote;

    /**
     * 方案内部元素顺序
     */
    String priority;

    /**
     * 前端组件
     */
    String frontComponent;

}
