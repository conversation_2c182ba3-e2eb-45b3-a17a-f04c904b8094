package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class AcquireValueRangeByDubboSearch implements AcquireValueRangeStrategy {

    @Resource
    MetaDomainService metaDomainService;

    @Override
    public ElementMetaEntity cleanParam(ElementMetaEntity elementMetaEntity, Map<String, Object> content) {
        ElementMetaEntity incokeElementMetaEntity = new ElementMetaEntity();
        incokeElementMetaEntity.setSchemaCode(elementMetaEntity.getSchemaCode());
        incokeElementMetaEntity.setElementCode(elementMetaEntity.getElementCode());
        incokeElementMetaEntity.setValueRangeType(elementMetaEntity.getValueRangeType());
        incokeElementMetaEntity.setElementInterfaceSet(elementMetaEntity.getElementInterfaceSet());
        incokeElementMetaEntity.setExtParam(content);
        return incokeElementMetaEntity;
    }

    @Override
    public List<ElementEnumVO> extractElementEnums(ElementMetaEntity elementMetaEntity) {
        Object searchType = elementMetaEntity.getExtParam().get("searchType");
        if (Objects.equals(String.valueOf(searchType), DubboSearchTypeEnum.VALUE_TO_DESC.getType())) {
            Object elementValue = elementMetaEntity.getExtParam().get("elementValue");
            return metaDomainService.getElementEnums(elementMetaEntity.getElementInterfaceSet().getValueToDescInterfaceVO(), String.valueOf(elementValue));
        } else if (Objects.equals(String.valueOf(searchType), DubboSearchTypeEnum.DESC_TO_VALUE.getType())) {
            Object elementValue = elementMetaEntity.getExtParam().get("elementDesc");
            return metaDomainService.getElementEnums(elementMetaEntity.getElementInterfaceSet().getDescToValueInterfaceVO(), String.valueOf(elementValue));
        }
        return new ArrayList<>();
    }
}
