package com.shizhuang.scm.rulecenter.domains.anticorrosion.user;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class HttpHeaderContext implements Serializable {
    public static String IOS = "iPhone";
    public static String Android = "Android";
    private String userId;
    private String userName;
    private String platform;
    private String deviceId;
    private String userAgent;
    private String appVersion;
    private String appBuild;
    private String acceptLanguage;
}
