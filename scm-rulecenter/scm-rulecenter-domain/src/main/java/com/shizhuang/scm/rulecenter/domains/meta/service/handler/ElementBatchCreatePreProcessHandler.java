package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.ElementBatchCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementBatchCreateContext;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ElementBatchCreatePreProcessHandler implements IBasicHandler<ElementBatchCreateContext>{

    @Override
    public void doHandle(ElementBatchCreateContext elementBatchCreateContext) {
        ElementBatchCreateCmd elementBatchCreateCmd = elementBatchCreateContext.getElementBatchCreateCmd();
        String schemaCode = elementBatchCreateCmd.getSchemaCode();
        List<ElementCreateCmd> createCmdList = elementBatchCreateCmd.getCreateCmdList();
        createCmdList.forEach(cmd-> cmd.setSchemaCode(schemaCode));
    }


}
