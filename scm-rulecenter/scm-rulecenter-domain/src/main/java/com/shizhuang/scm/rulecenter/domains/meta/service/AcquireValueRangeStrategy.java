package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;

import java.util.List;
import java.util.Map;

public interface AcquireValueRangeStrategy {

    ElementMetaEntity cleanParam(ElementMetaEntity elementMetaEntity, Map<String,Object> content);

    List<ElementEnumVO> extractElementEnums(ElementMetaEntity elementMetaEntity);

}
