package com.shizhuang.scm.rulecenter.domains.inst.repository.impl;

import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.poizon.fusion.utils.DateUtils;
import com.shizhuang.scm.rulecenter.api.constant.ParamInstOptLogEnum;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.query.RedisKeyHelper;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstAggEntity;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.AggInstMapVO;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ElementInstVO;
import com.shizhuang.scm.rulecenter.domains.inst.assembler.ParamInstAssembler;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstEditPersistentHandler;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.CompareIssueVO;
import com.shizhuang.scm.rulecenter.infrastructure.cache.RedisCacheService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.transaction.TransactionUtil;
import com.shizhuang.scm.rulecenter.infrastructure.common.utils.LetterUtil;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceOptLogDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ParamInstanceMapper;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ParamInstanceOptLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.DATABASE_ERROR;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.REFLECT_ERROR;

@Slf4j
@Component
public class ParamInstRepositoryImpl implements ParamInstRepository {

    @Resource
    ParamInstanceMapper paramInstanceMapper;

    @Resource
    ParamInstanceOptLogMapper paramInstanceOptLogMapper;

    @Resource
    TransactionUtil transactionUtil;

    @Resource
    RedisCacheService redisCacheService;


    @Resource
    ParamInstAssembler paramInstAssembler;

    @Resource
    ParamInstDomainService paramInstDomainService;

    @Resource
    private ThreadPoolTaskExecutor asyncServiceExecutor;

    @Override
    public int save(ParamInstEntity paramInstanceEntity, Map<String, String> elementFieldMap) {
        ParamInstanceDOWithBLOBs paramInstanceDO = initParamInst(paramInstanceEntity, elementFieldMap);
        ParamInstanceOptLogDO paramInstanceOptLogDO = buildSaveParamInstLog(paramInstanceEntity.getSchemaCode()
                , paramInstanceEntity.getDimension(), paramInstanceEntity.getDimensionKey(), paramInstanceEntity.getOptUser());
        try {
            transactionUtil.transaction(() -> {
                paramInstanceMapper.insertSelective(paramInstanceDO);
                paramInstanceOptLogMapper.insertSelective(paramInstanceOptLogDO);
            });
        } catch (Exception e) {
            log.error("ParamInstRepository.save database error paramInstanceDO:{} paramInstanceOptLogDO:{} "
                    , JSON.toJSONString(paramInstanceDO), JSON.toJSONString(paramInstanceOptLogDO), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }
        return 1;
    }

    public ParamInstanceDOWithBLOBs initParamInst(ParamInstEntity paramInstanceEntity, Map<String, String> elementFieldMap) {
        // 这里必须走initEmptyData 因为如果text字段为空，adb数据同步会出问题
        ParamInstanceDOWithBLOBs paramInstanceDO = initEmptyData();
        paramInstanceDO.setSchemaCode(paramInstanceEntity.getSchemaCode());
        paramInstanceDO.setDimension(paramInstanceEntity.getDimension());
        paramInstanceDO.setDimensionKey(paramInstanceEntity.getDimensionKey());
        paramInstanceDO.setEnabled(paramInstanceEntity.getEnabled() == null ? CommonConstant.ONE : paramInstanceEntity.getEnabled());
        paramInstanceDO.setIsDel(CommonConstant.ZERO.byteValue());
        paramInstanceDO.setVersion(CommonConstant.ONE);
        paramInstanceDO.setAggId(paramInstanceEntity.getAggId());
        paramInstanceDO.setCreator(paramInstanceEntity.getCreator());
        paramInstanceDO.setCtime(paramInstanceEntity.getCtime());
        paramInstanceDO.setModifier(paramInstanceEntity.getModifier());
        paramInstanceDO.setMtime(paramInstanceEntity.getMtime());
        List<ElementInstVO> dimensions = paramInstanceEntity.getDimensions();
        List<ElementInstVO> params = paramInstanceEntity.getParams();
        setValue(elementFieldMap, paramInstanceDO, dimensions);
        setValue(elementFieldMap, paramInstanceDO, params);

        return paramInstanceDO;
    }

    private void setValue(Map<String, String> elementFieldMap, ParamInstanceDOWithBLOBs paramInstanceDO, List<ElementInstVO> params) {
        for (ElementInstVO vo : params) {
            ParamInstEditPersistentHandler.setFieldValue(paramInstanceDO, elementFieldMap, vo.getElementCode(), vo.getElementValue());
        }
    }

    private ParamInstanceDOWithBLOBs initEmptyData() {
        ParamInstanceDOWithBLOBs paramInstanceDO = new ParamInstanceDOWithBLOBs();
        paramInstanceDO.setDimension1("");
        paramInstanceDO.setDimension2("");
        paramInstanceDO.setDimension3("");
        paramInstanceDO.setDimension4("");
        paramInstanceDO.setDimension5("");
        paramInstanceDO.setDimension6("");
        paramInstanceDO.setDimension7("");
        paramInstanceDO.setDimension8("");
        paramInstanceDO.setDimension9("");
        paramInstanceDO.setDimension10("");
        paramInstanceDO.setParam1("");
        paramInstanceDO.setParam2("");
        paramInstanceDO.setParam3("");
        paramInstanceDO.setParam4("");
        paramInstanceDO.setParam5("");
        paramInstanceDO.setParam6("");
        paramInstanceDO.setParam7("");
        paramInstanceDO.setParam8("");
        paramInstanceDO.setParam9("");
        paramInstanceDO.setParam10("");
        paramInstanceDO.setParam11("");
        paramInstanceDO.setParam12("");
        paramInstanceDO.setParam13("");
        paramInstanceDO.setParam14("");
        paramInstanceDO.setParam15("");
        paramInstanceDO.setParam16("");
        paramInstanceDO.setParam17("");
        paramInstanceDO.setParam18("");
        paramInstanceDO.setParam19("");
        paramInstanceDO.setParam20("");
        return paramInstanceDO;
    }


    private static ParamInstanceOptLogDO buildSaveParamInstLog(String schemaCode, String dimension, String dimensionKey, String optUser) {
        ParamInstanceOptLogDO paramInstanceOptLogDO = new ParamInstanceOptLogDO();
        paramInstanceOptLogDO.setSchemaCode(schemaCode);
        paramInstanceOptLogDO.setDimension(dimension);
        paramInstanceOptLogDO.setDimensionKey(dimensionKey);
        paramInstanceOptLogDO.setOptUser(optUser);
        paramInstanceOptLogDO.setOptContent(ParamInstOptLogEnum.CREATE.getDesc());
        paramInstanceOptLogDO.setIsDel(CommonConstant.IS_NOT_DELETE);
        return paramInstanceOptLogDO;
    }


    public String saveOrUpdateInstForImport(SchemaMetaAggregate schemaMetaAggregate, String dimension, String dimensionKey, Map<String, String> adsFieldValueMap, BaseImportExcel row, Map<String, String> fieldElementNameMap) {
        String schemaCode = schemaMetaAggregate.getSchemaEntity().getSchemaCode();
        ParamInstanceDOWithBLOBs newParamInstDO = initParamInst(schemaCode, dimension, dimensionKey, adsFieldValueMap);
        ParamInstanceDOWithBLOBs oldParamInstDO = getByDim(schemaCode, dimension, dimensionKey);
        if (Objects.nonNull(oldParamInstDO)) {
            if (CollectionUtils.isEmpty(schemaMetaAggregate.getParams())) { //如果同样的维度，并且没有参数的情况下，导入数据报数据重复导入（因为两条记录一样）
                return "数据重复导入";
            }
            newParamInstDO.setAggId(oldParamInstDO.getAggId());
            newParamInstDO.setModifier(row.getOperateUserName());
            newParamInstDO.setModifyTime(new Date());
            newParamInstDO.setMtime(new Date());
            ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
            paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andDimensionEqualTo(dimension).andDimensionKeyEqualTo(dimensionKey);
            try {
                transactionUtil.transaction(() -> {
                    paramInstanceMapper.updateByExampleSelective(newParamInstDO, paramInstanceDOExample);
                });

            } catch (Exception e) {
                log.error("ParamInstRepository.saveOrUpdateInstByMap save database error newParamInstDO:{} ",
                        JSON.toJSONString(newParamInstDO), e);
                return DATABASE_ERROR.getErrMsg();
            }
            ParamInstAggEntity oldAggEntity = paramInstAssembler.getParamAggInstEntityByDO(schemaMetaAggregate, Lists.newArrayList(oldParamInstDO));
            ParamInstAggEntity newAggEntity = paramInstAssembler.getParamAggInstEntityByDO(schemaMetaAggregate, Lists.newArrayList(newParamInstDO));
            ParamInstEditContext paramInstEditContext = ParamInstEditContext.builder().schemaMetaAggregate(schemaMetaAggregate)
                    .newAggEntity(newAggEntity)
                    .oldAggEntity(oldAggEntity)
                    .aggId(oldParamInstDO.getAggId())
                    .operatorName(row.getOperateUserName())
                    .operatorId(String.valueOf(row.getOperateUserId())).build();
            paramInstDomainService.recordEditInstBizLog(paramInstEditContext);
        } else {
            // 导入场景下需要每一行建立一个新的aggId
            String aggId = schemaCode + "_" + System.currentTimeMillis() / 1000 + UUID.randomUUID();
            newParamInstDO.setAggId(aggId);
            newParamInstDO.setCreator(row.getOperateUserName());
            newParamInstDO.setCtime(new Date());
            newParamInstDO.setModifier(row.getOperateUserName());
            newParamInstDO.setMtime(new Date());
            try {
                transactionUtil.transaction(() -> {
                    paramInstanceMapper.insertSelective(newParamInstDO);
                });
            } catch (Exception e) {
                log.error("ParamInstRepository.saveOrUpdateInstByMap update database error newParamInstDO:{} "
                        , JSON.toJSONString(newParamInstDO), e);
                return DATABASE_ERROR.getErrMsg();
            }
            ParamInstCreateContext paramInstCreateContext = ParamInstCreateContext.builder().aggId(aggId).build();
            paramInstCreateContext.setOperatorId(String.valueOf(row.getOperateUserId()));
            paramInstCreateContext.setOperatorName(row.getOperateUserName());
            paramInstDomainService.recordCreateInstBizLog(paramInstCreateContext);
        }
        return StringUtils.EMPTY;
    }

    private static ParamInstanceDOWithBLOBs initParamInst(String schemaCode, String dimension, String dimensionKey, Map<String, String> adsFieldValueMap) {
        ParamInstanceDOWithBLOBs newParamInstDO = new ParamInstanceDOWithBLOBs();
        newParamInstDO.setSchemaCode(schemaCode);
        newParamInstDO.setDimension(dimension);
        newParamInstDO.setDimensionKey(dimensionKey);
        newParamInstDO.setVersion(CommonConstant.ONE);
        newParamInstDO.setEnabled(CommonConstant.ONE);
        for (Map.Entry<String, String> entry : adsFieldValueMap.entrySet()) {
            String adsFieldCode = entry.getKey();
            adsFieldCode = adsFieldCode.replace("_", "");
            String value = entry.getValue();
            try {
                Method method = ParamInstanceDOWithBLOBs.class.getMethod("set" + LetterUtil.getMethodName(adsFieldCode), String.class);
                method.invoke(newParamInstDO, value);
            } catch (Exception ex) {
                log.error("ParamInstRepositoryImpl.compareParamInst reflect error schemaCode:{} dimension:{} dimensionKey:{} adsFieldValueMap:{} "
                        , schemaCode, dimension, dimensionKey, JSON.toJSONString(adsFieldValueMap), ex);
                throw new BizRuntimeException(REFLECT_ERROR.getCode(), REFLECT_ERROR.getErrMsg());
            }
        }
        return newParamInstDO;
    }

    @Override
    public ParamInstanceDOWithBLOBs getByDim(String schemaCode, String dimension, String dimensionKey) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andDimensionEqualTo(dimension)
                .andDimensionKeyEqualTo(dimensionKey);
        List<ParamInstanceDOWithBLOBs> paramInstanceDOS = paramInstanceMapper.selectByExampleWithBLOBs(paramInstanceDOExample);
        if (CollectionUtils.isEmpty(paramInstanceDOS)) {
            return null;
        }
        return paramInstanceDOS.get(0);
    }

    @Override
    public ParamInstanceDOWithBLOBs getEnabledByDim(String schemaCode, String dimension, String dimensionKey) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andDimensionEqualTo(dimension)
                .andDimensionKeyEqualTo(dimensionKey)
                .andIsDelEqualTo(CommonConstant.IS_NOT_DELETE)
                .andEnabledEqualTo(CommonConstant.ONE);
        List<ParamInstanceDOWithBLOBs> paramInstanceDOS = paramInstanceMapper.selectByExampleWithBLOBs(paramInstanceDOExample);
        if (CollectionUtils.isEmpty(paramInstanceDOS)) {
            return null;
        }
        return paramInstanceDOS.get(0);
    }


    @Override
    public List<ParamInstanceDOWithBLOBs> batchGetEnabledByDim(String schemaCode, List<String> dimensions, List<String> dimensionKeys) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andDimensionIn(dimensions)
                .andDimensionKeyIn(dimensionKeys)
                .andIsDelEqualTo(CommonConstant.IS_NOT_DELETE)
                .andEnabledEqualTo(CommonConstant.ONE);
        List<ParamInstanceDOWithBLOBs> paramInstanceDOS = paramInstanceMapper.selectByExampleWithBLOBs(paramInstanceDOExample);
        if (CollectionUtils.isEmpty(paramInstanceDOS)) {
            return null;
        }
        return paramInstanceDOS;
    }

    @Override
    public List<ParamInstanceDOWithBLOBs> getByAggId(String schemaCode, String aggId) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andAggIdEqualTo(aggId);
        List<ParamInstanceDOWithBLOBs> paramInstanceDOS = paramInstanceMapper.selectByExampleWithBLOBs(paramInstanceDOExample);
        if (CollectionUtils.isEmpty(paramInstanceDOS)) {
            return null;
        }
        return paramInstanceDOS;
    }

    @Resource
    ParamInstPageQueryStrategy paramInstPageQueryByMysql;

    @Resource
    ParamInstPageQueryStrategy paramInstPageQueryByAdb;


    @Override
    public PageInfo<InstDimPageResponse> queryParamInstPage(InstPageQuery instPageQuery) {
        log.info("ParamInstRepositoryImpl.queryParamInstPage instPageQuery:{}", JSON.toJSONString(instPageQuery));
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        List<ElementInstDTO> bizQueryParams = null;
        if (CollectionUtils.isNotEmpty(queryParams)) {
            queryParams = queryParams.stream().filter(q -> q.getElementValue() != null).collect(Collectors.toList());
            bizQueryParams = queryParams.stream().filter(q -> !"enabled".equals(q.getElementCode())).collect(Collectors.toList());
        }
        PageInfo<InstDimPageResponse> tempParamInstanceDOPageInfo;
        if (CollectionUtils.isEmpty(bizQueryParams)) {
            tempParamInstanceDOPageInfo = paramInstPageQueryByMysql.queryPage(instPageQuery);
        } else {
            queryParams = queryParams.stream().filter(p -> StringUtils.isNotBlank(p.getElementValue()) && !Objects.equals(p.getElementValue(), "[]")).collect(Collectors.toList());
            instPageQuery.setQueryParams(queryParams);
            // 先不用adb 超时可能比较麻烦
            tempParamInstanceDOPageInfo = paramInstPageQueryByAdb.queryPage(instPageQuery);
        }
        return sortOutResult(tempParamInstanceDOPageInfo);
    }

    /**
     * 将含有列表的结果打平
     *
     * @param tempParamInstanceDOPageInfo
     * @return
     */
    private PageInfo<InstDimPageResponse> sortOutResult(PageInfo<InstDimPageResponse> tempParamInstanceDOPageInfo) {
        List<InstDimPageResponse> paramInstanceDOList = tempParamInstanceDOPageInfo.getList();
        int pageNum = tempParamInstanceDOPageInfo.getPageNum();
        int pageSize = tempParamInstanceDOPageInfo.getPageSize();
        long total = tempParamInstanceDOPageInfo.getTotal();
        PageInfo<InstDimPageResponse> instDimResponsePageInfo = PageInfo.of(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(paramInstanceDOList)) {
            LinkedHashMap<String, List<InstDimPageResponse>> instDimResMap = new LinkedHashMap<>();
            paramInstanceDOList.forEach(p -> {
                if (instDimResMap.containsKey(p.getAggId())) {
                    instDimResMap.get(p.getAggId()).add(p);
                } else {
                    instDimResMap.put(p.getAggId(), Lists.newArrayList(p));
                }
            });
            List<AggInstMapVO> aggInstMapVOS = instDimResMap.entrySet().stream().map(m -> {
                AggInstMapVO aggInstMapVO = new AggInstMapVO();
                aggInstMapVO.setAggId(m.getKey());
                aggInstMapVO.setInstDimPageResponses(m.getValue());
                return aggInstMapVO;
            }).collect(Collectors.toList());

            InstDimPageResponse[] instDimPageResponses;
            if (CollectionUtils.isNotEmpty(aggInstMapVOS)) {
                instDimPageResponses = new InstDimPageResponse[aggInstMapVOS.size()];
                CountDownLatch countDownLatch = new CountDownLatch(aggInstMapVOS.size());
                for (int i = 0; i < aggInstMapVOS.size(); i++) {
                    int finalI = i;
                    asyncServiceExecutor.execute(() -> {
                        InstDimPageResponse instDimPageResponse = getInstDimPageResponse(aggInstMapVOS.get(finalI).getInstDimPageResponses());
                        instDimPageResponses[finalI] = instDimPageResponse;
                        countDownLatch.countDown();
                    });
                }
                try {
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            } else {
                instDimPageResponses = null;
            }
            instDimResponsePageInfo = PageInfo.of(instDimPageResponses == null ? new ArrayList<>() : Lists.newArrayList(instDimPageResponses));
        }
        instDimResponsePageInfo.setPageNum(pageNum);
        instDimResponsePageInfo.setPageSize(pageSize);
        instDimResponsePageInfo.setTotal(total);
        return instDimResponsePageInfo;
    }

    private static InstDimPageResponse getInstDimPageResponse(List<InstDimPageResponse> instDimResponsesAggs) {
        Map<String, Set<Object>> elementValueSetMap = new HashMap<>();
        Map<String, Set<Object>> elementDescSetMap = new HashMap<>();
        for (InstDimPageResponse r : instDimResponsesAggs) {
            Map<String, Object> elementValueMap1 = r.getElementValueMap();
            if(r.getSchemaCode().equals("low-price-simplify-s")){
                elementValueMap1.put("category",Lists.newArrayList(Lists.newArrayList(29,30,32),Lists.newArrayList(29,30,33)));
            }
            elementValueMap1.forEach((key, value) -> {
                Set<Object> orDefault = elementValueSetMap.getOrDefault(key, new HashSet<>());
                orDefault.add(value);
                elementValueSetMap.put(key, orDefault);
            });
            Map<String, Object> elementDescMap1 = r.getElementDescMap();
            elementDescMap1.forEach((key, value) -> {
                Set<Object> orDefault = elementDescSetMap.getOrDefault(key, new HashSet<>());
                orDefault.add(value);
                elementDescSetMap.put(key, orDefault);
            });
        }
        Map<String, Object> elementValueMap = new HashMap<>();
        Map<String, Object> elementDescMap = new HashMap<>();
        elementValueSetMap.forEach((key, value) -> {
            if (value.size() == 1) {
                elementValueMap.put(key, value.iterator().next());
            } else {
                elementValueMap.put(key, value);
            }
        });
        elementDescSetMap.forEach((key, value) -> {
            if (value.size() == 1) {
                elementDescMap.put(key, value.iterator().next());
            } else {
                elementDescMap.put(key, value);
            }
        });
        InstDimPageResponse instDimPageResponse = instDimResponsesAggs.get(0);
        instDimPageResponse.setElementValueMap(elementValueMap);
        instDimPageResponse.setElementDescMap(elementDescMap);
        return instDimPageResponse;
    }


    @Override
    public Integer update(ParamInstanceDOWithBLOBs newParamInst, Integer lastVersion, String optUserNameEn, Map<String, String> fieldElementNameMap) {
        String schemaCode = newParamInst.getSchemaCode();
        String dimension = newParamInst.getDimension();
        String dimensionKey = newParamInst.getDimensionKey();
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andDimensionEqualTo(dimension)
                .andDimensionKeyEqualTo(dimensionKey)
                .andVersionEqualTo(lastVersion);
        //先删除redis缓存，再进行数据更新
        invalidRedisCache(schemaCode, paramInstanceDOExample);
        try {
            transactionUtil.transaction(() -> {
                //todo 如果更新结果等于0，则说明更新失败，要抛异常
                int res = paramInstanceMapper.updateByExampleSelective(newParamInst, paramInstanceDOExample);
            });
        } catch (Exception e) {
            log.error("ParamInstRepositoryImpl.update database error newParamInst:{} lastVersion:{} optUserNameEn:{} fieldElementNameMap:{} "
                    , JSON.toJSONString(newParamInst), lastVersion, optUserNameEn, JSON.toJSONString(fieldElementNameMap), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }
        return 1;
    }


    @Override
    public Integer delete(String schemaCode, String aggId) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andAggIdEqualTo(aggId);
        ParamInstanceDOWithBLOBs updateParamInst = new ParamInstanceDOWithBLOBs();
        updateParamInst.setSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode);
        updateParamInst.setIsDel(CommonConstant.ONE.byteValue());

        invalidRedisCache(schemaCode, paramInstanceDOExample);
        return paramInstanceMapper.updateByExampleSelective(updateParamInst, paramInstanceDOExample);
    }

    @Override
    public Integer batchDelete(String schemaCode, List<String> aggIds) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andAggIdIn(aggIds);
        ParamInstanceDOWithBLOBs updateParamInst = new ParamInstanceDOWithBLOBs();
        updateParamInst.setSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode);
        updateParamInst.setIsDel(CommonConstant.ONE.byteValue());

        invalidRedisCache(schemaCode, paramInstanceDOExample);
        return paramInstanceMapper.updateByExampleSelective(updateParamInst, paramInstanceDOExample);
    }

    private void invalidRedisCache(String schemaCode, ParamInstanceDOExample paramInstanceDOExample) {
        List<ParamInstanceDOWithBLOBs> paramInstanceDOWithBLOBs = paramInstanceMapper.selectByExampleWithBLOBs(paramInstanceDOExample);
        if(CollectionUtils.isNotEmpty(paramInstanceDOWithBLOBs)){
            for(ParamInstanceDOWithBLOBs paramInstance:paramInstanceDOWithBLOBs){
                String dimKey = RedisKeyHelper.getRedisDimKey(schemaCode, paramInstance.getDimension(), paramInstance.getDimensionKey());
                redisCacheService.removeCache(dimKey);
            }
        }
    }

    @Override
    public Integer enable(String schemaCode, String aggId, String optUser) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andAggIdEqualTo(aggId);
        ParamInstanceDOWithBLOBs updateParamInst = new ParamInstanceDOWithBLOBs();
        updateParamInst.setEnabled(CommonConstant.ONE);
        updateParamInst.setModifier(optUser);
        updateParamInst.setMtime(new Date());

        invalidRedisCache(schemaCode, paramInstanceDOExample);

        try {
            transactionUtil.transaction(() -> {
                paramInstanceMapper.updateByExampleSelective(updateParamInst, paramInstanceDOExample);
            });
        } catch (Exception e) {
            log.error("ParamInstRepositoryImpl.disable database error updateParamInst:{} "
                    , JSON.toJSONString(updateParamInst), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }
        return 1;
    }

    private ParamInstanceOptLogDO buildParamInstEnableOptLog(String schemaCode, String aggId, String optUser) {
        ParamInstanceOptLogDO paramInstanceOptLogDO = new ParamInstanceOptLogDO();
        paramInstanceOptLogDO.setSchemaCode(schemaCode);
        paramInstanceOptLogDO.setDimensionKey(aggId);
        paramInstanceOptLogDO.setOptUser(optUser);
        paramInstanceOptLogDO.setOptContent(ParamInstOptLogEnum.ENABLED.getDesc());
        paramInstanceOptLogDO.setIsDel(CommonConstant.IS_NOT_DELETE);
        return paramInstanceOptLogDO;
    }

    @Override
    public Integer disable(String schemaCode, String aggId, String optUser) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andAggIdEqualTo(aggId);
        ParamInstanceDOWithBLOBs updateParamInst = new ParamInstanceDOWithBLOBs();
        updateParamInst.setEnabled(CommonConstant.ZERO);
        updateParamInst.setModifier(optUser);
        updateParamInst.setMtime(new Date());

        invalidRedisCache(schemaCode, paramInstanceDOExample);

        try {
            transactionUtil.transaction(() -> {
                paramInstanceMapper.updateByExampleSelective(updateParamInst, paramInstanceDOExample);
            });
        } catch (Exception e) {
            log.error("ParamInstRepositoryImpl.disable database error schemaCode:{} aggId:{} optUser:{} "
                    , schemaCode, aggId, optUser, e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }
        return 1;
    }

    private ParamInstanceOptLogDO buildParamInstDisableOptLog(String schemaCode, String aggId, String optUser) {
        ParamInstanceOptLogDO paramInstanceOptLogDO = new ParamInstanceOptLogDO();
        paramInstanceOptLogDO.setSchemaCode(schemaCode);
        paramInstanceOptLogDO.setDimensionKey(aggId);
        paramInstanceOptLogDO.setOptUser(optUser);
        paramInstanceOptLogDO.setOptContent(ParamInstOptLogEnum.DISABLED.getDesc());
        paramInstanceOptLogDO.setIsDel(CommonConstant.IS_NOT_DELETE);
        return paramInstanceOptLogDO;
    }


    @Override
    public Integer batchEnable(String schemaCode, List<String> aggIds, String optUser) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andAggIdIn(aggIds);
        ParamInstanceDOWithBLOBs updateParamInst = new ParamInstanceDOWithBLOBs();
        updateParamInst.setEnabled(CommonConstant.ONE);
        invalidRedisCache(schemaCode, paramInstanceDOExample);
        try {
            transactionUtil.transaction(() -> {
                paramInstanceMapper.updateByExampleSelective(updateParamInst, paramInstanceDOExample);
            });
        } catch (Exception e) {
            log.error("ParamInstRepositoryImpl.disable database error updateParamInst:{} aggIds:{} "
                    , JSON.toJSONString(updateParamInst), JSON.toJSONString(aggIds), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }
        return 1;
    }

    @Override
    public Integer batchDisable(String schemaCode, List<String> aggIds, String optUser) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andAggIdIn(aggIds);
        ParamInstanceDOWithBLOBs updateParamInst = new ParamInstanceDOWithBLOBs();
        updateParamInst.setEnabled(CommonConstant.ZERO);
        invalidRedisCache(schemaCode, paramInstanceDOExample);
        try {
            transactionUtil.transaction(() -> {
                paramInstanceMapper.updateByExampleSelective(updateParamInst, paramInstanceDOExample);
            });
        } catch (Exception e) {
            log.error("ParamInstRepositoryImpl.disable database error updateParamInst:{} aggIds:{} "
                    , JSON.toJSONString(updateParamInst), JSON.toJSONString(aggIds), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }
        return 1;
    }


    @Override
    public List<String> queryAllDimension(String schemaCode) {
        return paramInstanceMapper.selectDistinctDimension(schemaCode);
    }


    @Override
    public Integer count(String schemaCode) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andIsDelEqualTo(CommonConstant.IS_NOT_DELETE);
        Long count = paramInstanceMapper.countByExample(paramInstanceDOExample);
        return count.intValue();
    }

    @Override
    public Integer count(String schemaCode, String dimension, String dimensionKey) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andDimensionEqualTo(dimension)
                .andDimensionKeyEqualTo(dimensionKey).andIsDelEqualTo(CommonConstant.IS_NOT_DELETE);
        Long count = paramInstanceMapper.countByExample(paramInstanceDOExample);
        return count.intValue();
    }

    @Override
    public void deleteBySchema(String schemaCode) {
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        ParamInstanceDOWithBLOBs updateParamInst = new ParamInstanceDOWithBLOBs();
        updateParamInst.setSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode);
        updateParamInst.setIsDel(CommonConstant.ONE.byteValue());
        paramInstanceMapper.updateByExampleSelective(updateParamInst, paramInstanceDOExample);
    }

}
