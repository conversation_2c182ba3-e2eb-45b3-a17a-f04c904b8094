package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstAggEntity;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.assembler.ParamInstAssembler;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 参数实例编辑预处理handler
 */
@Component
public class ParamInstEditPreProcessHandler implements IBasicHandler<ParamInstEditContext> {

    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    SchemaRepository schemaRepository;

    @Resource
    ParamInstAssembler paramInstAssembler;

    @Override
    public void doHandle(ParamInstEditContext paramInstEditContext) {
        ParamInstEditCmd paramInstEditCmd = paramInstEditContext.getParamInstEditCmd();
        SchemaMetaAggregate schemaMetaAggregate = schemaRepository.getSchemaAggregateByCode(paramInstEditCmd.getSchemaCode(), SchemaAggQueryOption.needLatestVersion());
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        paramInstEditContext.setOperatorName(operationUser.getUserNameUnBlank());
        paramInstEditContext.setOperatorId(String.valueOf(operationUser.getUserId()));
        paramInstEditContext.setSchemaMetaAggregate(schemaMetaAggregate);
        String schemaCode = paramInstEditCmd.getSchemaCode();
        String aggId = paramInstEditCmd.getAggId();
        List<ParamInstanceDOWithBLOBs> paramInstanceDOS = paramInstRepository.getByAggId(schemaCode, aggId);
        paramInstEditContext.setOldParamInstanceDOs(paramInstanceDOS);
        List<String> elementCodes = schemaMetaAggregate.getAllElements().stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        List<ElementInstAddCmd> paramMap = paramInstEditCmd.getParamMap();
        paramMap = paramMap.stream()
                .filter(cmd -> elementCodes.contains(cmd.getElementCode()))
                .filter(cmd -> cmd.getElementValue() != null)
                .filter(cmd -> StringUtils.isNotBlank(String.valueOf(cmd.getElementValue())))
                .filter(cmd -> {
                    if (cmd.getElementValue() instanceof List) {
                        return CollectionUtils.isNotEmpty((List) cmd.getElementValue());
                    }
                    return true;
                })
                .collect(Collectors.toList());
        paramInstEditCmd.setParamMap(paramMap);

        List<ParamInstEntity> paramInstEntities = ParamInstAssembler.getParamInstEntities(paramMap, schemaMetaAggregate, aggId);
        paramInstEntities.forEach(p -> {
                    p.setCreator(paramInstanceDOS.get(0).getCreator());
                    p.setCtime(paramInstanceDOS.get(0).getCtime());
                    p.setModifier(operationUser.getUserNameUnBlank());
                    p.setMtime(new Date());
                    p.setAggId(aggId);
                    p.setEnabled(paramInstanceDOS.get(0).getEnabled());
                }
        );
        paramInstEditContext.setParamInstEntities(paramInstEntities);
        paramInstEditContext.setAggId(paramInstEditCmd.getAggId());
        //初始化旧参数与新参数的ori entity，这两个变量用于在 业务操作日志平台（申辉）
        ParamInstAggEntity oldAggEntity = paramInstAssembler.getParamAggInstEntityByDO(schemaMetaAggregate, paramInstanceDOS);
        ParamInstAggEntity newAggEntity = paramInstAssembler.getParamAggInstEntity(schemaMetaAggregate, paramMap);
        oldAggEntity.setAggId(aggId);
        newAggEntity.setAggId(aggId);
        paramInstEditContext.setOldAggEntity(oldAggEntity);
        paramInstEditContext.setNewAggEntity(newAggEntity);
    }
}
