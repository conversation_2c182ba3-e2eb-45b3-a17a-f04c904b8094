package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstDeleteContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext.*;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ParamInstDeleteValidateChain implements IBasicHandler<ParamInstDeleteContext>, ApplicationContextAware {
    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(ParamInstDeleteContext paramInstDeleteContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(paramInstDeleteContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、添加方案相关下的自定义校验逻辑
                ValidatorHandlerFactory.build(ctx -> validateCustom((ParamInstDeleteContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);

        BrandSendQualitySolutionValidator brandSendQualitySolutionValidator = applicationContext.getBean(BrandSendQualitySolutionValidator.class);
        extMap.put("brand-send-quality-solution", Lists.newArrayList(brandSendQualitySolutionValidator));

    }

    Map<String, List<ParamInstDeleteCustomValidatorExt>> extMap = new HashMap<>();


    private ValidationError validateCustom(ParamInstDeleteContext ctx) {
        List<ParamInstDeleteCustomValidatorExt> paramInstEditCustomValidatorExts = extMap.get(ctx.getParamInstDeleteCmd().getSchemaCode());
        if (CollectionUtils.isEmpty(paramInstEditCustomValidatorExts)) {
            return null;
        }
        for (ParamInstDeleteCustomValidatorExt ext : paramInstEditCustomValidatorExts) {
            ValidationError validate = ext.validate(ctx);
            if (validate != null) {
                return validate;
            }
        }
        return null;
    }
}
