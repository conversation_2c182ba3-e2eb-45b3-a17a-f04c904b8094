package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.MapUtils;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceSet;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class AcquireValueRangeByDubbo implements AcquireValueRangeStrategy {

    @Resource
    MetaDomainService metaDomainService;

    @Override
    public ElementMetaEntity cleanParam(ElementMetaEntity elementMetaEntity, Map<String, Object> content) {
        ElementMetaEntity incokeElementMetaEntity = new ElementMetaEntity();
        incokeElementMetaEntity.setSchemaCode(elementMetaEntity.getSchemaCode());
        incokeElementMetaEntity.setElementCode(elementMetaEntity.getElementCode());
        incokeElementMetaEntity.setValueRangeType(elementMetaEntity.getValueRangeType());
        ElementInterfaceSet elementInterfaceSet = elementMetaEntity.getElementInterfaceSet();
        ElementInterfaceVO fullInterfaceVO = elementInterfaceSet.getFullInterfaceVO();
        ElementInterfaceVO invokeFullInterfaceVO = JSON.parseObject(JSON.toJSONString(fullInterfaceVO), ElementInterfaceVO.class);
        List<String> dubboParamValue = invokeFullInterfaceVO.getDubboParamValue();
        // 有参数的替换参数，没有参数的统一替换成null
        if (CollectionUtils.isNotEmpty(dubboParamValue)) {
            Map<String, Object> extParam = Optional.ofNullable(elementMetaEntity.getExtParam()).orElseGet(HashMap::new);
            extParam.forEach((varName, varValue) -> {
                if ((varValue instanceof String && StringUtils.isNotBlank(String.valueOf(varValue)))
                        || (varValue instanceof List && CollectionUtils.isNotEmpty((List) varValue))
                        || (varValue instanceof Set && CollectionUtils.isNotEmpty((Set) varValue))) {
                    dubboParamValue.replaceAll(s -> s.replace("#{" + varName + "}", String.valueOf(varValue)));
                } else {
                    dubboParamValue.replaceAll(s -> s.replace("#{" + varName + "}", "null"));
                }
            });
            if (MapUtils.isNotEmpty(extParam) && CollectionUtils.isNotEmpty(dubboParamValue)) {
            }
            dubboParamValue.replaceAll(s -> s.replaceAll("#\\{[a-zA-Z0-9_]+\\}", "null"));
        }
        ElementInterfaceSet invokeElementInterfaceSet = new ElementInterfaceSet();
        invokeElementInterfaceSet.setFullInterfaceVO(invokeFullInterfaceVO);
        incokeElementMetaEntity.setElementInterfaceSet(invokeElementInterfaceSet);
        return incokeElementMetaEntity;
    }

    @Override
    public List<ElementEnumVO> extractElementEnums(ElementMetaEntity elementMetaEntity) {
        ElementInterfaceVO fullInterfaceVO = elementMetaEntity.getElementInterfaceSet().getFullInterfaceVO();
        //深拷贝 避免影响原有参数
        return metaDomainService.getElementEnums(fullInterfaceVO, null);
    }

}
