package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.ElementEditCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.ElementMetaAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementEditContext;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

@Component
public class ElementEditHandler implements IBasicHandler<ElementEditContext> {
    @Override
    public void doHandle(ElementEditContext elementEditContext) {
        ElementEditCmd elementEditCmd = elementEditContext.getElementEditCmd();
        ElementMetaEntity editElementMetaEntity = ElementMetaAssembler.transEntity(elementEditCmd);
        elementEditContext.setEditElementMetaEntity(editElementMetaEntity);
    }
}
