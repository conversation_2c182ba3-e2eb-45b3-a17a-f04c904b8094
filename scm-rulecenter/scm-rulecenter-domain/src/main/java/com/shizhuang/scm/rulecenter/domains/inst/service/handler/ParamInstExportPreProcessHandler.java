package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.request.ReportPageRequest;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.query.ParamInstExportRequest;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstExportContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ParamInstExportPreProcessHandler implements IBasicHandler<ParamInstExportContext> {

    @Resource
    SchemaRepository schemaRepository;
    @Resource
    ParamInstDomainService paramInstDomainService;


    @Override
    public void doHandle(ParamInstExportContext paramInstExportContext) {
        ReportPageRequest<ParamInstExportRequest> pageRequest = paramInstExportContext.getPageRequest();
        int pageNum = pageRequest.getPageNum();
        int pageSize = pageRequest.getPageSize();
        ParamInstExportRequest queryParam = JSON.parseObject(JSON.toJSONString(pageRequest.getQueryParam()), ParamInstExportRequest.class);
        String schemaCode = queryParam.getSchemaCode();
        if (StringUtils.isBlank(schemaCode)) {
            throw new BizRuntimeException(ErrorCode.DATABASE_ERROR.getCode(), ErrorCode.DATABASE_ERROR.getErrMsg());
        }
        InstPageQuery instPageQuery = new InstPageQuery();
        instPageQuery.setPageNum(pageNum);
        instPageQuery.setPageSize(pageSize);
        instPageQuery.setSchemaCode(schemaCode);
        instPageQuery.setCheckAuthUserWarehouse(queryParam.getCheckAuthUserWarehouse());
        if(Objects.nonNull(queryParam)&& CollectionUtils.isNotEmpty(queryParam.getQueryParams())){
            List<ElementInstDTO> filterParams = queryParam.getQueryParams().stream().filter(e -> Objects.nonNull(e.getElementCode()) && Objects.nonNull(e.getElementValue())).collect(Collectors.toList());
            instPageQuery.setQueryParams(filterParams);
        }else{
            instPageQuery.setQueryParams(new ArrayList<>());
        }
        paramInstDomainService.checkAuthUserWarehouser(instPageQuery,pageRequest.getOperateUserId());
        paramInstExportContext.setInstPageQuery(instPageQuery);

        SchemaMetaAggregate schemaMetaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLargestVersion());
        paramInstExportContext.setSchemaMetaAggregate(schemaMetaAggregate);
    }
}
