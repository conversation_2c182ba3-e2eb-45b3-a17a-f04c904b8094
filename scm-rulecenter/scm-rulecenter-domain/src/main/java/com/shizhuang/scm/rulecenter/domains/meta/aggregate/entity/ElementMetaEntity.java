package com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity;

import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceSet;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 元素实体类
 */
@Getter
@Setter
public class ElementMetaEntity {

    /**
     * 方案code 非必填，跟着方案一起创建的元素需要带上schemaCode
     */
    String schemaCode;

    /**
     * 元素code
     */
    String elementCode;

    /**
     * 元素名称
     */
    String elementName;

    /**
     * 元素来源类型 1公共元素 2自定义方案元素
     */
    Integer elementSourceType;


    /**
     * 取值范围类型 1枚举，2dubbo全量接口，21dubbo单点查询，3自定义文本
     */
    Byte valueRangeType;

    /**
     * 元素取值范围接口合集
     */
    ElementInterfaceSet elementInterfaceSet;

    /**
     * 元素值枚举
     */
    List<ElementEnumVO> enums;


    /**
     * 关联前端组件
     */
    String frontComponent;

    /**
     * 元素描述
     */
    String elementNote;


    /**
     * 创建人
     */
    String creator;

    /**
     * 辅助查询元素枚举值的外部变量
     */
    Map<String, Object> extParam;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ElementMetaEntity)) return false;
        ElementMetaEntity that = (ElementMetaEntity) o;
        return Objects.equals(schemaCode, that.schemaCode) && Objects.equals(elementCode, that.elementCode) && Objects.equals(elementName, that.elementName) && Objects.equals(elementSourceType, that.elementSourceType) && Objects.equals(valueRangeType, that.valueRangeType) && Objects.equals(elementInterfaceSet, that.elementInterfaceSet) && Objects.equals(enums, that.enums) && Objects.equals(frontComponent, that.frontComponent) && Objects.equals(elementNote, that.elementNote) && Objects.equals(creator, that.creator) && Objects.equals(extParam, that.extParam);
    }

    @Override
    public int hashCode() {
        return Objects.hash(schemaCode, elementCode, elementName, elementSourceType, valueRangeType, elementInterfaceSet, enums, frontComponent, elementNote, creator, extParam);
    }

    public boolean noNeedToTransValue(){
        if(ValueRangeTypeEnum.CUSTOM_TEXT.getType().equals(valueRangeType)){
            return true;
        }
        return false;
    }

    public boolean needCacheFullData() {
        ValueRangeTypeEnum valueRangeTypeEnum = ValueRangeTypeEnum.of(valueRangeType);
        if (ValueRangeTypeEnum.ENUM.equals(valueRangeTypeEnum)
                || ValueRangeTypeEnum.DUBBO_FULL.equals(valueRangeTypeEnum)) {
            return true;
        }
        return false;
    }

}
