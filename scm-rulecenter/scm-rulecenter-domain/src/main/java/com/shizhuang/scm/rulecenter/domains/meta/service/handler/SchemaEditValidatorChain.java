package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaEditCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaElementCreateCmd;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.OptUserValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.SchemaValidator;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class SchemaEditValidatorChain implements IBasicHandler<SchemaEditContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Resource
    ParamInstRepository paramInstRepository;

    @Override
    public void doHandle(SchemaEditContext schemaEditContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(schemaEditContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;

        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、方案下不能有重复元素
                ValidatorHandlerFactory.build(ctx -> validateSchemaRepeatable((SchemaEditContext) ctx)),

                // 2、方案编辑人必须是方案创建人或者白名单的人
                ValidatorHandlerFactory.build(ctx -> validateOptUserIllegal((SchemaEditContext) ctx)),

                // 4、已经上线的方案，不允许删除维度
                ValidatorHandlerFactory.build(ctx -> acceptHasDeployedSchema((SchemaEditContext) ctx)
                        , ctx -> validateDimensionMissing((SchemaEditContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }


    private Boolean acceptHasDeployedSchema(SchemaEditContext ctx) {
        SchemaMetaAggregate deployedSchemaMetaAggregate = ctx.getDeployedSchemaMetaAggregate();
        return Objects.nonNull(deployedSchemaMetaAggregate);
    }

    private ValidationError validateDimensionMissing(SchemaEditContext ctx) {
        SchemaValidator validator = applicationContext.getBean(SchemaValidator.class);
        List<SchemaElementCreateCmd> dimension = ctx.getSchemaEditCmd().getDimension();
        List<SchemaElementCreateCmd> params = ctx.getSchemaEditCmd().getParams();
        SchemaMetaAggregate deployedSchemaMetaAggregate = ctx.getDeployedSchemaMetaAggregate();
        return validator.validateElementMissing(dimension, params, deployedSchemaMetaAggregate);
    }

    private ValidationError validateOptUserIllegal(SchemaEditContext ctx) {
        OptUserValidator validator = applicationContext.getBean(OptUserValidator.class);
        SchemaMetaAggregate deployedSchemaMetaAggregate = ctx.getDeployedSchemaMetaAggregate();
        SchemaMetaAggregate largestNoDeploySchemaMetaAggregate = ctx.getLargestNoDeploySchemaMetaAggregate();
        SchemaEntity schemaEntity;
        if (Objects.nonNull(deployedSchemaMetaAggregate)) {
            schemaEntity = deployedSchemaMetaAggregate.getSchemaEntity();
        } else {
            schemaEntity = largestNoDeploySchemaMetaAggregate.getSchemaEntity();
        }
        String creator = schemaEntity.getCreator();
        return validator.validate(creator);
    }

    private ValidationError validateSchemaRepeatable(SchemaEditContext ctx) {
        SchemaValidator validator = applicationContext.getBean(SchemaValidator.class);
        SchemaEditCmd schemaEditCmd = ctx.getSchemaEditCmd();
        List<SchemaElementCreateCmd> dimension = schemaEditCmd.getDimension();
        List<SchemaElementCreateCmd> params = schemaEditCmd.getParams();
        List<SchemaElementCreateCmd> allElements = Stream.concat(dimension.stream(), params.stream()).collect(Collectors.toList());
        return validator.validateElementRepeatable(allElements);
    }


}
