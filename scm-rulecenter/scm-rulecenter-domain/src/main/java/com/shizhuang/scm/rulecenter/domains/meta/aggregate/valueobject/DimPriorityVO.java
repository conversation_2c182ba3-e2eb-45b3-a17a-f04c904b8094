package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DimPriorityVO {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * "维度code集合，#号分隔"
     */
    String dimension;

    /**
     * 优先级
     */
    Integer priority;

    /**
     * 是否删除 0 否 1 是
     */
    Byte isDel;

    /**
     * 版本
     */
    Integer version;

    /**
     * 记录原子维度
     */
    List<String> atomicDimension;

}
