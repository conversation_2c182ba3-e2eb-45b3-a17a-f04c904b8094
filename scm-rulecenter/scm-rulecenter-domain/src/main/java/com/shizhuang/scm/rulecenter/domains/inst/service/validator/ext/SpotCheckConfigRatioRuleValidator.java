package com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Component
public class SpotCheckConfigRatioRuleValidator implements ParamInstCreateCustomValidatorExt,ParamInstEditCustomValidatorExt {
    @Override
    public ValidationError validate(ParamInstCreateContext paramInstCreateContext) {

        ParamInstAddCmd paramInstAddCmd = paramInstCreateContext.getParamInstAddCmd();
        Map<String, Object> paramInstValueMap = paramInstAddCmd.getParamMap().stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        return getValidationError(paramInstValueMap);
    }

    private static ValidationError getValidationError(Map<String, Object> paramInstValueMap) {
        Object ratioRuleItemsObj = paramInstValueMap.get("ratioRuleItems");
        if (Objects.isNull(ratioRuleItemsObj)) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "抽检合格率至少要有一条记录");
        }
        List<RatioRuleItem> ratioRuleItems = JSON.parseArray(JSON.toJSONString(ratioRuleItemsObj), RatioRuleItem.class);
        if (CollectionUtils.isEmpty(ratioRuleItems)) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "抽检合格率至少要有一条记录");
        }
        ratioRuleItems = ratioRuleItems.stream().sorted(Comparator.comparing(RatioRuleItem::getStartPrice)).collect(Collectors.toList());
        RatioRuleItem[] ratioRuleItemsArray = ratioRuleItems.toArray(new RatioRuleItem[ratioRuleItems.size()]);
        for (int i = 0; i < ratioRuleItemsArray.length; i++) {
            if (i + 1 < ratioRuleItemsArray.length && ratioRuleItemsArray[i + 1].getStartPrice() - ratioRuleItemsArray[i].getEndPrice() > 100L) {
                return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "数量间隔过大");
            }

            for (int j = i + 1; j < ratioRuleItemsArray.length; j++) {
                RatioRuleItem current = ratioRuleItemsArray[j];
                RatioRuleItem last = ratioRuleItemsArray[i];
                if (Math.max(last.getStartPrice(), current.getStartPrice()) <= Math.min(last.getEndPrice(), current.getEndPrice())) {
                    return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "抽检合格率 发生价格区间重叠");
                }
            }

            if(Objects.isNull(ratioRuleItemsArray[i].getUnqualifiedRatio())){
                return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "请输入不合格率");
            }

            if(ratioRuleItemsArray[i].getUnqualifiedRatio()<=0||ratioRuleItemsArray[i].getUnqualifiedRatio()>100){
                return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "不合格率范围为1-100之间");
            }
        }
        return null;
    }

    @Override
    public ValidationError validate(ParamInstEditContext paramInstEditContext) {
        ParamInstEditCmd paramInstEditCmd = paramInstEditContext.getParamInstEditCmd();
        Map<String, Object> paramInstValueMap = paramInstEditCmd.getParamMap().stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        return getValidationError(paramInstValueMap);
    }
}
