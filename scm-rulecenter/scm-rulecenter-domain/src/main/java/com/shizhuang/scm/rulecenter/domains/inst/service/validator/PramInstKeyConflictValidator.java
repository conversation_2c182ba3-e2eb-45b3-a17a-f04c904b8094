package com.shizhuang.scm.rulecenter.domains.inst.service.validator;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ElementInstVO;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.PARAM_INST_EXIST;

/**
 * 参数实例冲突校验逻辑
 */
@Component
public class PramInstKeyConflictValidator {
    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    MetaDomainService metaDomainService;

    public ValidationError validate(ParamInstEntity paramInstEntity) {
        String schemaCode = paramInstEntity.getSchemaCode();
        String dimension = paramInstEntity.getDimension();
        String dimensionKey = paramInstEntity.getDimensionKey();
        Integer count = paramInstRepository.count(schemaCode, dimension, dimensionKey);
        if (count >= 1) {
            Map<String, Object> extParam = paramInstEntity.getAllElementInst().stream().collect(Collectors.toMap(ElementInstVO::getElementCode, ElementInstVO::getElementValue, (o, n) -> n));
            String dimensionKeyDesc = paramInstEntity.getDimensions().stream().map(p -> {
                return metaDomainService.getFirstElemDesc(schemaCode, p.getElementCode(), p.getElementValue(),extParam);
            }).collect(Collectors.joining(" "));
            String specificMsg = String.format(PARAM_INST_EXIST.getErrMsg(), dimensionKeyDesc);
            return ValidationErrorFactory.create(PARAM_INST_EXIST, specificMsg);
        }
        return null;
    }
}
