package com.shizhuang.scm.rulecenter.domains.meta.service.validator;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaElementCreateCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.*;

/**
 * 方案信息校验器
 */
@Slf4j
@Component
public class SchemaValidator {

    @Resource
    SchemaRepository schemaRepository;

    public ValidationError validateSchemaRepeatable(String schemaCode){
        SchemaEntity schemaEntityByCode = schemaRepository.getSchemaEntityByCode(schemaCode, SchemaAggQueryOption.needLargestVersion());
        if(Objects.nonNull(schemaEntityByCode)){
            return ValidationErrorFactory.create(SCHEMA_IS_REPEATABLE);
        }
        return null;
    }

    public ValidationError validateElementRepeatable(List<SchemaElementCreateCmd> allElements) {
        log.info("SchemaValidator.validateElementRepeatable param allElements: {}  ", JSON.toJSONString(allElements));
        Set<String> elementCodeSet = new HashSet<>();
        for (SchemaElementCreateCmd schemaElementCreateCmd : allElements) {
            String elementCode = schemaElementCreateCmd.getElementCode();
            if (elementCodeSet.contains(elementCode)) {
                String specificMsg = String.format(DIMENSION_IS_REPEATABLE.getErrMsg(), elementCode);
                return ValidationErrorFactory.create(DIMENSION_IS_REPEATABLE, specificMsg);
            }
            elementCodeSet.add(elementCode);
        }
        return null;
    }

    /**
     * 若方案已经上线，则不允许删除已有的维度以及参数
     *
     * @param editDimension
     * @param deployedSchemaMetaAggregate
     * @return
     */
    public ValidationError validateElementMissing(List<SchemaElementCreateCmd> editDimension, List<SchemaElementCreateCmd> editParams, SchemaMetaAggregate deployedSchemaMetaAggregate) {
        List<SchemaElementEntity> deployedDimensions = deployedSchemaMetaAggregate.getDimensions();
        List<String> deployedDimensionCodes = deployedDimensions.stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        List<String> editDimensionCodes = editDimension.stream().map(SchemaElementCreateCmd::getElementCode).collect(Collectors.toList());
        log.info("SchemaValidator.validateDimensionMissing param deployedDimensionCodes: {}  editDimensionCodes: {} "
                , JSON.toJSONString(deployedDimensionCodes), JSON.toJSONString(editDimensionCodes));
        for (String code : deployedDimensionCodes) {
            if (!editDimensionCodes.contains(code)) {
                String specificMsg = String.format(DIMENSION_CAN_NOT_DELETE.getErrMsg(), code);
                return ValidationErrorFactory.create(DIMENSION_CAN_NOT_DELETE, specificMsg);
            }
        }

        List<SchemaElementEntity> params = deployedSchemaMetaAggregate.getParams();
        List<String> editParamCodes = editParams.stream().map(SchemaElementCreateCmd::getElementCode).collect(Collectors.toList());
        List<String> deployedParamCodes = params.stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        for (String code : deployedParamCodes) {
            if (!editParamCodes.contains(code)) {
                String specificMsg = String.format(PARAM_CAN_NOT_DELETE.getErrMsg(), code);
                return ValidationErrorFactory.create(PARAM_CAN_NOT_DELETE, specificMsg);
            }
        }

        return null;
    }


}
