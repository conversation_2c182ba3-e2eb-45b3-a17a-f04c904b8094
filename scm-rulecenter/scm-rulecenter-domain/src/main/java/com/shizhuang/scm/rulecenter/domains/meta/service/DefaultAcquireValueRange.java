package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class DefaultAcquireValueRange implements AcquireValueRangeStrategy{

    @Override
    public ElementMetaEntity cleanParam(ElementMetaEntity elementMetaEntity, Map<String,Object> content) {
        return elementMetaEntity;
    }

    @Override
    public List<ElementEnumVO> extractElementEnums(ElementMetaEntity elementMetaEntity) {
        return new ArrayList<>();
    }
}
