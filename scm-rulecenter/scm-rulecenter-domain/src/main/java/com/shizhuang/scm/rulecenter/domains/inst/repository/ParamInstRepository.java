package com.shizhuang.scm.rulecenter.domains.inst.repository;

import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.github.pagehelper.PageInfo;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;

import java.util.List;
import java.util.Map;

public interface ParamInstRepository {

    int save(ParamInstEntity paramInstanceEntity, Map<String, String> elementFieldMap);

    ParamInstanceDOWithBLOBs initParamInst(ParamInstEntity paramInstanceEntity, Map<String, String> elementFieldMap);

    String saveOrUpdateInstForImport(SchemaMetaAggregate schemaMetaAggregate, String dimension, String dimensionKey, Map<String, String> adsFieldValueMap, BaseImportExcel row, Map<String, String> fieldElementNameMap);

    ParamInstanceDOWithBLOBs getByDim(String schemaCode, String dimension, String dimensionKey);

    Integer update(ParamInstanceDOWithBLOBs newParamInst, Integer lastVersion, String optUserNameEn, Map<String, String> fieldElementNameMap);

    Integer delete(String schemaCode, String aggId);

    Integer enable(String schemaCode, String aggId, String optUser);

    Integer disable(String schemaCode, String aggId, String optUser);

    Integer batchEnable(String schemaCode, List<String> aggIds, String optUser);

    Integer batchDisable(String schemaCode, List<String> aggIds, String optUser);

    List<String> queryAllDimension(String schemaCode);

    ParamInstanceDOWithBLOBs getEnabledByDim(String schemaCode, String dimension, String dimensionKey);

    List<ParamInstanceDOWithBLOBs> batchGetEnabledByDim(String schemaCode, List<String> dimensions, List<String> dimensionKeys);
    List<ParamInstanceDOWithBLOBs> getByAggId(String schemaCode, String aggId);

    PageInfo<InstDimPageResponse> queryParamInstPage(InstPageQuery instPageQuery);

    Integer count(String schemaCode);

    Integer count(String schemaCode, String dimension, String dimensionKey);

    void deleteBySchema(String schemaCode);

    Integer batchDelete(String schemaCode, List<String> aggIds);
}
