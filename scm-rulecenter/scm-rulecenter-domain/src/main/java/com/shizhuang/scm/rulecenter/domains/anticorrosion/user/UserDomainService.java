package com.shizhuang.scm.rulecenter.domains.anticorrosion.user;


import com.dewu.scm.lms.api.interfaces.dto.UserInfoDto;
import com.dewu.scm.lms.api.interfaces.user.IUserInfoApi;
import com.dewu.scp.user.core.api.user.query.UserCoreQueryApi;
import com.dewu.scp.user.core.api.user.query.request.UserInfoQueryRequest;
import com.dewu.scp.user.core.api.user.query.response.UserInfoResponse;
import com.dewu.scp.user.core.api.user.query.response.WarehouseInfo;
import com.shizhuang.ark.config.client.annotation.ArkJsonValue;
import com.shizhuang.avatar.common.model.Result;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UserDomainService {

    @Autowired
    private IUserInfoApi iUserInfoApi;
    @Autowired
    private UserCoreQueryApi userCoreQueryApi;

    @ArkJsonValue("${warehouse.use.auth:false}")
    private boolean warehouseUseAuthSwitch;

    public boolean getUserAuthSwitch(){
        return warehouseUseAuthSwitch;
    }

    public List<String> getUserAuthWarehouseCodeList() {
        List<String> warehouseCodeList = new ArrayList<>();
        OperationUserContext userContext = BizIdentityContext.getOperationUser();
        if (Objects.nonNull(userContext)) {
            List<OperationUserContext.WarehouseDto> warehouses = userContext.getWarehouses();
            if (CollectionUtils.isNotEmpty(warehouses)) {
                warehouseCodeList = warehouses.stream().map(OperationUserContext.WarehouseDto::getWarehouseCode).filter(Objects::nonNull).collect(Collectors.toList());
            }
        }
        return warehouseCodeList;
    }

    public List<String> getUserAuthWarehouseCodeListByUserId(Long userId) {
        if(Objects.isNull(userId)){
            return getUserAuthWarehouseCodeList();
        }
        List<String> warehouseCodeList = new ArrayList<>();
        UserInfoResponse userInfoResponse = getUserInfoResponse(userId);
        if (Objects.nonNull(userInfoResponse)) {
            List<WarehouseInfo> warehouses = userInfoResponse.getWarehouseCodes();
            if (CollectionUtils.isNotEmpty(warehouses)) {
                warehouseCodeList = warehouses.stream().map(WarehouseInfo::getWarehouseCode).filter(Objects::nonNull).collect(Collectors.toList());
            }
        }
        return warehouseCodeList;
    }

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return UserInfoDto 返回用户信息
     */
    public UserInfoDto getUserInfo(Long userId) {
        if (Objects.isNull(userId)) {
            return null;
        }
        try {
            Result<UserInfoDto> userInfo = iUserInfoApi.userInfo(userId);
            if (Result.isSuccess(userInfo)) {
                return userInfo.getData();
            }
        } catch (Exception e) {
            log.error("Fail LmsUserServiceImpl userInfo userId={}", userId, e);
        }
        return null;
    }

    /**
     * 获取用户信息
     *
     * @param userId               用户ID
     * @param needCheckStationFlag 是否检查工位信息
     * @return UserInfoDto 返回用户信息
     */
    public UserInfoDto getUserInfo(Long userId, boolean needCheckStationFlag) {
        if (Objects.isNull(userId)) {
            return null;
        }
        try {
            Result<UserInfoDto> userInfo = iUserInfoApi.userInfo(userId, needCheckStationFlag);
            if (Result.isSuccess(userInfo)) {
                return userInfo.getData();
            }
        } catch (Exception e) {
            log.error("Fail LmsUserServiceImpl userInfo userId={}", userId, e);
        }
        return null;
    }

    /**
     * 获取用户信息
     *
     * @param userId               用户ID
     * @return UserInfoResponse 返回用户信息
     */
    public UserInfoResponse getUserInfoResponse(Long userId) {
        if (Objects.isNull(userId)) {
            return null;
        }
        try {
            UserInfoQueryRequest userInfoQueryRequest = new UserInfoQueryRequest();
            userInfoQueryRequest.setUserId(userId);
            com.poizon.fusion.common.model.Result<UserInfoResponse> userInfoResponse = userCoreQueryApi.userInfo(userInfoQueryRequest);
            if (com.poizon.fusion.common.model.Result.isSuccess(userInfoResponse)) {
                return userInfoResponse.getData();
            }
        } catch (Exception e) {
            log.error("Fail LmsUserServiceImpl userInfo userId={}", userId, e);
        }
        return null;
    }



    /**
     * 根据用户id查询用户上下文信息(并设置到用户上下文中)
     *
     * @param userId
     * @return
     */
//    public OperationUserContext getOperationUser(Long userId) {
//        OperationUserContext userContext = MesIdentifyContext.getOperationUser();
//        if (Objects.equals(userContext.getUserId(), userId)) {
//            return userContext;
//        }
//        userContext.setUserId(userId);
//        userContext.setRealName("系统自动");
//        userContext.setUserName("系统自动");
//        UserInfoDto userInfoDto = getUserInfo(userId);
//        if (Objects.isNull(userInfoDto)) {
//            return userContext;
//        }
//
//        userContext.setMealStatus(userInfoDto.getMealStatus());
//        userContext.setUserId(userInfoDto.getUserId());
//        userContext.setUserName(userInfoDto.getRealName());
//        userContext.setOperationRepositoryCode(userInfoDto.getWarehouseCode());
//        userContext.setOperationRepositoryName(userInfoDto.getWarehouseName());
//        userContext.setRealName(userInfoDto.getRealName());
//        userContext.setStationNo(userInfoDto.getStationCode());
//        userContext.setUserType(userInfoDto.getUserType());
//        userContext.setUserStatus(userInfoDto.getUserStatus());
//        userContext.setSignStatus(userInfoDto.getSignStatus());
//        userContext.setSignId(userInfoDto.getSignId());
//
//        // 用户信息新增 组别信息
//        userContext.setOrgCode(userInfoDto.getOrgCode());
//        userContext.setOrgName(userInfoDto.getOrgName());
//        userContext.setOrgFullName(userInfoDto.getOrgFullName());
//        userContext.setTenantId(userInfoDto.getTenantId());
//        List<OperationUserContext.WarehouseDto> warehouses = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(userInfoDto.getWarehouses())) {
//            OperationUserContext.WarehouseDto pinkWarehouseDto = null;
//            List<com.dewu.scm.lms.api.interfaces.dto.WarehouseDto> lmsWarehouseDtos = userInfoDto.getWarehouses();
//            for (com.dewu.scm.lms.api.interfaces.dto.WarehouseDto warehouseDto : lmsWarehouseDtos) {
//                pinkWarehouseDto = new OperationUserContext.WarehouseDto();
//                pinkWarehouseDto.setWarehouseCode(warehouseDto.getWarehouseCode());
//                pinkWarehouseDto.setWarehouseName(warehouseDto.getWarehouseName());
//                warehouses.add(pinkWarehouseDto);
//            }
//        }
//        userContext.setWarehouses(warehouses);
//        MesIdentifyContext.setOperationUser(userContext);
//        return userContext;
//    }

}
