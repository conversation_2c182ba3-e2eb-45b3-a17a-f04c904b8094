package com.shizhuang.scm.rulecenter.domains.archive.service;

import com.dewu.scp.qms.sdk.api.QmsEmployeeApi;
import com.dewu.scp.qms.sdk.domain.request.EmployeeRequest;
import com.dewu.scp.qms.sdk.domain.response.EmployeeResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Slf4j
@Service
public class UserApplicationService {

    private static final String URL = "/open-api/cmdb/v1/applications/user-app/";
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${scp.archive.cmdb.domain:''}")
    private String domain;
    @Value("${scp.archive.cmdb.identity:''}")
    private String token;

    private CloseableHttpClient httpClient;

    @Resource
    private QmsEmployeeApi qmsEmployeeApi;

    @PostConstruct
    public void init() {
        httpClient = HttpClients.createDefault();
    }

    @PreDestroy
    public void cleanup() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            log.error("Error closing HttpClient", e);
        }
    }

    public List<String> getAppList() {
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        EmployeeRequest employeeRequest = new EmployeeRequest();
        employeeRequest.setSsoId(Long.toString(operationUser.getUserId()));
        Result<EmployeeResponse> result = qmsEmployeeApi.getEmployeeByUserId(employeeRequest);
        if (!Result.isSuccess(result) || result.getData() == null
                || result.getData().getSsoId() == null || result.getData().getSsoId() <= 0) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "获取当前用户信息失败");
        }
        List<String> appNames = new ArrayList<>();
        try {
            String requestUserId = String.valueOf(result.getData().getUserId());
            String fullUrl = domain + URL + "?user=" + requestUserId;
            HttpGet request = new HttpGet(fullUrl);
            request.setHeader("Authorization", token);
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JsonNode rootNode = objectMapper.readTree(responseBody);
                if (rootNode.get("code").asInt() == 0) {
                    JsonNode dataNode = rootNode.get("data");
                    if (dataNode.isArray()) {
                        for (JsonNode appNode : dataNode) {
                            appNames.add(appNode.get("name").asText());
                        }
                    }
                } else {
                    log.error("Failed to get app list. Error code: {}, message: {}",
                            rootNode.get("code").asInt(),
                            rootNode.get("msg").asText());
                }
            }
        } catch (IOException e) {
            log.error("Error while fetching app list", e);
        }
        return appNames;
    }
}
