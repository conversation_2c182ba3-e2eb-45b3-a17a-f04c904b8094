package com.shizhuang.scm.rulecenter.domains.meta.repository.transfer;

import com.alibaba.fastjson.JSON;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriTypeVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.SchemaFeatureVO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.SchemaDO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Objects;


@Mapper(componentModel = "spring")
public interface SchemaTransfer extends BaseTransfer<SchemaDO, SchemaEntity> {

    SchemaTransfer INSTANCE = Mappers.getMapper(SchemaTransfer.class);

    @Override
    default SchemaDO dToS(SchemaEntity data) {
        if (data == null) {
            return null;
        }

        SchemaDO schemaDO = new SchemaDO();

        schemaDO.setSchemaCode(data.getSchemaCode());
        schemaDO.setSchemaName(data.getSchemaName());
        schemaDO.setSchemaDesc(data.getSchemaDesc());
        if (data.getStatus() != null) {
            schemaDO.setStatus(data.getStatus().longValue());
        }
        schemaDO.setVersion(data.getVersion());
        if (data.getIsLargestVersion() != null) {
            schemaDO.setIsLargestVersion(data.getIsLargestVersion().byteValue());
        }
        if (data.getIsLatestVersion() != null) {
            schemaDO.setIsLatestVersion(data.getIsLatestVersion().byteValue());
        }
        schemaDO.setCreator(data.getCreator());
        if (data.getIsDel() != null) {
            schemaDO.setIsDel(data.getIsDel().byteValue());
        }
        schemaDO.setMenu(data.getMenu());
        schemaDO.setProjectName(data.getProjectName());

        Integer redisCacheExpireTime = data.getRedisCacheExpireTime();
        SchemaFeatureVO schemaFeatureVO = new SchemaFeatureVO();
        schemaFeatureVO.setRedisCacheExpireTime(redisCacheExpireTime);
        schemaFeatureVO.setActionFeature(data.getActionFeature());
        DimPriTypeVO dimPriTypeVO = data.getDimPriTypeVO();
        if(Objects.isNull(dimPriTypeVO)){
            schemaFeatureVO.setDimPriType(0);
        }else{
            schemaFeatureVO.setDimPriType(dimPriTypeVO.getDimPriType());
            schemaFeatureVO.setOrderField(dimPriTypeVO.getOrderField());
            schemaFeatureVO.setOrderType(dimPriTypeVO.getOrderType());
        }
        schemaDO.setAttributes(JSON.toJSONString(schemaFeatureVO));

        return schemaDO;
    }

    @Override
    default SchemaEntity sToT(SchemaDO data) {
        if (data == null) {
            return null;
        }

        SchemaEntity schemaEntity = new SchemaEntity();

        schemaEntity.setSchemaCode(data.getSchemaCode());
        schemaEntity.setSchemaName(data.getSchemaName());
        schemaEntity.setSchemaDesc(data.getSchemaDesc());
        if (data.getStatus() != null) {
            schemaEntity.setStatus(data.getStatus().intValue());
        }
        schemaEntity.setVersion(data.getVersion());
        schemaEntity.setCreator(data.getCreator());
        if (data.getIsLargestVersion() != null) {
            schemaEntity.setIsLargestVersion(data.getIsLargestVersion().intValue());
        }
        if (data.getIsLatestVersion() != null) {
            schemaEntity.setIsLatestVersion(data.getIsLatestVersion().intValue());
        }
        if (data.getIsDel() != null) {
            schemaEntity.setIsDel(data.getIsDel().intValue());
        }
        schemaEntity.setMenu(data.getMenu());
        schemaEntity.setProjectName(data.getProjectName());
        if (StringUtils.isNotBlank(data.getAttributes())) {
            SchemaFeatureVO schemaFeatureVO = JSON.parseObject(data.getAttributes(), SchemaFeatureVO.class);
            schemaEntity.setRedisCacheExpireTime(schemaFeatureVO.getRedisCacheExpireTime());
            schemaEntity.setActionFeature(schemaFeatureVO.getActionFeature());
            DimPriTypeVO dimPriTypeVO = new DimPriTypeVO();
            dimPriTypeVO.setDimPriType(schemaFeatureVO.getDimPriType() == null ? 0 : schemaFeatureVO.getDimPriType());
            dimPriTypeVO.setOrderField(schemaFeatureVO.getOrderField());
            dimPriTypeVO.setOrderType(schemaFeatureVO.getOrderType());
            schemaEntity.setDimPriTypeVO(dimPriTypeVO);
        }else{
            DimPriTypeVO dimPriTypeVO = new DimPriTypeVO();
            dimPriTypeVO.setDimPriType(0);
            schemaEntity.setDimPriTypeVO(dimPriTypeVO);
        }
        return schemaEntity;
    }

}
