package com.shizhuang.scm.rulecenter.domains.inst.service;


import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.dewu.scm.lms.api.report.dto.response.ImportExcelResponse;
import com.dw.scp.base.enums.OperationType;
import com.dw.scp.base.enums.ScpDomainEnum;
import com.dw.scp.bizlog.gateway.BizOperationLogGateway;
import com.dw.scp.bizlog.model.BizOperationLogBase;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAbleSwitchCmd;
import com.shizhuang.scm.rulecenter.api.constant.DimPriTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.DimensionKeyDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.dto.TableHeadDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.query.RedisKeyHelper;
import com.shizhuang.scm.rulecenter.domains.anticorrosion.user.UserDomainService;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstAggEntity;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstAddOrUpdateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimensionKeyVO;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import com.shizhuang.scm.rulecenter.infrastructure.cache.RedisCacheService;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 参数实例领域服务
 */
@Slf4j
@Component
public class ParamInstDomainService {


    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    RedisCacheService redisCacheService;

    @Resource
    BizOperationLogGateway bizOperationLogGateway;

    @Resource
    UserDomainService userDomainService;

    //操作仓code
    private static String operateWarehouseCode = "operateWarehouseCode";
    //寄存仓code
    private static String depositWarehouseCode = "depositWarehouseCode";
    //仓库code
    //private static String warehouseCode = "warehouseCode";


    public static ImportExcelResponse buildRowRes(BaseImportExcel row, String res) {
        ImportExcelResponse importExcelResponse = new ImportExcelResponse();
        importExcelResponse.setTaskId(row.getTaskId());
        importExcelResponse.setRowId(row.getRowId());
        if (StringUtils.isNotBlank(res)) {
            importExcelResponse.setResultDesc(res);
        } else {
            importExcelResponse.setResultDesc("成功");
        }
        return importExcelResponse;
    }

    public InstPageQuery checkAuthUserWarehouser(InstPageQuery instPageQuery,Long userId){
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        //添加用户仓库权限逻辑
        if(BooleanUtils.isTrue(instPageQuery.getCheckAuthUserWarehouse()) && userDomainService.getUserAuthSwitch()){
            if(CollectionUtils.isNotEmpty(queryParams)){
                List<ElementInstDTO> operateWarehouseCodeList = queryParams.stream().filter(dto -> operateWarehouseCode.equals(dto.getElementCode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(operateWarehouseCodeList)){
                    addElementInstDTO(operateWarehouseCode,instPageQuery,userId);
                }
                List<ElementInstDTO> depositWarehouseCodeList= queryParams.stream().filter(dto -> depositWarehouseCode.equals(dto.getElementCode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(depositWarehouseCodeList)){
                    addElementInstDTO(depositWarehouseCode,instPageQuery,userId);
                }
                //暂时注释掉 后续如果有使用这个字段再放开
                /*List<ElementInstDTO> warehouseCodeList = queryParams.stream().filter(dto -> warehouseCode.equals(dto.getElementCode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(warehouseCodeList)){
                    addElementInstDTO(warehouseCode,instPageQuery,userId);
                }*/
            }else{
                queryParams = new ArrayList<ElementInstDTO>();
                instPageQuery.setQueryParams(queryParams);
                addElementInstDTO(operateWarehouseCode,instPageQuery,userId);
                addElementInstDTO(depositWarehouseCode,instPageQuery,userId);
            }
        }
        return instPageQuery;
    }

    /**
     * 添加仓库权限
     * @param elementCode
     * @param instPageQuery
     * @param userId
     */
    public void addElementInstDTO(String elementCode,InstPageQuery instPageQuery,Long userId) {
        ElementInstDTO elementInstDTO = new ElementInstDTO();
        elementInstDTO.setElementCode(elementCode);
        elementInstDTO.setElementValue(JSON.toJSONString(userDomainService.getUserAuthWarehouseCodeListByUserId(userId)));
        instPageQuery.getQueryParams().add(elementInstDTO);
        log.info("ParamInstDomainService#addElementInstDTO 校验仓库权限,获取到的仓库{}:{}",elementCode, com.alibaba.fastjson2.JSON.toJSONString(elementInstDTO));
    }


    public static List<DimensionKeyVO> getDimensionKeyVOs(List<DimPriorityVO> orderedDimPriorityVO, List<String> queryDimensionCode, Map<String, ElementInstDTO> queryElementMap) {
        // 若方案无优先级设置，则做全匹配; 若有优先级，则按优先级提取可能的dimensionKey
        if (CollectionUtils.isEmpty(orderedDimPriorityVO)) {
            String dimension = String.join("#", queryDimensionCode);
            String dimensionKey = queryDimensionCode.stream().map(vo -> {
                ElementInstDTO elementInstDTO = queryElementMap.get(vo);
                return String.valueOf(elementInstDTO.getElementValue());
            }).collect(Collectors.joining("#"));
            return Lists.newArrayList(DimensionKeyVO.builder().dimension(dimension).dimensionKey(dimensionKey).build());
        } else {
            return orderedDimPriorityVO.stream().filter(vo -> {
                List<String> atomicDimension = vo.getAtomicDimension();
                for (String atomicDimensionCode : atomicDimension) {
                    if (!queryDimensionCode.contains(atomicDimensionCode)) {
                        return false;
                    }
                }
                return true;
            }).map(vo -> {
                List<String> elementValues = new ArrayList<>();
                for (String atomicDimension : vo.getAtomicDimension()) {
                    String elementValue = queryElementMap.get(atomicDimension).getElementValue();
                    elementValues.add(elementValue);
                }
                String dimensionKey = String.join("#", elementValues);
                return DimensionKeyVO.builder().dimension(vo.getDimension()).dimensionKey(dimensionKey).build();
            }).collect(Collectors.toList());
        }
    }

    public ParamInstTempDimRes getParamInstDimRes(String redisCustomDimKey, List<DimensionKeyVO> hitDimensionKeys, String schemaCode, Long cacheExpireTime, Integer dimPriType) {
        if (DimPriTypeEnum.DIMENSION.getType().equals(dimPriType)) {
            String redisDimKey = null;
            ParamInstanceDOWithBLOBs paramInstanceDO = null;
            DimensionKeyDTO dimensionKeyDTO = null;
            for (DimensionKeyVO vo : hitDimensionKeys) {
                paramInstanceDO = paramInstRepository.getEnabledByDim(schemaCode, vo.getDimension(), vo.getDimensionKey());
                if (Objects.nonNull(paramInstanceDO)) {
                    dimensionKeyDTO = new DimensionKeyDTO();
                    dimensionKeyDTO.setDimension(vo.getDimension());
                    dimensionKeyDTO.setDimensionKey(vo.getDimensionKey());
                    redisDimKey = RedisKeyHelper.getRedisDimKey(schemaCode, vo.getDimension(), vo.getDimensionKey());
                    break;
                }
            }
            if (Objects.nonNull(dimensionKeyDTO)) {
                redisCacheService.putToValue(redisCustomDimKey, dimensionKeyDTO, cacheExpireTime, TimeUnit.SECONDS);
            }
            return new ParamInstTempDimRes(redisDimKey, paramInstanceDO);

        } else if (DimPriTypeEnum.MODIFY_TIME.getType().equals(dimPriType)) {
            String redisDimKey = null;
            ParamInstanceDOWithBLOBs paramInstanceDO = null;
            DimensionKeyDTO dimensionKeyDTO = null;
            List<ParamInstanceDOWithBLOBs> paramInstList = new ArrayList<>();
            for (DimensionKeyVO vo : hitDimensionKeys) {
                ParamInstanceDOWithBLOBs candidateParamInst = paramInstRepository.getEnabledByDim(schemaCode, vo.getDimension(), vo.getDimensionKey());
                if (Objects.nonNull(candidateParamInst)) {
                    paramInstList.add(candidateParamInst);
                }
            }

            List<ParamInstanceDOWithBLOBs> orderedParamInstList = paramInstList.stream().sorted(Comparator.comparing(ParamInstanceDOWithBLOBs::getModifyTime).reversed()).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(orderedParamInstList)) {
                paramInstanceDO = orderedParamInstList.get(0);
                dimensionKeyDTO = new DimensionKeyDTO();
                dimensionKeyDTO.setDimension(paramInstanceDO.getDimension());
                dimensionKeyDTO.setDimensionKey(paramInstanceDO.getDimensionKey());
                redisDimKey = RedisKeyHelper.getRedisDimKey(schemaCode, paramInstanceDO.getDimension(), paramInstanceDO.getDimensionKey());
            }

            if (Objects.nonNull(dimensionKeyDTO)) {
                redisCacheService.putToValue(redisCustomDimKey, dimensionKeyDTO, cacheExpireTime, TimeUnit.SECONDS);
            }
            return new ParamInstTempDimRes(redisDimKey, paramInstanceDO);

        }
        return null;
    }

    public void recordEditInstBizLog(ParamInstEditContext paramInstCreateContext) {

        BizOperationLogBase<ParamInstAggEntity> bizOperationLogBase = new BizOperationLogBase<>();
        bizOperationLogBase.setScpDomainCode(ScpDomainEnum.PINK.name());
        bizOperationLogBase.setBizType("scm-rulecenter-paramInstance");
        bizOperationLogBase.setBizId(paramInstCreateContext.getAggId());
        bizOperationLogBase.setOperationTypeCode(OperationType.UPDATE.getCode());
        bizOperationLogBase.setClazz(ParamInstAggEntity.class);
        bizOperationLogBase.setOperatorName(paramInstCreateContext.getOperatorName());
        bizOperationLogBase.setOperatorId(paramInstCreateContext.getOperatorId());

        bizOperationLogBase.setOldObject(paramInstCreateContext.getOldAggEntity());
        bizOperationLogBase.setNewObject(paramInstCreateContext.getNewAggEntity());

        Map<String, String> fieldElementNameMap = paramInstCreateContext.getSchemaMetaAggregate().getFieldElementNameMap();
        Map<String, String> fieldNameMap = new HashMap<>();
        for (Map.Entry<String, String> entry : fieldElementNameMap.entrySet()) {
            String dbFieldCode = entry.getKey();
            dbFieldCode = dbFieldCode.replace("_", "");
            fieldNameMap.put(dbFieldCode, entry.getValue());
        }
        bizOperationLogBase.setFieldNameMap(fieldNameMap);
        bizOperationLogGateway.createLog(bizOperationLogBase);
    }

    public void recordEnableInstBizLog(ParamInstAbleSwitchCmd paramInstAbleSwitchCmd) {
        BizOperationLogBase<ParamInstAggEntity> bizOperationLogBase = new BizOperationLogBase<>();
        bizOperationLogBase.setScpDomainCode(ScpDomainEnum.PINK.name());
        bizOperationLogBase.setBizType("scm-rulecenter-paramInstance");
        bizOperationLogBase.setBizId(paramInstAbleSwitchCmd.getAggId());
        bizOperationLogBase.setClazz(ParamInstAggEntity.class);
        bizOperationLogBase.setDisplayContent("生效");
        ParamInstAggEntity paramInstAggEntity = new ParamInstAggEntity();
        paramInstAggEntity.setAggId(paramInstAbleSwitchCmd.getAggId());
        bizOperationLogBase.setOldObject(paramInstAggEntity);
        bizOperationLogBase.setNewObject(paramInstAggEntity);
        bizOperationLogBase.setFieldNameMap(new HashMap<>());
        bizOperationLogBase.setOperationTypeCode(OperationType.TAKE_EFFECT.getCode());
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        bizOperationLogBase.setOperatorId(String.valueOf(operationUser.getUserId()));
        bizOperationLogBase.setOperatorName(operationUser.getUserNameUnBlank());
        bizOperationLogGateway.createLog(bizOperationLogBase);

    }

    public void recordDisableInstBizLog(ParamInstAbleSwitchCmd paramInstAbleSwitchCmd) {
        BizOperationLogBase<ParamInstAggEntity> bizOperationLogBase = new BizOperationLogBase<>();
        bizOperationLogBase.setScpDomainCode(ScpDomainEnum.PINK.name());
        bizOperationLogBase.setBizType("scm-rulecenter-paramInstance");
        bizOperationLogBase.setBizId(paramInstAbleSwitchCmd.getAggId());
        bizOperationLogBase.setClazz(ParamInstAggEntity.class);
        bizOperationLogBase.setOperationTypeCode(OperationType.LOSE_EFFECT.getCode());
        bizOperationLogBase.setDisplayContent("失效");
        ParamInstAggEntity paramInstAggEntity = new ParamInstAggEntity();
        paramInstAggEntity.setAggId(paramInstAbleSwitchCmd.getAggId());
        bizOperationLogBase.setOldObject(paramInstAggEntity);
        bizOperationLogBase.setNewObject(paramInstAggEntity);
        bizOperationLogBase.setFieldNameMap(new HashMap<>());
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        bizOperationLogBase.setOperatorId(String.valueOf(operationUser.getUserId()));
        bizOperationLogBase.setOperatorName(operationUser.getUserNameUnBlank());
        bizOperationLogGateway.createLog(bizOperationLogBase);
    }

    public void recordCreateInstBizLog(ParamInstCreateContext paramInstCreateContext) {
        BizOperationLogBase<ParamInstAggEntity> bizOperationLogBase = new BizOperationLogBase<>();
        bizOperationLogBase.setScpDomainCode(ScpDomainEnum.PINK.name());
        bizOperationLogBase.setBizType("scm-rulecenter-paramInstance");
        bizOperationLogBase.setBizId(paramInstCreateContext.getAggId());
        bizOperationLogBase.setClazz(ParamInstAggEntity.class);
        bizOperationLogBase.setOperationTypeCode(OperationType.ADD.getCode());
        if (paramInstCreateContext != null
                && paramInstCreateContext.getParamInstAddCmd() != null
                && paramInstCreateContext.getParamInstAddCmd().getParamMap() != null) {
            bizOperationLogBase.setDisplayContent("新增" + JSON.toJSONString(paramInstCreateContext.getParamInstAddCmd().getParamMap()));
        }else{
            bizOperationLogBase.setDisplayContent("新增");
        }
        ParamInstAggEntity paramInstAggEntity = new ParamInstAggEntity();
        paramInstAggEntity.setAggId(paramInstCreateContext.getAggId());
        bizOperationLogBase.setOldObject(paramInstAggEntity);
        bizOperationLogBase.setNewObject(paramInstAggEntity);
        bizOperationLogBase.setFieldNameMap(new HashMap<>());
        bizOperationLogBase.setOperatorId(paramInstCreateContext.getOperatorId());
        bizOperationLogBase.setOperatorName(paramInstCreateContext.getOperatorName());
        bizOperationLogGateway.createLog(bizOperationLogBase);
    }

    public void recordAddOrUpdateInstBizLog(ParamInstAddOrUpdateContext paramInstAddOrUpdateContext) {



    }


    public static List<TableHeadDTO> getTableHeadDTOS(SchemaMetaAggregate schemaAggregateByCode) {
        List<SchemaElementEntity> allElements = schemaAggregateByCode.getAllElements();
        List<TableHeadDTO> tableHeadDTOS = allElements.stream().map(e -> {
            TableHeadDTO tableHeadDTO = new TableHeadDTO();
            tableHeadDTO.setLabel(e.getElementName());
            tableHeadDTO.setName(e.getElementCode());
            return tableHeadDTO;
        }).collect(Collectors.toList());

        TableHeadDTO lastModifierTableHead = new TableHeadDTO();
        lastModifierTableHead.setLabel("最后修改人");
        lastModifierTableHead.setName("lastModifier");
        tableHeadDTOS.add(lastModifierTableHead);
        TableHeadDTO lastModifyTimeHead = new TableHeadDTO();
        lastModifyTimeHead.setLabel("最后修改时间");
        lastModifyTimeHead.setName("lastModifyTime");
        tableHeadDTOS.add(lastModifyTimeHead);
        TableHeadDTO instCreatorHead = new TableHeadDTO();
        instCreatorHead.setLabel("创建人");
        instCreatorHead.setName("instCreator");
        tableHeadDTOS.add(instCreatorHead);
        TableHeadDTO instCreateTimeHead = new TableHeadDTO();
        instCreateTimeHead.setLabel("创建时间");
        instCreateTimeHead.setName("instCreateTime");
        tableHeadDTOS.add(instCreateTimeHead);
        return tableHeadDTOS;
    }

}
