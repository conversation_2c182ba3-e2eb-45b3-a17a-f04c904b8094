package com.shizhuang.scm.rulecenter.domains.inst.context;

import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
public class ParamInstAddOrUpdateContext {

    /**
     * 参数实例新增cmd
     */
    ParamInstAddCmd paramInstAddCmd;

    /**
     * 参数实例Entity
     */
    List<ParamInstEntity> paramInstEntities;

    /**
     * 方案聚合
     */
    SchemaMetaAggregate schemaAggregate;

    /**
     * 参数实例唯一id
     */
    String aggId;

    /**
     * 操作人名
     */
    private String operatorName;

    /**
     * 操作人id
     */
    private String operatorId;

}
