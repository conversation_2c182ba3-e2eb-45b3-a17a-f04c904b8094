package com.shizhuang.scm.rulecenter.domains.meta.repository.impl;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SchemaAggQueryOption {

    Integer needLatestVersion = 0;

    Integer needDeployed = 0;

    Integer noDeployedLargest = 0;

    public static SchemaAggQueryOption needDeployed() {
        SchemaAggQueryOption schemaAggQueryOption = new SchemaAggQueryOption();
        schemaAggQueryOption.setNeedDeployed(1);
        return schemaAggQueryOption;
    }

    public static SchemaAggQueryOption needLatestVersion() {
        SchemaAggQueryOption schemaAggQueryOption = new SchemaAggQueryOption();
        schemaAggQueryOption.setNeedLatestVersion(1);
        return schemaAggQueryOption;
    }

    public static SchemaAggQueryOption needLargestVersion() {
        return new SchemaAggQueryOption();
    }

    public static SchemaAggQueryOption noDeployedLargestVersion() {
        SchemaAggQueryOption schemaAggQueryOption = new SchemaAggQueryOption();
        schemaAggQueryOption.setNoDeployedLargest(1);
        return schemaAggQueryOption;
    }

    public String getRedisKey() {
        if (needDeployed == 1) {
            return REDIS_D;
        } else if (needLatestVersion == 1) {
            return REDIS_LE;
        } else if (noDeployedLargest == 1) {
            return REDIS_NDL;
        } else {
            return REDIS_LA;
        }
    }

    static String REDIS_D = "d";

    static String  REDIS_LE ="le";

    static String REDIS_NDL = "ndl";

    static String  REDIS_LA ="la";

    public static List<String> getRedisKeys(){
        return Lists.newArrayList(REDIS_D,REDIS_LE,REDIS_NDL,REDIS_LA);
    }

}
