package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstDimPriorityValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstElementValueValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.PramInstKeyConflictValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext.*;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ParamInstCreateValidateChain implements IBasicHandler<ParamInstCreateContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(ParamInstCreateContext paramInstCreateContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(paramInstCreateContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、校验当前的入参不能出现重复
                ValidatorHandlerFactory.build(ctx -> validateKeyConflict((ParamInstCreateContext) ctx)),

                // 2、校验当前的维度优先级已经被配置（若方案下没有优先级可以不校验）
                ValidatorHandlerFactory.build(ctx -> validateDimPriority((ParamInstCreateContext) ctx)),

                // 3、当前的入参需要满足于元素约定好的取值范围
                ValidatorHandlerFactory.build(ctx -> validateElementValue((ParamInstCreateContext) ctx)),

                // 4、添加方案相关下的自定义校验逻辑
                ValidatorHandlerFactory.build(ctx -> validateCustom((ParamInstCreateContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);

        SpotCheckConfigPriceValidator spotCheckConfigPriceValidator = applicationContext.getBean(SpotCheckConfigPriceValidator.class);
        SpotCheckConfigRatioRuleValidator spotCheckConfigRatioRuleValidator = applicationContext.getBean(SpotCheckConfigRatioRuleValidator.class);
        SpotCheckConfigNumberRuleValidator spotCheckConfigNumberRuleValidator = applicationContext.getBean(SpotCheckConfigNumberRuleValidator.class);
        extMap.put("spot-check-config", Lists.newArrayList(spotCheckConfigPriceValidator, spotCheckConfigRatioRuleValidator, spotCheckConfigNumberRuleValidator));

        RangeOverLapValidator rangeOverLapValidator = applicationContext.getBean(RangeOverLapValidator.class);
        extMap.put("brand-direct-boxinfo-rule", Lists.newArrayList(rangeOverLapValidator));

    }

    private ValidationError validateCustom(ParamInstCreateContext ctx) {

        List<ParamInstCreateCustomValidatorExt> paramInstCreateCustomValidatorExts = extMap.get(ctx.getParamInstAddCmd().getSchemaCode());
        if (CollectionUtils.isEmpty(paramInstCreateCustomValidatorExts)) {
            return null;
        }
        for (ParamInstCreateCustomValidatorExt ext : paramInstCreateCustomValidatorExts) {
            ValidationError validate = ext.validate(ctx);
            if (validate != null) {
                return validate;
            }
        }
        return null;
    }

    private ValidationError validateDimPriority(ParamInstCreateContext ctx) {
        ParamInstDimPriorityValidator validator = applicationContext.getBean(ParamInstDimPriorityValidator.class);
        ParamInstAddCmd paramInstAddCmd = ctx.getParamInstAddCmd();
        String schemaCode = paramInstAddCmd.getSchemaCode();
        List<ParamInstEntity> paramInstEntities = ctx.getParamInstEntities();
        List<ValidationError> validationErrors = paramInstEntities.stream().map(paramInstEntity -> {
            String dimension = paramInstEntity.getDimension();
            return validator.validate(schemaCode, dimension);
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(validationErrors)) {
            return validationErrors.get(0);
        }
        return null;
    }

    private ValidationError validateElementValue(ParamInstCreateContext ctx) {
        ParamInstElementValueValidator validator = applicationContext.getBean(ParamInstElementValueValidator.class);
        ParamInstAddCmd paramInstAddCmd = ctx.getParamInstAddCmd();
        SchemaMetaAggregate schemaAggregate = ctx.getSchemaAggregate();
        Map<String, String> elementCodeToNameMap = schemaAggregate.getElementCodeToNameMap();
        List<ElementInstAddCmd> cmdList = paramInstAddCmd.getParamMap();
        Map<String, Object> paramMap = cmdList.stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        return validator.validate(paramInstAddCmd.getSchemaCode(), paramMap, elementCodeToNameMap);
    }

    private ValidationError validateKeyConflict(ParamInstCreateContext ctx) {
        PramInstKeyConflictValidator validator = applicationContext.getBean(PramInstKeyConflictValidator.class);
        List<ParamInstEntity> paramInstEntities = ctx.getParamInstEntities();
        List<ValidationError> validationErrors = paramInstEntities.stream().map(paramInstEntity -> {
            return validator.validate(paramInstEntity);
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(validationErrors)) {
            return validationErrors.get(0);
        }
        return null;
    }

    Map<String, List<ParamInstCreateCustomValidatorExt>> extMap = new HashMap<>();

}
