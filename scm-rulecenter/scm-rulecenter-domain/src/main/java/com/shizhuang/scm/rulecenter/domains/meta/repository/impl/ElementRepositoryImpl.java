package com.shizhuang.scm.rulecenter.domains.meta.repository.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.poizon.fusion.utils.DateUtils;
import com.shizhuang.scm.rulecenter.api.constant.ElementSourceTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.ElementMetaDTO;
import com.shizhuang.scm.rulecenter.api.query.ElementPageQuery;
import com.shizhuang.scm.rulecenter.api.query.RedisKeyHelper;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceVO;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.transfer.ElementTransfer;
import com.shizhuang.scm.rulecenter.domains.meta.service.AcquireElementEnumsContext;
import com.shizhuang.scm.rulecenter.domains.meta.service.AcquireValueRangeStrategy;
import com.shizhuang.scm.rulecenter.domains.meta.service.DubboSearchTypeEnum;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.CreatorVO;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import com.shizhuang.scm.rulecenter.infrastructure.cache.RedisCacheService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.spring.ApplicationContextHelper;
import com.shizhuang.scm.rulecenter.infrastructure.common.transaction.TransactionUtil;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementValueRangeDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementValueRangeDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ElementMapper;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ElementValueRangeMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.DATABASE_ERROR;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.ELEMENT_NOT_EXIST;

@Slf4j
@Component
public class ElementRepositoryImpl implements ElementRepository {

    @Resource
    ElementMapper elementMapper;

    @Resource
    ElementValueRangeMapper elementValueRangeMapper;

    @Resource
    TransactionUtil transactionUtil;

    @Resource
    MetaDomainService metaDomainService;

    @Resource
    RedisCacheService redisCacheService;

    @Override
    public int save(ElementMetaEntity elementMetaEntity) {

        //1、存储元素本身信息
        ElementDO elementDO = ElementTransfer.INSTANCE.dToS(elementMetaEntity);
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        elementDO.setCreator(JSON.toJSONString(operationUser.getCreatorVO()));

        //2、存储元素取值范围信息
        List<ElementEnumVO> enums = elementMetaEntity.getEnums();

        List<ElementValueRangeDO> elementValueRangeDOS = enums.stream().map(e -> {
            ElementValueRangeDO elementValueRangeDO = new ElementValueRangeDO();
            elementValueRangeDO.setBelongToSchemaCode(elementMetaEntity.getSchemaCode());
            elementValueRangeDO.setElementCode(elementDO.getElementCode());
            elementValueRangeDO.setValue(e.getValue());
            elementValueRangeDO.setDesc(e.getDesc());
            elementValueRangeDO.setIsDel(CommonConstant.ZERO.byteValue());
            return elementValueRangeDO;
        }).collect(Collectors.toList());

        try {
            transactionUtil.transaction(() -> {
                elementMapper.insertSelective(elementDO);
                elementValueRangeDOS.forEach(e -> elementValueRangeMapper.insertSelective(e));
            });
        } catch (Exception e) {
            log.error("ElementRepository.save database error elementDO:{} elementValueRangeDOS:{}"
                    , JSON.toJSONString(elementDO), JSON.toJSONString(elementValueRangeDOS));
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }

        return 1;
    }

    @Override
    public int update(ElementMetaEntity elementMetaEntity) {

        //1、存储元素本身信息
        ElementDO elementDO = ElementTransfer.INSTANCE.dToS(elementMetaEntity);
        //2、存储元素取值范围信息
        List<ElementEnumVO> enums = elementMetaEntity.getEnums();

        List<ElementValueRangeDO> elementValueRangeDOS = enums.stream().map(e -> {
            ElementValueRangeDO elementValueRangeDO = new ElementValueRangeDO();
            elementValueRangeDO.setBelongToSchemaCode(elementMetaEntity.getSchemaCode());
            elementValueRangeDO.setElementCode(elementDO.getElementCode());
            elementValueRangeDO.setValue(e.getValue());
            elementValueRangeDO.setDesc(e.getDesc());
            elementValueRangeDO.setIsDel(CommonConstant.ZERO.byteValue());
            return elementValueRangeDO;
        }).collect(Collectors.toList());

        String elementRedisKey = RedisKeyHelper.getElementEntityKey(elementMetaEntity.getSchemaCode(), elementMetaEntity.getElementCode());
        redisCacheService.removeCache(elementRedisKey);

        try {
            transactionUtil.transaction(() -> {
                //失效掉原有的元素数据
                ElementDO invalidElementDO = new ElementDO();
                invalidElementDO.setBelongToSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + elementMetaEntity.getSchemaCode());
                invalidElementDO.setElementCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + elementMetaEntity.getElementCode());
                invalidElementDO.setIsDel(CommonConstant.ONE.byteValue());
                ElementDOExample elementDOExample = new ElementDOExample();
                elementDOExample.createCriteria().andElementCodeEqualTo(elementMetaEntity.getElementCode()).andBelongToSchemaCodeEqualTo(elementMetaEntity.getSchemaCode());
                elementMapper.updateByExampleSelective(invalidElementDO, elementDOExample);
                //重新插入新编辑的数据
                elementMapper.insertSelective(elementDO);

                //失效掉原有的元素枚举取值数据
                ElementValueRangeDO invalidElementValueRangeDO = new ElementValueRangeDO();
                invalidElementValueRangeDO.setElementCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + elementMetaEntity.getElementCode());
                invalidElementValueRangeDO.setIsDel(CommonConstant.ONE.byteValue());
                ElementValueRangeDOExample elementValueRangeDOExample = new ElementValueRangeDOExample();
                elementValueRangeDOExample.createCriteria().andElementCodeEqualTo(elementMetaEntity.getElementCode()).andBelongToSchemaCodeEqualTo(elementMetaEntity.getSchemaCode());
                elementValueRangeMapper.updateByExampleSelective(invalidElementValueRangeDO, elementValueRangeDOExample);
                //重新插入新编辑的数据
                elementValueRangeDOS.forEach(e -> elementValueRangeMapper.insertSelective(e));
            });
        } catch (Exception e) {
            log.error("ElementRepository.update database error elementDO:{} elementValueRangeDOS:{} "
                    , JSON.toJSONString(elementDO), JSON.toJSONString(elementValueRangeDOS));
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }

        return 1;
    }

    LoadingCache<String, Optional<ElementMetaEntity>> elementMetaEntityLoadingCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.SECONDS).maximumSize(10000)
            .build(new CacheLoader<String, Optional<ElementMetaEntity>>() {
                @Override
                public Optional<ElementMetaEntity> load(@NonNull String key) {
                    return Optional.empty();
                }
            });

    @Override
    public ElementMetaEntity getByCode(String schemaCode, String elementCode) {
        if (StringUtils.isBlank(schemaCode)) {
            schemaCode = CommonConstant.COMMON;
        }
        ElementMetaEntity elementMetaEntity = getByCodeSimple(schemaCode, elementCode);
        if (Objects.isNull(elementMetaEntity)) {
            throw new BizRuntimeException(ELEMENT_NOT_EXIST.getCode(), String.format(ELEMENT_NOT_EXIST.getErrMsg(), elementCode));
        }
        return elementMetaEntity;
    }

    @Resource
    AcquireElementEnumsContext acquireElementEnumsContext;

    @Override
    public ElementMetaEntity getByCodeWithEnum(String schemaCode, String elementCode, Map<String, Object> extParam) {
        if (StringUtils.isBlank(schemaCode)) {
            schemaCode = CommonConstant.COMMON;
        }
        ElementMetaEntity elementMetaEntity = getByCode(schemaCode, elementCode);
        elementMetaEntity.setExtParam(extParam);
        Map<String, Object> context = new HashMap<>();
        if (MapUtils.isNotEmpty(extParam) && extParam.containsKey("elementValue")) {
            context.put("elementValue", extParam.get("elementValue"));
        }
        if (MapUtils.isNotEmpty(extParam) && extParam.containsKey("elementDesc")) {
            context.put("elementDesc", extParam.get("elementDesc"));
        }
        context.put("searchType", DubboSearchTypeEnum.DESC_TO_VALUE.getType());
        ValueRangeTypeEnum valueRangeTypeEnum = ValueRangeTypeEnum.of(elementMetaEntity.getValueRangeType());
        AcquireValueRangeStrategy strategy = acquireElementEnumsContext.select(valueRangeTypeEnum.getType());
        ElementMetaEntity incokeElementMetaEntity = strategy.cleanParam(elementMetaEntity, context);
        List<ElementEnumVO> elementEnumVOS = metaDomainService.extractElementEnums(incokeElementMetaEntity);
        elementMetaEntity.setEnums(elementEnumVOS);
        return elementMetaEntity;
    }

    @Override
    public ElementMetaEntity getByCodeSimple(String schemaCode, String elementCode) {
        if (StringUtils.isBlank(schemaCode)) {
            schemaCode = CommonConstant.COMMON;
        }
        String key = RedisKeyHelper.getElementEntityKey(schemaCode, elementCode);
        Optional<ElementMetaEntity> elementLoadingCache = elementMetaEntityLoadingCache.getUnchecked(key);
        if (elementLoadingCache.isPresent()) {
            return elementLoadingCache.get();
        }
        ElementMetaEntity elementMetaEntity = (ElementMetaEntity) redisCacheService.getUnCheck(key);
        log.info("redisCacheService.getUnCheck key:{} ElementMetaEntity:{}", key, JSON.toJSONString(elementMetaEntity));
        if (Objects.nonNull(elementMetaEntity)) {
            elementMetaEntityLoadingCache.put(key, Optional.of(elementMetaEntity));
            return elementMetaEntity;
        }
        ElementDOExample elementDOExample = new ElementDOExample();
        elementDOExample.createCriteria().andBelongToSchemaCodeEqualTo(schemaCode).andElementCodeEqualTo(elementCode);
        List<ElementDO> elementDOS = elementMapper.selectByExample(elementDOExample);
        if (CollectionUtils.isEmpty(elementDOS)) {
            if (!CommonConstant.COMMON.equals(schemaCode)) {
                elementMetaEntity = getByCode("COMMON", elementCode);
                elementMetaEntityLoadingCache.put(key, Optional.of(elementMetaEntity));
                redisCacheService.putToValue(key, elementMetaEntity, 60L, TimeUnit.SECONDS);
                return elementMetaEntity;
            }
            return null;
        }
        ElementDO elementDO = elementDOS.get(0);
        elementMetaEntity = ElementTransfer.INSTANCE.sToT(elementDO);
        elementMetaEntityLoadingCache.put(key, Optional.of(elementMetaEntity));
        redisCacheService.putToValue(key, elementMetaEntity, 60L, TimeUnit.SECONDS);
        return elementMetaEntity;
    }

    @Override
    public Boolean existElement(String schemaCode, String elementCode) {
        ElementDOExample elementDOExample = new ElementDOExample();
        elementDOExample.createCriteria().andBelongToSchemaCodeEqualTo(schemaCode).andElementCodeEqualTo(elementCode);
        long count = elementMapper.countByExample(elementDOExample);
        return count > 0;
    }

    public PageInfo<ElementMetaDTO> queryByPage(ElementPageQuery elementPageQuery) {
        Integer pageNum = elementPageQuery.getPageNum();
        Integer pageSize = elementPageQuery.getPageSize();
        String elementCode = elementPageQuery.getElementCode();
        String elementName = elementPageQuery.getElementName();
        Byte elementSourceType = elementPageQuery.getElementSourceType();
        ElementDOExample elementDOExample = new ElementDOExample();
        elementDOExample.setOrderByClause(" id desc ");
        ElementDOExample.Criteria criteria = elementDOExample.createCriteria();
        if (StringUtils.isNotBlank(elementCode)) {
            criteria.andElementCodeEqualTo(elementCode);
        }
        if (StringUtils.isNotBlank(elementName)) {
            criteria.andElementNameLike(elementName);
        }
        criteria.andIsDelEqualTo(CommonConstant.ZERO.byteValue());
        if (Objects.nonNull(elementSourceType)) {
            criteria.andElementSourceTypeEqualTo(elementSourceType.intValue());
        }
        Page<ElementDO> elementDOPage = PageHelper.startPage(pageNum, pageSize).doSelectPage(() -> elementMapper.selectByExample(elementDOExample));

        List<ElementDO> result = elementDOPage.getResult();
        if (CollectionUtils.isEmpty(result)) {
            PageInfo<ElementMetaDTO> emptyPage = PageInfo.of(new ArrayList<>());
            emptyPage.setPageNum(pageNum);
            emptyPage.setPageSize(pageSize);
            return emptyPage;
        }
        List<ElementMetaDTO> elementMetaDTOS = result.stream().map(ElementRepositoryImpl::assemble).collect(Collectors.toList());
        elementMetaDTOS.forEach(ElementRepositoryImpl::fillRelationSchema);

        PageInfo<ElementMetaDTO> elementMetaDTOPageInfo = PageInfo.of(elementMetaDTOS);
        elementMetaDTOPageInfo.setPageNum(pageNum);
        elementMetaDTOPageInfo.setPageSize(pageSize);
        elementMetaDTOPageInfo.setTotal(elementDOPage.getTotal());
        return elementMetaDTOPageInfo;
    }

    private static ElementMetaDTO assemble(ElementDO elemenetDo) {
        ElementMetaDTO elementMetaDTO = new ElementMetaDTO();
        elementMetaDTO.setBelongToSchemaCode(elemenetDo.getBelongToSchemaCode());
        elementMetaDTO.setElementCode(elemenetDo.getElementCode());
        elementMetaDTO.setElementName(elemenetDo.getElementName());
        elementMetaDTO.setElementSourceType(elemenetDo.getElementSourceType());
        elementMetaDTO.setElementSourceTypeName(ElementSourceTypeEnum.of(elemenetDo.getElementSourceType().byteValue()).getDesc());
        elementMetaDTO.setValueRangeType(elemenetDo.getValueRangeType());
        elementMetaDTO.setValueRangeTypeName(ValueRangeTypeEnum.of(elemenetDo.getValueRangeType()).getDesc());
        elementMetaDTO.setValueRangeFeature(elemenetDo.getInterfaceFeature());
        elementMetaDTO.setCreateTime(DateUtils.parseDateToStr(elemenetDo.getCreateTime()));
        elementMetaDTO.setModifyTime(DateUtils.parseDateToStr(elemenetDo.getModifyTime()));
        if (StringUtils.isNotBlank(elemenetDo.getInterfaceFeature())) {
            try {
                ElementInterfaceVO elementInterfaceVO = JSON.parseObject(elemenetDo.getInterfaceFeature(), ElementInterfaceVO.class);
                elementMetaDTO.setValuePath(elementInterfaceVO.getValueCodePath() + " " + elementInterfaceVO.getValueDescPath());
            } catch (Exception ex) {
                log.error("ElementRepositoryImpl.assemble error ElementDO:{}", elemenetDo, ex);
            }
        }
        elementMetaDTO.setValueRangeFeature(elemenetDo.getInterfaceFeature());
        ArrayList<String> schemas = Lists.newArrayList(elemenetDo.getBelongToSchemaCode());
        elementMetaDTO.setRelatedSchema(schemas);
        elementMetaDTO.setElementNote(elementMetaDTO.getElementNote());
        if (StringUtils.isNotBlank(elemenetDo.getCreator())) {
            CreatorVO creatorVO = JSON.parseObject(elemenetDo.getCreator(), CreatorVO.class);
            elementMetaDTO.setCreator(creatorVO.getUserName());
        }
        return elementMetaDTO;
    }

    private static void fillRelationSchema(ElementMetaDTO e) {
        String elementCode = e.getElementCode();
        String belongToSchemaCode = e.getBelongToSchemaCode();
        SchemaRepository schemaRepository = ApplicationContextHelper.getBean(SchemaRepository.class);
        List<String> relatedSchemaByElement = schemaRepository.getRelatedSchemaByElement(belongToSchemaCode, elementCode);
        e.setRelatedSchema(relatedSchemaByElement);
    }

    @Override
    public int delete(String schemaCode, String elementCode) {
        ElementDO invalidElementDO = new ElementDO();
        String invalidSchemaCode = "DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode;
        invalidElementDO.setBelongToSchemaCode(invalidSchemaCode);
        invalidElementDO.setElementCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + elementCode);
        invalidElementDO.setIsDel(CommonConstant.ONE.byteValue());
        ElementDOExample elementDOExample = new ElementDOExample();
        elementDOExample.createCriteria().andElementCodeEqualTo(elementCode).andBelongToSchemaCodeEqualTo(schemaCode);

        ElementValueRangeDOExample elementValueRangeDOExample = new ElementValueRangeDOExample();
        ElementValueRangeDOExample.Criteria criteria = elementValueRangeDOExample.createCriteria();
        criteria.andBelongToSchemaCodeEqualTo(schemaCode);
        criteria.andElementCodeEqualTo(elementCode);
        ElementValueRangeDO elementValueRangeDO = new ElementValueRangeDO();
        elementValueRangeDO.setIsDel(CommonConstant.IS_DELETE);
        elementValueRangeDO.setBelongToSchemaCode(invalidSchemaCode);

        elementValueRangeMapper.updateByExampleSelective(elementValueRangeDO, elementValueRangeDOExample);
        return elementMapper.updateByExampleSelective(invalidElementDO, elementDOExample);
    }

}
