package com.shizhuang.scm.rulecenter.domains.meta.repository.transfer;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.SchemaFieldMappingDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface SchemaFieldTransfer  extends BaseTransfer<SchemaFieldMappingDO, FieldMappingVO>{

    SchemaFieldTransfer INSTANCE = Mappers.getMapper(SchemaFieldTransfer.class);

    @Override
    default FieldMappingVO sToT(SchemaFieldMappingDO schemaFieldMappingDO) {
        if ( schemaFieldMappingDO == null ) {
            return null;
        }
        FieldMappingVO fieldMappingVO = new FieldMappingVO();

        fieldMappingVO.setSchemaCode( schemaFieldMappingDO.getSchemaCode() );
        fieldMappingVO.setElementCode( schemaFieldMappingDO.getElementCode() );
        fieldMappingVO.setFieldCode(schemaFieldMappingDO.getAdsFieldCode());
        fieldMappingVO.setVersion(schemaFieldMappingDO.getVersion());
        return fieldMappingVO;
    }

}
