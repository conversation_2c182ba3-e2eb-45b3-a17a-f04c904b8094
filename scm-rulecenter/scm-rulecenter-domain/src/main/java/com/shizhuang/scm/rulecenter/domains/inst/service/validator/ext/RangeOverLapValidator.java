package com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Component
public class RangeOverLapValidator implements ParamInstCreateCustomValidatorExt, ParamInstEditCustomValidatorExt {

    @Resource
    ParamInstRepository paramInstRepository;

    @Override
    public ValidationError validate(ParamInstCreateContext paramInstCreateContext) {
        ParamInstAddCmd paramInstAddCmd = paramInstCreateContext.getParamInstAddCmd();
        String schemaCode = paramInstAddCmd.getSchemaCode();
        List<ElementInstAddCmd> paramMap = paramInstAddCmd.getParamMap();
        return getValidationError(schemaCode, paramMap,null);
    }

    private ValidationError getValidationError(String schemaCode, List<ElementInstAddCmd> paramMap, String aggId) {
        InstPageQuery instPageQuery = new InstPageQuery();
        instPageQuery.setPageNum(1);
        instPageQuery.setPageSize(100);
        instPageQuery.setSchemaCode(schemaCode);
        List<String> dims = Lists.newArrayList("firstCategoryId", "secondCategoryId", "thirdCategoryId");
        List<ElementInstDTO> queryParams = paramMap.stream().filter(e -> dims.contains(e.getElementCode()))
                .map(e -> {
                    ElementInstDTO elementInstDTO = new ElementInstDTO();
                    elementInstDTO.setElementCode(e.getElementCode());
                    elementInstDTO.setElementValue(String.valueOf(e.getElementValue()));
                    return elementInstDTO;
                }).collect(Collectors.toList());
        instPageQuery.setQueryParams(queryParams);
        PageInfo<InstDimPageResponse> instDimPageResponsePageInfo = paramInstRepository.queryParamInstPage(instPageQuery);
        List<InstDimPageResponse> list = instDimPageResponsePageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        String priceStage = "priceStage";
        List<String> priceRanges = list.stream()
                .filter(e -> !Objects.equals(aggId, e.getAggId()))
                .map(in -> String.valueOf(in.getElementValueMap().get(priceStage)))
                .collect(Collectors.toList());
        paramMap.stream().filter(e -> Objects.equals(priceStage, e.getElementCode())).forEach(e -> priceRanges.add(String.valueOf(e.getElementValue())));

        if (checkAllOverlaps(priceRanges)) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "相同的类目下，价格段不能有重叠: " + priceRanges);
        }

        return null;
    }

    @Override
    public ValidationError validate(ParamInstEditContext paramInstEditContext) {
        ParamInstEditCmd paramInstEditCmd = paramInstEditContext.getParamInstEditCmd();
        String schemaCode = paramInstEditCmd.getSchemaCode();
        List<ElementInstAddCmd> paramMap = paramInstEditCmd.getParamMap();
        return getValidationError(schemaCode, paramMap,paramInstEditContext.getAggId());
    }


    public boolean checkAllOverlaps(List<String> ranges) {
        for (int i = 0; i < ranges.size(); i++) {
            for (int j = i + 1; j < ranges.size(); j++) {
                if (isOverlap(ranges.get(i), ranges.get(j))) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isOverlap(String range1, String range2) {
        String[] split1 = range1.split("-");
        String[] split2 = range2.split("-");

        if ((Long.parseLong(split2[0]) >= Long.parseLong(split1[0]) && Long.parseLong(split2[0]) < Long.parseLong(split1[1]))
                || (Long.parseLong(split2[1]) >= Long.parseLong(split1[0]) && Long.parseLong(split2[1]) < Long.parseLong(split1[1]))) {
            return true;
        }
        return false;
    }

}

