package com.shizhuang.scm.rulecenter.domains.inst.context;

import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
public class ParamInstCreateContext {

    /**
     * 参数实例新增cmd
     */
    ParamInstAddCmd paramInstAddCmd;

    /**
     * 参数实例Entity
     */
    List<ParamInstEntity> paramInstEntities;


    /**
     * 参数对应的方案聚合
     */
    SchemaMetaAggregate schemaAggregate;

    /**
     * 聚合id
     */
    String aggId;

    private String operatorName;

    private String operatorId;

}
