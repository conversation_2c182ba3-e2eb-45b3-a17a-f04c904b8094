package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;
import com.shizhuang.scm.rulecenter.api.service.ParamInstApplicationService;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstExportContext;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ParamInstExportQueryHandler implements IBasicHandler<ParamInstExportContext> {

    @Resource
    ParamInstApplicationService paramInstApplicationService;

    @Override
    public void doHandle(ParamInstExportContext paramInstExportContext) {
        InstPageQuery instPageQuery = paramInstExportContext.getInstPageQuery();
        Result<PageInfoResponse<InstDimPageResponse>> pageInfoResponseResult = paramInstApplicationService.queryParamInstPage(instPageQuery);
        PageInfoResponse<InstDimPageResponse> response = pageInfoResponseResult.getData();
        paramInstExportContext.setInstDimResponsePageInfo(response);
    }
}
