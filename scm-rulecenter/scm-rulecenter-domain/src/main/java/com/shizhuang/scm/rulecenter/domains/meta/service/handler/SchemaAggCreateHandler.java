package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.SchemaCreateCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaCreateContext;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

@Component
public class SchemaAggCreateHandler implements IBasicHandler<SchemaCreateContext> {

    @Override
    public void doHandle(SchemaCreateContext schemaCreateContext) {
        SchemaCreateCmd schemaCreateCmd = schemaCreateContext.getSchemaCreateCmd();
        SchemaMetaAggregate schemaMetaAggregate = SchemaMetaAggregate.init(schemaCreateCmd);
        schemaCreateContext.setSchemaMetaAggregate(schemaMetaAggregate);
    }

}
