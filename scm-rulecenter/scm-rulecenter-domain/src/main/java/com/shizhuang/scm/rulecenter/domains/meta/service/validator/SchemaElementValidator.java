package com.shizhuang.scm.rulecenter.domains.meta.service.validator;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.ELEMENT_NOT_MATCH_SCHEMA;

/**
 * 方案下元素明细校验器
 */
@Slf4j
@Component
public class SchemaElementValidator {

    @Resource
    SchemaRepository schemaRepository;

    /**
     * 校验元素与方案一致性是否正确
     *
     * @param schemaCode
     * @param elementCodes
     * @return
     */
    public ValidationError validateConsistency(String schemaCode, List<String> elementCodes) {
        log.info("SchemaElementValidator.validateConsistency param schemaCode: {} , elementCodes:{} ", schemaCode, elementCodes);
        for (String elementCode : elementCodes) {
            ElementMetaEntity schemaDetailByCode = schemaRepository.getCustomElementByCode(schemaCode, elementCode);
            if (Objects.isNull(schemaDetailByCode)) {
                String specificMsg = ELEMENT_NOT_MATCH_SCHEMA.getErrMsg() + " 方案code：" + schemaCode + " 元素code：" + elementCode;
                return ValidationErrorFactory.create(ELEMENT_NOT_MATCH_SCHEMA, specificMsg);
            }
        }
        return null;
    }
}
