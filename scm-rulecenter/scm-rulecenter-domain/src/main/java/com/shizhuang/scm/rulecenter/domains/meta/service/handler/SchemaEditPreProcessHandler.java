package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.SchemaEditCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaElementCreateCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 方案编辑预处理逻辑，查询对应的已经上线的聚合，以及未上线的但是是生效版本的聚合
 */
@Slf4j
@Component
public class SchemaEditPreProcessHandler implements IBasicHandler<SchemaEditContext> {

    @Resource
    SchemaRepository schemaRepository;

    @Override
    public void doHandle(SchemaEditContext schemaEditContext) {
        SchemaEditCmd schemaEditCmd = schemaEditContext.getSchemaEditCmd();
        String schemaCode = schemaEditCmd.getSchemaCode();
        SchemaMetaAggregate deployedSchemaAggregate = null;
        try {
            deployedSchemaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needDeployed());
        } catch (Exception e) {
            log.warn("deployedSchemaAggregate is null :{}", schemaCode);
        }
        schemaEditContext.setDeployedSchemaMetaAggregate(deployedSchemaAggregate);

        SchemaMetaAggregate noDeployLargestSchemaAggregate = null;
        try {
            noDeployLargestSchemaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.noDeployedLargestVersion());
        } catch (Exception e) {
            log.warn("noDeployLargestSchemaAggregate is null :{}", schemaCode);
        }
        schemaEditContext.setLargestNoDeploySchemaMetaAggregate(noDeployLargestSchemaAggregate);

        List<SchemaElementCreateCmd> dimension = schemaEditCmd.getDimension();
        List<SchemaElementCreateCmd> params = Optional.ofNullable(schemaEditCmd.getParams()).orElse(new ArrayList<>());

        List<SchemaElementEntity> exsitDimensionElements = null;
        List<SchemaElementEntity> exsitParamsElements = null;
        if(noDeployLargestSchemaAggregate!=null){
            exsitDimensionElements = noDeployLargestSchemaAggregate.getDimensions();
            exsitParamsElements = Optional.ofNullable(noDeployLargestSchemaAggregate.getParams()).orElse(new ArrayList<>());
        }else{
            exsitDimensionElements = deployedSchemaAggregate.getDimensions();
            exsitParamsElements = Optional.ofNullable(deployedSchemaAggregate.getParams()).orElse(new ArrayList<>());
        }
        List<String> exsitDimensionElementCodes = exsitDimensionElements.stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        List<String> exsitParamElementCodes = exsitParamsElements.stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());

        List<String> editDimensionElementCodes = dimension.stream().map(SchemaElementCreateCmd::getElementCode).collect(Collectors.toList());
        List<String> editParamElementCodes = params.stream().map(SchemaElementCreateCmd::getElementCode).collect(Collectors.toList());

        for(String s:exsitDimensionElementCodes){
            if(!editDimensionElementCodes.contains(s)){
                schemaEditContext.setHasDeleteElement(true);
            }
        }
        for(String s:exsitParamElementCodes){
            if(!editParamElementCodes.contains(s)){
                schemaEditContext.setHasDeleteElement(true);
            }
        }
        schemaEditContext.setHasDeleteElement(false);

    }
}
