package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class AcquireElementEnumsContext implements ApplicationContextAware {

    Map<Byte, AcquireValueRangeStrategy> strategyMap = new HashMap<>();

    public AcquireValueRangeStrategy select(Byte valueRangeTypeEnum){
        return strategyMap.get(valueRangeTypeEnum);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        strategyMap.put(ValueRangeTypeEnum.ENUM.getType(), applicationContext.getBean(AcquireValueRangeByEnum.class));
        strategyMap.put(ValueRangeTypeEnum.DUBBO_FULL.getType(), applicationContext.getBean(AcquireValueRangeByDubbo.class));
        strategyMap.put(ValueRangeTypeEnum.DUBBO_SEARCH.getType(), applicationContext.getBean(AcquireValueRangeByDubboSearch.class));
        strategyMap.put(ValueRangeTypeEnum.CUSTOM_TEXT.getType(), applicationContext.getBean(DefaultAcquireValueRange.class));
    }
}
