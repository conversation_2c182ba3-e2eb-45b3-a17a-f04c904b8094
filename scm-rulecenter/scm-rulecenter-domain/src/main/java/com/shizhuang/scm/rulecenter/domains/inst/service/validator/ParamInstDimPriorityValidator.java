package com.shizhuang.scm.rulecenter.domains.inst.service.validator;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.DIMENSION_PRIORITY_NOT_EXIST;

@Component
public class ParamInstDimPriorityValidator {

    @Resource
    SchemaRepository schemaRepository;

    //  todo 这里可以优化一下，把dimensionSet放到入参里，方便判断避免重复调用
    public ValidationError validate(String schemaCode, String dimension) {
        SchemaMetaAggregate schemaAggregateByCode = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLargestVersion());
        List<DimPriorityVO> dimPriority = schemaAggregateByCode.getDimPriority();
        // 如果方案下没有设置过优先级，就不存在优先级的问题
        if (CollectionUtils.isEmpty(dimPriority)) {
            return null;
        }
        // 如果方案下有设置过优先级，那么如果新增的参数维度没有被设置优先级则报错
        Set<String> dimensionSet = dimPriority.stream().map(DimPriorityVO::getDimension).collect(Collectors.toSet());
        if (!dimensionSet.contains(dimension)) {
            String specificMsg = String.format(DIMENSION_PRIORITY_NOT_EXIST.getErrMsg(), dimension);
            return ValidationErrorFactory.create(DIMENSION_PRIORITY_NOT_EXIST, specificMsg);
        }
        return null;
    }

}
