package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementValueRangeDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementValueRangeDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ElementValueRangeMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class AcquireValueRangeByEnum implements AcquireValueRangeStrategy{

    @Resource
    ElementValueRangeMapper elementValueRangeMapper;

    @Override
    public ElementMetaEntity cleanParam(ElementMetaEntity elementMetaEntity, Map<String,Object> content) {
        ElementMetaEntity incokeElementMetaEntity = new ElementMetaEntity();
        incokeElementMetaEntity.setSchemaCode(elementMetaEntity.getSchemaCode());
        incokeElementMetaEntity.setElementCode(elementMetaEntity.getElementCode());
        incokeElementMetaEntity.setValueRangeType(elementMetaEntity.getValueRangeType());
        return incokeElementMetaEntity;
    }

    @Override
    public List<ElementEnumVO> extractElementEnums(ElementMetaEntity elementMetaEntity) {

        String schemaCode = elementMetaEntity.getSchemaCode();
        String elementCode = elementMetaEntity.getElementCode();

        ElementValueRangeDOExample example = new ElementValueRangeDOExample();
        example.createCriteria()
                .andBelongToSchemaCodeEqualTo(schemaCode)
                .andElementCodeEqualTo(elementCode)
                .andIsDelEqualTo(CommonConstant.ZERO.byteValue());
        List<ElementValueRangeDO> elementValueRangeDOS = elementValueRangeMapper.selectByExample(example);
        List<ElementEnumVO> enums = elementValueRangeDOS.stream()
                .map(e -> new ElementEnumVO(e.getValue(),e.getDesc()))
                .collect(Collectors.toList());
        return enums;
    }
}
