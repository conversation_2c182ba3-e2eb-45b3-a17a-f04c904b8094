package com.shizhuang.scm.rulecenter.domains.meta.repository.impl;

import java.util.List;

import com.shizhuang.scm.rulecenter.api.constant.ElementSourceTypeEnum;
import com.shizhuang.scm.rulecenter.api.query.RedisKeyHelper;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.*;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaDimPriorityEditContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.CreatorVO;
import com.shizhuang.scm.rulecenter.infrastructure.cache.RedisCacheService;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.SchemaFieldMappingDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementValueRangeDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.SchemaDetailDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.DimensionPriorityDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.SchemaDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ElementDO;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.poizon.fusion.utils.DateUtils;
import com.shizhuang.scm.rulecenter.api.constant.ElementTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.SchemaOptLogEnum;
import com.shizhuang.scm.rulecenter.api.constant.SchemaStatusEnum;
import com.shizhuang.scm.rulecenter.api.dto.SchemaMetaDTO;
import com.shizhuang.scm.rulecenter.api.query.SchemaPageQuery;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.SchemaFieldMappingAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.transfer.*;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.spring.ApplicationContextHelper;
import com.shizhuang.scm.rulecenter.infrastructure.common.transaction.TransactionUtil;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.*;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.DATABASE_ERROR;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.SCHEMA_NOT_EXIST;

@Slf4j
@Repository
public class SchemaRepositoryImpl implements SchemaRepository {

    @Resource
    ElementMapper elementMapper;

    @Resource
    SchemaMapper schemaMapper;

    @Resource
    SchemaDetailMapper schemaDetailMapper;

    @Resource
    DimensionPriorityMapper dimensionPriorityMapper;

    @Resource
    SchemaOptLogMapper schemaOptLogMapper;

    @Resource
    SchemaFieldMappingMapper schemaFieldMappingMapper;

    @Resource
    TransactionUtil transactionUtil;

    @Resource
    ElementRepository elementRepository;

    @Resource
    ElementValueRangeMapper elementValueRangeMapper;
    @Resource
    RedisCacheService redisCacheService;

    @Override
    public void save(SchemaMetaAggregate schemaMetaAggregate) {
        SchemaDO schemaDO = SchemaTransfer.INSTANCE.dToS(schemaMetaAggregate.getSchemaEntity());
        String schemaCode = schemaDO.getSchemaCode();
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        schemaDO.setCreator(JSON.toJSONString(operationUser.getCreatorVO()));

        List<SchemaDetailDO> combineSchemaElementDOs = extractCombineSchemaDetailDOS(schemaMetaAggregate);
        List<FieldMappingVO> fieldMappings = schemaMetaAggregate.getFieldMappings();
        List<SchemaFieldMappingDO> schemaFieldMappingDOS = fieldMappings.stream().map(SchemaFieldMappingAssembler::toDO).collect(Collectors.toList());

        SchemaOptLogDO schemaOptLogDO = schemaCreateOptLog(schemaDO);

        try {
            transactionUtil.transaction(() -> {
                schemaMapper.insertSelective(schemaDO);
                schemaDetailMapper.insertBatch(combineSchemaElementDOs);
                schemaFieldMappingMapper.insertBatch(schemaFieldMappingDOS);
                schemaOptLogMapper.insertSelective(schemaOptLogDO);
            });
        } catch (Exception e) {
            log.error("SchemaRepository.save() database error schemaDO:{} combineSchemaElementDOs:{} schemaFieldMappingDOS:{} schemaOptLogDO:{}"
                    , schemaDO, JSON.toJSONString(combineSchemaElementDOs), JSON.toJSONString(schemaFieldMappingDOS), JSON.toJSONString(schemaOptLogDO), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }finally {
            removeSchemaCache(schemaCode);
        }
    }

    private static SchemaOptLogDO schemaCreateOptLog(SchemaDO schemaDO) {
        SchemaOptLogDO schemaOptLogDO = new SchemaOptLogDO();
        schemaOptLogDO.setSchemaCode(schemaDO.getSchemaCode());
        schemaOptLogDO.setOptUser(schemaDO.getCreator());
        schemaOptLogDO.setOptContent(SchemaOptLogEnum.CREATE.getDesc());
        schemaOptLogDO.setIsDel(CommonConstant.ZERO.byteValue());
        return schemaOptLogDO;
    }

    private static List<SchemaDetailDO> extractCombineSchemaDetailDOS(SchemaMetaAggregate schemaMetaAggregate) {
        List<SchemaDetailDO> dimensionElementDOs = extractSchemaDetailDOS(schemaMetaAggregate.getDimensions());

        List<SchemaDetailDO> paramElementDOs = extractSchemaDetailDOS(schemaMetaAggregate.getParams());

        List<SchemaDetailDO> combineSchemaElementDOs = Stream.concat(dimensionElementDOs.stream(), paramElementDOs.stream()).collect(Collectors.toList());
        //用于标记用户输入顺序
        int i = 1;
        ElementRepository elementRepository = ApplicationContextHelper.getBean(ElementRepository.class);
        for (SchemaDetailDO schemaDetailDO : combineSchemaElementDOs) {
            schemaDetailDO.setPriority(String.valueOf(i++));
            ElementMetaEntity elementMeta = elementRepository.getByCode(schemaDetailDO.getSchemaCode(), schemaDetailDO.getElementCode());
            schemaDetailDO.setElementName(elementMeta.getElementName());
        }
        return combineSchemaElementDOs;
    }

    private static List<SchemaDetailDO> extractSchemaDetailDOS(List<SchemaElementEntity> schemaMetaAggregate) {
        List<SchemaDetailDO> dimensionElementDOs = schemaMetaAggregate.stream().map(dim -> {
            SchemaDetailDO schemaDetailDO = SchemaElementTransfer.INSTANCE.dToS(dim);

            ElementUIAttributeVO uiAttributeVO = new ElementUIAttributeVO();
            uiAttributeVO.setQueryCondition(dim.getQueryCondition());
            uiAttributeVO.setIsRequired(dim.getIsRequired());
            uiAttributeVO.setIsMulti(dim.getIsMulti());
            schemaDetailDO.setUiAttributes(JSON.toJSONString(uiAttributeVO));
            return schemaDetailDO;
        }).collect(Collectors.toList());
        return dimensionElementDOs;
    }


    @Override
    public void deploySchema(SchemaMetaAggregate schemaMetaAggregate) {
        SchemaEntity schemaEntity = schemaMetaAggregate.getSchemaEntity();
        String schemaCode = schemaEntity.getSchemaCode();
        Integer version = schemaEntity.getVersion();

        SchemaDOExample oldOnlineSchemaDOExample = new SchemaDOExample();
        oldOnlineSchemaDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andStatusEqualTo(SchemaStatusEnum.ONLINE.getStatus().longValue()).andIsDelEqualTo(CommonConstant.ZERO.byteValue());
        SchemaDO oldOlineSchemaDO = new SchemaDO();
        oldOlineSchemaDO.setStatus(SchemaStatusEnum.OFFLINE.getStatus().longValue());
        oldOlineSchemaDO.setIsLatestVersion(CommonConstant.ZERO.byteValue());

        List<SchemaDO> deploySchemaDO = schemaMapper.selectByExample(oldOnlineSchemaDOExample);

        SchemaDOExample draftSchemaDOExample = new SchemaDOExample();
        draftSchemaDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andVersionEqualTo(version);
        SchemaDO draftSchemaDO = new SchemaDO();
        draftSchemaDO.setStatus(SchemaStatusEnum.ONLINE.getStatus().longValue());
        draftSchemaDO.setIsLatestVersion(CommonConstant.ONE.byteValue());

        SchemaOptLogDO schemaOptLogDO = schemaDeployOptLog(draftSchemaDO);

        try {
            transactionUtil.transaction(() -> {
                //将原有的线上版本下线
                if (CollectionUtils.isNotEmpty(deploySchemaDO)) {
                    schemaMapper.updateByExampleSelective(oldOlineSchemaDO, oldOnlineSchemaDOExample);
                }
                //将指定的编辑草稿上线
                schemaMapper.updateByExampleSelective(draftSchemaDO, draftSchemaDOExample);
                schemaOptLogMapper.insertSelective(schemaOptLogDO);
            });
        } catch (Exception e) {
            log.error("SchemaRepository.deploySchema database error deploySchemaDO:{} oldOlineSchemaDO:{} draftSchemaDO:{} schemaOptLogDO:{}"
                    , JSON.toJSONString(deploySchemaDO), JSON.toJSONString(oldOlineSchemaDO), JSON.toJSONString(draftSchemaDO), JSON.toJSONString(schemaOptLogDO), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        } finally {
            removeSchemaCache(schemaCode);
        }
    }

    @Override
    public void removeSchemaCache(String schemaCode){
        List<String> redisKeys = SchemaAggQueryOption.getRedisKeys();
        for(String key:redisKeys){
            String schemaDetailKey = RedisKeyHelper.getSchemaDetailKey(schemaCode, key);
            redisCacheService.removeCache(schemaDetailKey);
        }
    }

    private static SchemaOptLogDO schemaDeployOptLog(SchemaDO schemaDO) {
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        String userName = operationUser.getUserNameUnBlank();
        SchemaOptLogDO schemaOptLogDO = new SchemaOptLogDO();
        schemaOptLogDO.setSchemaCode(schemaDO.getSchemaCode());
        schemaOptLogDO.setOptUser(userName);
        schemaOptLogDO.setOptContent(SchemaOptLogEnum.DEPLOY.getDesc());
        schemaOptLogDO.setIsDel(CommonConstant.ZERO.byteValue());
        return schemaOptLogDO;
    }

    @Override
    public void save(SchemaDimPriorityEditContext schemaDimPriorityEditContext) {
        List<DimPriorityVO> dimPriorityVOList = schemaDimPriorityEditContext.getDimPriorityVOList();
        List<DimensionPriorityDO> sourceList = DimPriorityTransfer.INSTANCE.toSourceList(dimPriorityVOList);
        String schemaCode = dimPriorityVOList.get(0).getSchemaCode();
        DimensionPriorityDOExample dimensionPriorityDOExample = new DimensionPriorityDOExample();
        dimensionPriorityDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        DimensionPriorityDO dimensionPriorityDO = new DimensionPriorityDO();
        dimensionPriorityDO.setIsDel(CommonConstant.ONE.byteValue());
        String invalidSchemaCode = "DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode;
        dimensionPriorityDO.setSchemaCode(invalidSchemaCode);


        SchemaEntity schemaEntity = schemaDimPriorityEditContext.getSchemaMetaAggregate().getSchemaEntity();
        DimPriTypeVO dimPriTypeVO = schemaEntity.getDimPriTypeVO();
        dimPriTypeVO.setDimPriType(schemaDimPriorityEditContext.getDimPriType());
        dimPriTypeVO.setOrderField(schemaDimPriorityEditContext.getOrderField());
        dimPriTypeVO.setOrderType(schemaDimPriorityEditContext.getOrderType());
        schemaEntity.setDimPriTypeVO(dimPriTypeVO);
        SchemaTransfer.INSTANCE.dToS(schemaEntity);

        try {
            transactionUtil.transaction(() -> {
                dimensionPriorityMapper.updateByExampleSelective(dimensionPriorityDO, dimensionPriorityDOExample);
                dimensionPriorityMapper.insertBatch(sourceList);
            });
        } catch (Exception e) {
            log.error("SchemaRepository.deploySchema database error dimensionPriorityDO:{} sourceList:{}"
                    , JSON.toJSONString(dimensionPriorityDO), JSON.toJSONString(sourceList), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }finally {
            removeSchemaCache(schemaCode);
        }


    }

    @Override
    public List<DimPriorityVO> querySchemaDimPriority(String schemaCode) {
        DimensionPriorityDOExample dimensionPriorityDOExample = new DimensionPriorityDOExample();
        dimensionPriorityDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        List<DimensionPriorityDO> dimensionPriorityDOS = dimensionPriorityMapper.selectByExample(dimensionPriorityDOExample);
        List<DimPriorityVO> targetList = DimPriorityTransfer.INSTANCE.toTargetList(dimensionPriorityDOS);
        if (CollectionUtils.isEmpty(targetList)) {
            return new ArrayList<>();
        }
        targetList.forEach(t -> {
            String dimension = t.getDimension();
            t.setAtomicDimension(Lists.newArrayList(dimension.split("#")));
        });
        return targetList;
    }

    @Override
    public SchemaEntity getSchemaEntityByCode(String schemaCode, SchemaAggQueryOption option) {
        SchemaDOExample schemaDOExample = new SchemaDOExample();
        SchemaDOExample.Criteria criteria = schemaDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andIsDelEqualTo(CommonConstant.ZERO.byteValue());
        if (option.needLatestVersion == CommonConstant.ONE.byteValue()) {
            criteria.andIsLatestVersionEqualTo(CommonConstant.ONE.byteValue());
        } else if (option.needDeployed == CommonConstant.ONE.byteValue()) {
            criteria.andIsLatestVersionEqualTo(CommonConstant.ONE.byteValue());
            criteria.andStatusEqualTo(SchemaStatusEnum.ONLINE.getStatus().longValue());
        } else if (option.noDeployedLargest == CommonConstant.ONE.byteValue()) {
            criteria.andIsLargestVersionEqualTo(CommonConstant.ONE.byteValue());
            criteria.andStatusEqualTo(SchemaStatusEnum.DRAFT.getStatus().longValue());
        } else {
            criteria.andIsLargestVersionEqualTo(CommonConstant.ONE.byteValue());
        }

        List<SchemaDO> schemaDOS = schemaMapper.selectByExample(schemaDOExample);
        if (CollectionUtils.isEmpty(schemaDOS)) {
            return null;
        }
        SchemaDO schemaDO = schemaDOS.get(0);
        return SchemaTransfer.INSTANCE.sToT(schemaDO);
    }


    @Override
    public ElementMetaEntity getCustomElementByCode(String schemaCode, String elementCode) {
        ElementDOExample elementDOExample = new ElementDOExample();
        elementDOExample.createCriteria().andBelongToSchemaCodeEqualTo(schemaCode).andElementCodeEqualTo(elementCode);
        List<ElementDO> elementDOS = elementMapper.selectByExample(elementDOExample);
        if (CollectionUtils.isEmpty(elementDOS)) {
            return null;
        }
        return ElementTransfer.INSTANCE.sToT(elementDOS.get(0));
    }


    @Override
    public SchemaMetaAggregate getSchemaAggregateByCode(String schemaCode, SchemaAggQueryOption option) {

        String schemaDetailKey = RedisKeyHelper.getSchemaDetailKey(schemaCode, option.getRedisKey());
        SchemaMetaAggregate schemaMetaAggregate = (SchemaMetaAggregate) redisCacheService.getUnCheck(schemaDetailKey);
        if (Objects.nonNull(schemaMetaAggregate)) {
            return schemaMetaAggregate;
        }
        schemaMetaAggregate = new SchemaMetaAggregate();
        SchemaEntity schemaEntity = getSchemaEntityByCode(schemaCode, option);
        // 若查不到聚合直接抛出异常，理论上这个接口的使用场景，schemaCode 不应该有不存在的方案
        if (Objects.isNull(schemaEntity)) {
            throw new BizRuntimeException(SCHEMA_NOT_EXIST.getCode(), String.format(SCHEMA_NOT_EXIST.getErrMsg(), schemaCode));
        }

        SchemaDetailDOExample schemaDetailDOExample = new SchemaDetailDOExample();
        schemaDetailDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andVersionEqualTo(schemaEntity.getVersion())
                .andIsDelEqualTo(CommonConstant.ZERO.byteValue());
        schemaDetailDOExample.setOrderByClause(" priority asc ");
        List<SchemaDetailDO> schemaDetailDOS = schemaDetailMapper.selectByExample(schemaDetailDOExample);
        List<SchemaElementEntity> schemaElementEntities = SchemaElementTransfer.INSTANCE.toTargetList(schemaDetailDOS);
        schemaElementEntities.forEach(e -> {
            ElementMetaEntity elementMeta = elementRepository.getByCodeSimple(schemaCode, e.getElementCode());
            e.setElementSourceType(elementMeta.getElementSourceType());
            e.setElementNote(elementMeta.getElementNote());
            e.setValueRangeType(elementMeta.getValueRangeType());
            e.setFrontComponent(elementMeta.getFrontComponent());
        });
        schemaElementEntities = schemaElementEntities.stream().sorted(Comparator.comparing(e -> Integer.valueOf(e.getPriority()))).collect(Collectors.toList());
        List<SchemaElementEntity> dimensions = schemaElementEntities.stream().filter(e -> ElementTypeEnum.DIMENSION.getType().equals(e.getElementType())).collect(Collectors.toList());
        List<SchemaElementEntity> params = schemaElementEntities.stream().filter(e -> ElementTypeEnum.PARAM.getType().equals(e.getElementType())).collect(Collectors.toList());

        DimensionPriorityDOExample dimensionPriorityDOExample = new DimensionPriorityDOExample();
        dimensionPriorityDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andIsDelEqualTo(CommonConstant.ZERO.byteValue());
        List<DimensionPriorityDO> dimensionPriorityDOS = dimensionPriorityMapper.selectByExample(dimensionPriorityDOExample);
        List<DimPriorityVO> dimPriorityVOS = Optional.ofNullable(DimPriorityTransfer.INSTANCE.toTargetList(dimensionPriorityDOS)).orElseGet(ArrayList::new);
        if (CollectionUtils.isNotEmpty(dimPriorityVOS)) {
            dimPriorityVOS = dimPriorityVOS.stream().sorted(Comparator.comparing(DimPriorityVO::getPriority)).collect(Collectors.toList());
        }

        List<FieldMappingVO> fieldMappingVOS = getFieldMappingVOS(schemaCode, schemaEntity.getVersion());

        schemaMetaAggregate.setSchemaEntity(schemaEntity);
        schemaMetaAggregate.setDimensions(dimensions);
        schemaMetaAggregate.setParams(params);
        schemaMetaAggregate.setDimPriority(dimPriorityVOS);
        schemaMetaAggregate.setFieldMappings(fieldMappingVOS);
        redisCacheService.putToValue(schemaDetailKey, schemaMetaAggregate, 24L, TimeUnit.HOURS);
        return schemaMetaAggregate;
    }

    public List<FieldMappingVO> getFieldMappingVOS(String schemaCode, Integer version) {
        SchemaFieldMappingDOExample schemaFieldMappingDOExample = new SchemaFieldMappingDOExample();
        schemaFieldMappingDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andVersionEqualTo(version);
        List<SchemaFieldMappingDO> schemaFieldMappingDOS = schemaFieldMappingMapper.selectByExample(schemaFieldMappingDOExample);
        return SchemaFieldTransfer.INSTANCE.toTargetList(schemaFieldMappingDOS);
    }

    @Override
    public PageInfo<SchemaMetaDTO> queryByPage(SchemaPageQuery schemaPageQuery) {
        Integer pageNum = schemaPageQuery.getPageNum();
        Integer pageSize = schemaPageQuery.getPageSize();
        String schemaCode = schemaPageQuery.getSchemaCode();
        String schemaName = schemaPageQuery.getSchemaName();
        String creator = schemaPageQuery.getCreator();
        String projectName = schemaPageQuery.getProjectName();
        SchemaDOExample schemaDOExample = new SchemaDOExample();
        schemaDOExample.setOrderByClause(" modify_time desc ");
        SchemaDOExample.Criteria criteria = schemaDOExample.createCriteria();
        if (StringUtils.isNotBlank(schemaCode)) {
            criteria.andSchemaCodeEqualTo(schemaCode);
        }
        if (StringUtils.isNotBlank(schemaName)) {
            criteria.andSchemaNameLike(schemaName);
        }
        if (StringUtils.isNotBlank(creator)) {
            criteria.andCreatorLike(creator);
        }
        if (StringUtils.isNotBlank(projectName)) {
            criteria.andProjectNameEqualTo(projectName);
        }
        criteria.andIsDelEqualTo(CommonConstant.IS_NOT_DELETE);
        criteria.andIsLargestVersionEqualTo(CommonConstant.ONE.byteValue());
        Page<SchemaDO> schemaDOPage = PageHelper.startPage(pageNum, pageSize).doSelectPage(() -> schemaMapper.selectByExample(schemaDOExample));

        List<SchemaDO> result = schemaDOPage.getResult();
        if (CollectionUtils.isEmpty(result)) {
            PageInfo<SchemaMetaDTO> emptyPage = PageInfo.of(new ArrayList<>());
            emptyPage.setPageNum(pageNum);
            emptyPage.setPageSize(pageSize);
            return emptyPage;
        }
        List<SchemaMetaDTO> schemaMetaDTOS = result.stream().map(SchemaRepositoryImpl::assemble).collect(Collectors.toList());
        PageInfo<SchemaMetaDTO> elementMetaDTOPageInfo = PageInfo.of(schemaMetaDTOS);
        elementMetaDTOPageInfo.setPageNum(pageNum);
        elementMetaDTOPageInfo.setPageSize(pageSize);
        elementMetaDTOPageInfo.setTotal(schemaDOPage.getTotal());
        return elementMetaDTOPageInfo;
    }

    private static SchemaMetaDTO assemble(SchemaDO schemaDO) {
        SchemaMetaDTO schemaMetaDTO = new SchemaMetaDTO();
        schemaMetaDTO.setSchemaCode(schemaDO.getSchemaCode());
        schemaMetaDTO.setSchemaName(schemaDO.getSchemaName());
        schemaMetaDTO.setSchemaDesc(schemaDO.getSchemaDesc());
        if (StringUtils.isNotBlank(schemaDO.getCreator())) {
            CreatorVO creatorVO = JSON.parseObject(schemaDO.getCreator(), CreatorVO.class);
            schemaMetaDTO.setCreator(creatorVO.getUserName());
        }
        schemaMetaDTO.setStatus(schemaDO.getStatus().intValue());
        schemaMetaDTO.setStatusName(SchemaStatusEnum.of(schemaDO.getStatus().intValue()).getDesc());
        schemaMetaDTO.setCreateTime(DateUtils.parseDateToStr(schemaDO.getCreateTime()));
        schemaMetaDTO.setModifyTime(DateUtils.parseDateToStr(schemaDO.getModifyTime()));
        schemaMetaDTO.setMenu(schemaDO.getMenu());
        return schemaMetaDTO;
    }


    /**
     * 更新编辑方案
     *
     * @param editschemaMetaAggregate
     */
    @Override
    public void update(SchemaMetaAggregate editschemaMetaAggregate, SchemaMetaAggregate oldSchemaMetaAggregate) {
        SchemaEntity schemaEntity = editschemaMetaAggregate.getSchemaEntity();
        String schemaCode = schemaEntity.getSchemaCode();
        Integer version = schemaEntity.getVersion();

        SchemaDO schemaDO = SchemaTransfer.INSTANCE.dToS(editschemaMetaAggregate.getSchemaEntity());
        List<SchemaDetailDO> combineSchemaElementDOs = extractCombineSchemaDetailDOS(editschemaMetaAggregate);
        List<FieldMappingVO> fieldMappings = editschemaMetaAggregate.getFieldMappings();
        List<SchemaFieldMappingDO> schemaFieldMappingDOS = fieldMappings.stream().map(SchemaFieldMappingAssembler::toDO).collect(Collectors.toList());

        String invalidSchemaCode = "DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode;

        SchemaDOExample invalidSchemaDOExample = new SchemaDOExample();
        invalidSchemaDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andVersionEqualTo(version);
        SchemaDO invalidSchemaDO = new SchemaDO();
        invalidSchemaDO.setSchemaCode(invalidSchemaCode);
        invalidSchemaDO.setIsDel(CommonConstant.IS_DELETE);

        SchemaDetailDOExample invalidSchemaDetailDOExample = new SchemaDetailDOExample();
        invalidSchemaDetailDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andVersionEqualTo(version);
        SchemaDetailDO invalidSchemaDetailDO = new SchemaDetailDO();
        invalidSchemaDetailDO.setSchemaCode(invalidSchemaCode);
        invalidSchemaDetailDO.setIsDel(CommonConstant.IS_DELETE);

        SchemaFieldMappingDOExample invalidSchemaFieldMappingDOExample = new SchemaFieldMappingDOExample();
        invalidSchemaFieldMappingDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andVersionEqualTo(version);
        SchemaFieldMappingDO schemaFieldMappingDO = new SchemaFieldMappingDO();
        schemaFieldMappingDO.setSchemaCode(invalidSchemaCode);
        schemaFieldMappingDO.setIsDel(CommonConstant.IS_DELETE);

        List<SchemaOptLogDO> schemaOptLogDOS = editschemaMetaAggregate.getAddSchemaOptLogVOs().stream().map(log -> {
            SchemaOptLogDO schemaOptLogDO = new SchemaOptLogDO();
            schemaOptLogDO.setOptUser(log.getModifier());
            List<CompareIssueVO> compareIssueVOList = log.getCompareIssueVOList();
            String optContent = compareIssueVOList.stream().map(CompareIssueVO::toLog).collect(Collectors.joining(";"));
            schemaOptLogDO.setOptContent(optContent);
            return schemaOptLogDO;
        }).collect(Collectors.toList());

        try {
            transactionUtil.transaction(() -> {
                //如果有线上数据，则更新线上数据的isLargestVersion为0
                if (Objects.nonNull(oldSchemaMetaAggregate)) {
                    SchemaDOExample onlineSchemaDOExample = new SchemaDOExample();
                    onlineSchemaDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode).andVersionEqualTo(oldSchemaMetaAggregate.getSchemaEntity().getVersion());
                    SchemaDO onlineSchemaDO = new SchemaDO();
                    onlineSchemaDO.setIsLargestVersion(CommonConstant.ZERO.byteValue());
                    schemaMapper.updateByExampleSelective(onlineSchemaDO, onlineSchemaDOExample);
                }

                //将原有的编辑版本数据失效
                schemaMapper.updateByExampleSelective(invalidSchemaDO, invalidSchemaDOExample);
                schemaDetailMapper.updateByExampleSelective(invalidSchemaDetailDO, invalidSchemaDetailDOExample);
                schemaFieldMappingMapper.updateByExampleSelective(schemaFieldMappingDO, invalidSchemaFieldMappingDOExample);

                //保存用户编辑的聚合数据
                schemaMapper.insertSelective(schemaDO);
                schemaDetailMapper.insertBatch(combineSchemaElementDOs);
                schemaFieldMappingMapper.insertBatch(schemaFieldMappingDOS);
                schemaOptLogDOS.forEach(e -> schemaOptLogMapper.insertSelective(e));
            });
        } catch (Exception e) {
            log.error("SchemaRepository.update database error schemaDO:{} combineSchemaElementDOs:{} schemaFieldMappingDOS:{} schemaOptLogDOS:{}"
                    , JSON.toJSONString(schemaDO), JSON.toJSONString(combineSchemaElementDOs), JSON.toJSONString(schemaFieldMappingDOS), JSON.toJSONString(schemaOptLogDOS), e);
            throw new BizRuntimeException(DATABASE_ERROR.getCode(), DATABASE_ERROR.getErrMsg());
        }finally {
            removeSchemaCache(schemaCode);
        }
    }

    @Override
    public int deleteSchema(String schemaCode) {
        SchemaDOExample schemaDOExample = new SchemaDOExample();
        schemaDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        SchemaDO schemaDO = new SchemaDO();
        schemaDO.setSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode);
        schemaDO.setIsDel(CommonConstant.ONE.byteValue());
        int i = schemaMapper.updateByExampleSelective(schemaDO, schemaDOExample);

        SchemaDetailDOExample schemaDetailDOExample = new SchemaDetailDOExample();
        schemaDetailDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        SchemaDetailDO schemaDetailDO = new SchemaDetailDO();
        schemaDetailDO.setIsDel(CommonConstant.ONE.byteValue());
        schemaDetailDO.setSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode);
        schemaDetailMapper.updateByExampleSelective(schemaDetailDO, schemaDetailDOExample);

        SchemaFieldMappingDOExample schemaFieldMappingDOExample = new SchemaFieldMappingDOExample();
        schemaFieldMappingDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        SchemaFieldMappingDO schemaFieldMappingDO = new SchemaFieldMappingDO();
        schemaFieldMappingDO.setIsDel(CommonConstant.ONE.byteValue());
        schemaFieldMappingDO.setSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode);
        schemaFieldMappingMapper.updateByExampleSelective(schemaFieldMappingDO, schemaFieldMappingDOExample);
        removeSchemaCache(schemaCode);
        return i;
    }

    @Override
    public List<String> getRelatedSchemaByElement(String belongToSchemaCode, String elementCode) {
        SchemaDetailDOExample schemaDetailDOExample = new SchemaDetailDOExample();
        SchemaDetailDOExample.Criteria criteria = schemaDetailDOExample.createCriteria()
                .andElementCodeEqualTo(elementCode).andIsDelEqualTo(CommonConstant.ZERO.byteValue());
        if (!CommonConstant.COMMON.equals(belongToSchemaCode)) {
            criteria.andSchemaCodeEqualTo(belongToSchemaCode);
        }
        List<SchemaDetailDO> schemaDetailDOS = schemaDetailMapper.selectByExample(schemaDetailDOExample);
        if (CollectionUtils.isNotEmpty(schemaDetailDOS)) {
            return schemaDetailDOS.stream().map(SchemaDetailDO::getSchemaCode).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public void override(EntireSchemaVO entireSchemaVO) {
        SchemaDO schemaDO = entireSchemaVO.getSchemaDO();
        String schemaCode = schemaDO.getSchemaCode();
        String invalidSchemaCode = "DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode;

        SchemaDOExample invalidSchemaDOExample = new SchemaDOExample();
        invalidSchemaDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        SchemaDO invalidSchemaDO = new SchemaDO();
        invalidSchemaDO.setSchemaCode(invalidSchemaCode);
        invalidSchemaDO.setIsDel(CommonConstant.IS_DELETE);


        List<SchemaDetailDO> schemaDetailDOS = entireSchemaVO.getSchemaDetailDOS();
        SchemaDetailDOExample invalidSchemaDetailDOExample = new SchemaDetailDOExample();
        invalidSchemaDetailDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        SchemaDetailDO invalidSchemaDetailDO = new SchemaDetailDO();
        invalidSchemaDetailDO.setSchemaCode(invalidSchemaCode);
        invalidSchemaDetailDO.setIsDel(CommonConstant.IS_DELETE);


        List<SchemaFieldMappingDO> schemaFieldMappingDOS = entireSchemaVO.getSchemaFieldMappingDOS();
        SchemaFieldMappingDOExample schemaFieldMappingDOExample = new SchemaFieldMappingDOExample();
        schemaFieldMappingDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        SchemaFieldMappingDO invalidSchemaFieldMappingDO = new SchemaFieldMappingDO();
        invalidSchemaFieldMappingDO.setSchemaCode(invalidSchemaCode);
        invalidSchemaFieldMappingDO.setIsDel(CommonConstant.IS_DELETE);


        List<DimensionPriorityDO> dimensionPriorityDOS = entireSchemaVO.getDimensionPriorityDOS();
        DimensionPriorityDOExample dimensionPriorityDOExample = new DimensionPriorityDOExample();
        dimensionPriorityDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode);
        DimensionPriorityDO invalidDimensionPriorityDO = new DimensionPriorityDO();
        invalidDimensionPriorityDO.setSchemaCode(invalidSchemaCode);
        invalidDimensionPriorityDO.setIsDel(CommonConstant.IS_DELETE);

        List<ElementDO> elementDOS = entireSchemaVO.getElementDOS();

        transactionUtil.transaction(() -> {
            schemaMapper.updateByExampleSelective(invalidSchemaDO, invalidSchemaDOExample);
            schemaMapper.insertSelective(schemaDO);
            schemaDetailMapper.updateByExampleSelective(invalidSchemaDetailDO, invalidSchemaDetailDOExample);
            schemaDetailMapper.insertBatch(schemaDetailDOS);
            schemaFieldMappingMapper.updateByExampleSelective(invalidSchemaFieldMappingDO, schemaFieldMappingDOExample);
            schemaFieldMappingMapper.insertBatch(schemaFieldMappingDOS);
            dimensionPriorityMapper.updateByExampleSelective(invalidDimensionPriorityDO, dimensionPriorityDOExample);
            if (CollectionUtils.isNotEmpty(dimensionPriorityDOS)) {
                dimensionPriorityMapper.insertBatch(dimensionPriorityDOS);
            }
            elementDOS.forEach(elementDO -> {
                ElementDOExample elementDOExample = new ElementDOExample();
                ElementDOExample.Criteria criteria = elementDOExample.createCriteria();
                criteria.andElementCodeEqualTo(elementDO.getElementCode()).andBelongToSchemaCodeEqualTo(elementDO.getBelongToSchemaCode());
                ElementDO invalidElementDO = new ElementDO();
                invalidElementDO.setElementCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + elementDO.getElementCode());
                invalidElementDO.setIsDel(CommonConstant.IS_DELETE);
                elementMapper.updateByExampleSelective(invalidElementDO, elementDOExample);
            });
            elementDOS.forEach(elementDO -> elementMapper.insertSelective(elementDO));

            List<ElementValueRangeDO> elementValueRangeDOS = entireSchemaVO.getElementValueRangeDOS();
            elementValueRangeDOS.stream().map(ElementValueRangeDO::getElementCode).distinct().forEach(elementCode -> {
                ElementValueRangeDOExample elementValueRangeDOExample = new ElementValueRangeDOExample();
                elementValueRangeDOExample.createCriteria().andElementCodeEqualTo(elementCode).andBelongToSchemaCodeEqualTo(schemaCode);
                ElementValueRangeDO invalidElementValueRangeDO = new ElementValueRangeDO();
                invalidElementValueRangeDO.setElementCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + elementCode);
                invalidElementValueRangeDO.setIsDel(CommonConstant.IS_DELETE);
                elementValueRangeMapper.updateByExampleSelective(invalidElementValueRangeDO, elementValueRangeDOExample);
            });
            elementValueRangeDOS.forEach(elementValueRangeDO -> elementValueRangeMapper.insertSelective(elementValueRangeDO));

        });
    }
}
