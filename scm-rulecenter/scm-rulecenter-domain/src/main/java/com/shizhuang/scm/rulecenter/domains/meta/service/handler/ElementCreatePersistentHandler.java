package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.domains.meta.context.ElementCreateContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ElementCreatePersistentHandler implements IBasicHandler<ElementCreateContext> {

    @Resource
    ElementRepository elementRepository;

    @Override
    public void doHandle(ElementCreateContext context) {
        elementRepository.save(context.getElementMetaEntity());
    }
}
