package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.dewu.scm.lms.api.report.dto.request.ReportPageRequest;
import com.dewu.scm.lms.api.report.dto.response.ReportPageResponse;
import com.github.pagehelper.PageInfo;
import com.shizhuang.scm.rulecenter.api.constant.SystemFieldEnum;
import com.shizhuang.scm.rulecenter.api.dto.TableHeadDTO;
import com.shizhuang.scm.rulecenter.api.query.ParamInstExportRequest;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstExportContext;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ParamInstExportExcelHandler implements IBasicHandler<ParamInstExportContext> {

    @Override
    public void doHandle(ParamInstExportContext paramInstExportContext) {
        ReportPageRequest<ParamInstExportRequest> pageRequest = paramInstExportContext.getPageRequest();
        int pageNum = pageRequest.getPageNum();
        int pageSize = pageRequest.getPageSize();
        PageInfoResponse<InstDimPageResponse> data = paramInstExportContext.getInstDimResponsePageInfo();
        List<InstDimPageResponse> responseList = data.getList();
        long total = data.getTotal();
        SchemaMetaAggregate schemaAggregate = paramInstExportContext.getSchemaMetaAggregate();
        List<SchemaElementEntity> allElements = schemaAggregate.getAllElements();
        LinkedHashMap<String, String> header = getTableHead(allElements);
        List<Map<String, Object>> content = new ArrayList<>();
        for (InstDimPageResponse instDimPageResponse : responseList) {
            Map<String, Object> elementDescMap = instDimPageResponse.getElementDescMap();
            Map<String, Object> finalMap = header.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> {
                Object desc = elementDescMap.get(e.getKey());
                if (desc instanceof List) {
                    return String.join(",", (List) desc);
                } else if (desc instanceof Set) {
                    return String.join(",", (Set) desc);
                }
                return Optional.ofNullable(desc).orElse("");
            }, (o, n) -> n));
            compatible(schemaAggregate, finalMap);
            content.add(finalMap);
        }

        ReportPageResponse response = new ReportPageResponse();
        response.setContents(content);
        response.setHeader(header);
        response.setPageNum(pageNum);
        response.setPageSize(pageSize);
        response.setTotal(total);
        paramInstExportContext.setReportPageResponse(response);
    }

    private static void compatible(SchemaMetaAggregate schemaAggregate, Map<String, Object> finalMap) {
        if (Objects.equals(schemaAggregate.getSchemaEntity().getSchemaCode(), "Identify-Last-Gray-Page-NEW")) {
            if (!finalMap.containsKey("firstCategoryId") || StringUtils.isBlank(String.valueOf(finalMap.get("firstCategoryId")))) {
                finalMap.put("firstCategoryId", "全部");
            }
            if (Objects.equals("-1", finalMap.get("grayRatio"))) {
                finalMap.put("grayRatio", "全量");
            }
        }
    }

    private static LinkedHashMap<String, String> getTableHead(List<SchemaElementEntity> allElements) {
        LinkedHashMap<String, String> header = allElements.stream()
                .sorted(Comparator.comparing(SchemaElementEntity::getPriority))
                .collect(Collectors.toMap(SchemaElementEntity::getElementCode, SchemaElementEntity::getElementName, (o, n) -> n, LinkedHashMap::new));
        header.put("enabled", "是否生效");
        header.put("instCreator", "创建人");
        header.put("instCreateTime", "创建时间");
        header.put("lastModifier", "最后修改人");
        header.put("lastModifyTime", "最后修改时间");
        return header;
    }
}
