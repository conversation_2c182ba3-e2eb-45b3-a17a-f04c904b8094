package com.shizhuang.scm.rulecenter.domains.meta.repository;

import com.github.pagehelper.PageInfo;
import com.shizhuang.scm.rulecenter.api.dto.ElementMetaDTO;
import com.shizhuang.scm.rulecenter.api.query.ElementPageQuery;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;

import java.util.Map;

/**
 *  元素Repository
 */
public interface ElementRepository {

    int save(ElementMetaEntity elementMetaEntity);

    int update(ElementMetaEntity elementMetaEntity);

    int delete(String schemaCode,String elementCode);

    ElementMetaEntity getByCode(String schemaCode,String elementCode);
    ElementMetaEntity getByCodeWithEnum(String schemaCode, String elementCode, Map<String, Object> extParam);

    ElementMetaEntity getByCodeSimple(String schemaCode, String elementCode);

    Boolean existElement(String schemaCode, String elementCode);
    PageInfo<ElementMetaDTO> queryByPage(ElementPageQuery elementPageQuery);


}
