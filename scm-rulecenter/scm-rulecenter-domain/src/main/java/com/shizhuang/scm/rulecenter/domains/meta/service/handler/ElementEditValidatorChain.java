package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ElementEditCmd;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.DubboInterfaceValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.OptUserValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.ValueRepeatableValidator;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 校验
 */
@Component
public class ElementEditValidatorChain implements IBasicHandler<ElementEditContext>, ApplicationContextAware {


    ValidatorChain validatorChain;

    ApplicationContext applicationContext;


    @Override
    public void doHandle(ElementEditContext elementEditContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(elementEditContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、枚举类型里的值域 值或者值描述不能有相同的情况，比如品牌id不能有重复，品牌名称不能有重复
                ValidatorHandlerFactory.build(ctx -> acceptEnumElement((ElementEditContext) ctx)
                        , ctx -> validateValueRepeatable((ElementEditContext) ctx)),

                // 2、如果是dubbo查询接口类型，需要做基本的字段判空校验
                ValidatorHandlerFactory.build(ctx -> acceptDubboElement((ElementEditContext) ctx)
                        , ctx -> validateDubboInterface((ElementEditContext) ctx)),

                // 3、需要确定检查如果是dubbo单点查询，需要强制设置有且仅有一个#{var}的变量
                ValidatorHandlerFactory.build(ctx -> acceptSearchDubboElement((ElementEditContext) ctx)
                        , (ctx) -> validateSearchDubboElement((ElementEditContext) ctx))

                // 4、元素编辑人必须是元素创建人或者白名单的人
                //ValidatorHandlerFactory.build(ctx -> validateOptUserIllegal((ElementEditContext) ctx))


        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }

    private ValidationError validateOptUserIllegal(ElementEditContext ctx) {
        OptUserValidator validator = applicationContext.getBean(OptUserValidator.class);
        ElementMetaEntity editElementMetaEntity = ctx.getEditElementMetaEntity();
        String creator = editElementMetaEntity.getCreator();
        return validator.validate(creator);
    }


    private Boolean acceptEnumElement(ElementEditContext edc) {
        ValueRepeatableValidator valueRepeatableValidator = applicationContext.getBean(ValueRepeatableValidator.class);
        ElementEditCmd elementEditCmd = edc.getElementEditCmd();
        return valueRepeatableValidator.accept(elementEditCmd.getValueRangeType());
    }

    private ValidationError validateValueRepeatable(ElementEditContext edc) {
        ValueRepeatableValidator valueRepeatableValidator = applicationContext.getBean(ValueRepeatableValidator.class);
        ElementEditCmd elementCreateCmd = edc.getElementEditCmd();
        return valueRepeatableValidator.validate(elementCreateCmd.getEnums());
    }

    private Boolean acceptDubboElement(ElementEditContext edc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        ElementEditCmd elementEditCmd = edc.getElementEditCmd();
        return dubboInterfaceValidator.acceptBaseDubboInterface(elementEditCmd.getValueRangeType());
    }

    private ValidationError validateDubboInterface(ElementEditContext edc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        ElementEditCmd elementEditCmd = edc.getElementEditCmd();
        Byte valueRangeType = elementEditCmd.getValueRangeType();
        ElementInterfaceDTO fullInterface = elementEditCmd.getFullInterface();
        ElementInterfaceDTO valueToDescInterface = elementEditCmd.getValueToDescInterface();
        ElementInterfaceDTO descToValueInterface = elementEditCmd.getDescToValueInterface();
        return dubboInterfaceValidator.validate(valueRangeType, fullInterface, valueToDescInterface, descToValueInterface);
    }


    private Boolean acceptSearchDubboElement(ElementEditContext edc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        ElementEditCmd elementEditCmd = edc.getElementEditCmd();
        return dubboInterfaceValidator.acceptSearchDubboInterface(elementEditCmd.getValueRangeType());
    }

    private ValidationError validateSearchDubboElement(ElementEditContext edc) {
        DubboInterfaceValidator dubboInterfaceValidator = applicationContext.getBean(DubboInterfaceValidator.class);
        ElementEditCmd elementEditCmd = edc.getElementEditCmd();
        return dubboInterfaceValidator.validateSearchDubboInterface(elementEditCmd.getValueToDescInterface(), elementEditCmd.getDescToValueInterface());
    }

}
