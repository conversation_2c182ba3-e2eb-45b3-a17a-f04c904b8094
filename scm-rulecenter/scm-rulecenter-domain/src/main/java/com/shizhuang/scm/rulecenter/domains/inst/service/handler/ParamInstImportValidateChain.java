package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.dewu.scm.lms.api.report.dto.response.ImportExcelResponse;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ParamInstImportExcelVO;
import com.shizhuang.scm.rulecenter.domains.inst.assembler.ParamInstAssembler;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstImportContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstDimPriorityValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstDimensionValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstElementValueValidator;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ParamInstImportValidateChain implements IBasicHandler<ParamInstImportContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(ParamInstImportContext paramInstImportContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(paramInstImportContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、批量校验当前的导入文案是否与方案中指定的一致
                ValidatorHandlerFactory.build(ctx -> batchValidateElementLegal((ParamInstImportContext) ctx)),

                // 2、批量校验当前的维度优先级已经被配置（若方案下没有优先级可以不校验）
                ValidatorHandlerFactory.build(ctx -> batchValidateDimPriority((ParamInstImportContext) ctx)),

                // 3、批量校验属性必填项
                ValidatorHandlerFactory.build(ctx -> batchValidateRequiredElement((ParamInstImportContext) ctx)),

                // 4、批量校验当前的入参需要满足于元素约定好的取值范围
                ValidatorHandlerFactory.build(ctx -> batchValidateElementValue((ParamInstImportContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);

    }

    private ValidationError batchValidateElementLegal(ParamInstImportContext ctx) {
        ParamInstDimensionValidator validator = applicationContext.getBean(ParamInstDimensionValidator.class);
        SchemaMetaAggregate schemaMetaAggregate = ctx.getSchemaMetaAggregate();
        List<SchemaElementEntity> allElements = schemaMetaAggregate.getAllElements();
        List<String> inputNames = new ArrayList<>(ctx.getHeadMap().values());
        ValidationError validationError = validator.validateElementNameLegal(inputNames, allElements);

        List<BaseImportExcel> importRows = ctx.getImportRows();

        //若表头输入了非法的列，整个excel全部报错，打回重填
        if (Objects.nonNull(validationError)) {
            List<ImportExcelResponse> errorRes = importRows.stream()
                    .map(i -> getImportExcelResponse(i.getTaskId(), i.getRowId(), validationError.getErrorMsg()))
                    .collect(Collectors.toList());
            ctx.setImportExcelResponseList(errorRes);
            return validationError;
        }

        //如果用户输入的表头符合方案下的元素列表，则顺势将其转换为具有业务语义的ParamInstImportExcelVO，方便后续校验以及操作
        List<ParamInstImportExcelVO> paramInstImportExcelVOS = importRows.parallelStream().map(i -> ParamInstAssembler.transToExcelVO(i, ctx))
                .collect(Collectors.toList());
        ctx.setParamInstImportExcelVOS(paramInstImportExcelVOS);
        return null;
    }

    private ValidationError batchValidateDimPriority(ParamInstImportContext ctx) {
        ParamInstDimPriorityValidator validator = applicationContext.getBean(ParamInstDimPriorityValidator.class);
        List<ParamInstImportExcelVO> paramInstImportExcelVOS = ctx.getParamInstImportExcelVOS();
        String schemaCode = ctx.getSchemaCode();
        List<ImportExcelResponse> importExcelResponseList = ctx.getImportExcelResponseList();
        paramInstImportExcelVOS = paramInstImportExcelVOS.stream().filter(vo -> {
            ValidationError validationError = validator.validate(schemaCode, vo.getDimension());
            return recordImportError(importExcelResponseList, vo, validationError);
        }).collect(Collectors.toList());
        ctx.setParamInstImportExcelVOS(paramInstImportExcelVOS);
        return null;
    }

    private ValidationError batchValidateRequiredElement(ParamInstImportContext ctx) {
        ParamInstDimensionValidator validator = applicationContext.getBean(ParamInstDimensionValidator.class);
        List<ParamInstImportExcelVO> paramInstImportExcelVOS = ctx.getParamInstImportExcelVOS();
        SchemaMetaAggregate schemaMetaAggregate = ctx.getSchemaMetaAggregate();
        List<ImportExcelResponse> importExcelResponseList = ctx.getImportExcelResponseList();
        paramInstImportExcelVOS = paramInstImportExcelVOS.stream().filter(vo -> {
            ValidationError validationError = validator.validateElementRequired(schemaMetaAggregate.getAllElements(), vo.getDescMap());
            return recordImportError(importExcelResponseList, vo, validationError);
        }).collect(Collectors.toList());
        ctx.setParamInstImportExcelVOS(paramInstImportExcelVOS);
        return null;
    }

    private ValidationError batchValidateElementValue(ParamInstImportContext ctx) {
        ParamInstElementValueValidator validator = applicationContext.getBean(ParamInstElementValueValidator.class);
        List<ParamInstImportExcelVO> paramInstImportExcelVOS = ctx.getParamInstImportExcelVOS();
        String schemaCode = ctx.getSchemaCode();
        Map<String, String> elementCodeToNameMap = ctx.getElementCodeToNameMap();
        List<ImportExcelResponse> importExcelResponseList = ctx.getImportExcelResponseList();
        Map<String,String> elementCodeValueMap = new HashMap<>();
        paramInstImportExcelVOS = paramInstImportExcelVOS.stream().filter(vo -> {
            ValidationError validationError = validator.validateDesc(schemaCode, vo.getElementInstVOList() ,elementCodeToNameMap,elementCodeValueMap);
            return recordImportError(importExcelResponseList, vo, validationError);
        }).collect(Collectors.toList());
        ctx.setParamInstImportExcelVOS(paramInstImportExcelVOS);
        return null;
    }

    private boolean recordImportError(List<ImportExcelResponse> importExcelResponseList, ParamInstImportExcelVO vo, ValidationError validationError) {
        if (validationError == null) {
            return true;
        }
        ImportExcelResponse importExcelResponse = getImportExcelResponse(vo.getTaskId(), vo.getRowId(), validationError.getErrorMsg());
        importExcelResponseList.add(importExcelResponse);
        return false;
    }

    private static ImportExcelResponse getImportExcelResponse(String taskId, String rowId, String errorMsg) {
        ImportExcelResponse importExcelResponse = new ImportExcelResponse();
        importExcelResponse.setTaskId(taskId);
        importExcelResponse.setRowId(rowId);
        importExcelResponse.setResultDesc(errorMsg);
        return importExcelResponse;
    }

}
