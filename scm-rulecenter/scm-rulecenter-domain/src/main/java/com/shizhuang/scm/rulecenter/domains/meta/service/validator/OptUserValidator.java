package com.shizhuang.scm.rulecenter.domains.meta.service.validator;

import com.alibaba.fastjson2.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.CreatorVO;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.SCHEMA_CREATOR_ERROR;

@Component
public class OptUserValidator {

    @Value("${scm.rule.center.admin:10604367,10114923}")
    private String adminWhiteList;

    public ValidationError validate(String creator) {

        String creatorId = "";
        if(StringUtils.isNotBlank(creator)){
            CreatorVO creatorVO = JSON.parseObject(creator, CreatorVO.class);
            creatorId = String.valueOf(creatorVO.getUserId());
        }

        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        String userId = String.valueOf(operationUser.getUserId());

        if (StringUtils.isNotBlank(userId)
                && !adminWhiteList.contains(userId)
                && !Objects.equals(creatorId,userId)) {
            return ValidationErrorFactory.create(SCHEMA_CREATOR_ERROR);
        }
        return null;

    }


}
