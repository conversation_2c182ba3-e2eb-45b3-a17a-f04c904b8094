package com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriTypeVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Objects;
import java.util.Random;

/**
 * 方案实体类
 */
@Getter
@Setter
public class SchemaEntity {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 方案名称
     */
    String schemaName;

    /**
     * 方案描述
     */
    String schemaDesc;

    /**
     * 方案状态 0草稿 1上线 2下线
     */
    Integer status;

    /**
     * 方案描述
     */
    Integer version;

    /**
     * 创建人
     */
    String creator;

    /**
     * 是否最大版本
     */
    Integer isLargestVersion;

    /**
     * 是否生效版本
     */
    Integer isLatestVersion;

    /**
     * 是否被删除
     */
    Integer isDel;

    /**
     * 菜单
     */
    String menu;

    /**
     * 缓存超时时间
     */
    Integer redisCacheExpireTime;

    /**
     * 项目空间
     */
    String projectName;

    /**
     * 页面操作配置（用户可以选择是否要导入导出功能等等）
     */
    List<String> actionFeature;

    /**
     * 优先级类型VO
     */
    DimPriTypeVO dimPriTypeVO;

    public Long calCacheExpireTime() {
        if (Objects.isNull(redisCacheExpireTime)) {
            return 60L + new Random().nextInt(10);
        }
        Integer time = redisCacheExpireTime + new Random().nextInt(10);
        return time.longValue();
    }

}
