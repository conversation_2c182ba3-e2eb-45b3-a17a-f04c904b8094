package com.shizhuang.scm.rulecenter.domains.meta.assembler;

import com.alibaba.fastjson.JSON;
import com.shizhuang.scm.rulecenter.api.dto.DimPriorityDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import com.shizhuang.scm.rulecenter.api.dto.SchemaElementDTO;
import com.shizhuang.scm.rulecenter.api.dto.SchemaFieldMappingDTO;
import com.shizhuang.scm.rulecenter.api.response.ElementDetailResponse;
import com.shizhuang.scm.rulecenter.api.response.SchemaDetailResponse;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceSet;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.SchemaFeatureVO;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class EntireSchemaAssembler {

    public SchemaDO toSchemaDO(SchemaDetailResponse schemaDetailResponse) {
        SchemaDO schemaDO = new SchemaDO();
        schemaDO.setSchemaCode(schemaDetailResponse.getSchemaCode());
        schemaDO.setSchemaName(schemaDetailResponse.getSchemaName());
        schemaDO.setSchemaDesc(schemaDetailResponse.getSchemaDesc());
        schemaDO.setStatus(schemaDetailResponse.getStatus().longValue());
        schemaDO.setVersion(schemaDetailResponse.getVersion());
        schemaDO.setIsLargestVersion((byte) 1);
        schemaDO.setIsLatestVersion((byte) 1);
        schemaDO.setCreator(schemaDetailResponse.getCreator());
        SchemaFeatureVO schemaFeatureVO = new SchemaFeatureVO();
        schemaFeatureVO.setRedisCacheExpireTime(schemaDetailResponse.getRedisCacheExpireTime());
        schemaDO.setAttributes(JSON.toJSONString(schemaFeatureVO));
        schemaDO.setIsDel((byte) 0);
        schemaDO.setMenu(schemaDetailResponse.getMenu());
        schemaDO.setProjectName(schemaDetailResponse.getProjectName());
        return schemaDO;
    }


    public List<SchemaDetailDO> toSchemaDetailDO(List<SchemaElementDTO> schemaElementDTOS) {
        List<SchemaDetailDO> schemaDetailDOS = schemaElementDTOS.stream().map(dto -> {
            SchemaDetailDO schemaDetailDO = new SchemaDetailDO();
            schemaDetailDO.setSchemaCode(dto.getSchemaCode());
            schemaDetailDO.setElementCode(dto.getElementCode());
            schemaDetailDO.setElementName(dto.getElementName());
            schemaDetailDO.setElementType(dto.getElementType().byteValue());
            schemaDetailDO.setPriority(dto.getPriority());
            schemaDetailDO.setVersion(dto.getVersion());
            ElementUIAttributeVO elementUIAttributeVO = new ElementUIAttributeVO();
            elementUIAttributeVO.setQueryCondition(dto.getQueryCondition());
            elementUIAttributeVO.setIsRequired(dto.getIsRequired());
            elementUIAttributeVO.setIsMulti(dto.getIsMulti());
            schemaDetailDO.setUiAttributes(JSON.toJSONString(elementUIAttributeVO));
            schemaDetailDO.setIsDel((byte) 0);
            return schemaDetailDO;
        }).collect(Collectors.toList());
        return schemaDetailDOS;
    }

    public List<SchemaFieldMappingDO> toSchemaFieldMappingDO(SchemaFieldMappingDTO schemaFieldMappingDTO) {
        String schemaCode = schemaFieldMappingDTO.getSchemaCode();
        Integer version = schemaFieldMappingDTO.getVersion();
        Map<String, String> schemaFieldMap = schemaFieldMappingDTO.getSchemaFieldMap();
        List<SchemaFieldMappingDO> fieldsMapping = new ArrayList<>();
        schemaFieldMap.forEach((key, value) -> {
            SchemaFieldMappingDO schemaFieldMappingDO = new SchemaFieldMappingDO();
            schemaFieldMappingDO.setElementCode(key);
            schemaFieldMappingDO.setAdsFieldCode(value);
            schemaFieldMappingDO.setSchemaCode(schemaCode);
            schemaFieldMappingDO.setVersion(version);
            schemaFieldMappingDO.setIsDel(CommonConstant.IS_NOT_DELETE);
            fieldsMapping.add(schemaFieldMappingDO);
        });
        return fieldsMapping;
    }

    /**
     * @param priorities
     */
    public List<DimensionPriorityDO> toDimensionPriorityDO(List<DimPriorityDTO> priorities) {
        List<DimensionPriorityDO> dimensionPriorityDOS = priorities.stream().map(dto -> {
            DimensionPriorityDO dimensionPriorityDO = new DimensionPriorityDO();
            dimensionPriorityDO.setSchemaCode(dto.getSchemaCode());
            dimensionPriorityDO.setVersion(dto.getVersion());
            dimensionPriorityDO.setDimension(dto.getDimension());
            dimensionPriorityDO.setPriority(dto.getPriority());
            dimensionPriorityDO.setIsDel((byte) 0);
            return dimensionPriorityDO;
        }).collect(Collectors.toList());
        return dimensionPriorityDOS;
    }


    public List<ElementDO> toElementDO(List<ElementDetailResponse> elementDetailList) {
        List<ElementDO> elementDOS = elementDetailList.stream().map(e -> {
            ElementDO elementDO = new ElementDO();
            elementDO.setElementCode(e.getElementCode());
            elementDO.setElementName(e.getElementName());
            elementDO.setValueRangeType(e.getValueRangeType());
            elementDO.setBelongToSchemaCode(e.getBelongToSchemaCode());
            elementDO.setIsDel((byte) 0);
            elementDO.setElementSourceType(e.getElementSourceType());

            ElementInterfaceSet elementInterfaceSet = new ElementInterfaceSet();
            if(Objects.nonNull(e.getFullInterface())){
                ElementInterfaceDTO fullInterface = e.getFullInterface();
                ElementInterfaceVO fullInterfaceVO = new ElementInterfaceVO();
                BeanUtils.copyProperties(fullInterface, fullInterfaceVO);
                elementInterfaceSet.setFullInterfaceVO(fullInterfaceVO);
            }

            if(Objects.nonNull(e.getValueToDescInterface())){
                ElementInterfaceDTO valueToDescInterface = e.getValueToDescInterface();
                ElementInterfaceVO valueToDescInterfaceVO = new ElementInterfaceVO();
                BeanUtils.copyProperties(valueToDescInterface,valueToDescInterfaceVO);
                elementInterfaceSet.setValueToDescInterfaceVO(valueToDescInterfaceVO);
            }

            if(Objects.nonNull(e.getDescToValueInterface())){
                ElementInterfaceDTO descToValueInterface = e.getDescToValueInterface();
                ElementInterfaceVO descToValueInterfaceVO = new ElementInterfaceVO();
                BeanUtils.copyProperties(descToValueInterface, descToValueInterfaceVO);
                elementInterfaceSet.setDescToValueInterfaceVO(descToValueInterfaceVO);
            }

            elementDO.setInterfaceFeature(JSON.toJSONString(elementInterfaceSet));
            elementDO.setFrontComponent(e.getFrontComponent());
            elementDO.setElementNote(e.getElementNote());
            elementDO.setCreator(e.getCreator());
            return elementDO;
        }).collect(Collectors.toList());
        return elementDOS;
    }
}
