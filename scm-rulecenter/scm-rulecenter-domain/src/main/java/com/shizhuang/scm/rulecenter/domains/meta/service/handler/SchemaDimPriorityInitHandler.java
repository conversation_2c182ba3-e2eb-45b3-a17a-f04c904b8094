package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.DimPriorityCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaDimPriorityEditCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.SchemaAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaDimPriorityEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SchemaDimPriorityInitHandler implements IBasicHandler<SchemaDimPriorityEditContext> {

    @Resource
    SchemaRepository schemaRepository;

    @Override
    public void doHandle(SchemaDimPriorityEditContext schemaDimPriorityEditContext) {
        SchemaDimPriorityEditCmd schemaDimPriorityEditCmd = schemaDimPriorityEditContext.getSchemaDimPriorityEditCmd();
        String schemaCode = schemaDimPriorityEditCmd.getSchemaCode();

        //将前端输入的维度按照方案内元素顺序重新排序
        SchemaMetaAggregate schemaAggregateByCode = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLargestVersion());
        schemaDimPriorityEditContext.setSchemaMetaAggregate(schemaAggregateByCode);
        List<SchemaElementEntity> allElements = schemaAggregateByCode.getAllElements().stream().sorted(Comparator.comparing(SchemaElementEntity::getPriority)).collect(Collectors.toList());
        List<DimPriorityCreateCmd> dimPriorityCreateCmds = schemaDimPriorityEditCmd.getPriority();
        dimPriorityCreateCmds.forEach(cmd->{
            List<String> atomicDimension = cmd.getAtomicDimension();
            List<String> orderedAtomicDimension = allElements.stream().filter(e -> atomicDimension.contains(e.getElementCode())).map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
            cmd.setAtomicDimension(orderedAtomicDimension);
        });
        List<DimPriorityVO> dimPriorityVOList = SchemaAssembler.toVO(schemaDimPriorityEditCmd);

        schemaDimPriorityEditContext.setDimPriorityVOList(dimPriorityVOList);
    }
}
