package com.shizhuang.scm.rulecenter.domains.meta.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.poizon.fusion.coloring.util.ColoringBaseUtil;
import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.monitor.instrument.core.consts.OpentracingConsts;
import com.shizhuang.scm.rulecenter.api.constant.SystemFieldConditionEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.service.component.RcElementValueRangeApi;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.*;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.spring.ApplicationContextHelper;
import com.shizhuang.scm.rulecenter.infrastructure.dubbo.DubboGenericServiceUtil;
import io.opentracing.Span;
import io.opentracing.tag.Tags;
import io.opentracing.util.GlobalTracer;
import lombok.NonNull;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.poizon.fusion.coloring.common.ColoringEnvConstant.TraceConstant.OPERATE_NAME_SET_COLOR;
import static com.poizon.fusion.coloring.common.ColoringEnvConstant.TraceConstant.PREFIX_CE;

@Component
public class MetaDomainService implements InitializingBean {

    List<String> dimensionFields;

    List<String> paramsFields;

    @Resource
    AcquireElementEnumsContext acquireElementEnumsContext;

    @Resource
    ElementRepository elementRepository;

    @Resource
    DubboGenericServiceUtil dubboGenericServiceUtil;


    public List<FieldMappingVO> fieldMapping(List<SchemaElementEntity> dimensions, List<SchemaElementEntity> params) {
        List<FieldMappingVO> res = new ArrayList<>();
        extractFieldMap(dimensions, res, dimensionFields);
        extractFieldMap(params, res, paramsFields);
        return res;
    }

    private void extractFieldMap(List<SchemaElementEntity> params, List<FieldMappingVO> res, List<String> paramsFields) {
        for (int i = 0; i < params.size(); i++) {
            FieldMappingVO fieldMappingVO = new FieldMappingVO();
            fieldMappingVO.setSchemaCode(params.get(i).getSchemaCode());
            fieldMappingVO.setElementCode(params.get(i).getElementCode());
            fieldMappingVO.setFieldCode(paramsFields.get(i));
            fieldMappingVO.setVersion(params.get(i).getVersion());
            res.add(fieldMappingVO);
        }
    }

    /**
     * 初始化数据库维度字段以及参数字段
     *
     * @throws Exception
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        //https://www.coder.work/article/151284 通过类似方法获取到数据库中包含的所有dimension字段以及 param字段
        dimensionFields = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            dimensionFields.add("dimension_" + i);
        }
        paramsFields = new ArrayList<>();
        for (int i = 1; i <= 20; i++) {
            paramsFields.add("param_" + i);
        }
    }

    LoadingCache<ElementMetaEntity, List<ElementEnumVO>> valueRangeCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.SECONDS).maximumSize(10000)
            .build(new CacheLoader<ElementMetaEntity, List<ElementEnumVO>>() {
                @Override
                public List<ElementEnumVO> load(@NonNull ElementMetaEntity key) {
                    return extractElementEnumsWithNoCache(key);
                }
            });


    public List<ElementEnumVO> extractElementEnums(ElementMetaEntity elementMetaEntity) {
        try {
            return valueRangeCache.getUnchecked(elementMetaEntity);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public List<ElementEnumVO> extractElementEnumsWithNoCache(ElementMetaEntity elementMetaEntity) {
        ValueRangeTypeEnum valueRangeTypeEnum = ValueRangeTypeEnum.of(elementMetaEntity.getValueRangeType());
        AcquireValueRangeStrategy strategy = acquireElementEnumsContext.select(valueRangeTypeEnum.getType());
        return strategy.extractElementEnums(elementMetaEntity);
    }

    LoadingCache<ElementMetaEntity, Map<String/*elementDesc*/, String/*elementValue*/>> elemDescMapCache =
            CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).maximumSize(10000)
                    .build(new CacheLoader<ElementMetaEntity, Map<String, String>>() {
                        @Override
                        public Map<String, String> load(@NonNull ElementMetaEntity key) {
                            return extractElementDescMapWithNoCache(key);
                        }
                    });

    LoadingCache<ElementMetaEntity, Map<String/*elementValue*/, String/*elementDesc*/>> elemValueMapCache =
            CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).maximumSize(10000)
                    .build(new CacheLoader<ElementMetaEntity, Map<String, String>>() {
                        @Override
                        public Map<String, String> load(@NonNull ElementMetaEntity key) {
                            return extractElementValueMapWithNoCache(key);
                        }
                    });

    private Map<String, String> extractElementDescMapWithNoCache(ElementMetaEntity entity) {
        if (entity.needCacheFullData()) {
            List<ElementEnumVO> elementEnumVOS = extractElementEnums(entity);
            return elementEnumVOS.stream().collect(Collectors.toMap(ElementEnumVO::getDesc, ElementEnumVO::getValue, (o, n) -> n));
        }else{
            List<ElementEnumVO> elementEnumVOS = extractElementEnums(entity);
            if(CollectionUtils.isNotEmpty(elementEnumVOS)){
                String desc = elementEnumVOS.get(0).getDesc();
                String value = elementEnumVOS.get(0).getValue();
                HashMap<String, String> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put(desc,value);
                return stringStringHashMap;
            }
        }
        return new HashMap<>();
    }

    private Map<String, String> extractElementValueMapWithNoCache(ElementMetaEntity entity) {
        //if (entity.needCacheFullData()) {
            List<ElementEnumVO> elementEnumVOS = extractElementEnums(entity);
            return elementEnumVOS.stream().collect(Collectors.toMap(ElementEnumVO::getValue, ElementEnumVO::getDesc, (o, n) -> n));
        //}
        //return new HashMap<>();
    }

    /**
     * 根据元素“值描述” 反查对应的元素值，导入以及新增校验时使用
     */
    public String getElemValue(String schemaCode, String elementCode, String valueDesc, Map<String,String> extParam) {
        ElementMetaEntity elementMetaEntity = elementRepository.getByCode(schemaCode, elementCode);
        if (elementMetaEntity.noNeedToTransValue()) {
            return valueDesc;
        }
        Map<String, Object> context = new HashMap<>();
        if(MapUtils.isNotEmpty(extParam)){
            context = new HashMap<>(extParam);
        }
        context.put("elementDesc",valueDesc);
        context.put("searchType",DubboSearchTypeEnum.DESC_TO_VALUE.getType());
        elementMetaEntity.setExtParam(context);
        ValueRangeTypeEnum valueRangeTypeEnum = ValueRangeTypeEnum.of(elementMetaEntity.getValueRangeType());
        AcquireValueRangeStrategy strategy = acquireElementEnumsContext.select(valueRangeTypeEnum.getType());
        ElementMetaEntity incokeElementMetaEntity = strategy.cleanParam(elementMetaEntity,context);
        Map<String, String> descValueMap = elemDescMapCache.getUnchecked(incokeElementMetaEntity);
        return descValueMap.getOrDefault(valueDesc,StringUtils.EMPTY);
    }

    /**
     * 通过元素实例值获取到对应的值描述（导出时使用）
     * 遗留问题，单点查询的场景，好像没有用code查询name的接口
     */
    public String getFirstElemDesc(String schemaCode, String elementCode, String elementValue, Map<String, Object> extParam) {
        if(SystemFieldConditionEnum.isSystemCondition(elementCode)){
            return elementValue;
        }
        ElementMetaEntity elementMetaEntity = elementRepository.getByCode(schemaCode, elementCode);
        ElementMetaEntity elementMetaEntityInvoke = new ElementMetaEntity();
        elementMetaEntityInvoke.setElementInterfaceSet(elementMetaEntity.getElementInterfaceSet());
        BeanUtils.copyProperties(elementMetaEntity,elementMetaEntityInvoke);
        elementMetaEntityInvoke.setExtParam(extParam);
        return getFirstElemDesc(elementValue, elementMetaEntityInvoke);
    }

    public String getFirstElemDesc(String elementValue, ElementMetaEntity elementMetaEntity) {
        if (Objects.isNull(elementMetaEntity)
                || elementMetaEntity.noNeedToTransValue()) {
            return elementValue;
        }
        Map<String,Object> context = new HashMap<>();
        context.put("elementValue",elementValue);
        context.put("searchType",DubboSearchTypeEnum.VALUE_TO_DESC.getType());
        ValueRangeTypeEnum valueRangeTypeEnum = ValueRangeTypeEnum.of(elementMetaEntity.getValueRangeType());
        AcquireValueRangeStrategy strategy = acquireElementEnumsContext.select(valueRangeTypeEnum.getType());
        ElementMetaEntity incokeElementMetaEntity = strategy.cleanParam(elementMetaEntity,context);
        Map<String, String> valueDescMap = elemValueMapCache.getUnchecked(incokeElementMetaEntity);
        return valueDescMap.getOrDefault(elementValue,elementValue);

    }

    public List<ElementEnumVO> getPageElemDesc(String schemaCode, String elementCode, String elementValue) {
        ElementMetaEntity elementMetaEntity = elementRepository.getByCode(schemaCode, elementCode);
        return getElementEnums(elementMetaEntity.getElementInterfaceSet().getDescToValueInterfaceVO(), elementValue);
    }

    LoadingCache<InvokeCmd, List<ElementEnumVO>> dubboEnumsCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).maximumSize(10000)
            .build(new CacheLoader<InvokeCmd, List<ElementEnumVO>>() {
                @Override
                public List<ElementEnumVO> load(@NonNull InvokeCmd key) {
                    return ApplicationContextHelper.getBean(MetaDomainService.class).getElementEnumsWithNoCache(key);
                }
            });


    public List<ElementEnumVO> getElementEnums(ElementInterfaceVO elementInterfaceVO, String replaceVariable) {
        InvokeCmd invokeCmd = new InvokeCmd();
        invokeCmd.setElementInterfaceVO(elementInterfaceVO);
        invokeCmd.setReplaceVariable(replaceVariable);
        return dubboEnumsCache.getUnchecked(invokeCmd);
    }

    //todo 这里要感知不同的元素要走不同的策略模式，valueCodePath如果末尾是cascader则默认命中级联模式来构造对象
    public List<ElementEnumVO> getElementEnumsWithNoCache(InvokeCmd invokeCmd) {
        Object originData = invoke(invokeCmd);
        ElementInterfaceVO elementInterfaceVO = invokeCmd.getElementInterfaceVO();
        String valueCodePath = elementInterfaceVO.getValueCodePath();
        String valueDescPath = elementInterfaceVO.getValueDescPath();

        JSONObject resJson = JSON.parseObject(JSON.toJSONString(originData), JSONObject.class);
        List<String> valueSequence = Lists.newArrayList(valueCodePath.split("\\."));
        List<String> valueByField = getByField(resJson, 0, valueSequence);

        List<String> descSequence = Lists.newArrayList(valueDescPath.split("\\."));
        List<String> descByField = getByField(resJson, 0, descSequence);

        List<ElementEnumVO> res = new ArrayList<>();
        for (int i = 0; i < valueByField.size(); i++) {
            String value = valueByField.get(i);
            String desc = descByField.get(i);
            res.add(new ElementEnumVO(value, desc));
        }
        return res;
    }

    @Resource
    RcElementValueRangeApi rcElementValueRangeApi;

    public Object invoke(InvokeCmd invokeCmd) {

        Span span = GlobalTracer.get().buildSpan(OPERATE_NAME_SET_COLOR)
                .asChildOf(GlobalTracer.get().activeSpan())
                .withTag(Tags.SPAN_KIND.getKey(), OPERATE_NAME_SET_COLOR).start();
        span.setBaggageItem(OpentracingConsts.TYPE_TAG.getKey(), PREFIX_CE + ColoringBaseUtil.getLocalColoringEnv());
        GlobalTracer.get().scopeManager().activate(span);

        ElementInterfaceVO elementInterfaceVO = invokeCmd.getElementInterfaceVO();
        String valueDesc = invokeCmd.getReplaceVariable();
        String dubboInterface = elementInterfaceVO.getDubboInterface();
        String dubboMethod = elementInterfaceVO.getDubboMethod();
        List<String> dubboParamType = elementInterfaceVO.getDubboParamType();
        List<String> dubboParamValue = elementInterfaceVO.getDubboParamValue();
        String[] dubboParamTypeArray = new String[]{};
        Object[] dubboParamValueArray = new Object[]{};
        if (CollectionUtils.isNotEmpty(dubboParamType)) {
            dubboParamValueArray = new Object[dubboParamValue.size()];
            dubboParamTypeArray = listToArray(dubboParamType);
            for (int i = 0; i < dubboParamType.size(); i++) {
                Class<?> aClass = null;
                try {
                    aClass = Class.forName(String.valueOf(dubboParamType.get(i)));
                } catch (ClassNotFoundException e) {
                    aClass = JSONObject.class;
                }
                String paramValue = dubboParamValue.get(i);
                if (StringUtils.isBlank(paramValue)) {
                    dubboParamValueArray[i] = null;
                } else if (paramValue.contains(CommonConstant.REPLACE_VAR)) {
                    paramValue = paramValue.replace(CommonConstant.REPLACE_VAR, valueDesc);
                }
                if (aClass == JSONObject.class) {
                    dubboParamValueArray[i] = JSON.parse(paramValue);
                } else {
                    Object convert = convertJsonToObject(paramValue, aClass);
                    dubboParamValueArray[i] = convert;
                }
            }
        }

//        if(Objects.equals(dubboInterface,"com.shizhuang.scm.rulecenter.api.service.component.RcElementValueRangeApi:1.0.0")){
//            return rcElementValueRangeApi.getValueRange(String.valueOf(dubboParamValueArray[0]),String.valueOf(dubboParamValueArray[1]),String.valueOf(dubboParamValueArray[2]));
//        }

        return dubboGenericServiceUtil.invokeWhitMultiParam(dubboInterface
                , dubboMethod
                , dubboParamTypeArray
                , dubboParamValueArray);
    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static Object convertJsonToObject(String jsonStr, Class<?> clazz) {
        try {
            // 处理基本类型
            if (clazz == String.class) {
                return jsonStr; // 直接返回原始字符串
            } else if (clazz == Integer.class || clazz == int.class) {
                return objectMapper.readValue(jsonStr, Integer.class);
            } else if (clazz == Long.class || clazz == long.class) {
                return objectMapper.readValue(jsonStr, Long.class);
            } else if (clazz == Boolean.class || clazz == boolean.class) {
                return objectMapper.readValue(jsonStr, Boolean.class);
            }
            // 处理集合和Map
            else if (List.class.isAssignableFrom(clazz)) {
                return objectMapper.readValue(jsonStr, new TypeReference<List<?>>() {});
            } else if (Set.class.isAssignableFrom(clazz)) {
                return objectMapper.readValue(jsonStr, new TypeReference<Set<?>>() {});
            } else if (Map.class.isAssignableFrom(clazz)) {
                return objectMapper.readValue(jsonStr, new TypeReference<Map<?, ?>>() {});
            }
            // 处理自定义对象
            else {
                return objectMapper.readValue(jsonStr, clazz);
            }
        } catch (Exception e) {
            throw new RuntimeException("JSON转换失败: " + e.getMessage(), e);
        }
    }




    public static String[] listToArray(List<String> testList) {
        if (CollectionUtils.isEmpty(testList)) {
            return new String[]{};
        }
        //初始化需要得到的数组
        String[] array = new String[testList.size()];

        //使用for循环得到数组
        for (int i = 0; i < testList.size(); i++) {
            array[i] = testList.get(i);
        }
        //打印数组
        for (int i = 0; i < array.length; i++) {
            System.out.println(array[i]);
        }
        return array;
    }

    private List<String> getByField(Object json, Integer position, List<String> orderSequence) {
        String field = orderSequence.get(position);
        if (json instanceof JSONArray) {
            if (isLast(field, orderSequence)) {
                return ((JSONArray) json).stream().map(i -> String.valueOf(((JSONObject) i).get(field))).collect(Collectors.toList());
            } else {
                return ((JSONArray) json).stream().map(subJson -> getByField(((JSONObject) subJson).get(field), position + 1, orderSequence)).flatMap(Collection::stream).collect(Collectors.toList());
            }
        } else if (json instanceof JSONObject) {
            if (isLast(field, orderSequence)) {
                return Lists.newArrayList(String.valueOf(((JSONObject) json).get(field)));
            } else {
                return getByField(((JSONObject) json).get(field), position + 1, orderSequence);
            }
        }
        return new ArrayList<>();
    }


    private boolean isLast(String field, List<String> orderSequence) {
        return orderSequence.indexOf(field) == orderSequence.size() - 1;
    }

    public List<CompareIssueVO> compareSchema(SchemaMetaAggregate newSchemaMetaAggregate, SchemaMetaAggregate oldSchemaMetaAggregate) {

        List<CompareIssueVO> compareIssueVOList = new ArrayList<>();

        SchemaEntity newSchemaEntity = newSchemaMetaAggregate.getSchemaEntity();
        String newSchemaName = newSchemaEntity.getSchemaName();
        String newSchemaDesc = newSchemaEntity.getSchemaDesc();

        SchemaEntity oldSchemaEntity = oldSchemaMetaAggregate.getSchemaEntity();
        String oldSchemaName = oldSchemaEntity.getSchemaName();
        String oldSchemaDesc = oldSchemaEntity.getSchemaDesc();
        if (!Objects.equals(newSchemaName, oldSchemaName)) {
            compareIssueVOList.add(CompareIssueVO.builder().modifyField("方案名称").oldContent(oldSchemaName).newContent(newSchemaName).build());
        }
        if (!Objects.equals(newSchemaDesc, oldSchemaDesc)) {
            compareIssueVOList.add(CompareIssueVO.builder().modifyField("方案描述").oldContent(oldSchemaDesc).newContent(newSchemaDesc).build());
        }

        List<SchemaElementEntity> newDimensions = newSchemaMetaAggregate.getDimensions();
        List<SchemaElementEntity> newParams = newSchemaMetaAggregate.getParams();

        String newDimensionStr = newDimensions.stream().map(SchemaElementEntity::getElementName).collect(Collectors.joining("#"));
        String newParamStr = newParams.stream().map(SchemaElementEntity::getElementName).collect(Collectors.joining("#"));

        List<SchemaElementEntity> oldDimensions = oldSchemaMetaAggregate.getDimensions();
        List<SchemaElementEntity> oldParams = oldSchemaMetaAggregate.getParams();
        String oldDimensionsStr = oldDimensions.stream().map(SchemaElementEntity::getElementName).collect(Collectors.joining("#"));
        String oldParamsStr = oldParams.stream().map(SchemaElementEntity::getElementName).collect(Collectors.joining("#"));

        if (!Objects.equals(newDimensionStr, oldDimensionsStr)) {
            compareIssueVOList.add(CompareIssueVO.builder().modifyField("方案维度").oldContent(oldDimensionsStr).newContent(newDimensionStr).build());
        }
        if (!Objects.equals(newParamStr, oldParamsStr)) {
            compareIssueVOList.add(CompareIssueVO.builder().modifyField("方案参数").oldContent(oldSchemaDesc).newContent(newSchemaDesc).build());
        }
        return compareIssueVOList;
    }

    public static void main(String[] args) {
        Class<?> aClass = null;
        try {
            aClass = Class.forName("com.dewu.scp.qms.sdk.domain.request.QualityOptimizeConfigRequest");
        } catch (ClassNotFoundException e) {
            aClass = JSONObject.class;
        }

        Object convert = convertJsonToObject("{\"ruleVersion\":2,\"spuId\":3225965}", aClass);
        System.out.println(JSON.toJSONString(convert));
    }

    public void invalidCache(){
        valueRangeCache.cleanUp();
        elemValueMapCache.cleanUp();
        elemDescMapCache.cleanUp();
        dubboEnumsCache.cleanUp();
    }

}
