package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstExportContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstDimensionValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstElementValueValidator;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ParamInstExportValidateChain implements IBasicHandler<ParamInstExportContext>, ApplicationContextAware {


    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(ParamInstExportContext paramInstExportContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(paramInstExportContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、批量校验当前的导出请求的元素否与方案中指定的一致
                ValidatorHandlerFactory.build(ctx -> validateElementCodeLegal((ParamInstExportContext) ctx)),

                // 2、批量校验当前的入参需要满足于元素约定好的取值范围
                ValidatorHandlerFactory.build(ctx -> validateElementValue((ParamInstExportContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }

    private ValidationError validateElementCodeLegal(ParamInstExportContext ctx) {
        InstPageQuery instPageQuery = ctx.getInstPageQuery();
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        if (CollectionUtils.isEmpty(queryParams)) {
            return null;
        }
        SchemaMetaAggregate schemaMetaAggregate = ctx.getSchemaMetaAggregate();
        List<SchemaElementEntity> allElements = schemaMetaAggregate.getAllElements();
        List<String> inputCodes = queryParams.stream().map(ElementInstDTO::getElementCode).collect(Collectors.toList());
        ParamInstDimensionValidator validator = applicationContext.getBean(ParamInstDimensionValidator.class);
        return validator.validateElementCodeLegal(inputCodes, allElements);
    }

    private ValidationError validateElementValue(ParamInstExportContext ctx) {
        InstPageQuery instPageQuery = ctx.getInstPageQuery();
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        if (CollectionUtils.isEmpty(queryParams)) {
            return null;
        }
        Map<String, Object> paramMap = queryParams.stream().collect(Collectors.toMap(ElementInstDTO::getElementCode, ElementInstDTO::getElementValue, (o, n) -> n));

        SchemaMetaAggregate schemaMetaAggregate = ctx.getSchemaMetaAggregate();
        String schemaCode = schemaMetaAggregate.getSchemaEntity().getSchemaCode();
        ParamInstElementValueValidator validator = applicationContext.getBean(ParamInstElementValueValidator.class);
        Map<String, String> elementCodeToNameMap = schemaMetaAggregate.getElementCodeToNameMap();
        return validator.validate(schemaCode, paramMap, elementCodeToNameMap);
    }


}
