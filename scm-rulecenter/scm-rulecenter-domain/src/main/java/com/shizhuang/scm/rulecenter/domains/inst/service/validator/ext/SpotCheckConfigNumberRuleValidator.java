package com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Component
public class SpotCheckConfigNumberRuleValidator implements ParamInstCreateCustomValidatorExt, ParamInstEditCustomValidatorExt {
    @Override
    public ValidationError validate(ParamInstCreateContext paramInstCreateContext) {

        ParamInstAddCmd paramInstAddCmd = paramInstCreateContext.getParamInstAddCmd();
        Map<String, Object> paramInstValueMap = paramInstAddCmd.getParamMap().stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        return getValidationError(paramInstValueMap);
    }

    private static ValidationError getValidationError(Map<String, Object> paramInstValueMap) {
        Object maxNumberObj = paramInstValueMap.get("maxNumber");
        int maxNumber = Integer.parseInt(maxNumberObj.toString());
        Object numberRuleItemsObj = paramInstValueMap.get("numberRuleItems");
        if (Objects.isNull(numberRuleItemsObj)) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "抽检数量至少要有一条记录");
        }
        List<NumberRuleItem> numberRuleItems = JSON.parseArray(JSON.toJSONString(numberRuleItemsObj), NumberRuleItem.class);
        if (CollectionUtils.isEmpty(numberRuleItems)) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "抽检数量至少要有一条记录");
        }
        numberRuleItems.forEach(x -> {
            if (Objects.isNull(x.getFirstNumber())) {
                x.setFirstNumber(0);
            }
            if (Objects.isNull(x.getSecondNumber())) {
                x.setSecondNumber(0);
            }
        });
        numberRuleItems = numberRuleItems.stream().sorted(Comparator.comparing(NumberRuleItem::getStartNumber)).collect(Collectors.toList());
        if (maxNumber >= 2 && numberRuleItems.stream().anyMatch(x -> Objects.isNull(x.getSecondNumber()) || x.getSecondNumber() <= 0)) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "当前抽检轮次大于等于2，但是第二轮抽检数量无值");
        }

        if (numberRuleItems.stream().anyMatch(x -> (x.getFirstNumber() + x.getSecondNumber()) > x.getStartNumber())) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "第一轮 + 第二轮 数值相加不能超过数量范围开始值");
        }

        for (int i = 0; i < numberRuleItems.size(); i++) {
            if (numberRuleItems.get(i).getStartNumber() <= 0 || numberRuleItems.get(i).getEndNumber() <= 0) {
                return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "来货数量必须大于0");
            }

            if (numberRuleItems.get(i).getFirstNumber() < 0 || numberRuleItems.get(i).getSecondNumber() < 0) {
                return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "抽检数量不能为负数");
            }

            for (int j = i + 1; j < numberRuleItems.size(); j++) {
                NumberRuleItem current = numberRuleItems.get(j);
                NumberRuleItem last = numberRuleItems.get(i);
                if (Math.max(last.getStartNumber(), current.getStartNumber()) < Math.min(last.getEndNumber(), current.getEndNumber())) {
                    return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "来货数量区间发生重叠");
                }
            }
        }

        return null;
    }

    @Override
    public ValidationError validate(ParamInstEditContext paramInstEditContext) {
        ParamInstEditCmd paramInstEditCmd = paramInstEditContext.getParamInstEditCmd();
        Map<String, Object> paramInstValueMap = paramInstEditCmd.getParamMap().stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        return getValidationError(paramInstValueMap);
    }
}
