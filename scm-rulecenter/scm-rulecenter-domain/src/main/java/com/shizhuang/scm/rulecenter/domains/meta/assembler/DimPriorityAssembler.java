package com.shizhuang.scm.rulecenter.domains.meta.assembler;

import com.shizhuang.scm.rulecenter.api.dto.DimPriorityDTO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;

public class DimPriorityAssembler {

    public static DimPriorityDTO toDTO(DimPriorityVO dimPriorityVO){
        DimPriorityDTO dimPriorityDTO = new DimPriorityDTO();
        dimPriorityDTO.setSchemaCode(dimPriorityVO.getSchemaCode());
        dimPriorityDTO.setDimension(dimPriorityVO.getDimension());
        dimPriorityDTO.setPriority(dimPriorityVO.getPriority());
        dimPriorityDTO.setIsDel(dimPriorityVO.getIsDel());
        dimPriorityDTO.setVersion(dimPriorityVO.getVersion());
        dimPriorityDTO.setAtomicDimension(dimPriorityVO.getAtomicDimension());
        return dimPriorityDTO;
    }

}
