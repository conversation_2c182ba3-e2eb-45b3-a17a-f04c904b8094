package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaDimPriorityEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.DimPriorityValidator;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SchemaDimPriorityValidatorChain implements IBasicHandler<SchemaDimPriorityEditContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(SchemaDimPriorityEditContext schemaDimPriorityEditContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(schemaDimPriorityEditContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            //记录日志
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、方案下不能存在相同的优先级
                ValidatorHandlerFactory.build(ctx -> validateDimPriorityRepeatable((SchemaDimPriorityEditContext) ctx)),

                // 2、若方案下已经有参数，name在设置方案维度优先级时，需要校验是否有已经存在的参数但是没有配置优先级的情况
                ValidatorHandlerFactory.build(ctx -> validateDimPriorityMissing((SchemaDimPriorityEditContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }

    private ValidationError validateDimPriorityMissing(SchemaDimPriorityEditContext ctx) {
        DimPriorityValidator validator = applicationContext.getBean(DimPriorityValidator.class);
        List<DimPriorityVO> dimPriorityVOList = ctx.getDimPriorityVOList();
        return validator.validateMissingPart(dimPriorityVOList);
    }

    private ValidationError validateDimPriorityRepeatable(SchemaDimPriorityEditContext ctx) {
        DimPriorityValidator validator = applicationContext.getBean(DimPriorityValidator.class);
        List<DimPriorityVO> dimPriorityVOList = ctx.getDimPriorityVOList();
        return validator.validateRepeatable(dimPriorityVOList);
    }
}
