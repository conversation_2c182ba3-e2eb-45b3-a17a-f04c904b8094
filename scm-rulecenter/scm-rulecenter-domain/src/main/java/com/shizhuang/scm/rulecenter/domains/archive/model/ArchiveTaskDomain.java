package com.shizhuang.scm.rulecenter.domains.archive.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ArchiveTaskDomain implements Serializable {

    private String taskName;

    /**
     * 数据源名称
     */
    private String datasourceName;

    /**
     * 数据库名称
     */
    private String databaseName;

    /**
     * 数据源url
     */
    private String dataSourceUrl;

    /**
     * 扫描数量 默认100条
     */
    private Integer limit = 100;

    /**
     * 扫描间隔 默认30秒
     */
    private Integer interval = 30;

    /**
     * 0:暂停 1:运行
     */
    private Integer status = 0;

    /**
     * 1 debug 2 生产
     */
    private Integer mode;

    /**
     * 任务明细配置
     */
    private List<ArchiveTaskDetailDomain> taskDetails;

    /**
     * 执行 1 全天 2 指定时间
     */
    private Integer executionType;

    /**
     * 开始执行时间 22:00
     */
    private String start;

    /**
     * 结束执行时间 6:00
     */
    private String end;

    private String key;
}
