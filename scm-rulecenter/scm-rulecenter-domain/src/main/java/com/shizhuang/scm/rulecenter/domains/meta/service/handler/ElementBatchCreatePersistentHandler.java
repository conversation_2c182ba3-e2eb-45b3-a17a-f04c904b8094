package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.domains.meta.context.ElementBatchCreateContext;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

@Component
public class ElementBatchCreatePersistentHandler  implements IBasicHandler<ElementBatchCreateContext> {


    @Override
    public void doHandle(ElementBatchCreateContext elementBatchCreateContext) {

    }
}
