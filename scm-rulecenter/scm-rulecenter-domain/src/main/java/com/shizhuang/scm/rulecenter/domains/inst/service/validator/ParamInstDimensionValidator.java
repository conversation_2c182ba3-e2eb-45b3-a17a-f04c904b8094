package com.shizhuang.scm.rulecenter.domains.inst.service.validator;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.constant.IsRequiredEnum;
import com.shizhuang.scm.rulecenter.api.constant.SystemFieldConditionEnum;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.*;

@Component
public class ParamInstDimensionValidator {

    public ValidationError validate(String oldDimension, String newDimension) {
        if (!Objects.equals(oldDimension, newDimension)) {
            return ValidationErrorFactory.create(DIMENSION_CAN_NOT_CHANGE);
        }
        return null;
    }

    public ValidationError validateElementNameLegal(List<String> inputNames, List<SchemaElementEntity> allElements) {
        List<String> elementNames = allElements.stream().map(SchemaElementEntity::getElementName).collect(Collectors.toList());
        for (String inputName : inputNames) {
            if (elementNames.contains(inputName)) {
                continue;
            }
            return ValidationErrorFactory.create(ELEMENT_NOT_MATCH_SCHEMA);
        }
        return null;
    }

    public ValidationError validateElementCodeLegal(List<String> inputCodes, List<SchemaElementEntity> allElements) {
        List<String> elementCodes = allElements.stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        for (String inputCode : inputCodes) {
            if (elementCodes.contains(inputCode)|| SystemFieldConditionEnum.isSystemCondition(inputCode)) {
                continue;
            }
            return ValidationErrorFactory.create(ELEMENT_NOT_MATCH_SCHEMA);
        }
        return null;
    }

    public ValidationError validateElementRequired(List<SchemaElementEntity> allSchemaElementEntities, Map<String, String> paramMap) {
        List<SchemaElementEntity> requiredElements = allSchemaElementEntities.stream()
                .filter(e -> IsRequiredEnum.YES.getType().equals(e.getIsRequired())).collect(Collectors.toList());
        for (SchemaElementEntity entity : requiredElements) {
            if (!paramMap.containsKey(entity.getElementCode())) {
                String specificMsg = String.format(ELEMENT_IS_REQUIRED.getErrMsg(), entity.getElementName());
                return ValidationErrorFactory.create(ELEMENT_IS_REQUIRED, specificMsg);
            }
        }

        return null;
    }
}
