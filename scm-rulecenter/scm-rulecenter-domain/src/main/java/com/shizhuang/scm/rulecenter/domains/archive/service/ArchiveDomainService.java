package com.shizhuang.scm.rulecenter.domains.archive.service;

import com.alibaba.fastjson2.JSON;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.*;
import com.shizhuang.duapp.scp.framework.admin.sdk.request.BatchFetchColumnsRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.request.FetchColumnsRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.request.PreviewRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.ColumnsResponse;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.DatasourceResponse;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.PreviewResponse;
import com.shizhuang.scm.rulecenter.domains.meta.context.ArchiveUpdateContext;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveGlobal;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveTaskDetailDomain;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveTaskDomain;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode;
import com.shizhuang.scm.rulecenter.infrastructure.integration.rpc.ArchiveDataPersistService;
import com.shizhuang.scm.rulecenter.infrastructure.integration.rpc.ArchiveSDKIntegrationApi;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.util.deparser.ExpressionDeParser;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shizhuang.duapp.scp.framework.admin.sdk.constants.ArchiveConstants.*;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Slf4j
@Service
public class ArchiveDomainService {

    @Resource
    private ArchiveDataPersistService archiveDataPersistService;

    @Resource
    private ArchiveSDKIntegrationApi archiveSDKIntegrationApi;

    public boolean saveOrUpdate(ArchiveTaskDomain archiveTaskDomain, ArchiveGlobal archiveGlobal) {
        if (StringUtils.isEmpty(archiveTaskDomain.getDatasourceName())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "数据源不存在");
        }
        if (StringUtils.isEmpty(archiveTaskDomain.getDatabaseName())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "数据库不存在");
        }
        if (StringUtils.isEmpty(archiveTaskDomain.getTaskName())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "任务名不能为空");
        }
        ArchiveUpdateContext archiveUpdateContext = new ArchiveUpdateContext();
        archiveUpdateContext.setArchiveTaskDomain(archiveTaskDomain);
        archiveUpdateContext.setArchiveGlobal(archiveGlobal);
        setTablesForContext(archiveTaskDomain, archiveGlobal, archiveUpdateContext);
        validateTaskConfig(archiveUpdateContext);
        if (!StringUtils.hasLength(archiveTaskDomain.getDataSourceUrl())) {
            Result<DatasourceResponse> datasourceResult = archiveSDKIntegrationApi.fetchDataSources(archiveGlobal.getAppName());
            if (datasourceResult.getData() == null || CollectionUtils.isEmpty(datasourceResult.getData().getDataSources())) {
                throw new BizRuntimeException(ErrorCode.DATASOURCE_CANNOT_FETCH.getCode(), ErrorCode.DATASOURCE_CANNOT_FETCH.getErrMsg());
            }
            datasourceResult.getData().getDataSources().stream().filter(e -> e.getName().equals(archiveGlobal.getDatasourceName()))
                    .findFirst().ifPresent(v -> archiveTaskDomain.setDataSourceUrl(v.getUrl()));
        }
        ArchiveDataConfig archiveDataConfig = archiveDataPersistService.getArchiveDataConfig(archiveGlobal.getAppName());
        TaskConfig taskConfig = convertToTaskConfig(archiveTaskDomain);
        if (archiveDataConfig == null) {
            archiveDataConfig = new ArchiveDataConfig();
            archiveDataConfig.setTasks(new ArrayList<>());
        }
        TaskConfig exist = null;
        if (!CollectionUtils.isEmpty(archiveDataConfig.getTasks())) {
            exist = archiveDataConfig.getTasks().stream().filter(e -> Objects.equals(taskConfig.getKey(), e.getKey()))
                    .findFirst().orElse(null);
        } else {
            archiveDataConfig.setTasks(new ArrayList<>());
        }
        if (exist == null) {
            archiveDataConfig.getTasks().add(taskConfig);
        } else {
            BeanUtils.copyProperties(taskConfig, exist);
        }
        return archiveDataPersistService.saveConfig(archiveDataConfig, archiveGlobal.getAppName());
    }


    public List<ArchiveTaskDomain> getTaskList(String appName) {
        ArchiveDataConfig archiveDataConfig = archiveDataPersistService.getArchiveDataConfig(appName);
        if (archiveDataConfig == null || CollectionUtils.isEmpty(archiveDataConfig.getTasks())) {
            return Collections.emptyList();
        }
        return archiveDataConfig.getTasks().stream().map(v -> convertTask(v)).collect(Collectors.toList());
    }

    private void setTablesForContext(ArchiveTaskDomain archiveTaskDomain, ArchiveGlobal archiveGlobal, ArchiveUpdateContext archiveUpdateContext) {
        try {
            List<String> tables = getUncheckedTableNames(archiveTaskDomain);
            if (!CollectionUtils.isEmpty(tables)) {
                BatchFetchColumnsRequest request = BatchFetchColumnsRequest.builder().dataSourceName(archiveGlobal.getDatasourceName())
                        .databaseName(archiveGlobal.getDatabaseName())
                        .tableNames(tables.stream().distinct().collect(Collectors.toList())).build();
                Result<List<ColumnsResponse>> result = archiveSDKIntegrationApi.doBatchFetchColumns(request, archiveGlobal.getAppName());
                Map<String, ColumnsResponse> columnsResponseMap = result.getData().stream().collect(Collectors.toMap(ColumnsResponse::getTableName, Function.identity()));
                archiveUpdateContext.setColumnsResponseMap(columnsResponseMap);
            }
        } catch (Exception e) {
            log.info("batch query table failed", e);
        }
    }


    public ArchiveTaskDomain getTask(String appName, String taskName) {
        ArchiveDataConfig archiveDataConfig = archiveDataPersistService.getArchiveDataConfig(appName);
        if (archiveDataConfig == null || CollectionUtils.isEmpty(archiveDataConfig.getTasks())) {
            return null;
        }
        for (TaskConfig taskConfig : archiveDataConfig.getTasks()) {
            if (Objects.equals(taskName, taskConfig.getTaskName())) {
                ArchiveTaskDomain domain = convertTask(taskConfig);
                markChecked(domain.getTaskDetails());
                return domain;
            }
        }
        return null;
    }

    /**
     * 如果是从持久化层查询到的数据，标记为已校验，从外部请求传入的数据则不做标记，用来进行校验
     *
     * @param taskDetails
     */
    private void markChecked(List<ArchiveTaskDetailDomain> taskDetails) {
        if (CollectionUtils.isEmpty(taskDetails)) {
            return;
        }
        taskDetails.forEach(v -> {
            v.setChecked(true);
            markChecked(v.getChildren());
        });
    }

    private ArchiveTaskDomain convertTask(TaskConfig taskConfig) {
        ArchiveTaskDomain domain = new ArchiveTaskDomain();
        domain.setTaskName(taskConfig.getTaskName());
        domain.setDatasourceName(taskConfig.getDatasourceName());
        domain.setDatabaseName(taskConfig.getDatabaseName());
        domain.setDataSourceUrl(taskConfig.getDataSourceUrl());
        domain.setLimit(taskConfig.getLimit());
        domain.setInterval(taskConfig.getInterval());
        domain.setStatus(taskConfig.getStatus());
        domain.setMode(taskConfig.getMode());
        domain.setExecutionType(taskConfig.getExecutionType());
        domain.setStart(taskConfig.getStart());
        domain.setEnd(taskConfig.getEnd());
        domain.setKey(taskConfig.getKey());
        if (taskConfig.getTaskDetails() != null) {
            List<ArchiveTaskDetailDomain> details = taskConfig.getTaskDetails().stream()
                    .map(v -> convertTaskDetail(v, null))
                    .collect(Collectors.toList());
            domain.setTaskDetails(details);
        }
        return domain;
    }

    private ArchiveTaskDetailDomain convertTaskDetail(TaskDetailConfig detailConfig, TaskDetailConfig parent) {
        ArchiveTaskDetailDomain detail = new ArchiveTaskDetailDomain();
        // 逐字段赋值，字段名基本一致
        detail.setTableName(detailConfig.getTableName());
        detail.setConditions(detailConfig.getConditions());
        detail.setIndexColumn(detailConfig.getIndexColumn());
        detail.setOrderBy(detailConfig.getOrderBy());
        detail.setRoot(parent == null);

        // 设置索引相关属性
        detail.setIndexColumn(detailConfig.getIndexColumn());
        detail.setOrderBy(detailConfig.getOrderBy());
        detail.setIndexType(detailConfig.getIndexType());
        detail.setIndexStart(detailConfig.getIndexStart());
        detail.setIndexEnd(detailConfig.getIndexEnd());
        detail.setReserveDays(detailConfig.getReserveDays());

        detail.setPlugins(detailConfig.getPlugins());
        detail.setParentTable(detailConfig.getParentTable());
        detail.setColumnRelations(detailConfig.getColumnRelations());
        detail.setRelationConditions(detailConfig.getRelationConditions());
        detail.setArchive(detailConfig.getArchive());
        detail.setPrimaryKeyColumn(detailConfig.getPrimaryKeyColumn());
        detail.setArchiveType(detailConfig.getArchiveType());
        detail.setFilterDefaultValue(detailConfig.getFilterDefaultValue());
        detail.setProps(detailConfig.getProps());
        detail.setEnableScan(detailConfig.isEnableScan());
        // 递归 children
        if (detailConfig.getChildren() != null) {
            List<ArchiveTaskDetailDomain> children = detailConfig.getChildren().stream()
                    .map(v -> {
                        ArchiveTaskDetailDomain sub = convertTaskDetail(v, detailConfig);
                        sub.setParentKey(detail.getKey());
                        return sub;
                    })
                    .collect(Collectors.toList());
            detail.setChildren(children);
        }
        return detail;
    }

    private void validateTaskConfig(ArchiveUpdateContext context) {
        if (Objects.equals(ARCHIVE_TYPE_SPECIFIC_TIME, context.getArchiveTaskDomain().getExecutionType())) {
            validateTimeSpan(context.getArchiveTaskDomain());
        }
        if (CollectionUtils.isEmpty(context.getArchiveTaskDomain().getTaskDetails())) {
            return;
        }
        if (context.getArchiveTaskDomain().getTaskDetails().size() > 1) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "跟节点任务配置不能超过1个");
        }
        ArchiveTaskDetailDomain rootNode = context.getArchiveTaskDomain().getTaskDetails().get(0);
        if (rootNode.getConditions() == null || rootNode.getConditions().trim().isEmpty()) {
            if (!StringUtils.hasLength(rootNode.getIndexColumn())
                    || (rootNode.getIndexType() == null || rootNode.getIndexType() <= 0)) {
                throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "查询条件和索引字段不能全部为空");
            }
            if (Objects.equals(INDEX_TYPE_RESERVE_DAYS, rootNode.getIndexType())
                    && (rootNode.getReserveDays() == null || rootNode.getReserveDays() < 0)) {
                throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "查询条件和索引字段不能全部为空");
            }
            if (Objects.equals(INDEX_TYPE_SPAN, rootNode.getIndexType())) {
                if ((rootNode.getIndexStart() == null || rootNode.getIndexStart() <= 0)
                        || rootNode.getIndexEnd() == null || rootNode.getIndexEnd() <= 0) {
                    throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "查询条件和索引字段不能全部为空");
                }
            }
        }
        validateTaskDetailConfigRecursive(context, rootNode, null);
    }

    private void validateTaskDetailConfigRecursive(ArchiveUpdateContext context, ArchiveTaskDetailDomain taskDetail, ArchiveTaskDetailDomain parent) {
        if (!taskDetail.isChecked()) {
            validateTaskDetailConfig(context, taskDetail, parent);
        }
        if (!CollectionUtils.isEmpty(taskDetail.getChildren())) {
            taskDetail.getChildren().forEach(v -> validateTaskDetailConfigRecursive(context, v, taskDetail));
        }
    }

    private void validateTaskDetailConfig(ArchiveUpdateContext context, ArchiveTaskDetailDomain taskDetail, ArchiveTaskDetailDomain parent) {
        if (taskDetail.isChecked()) {
            return;
        }
        if (taskDetail.getTableName() == null || taskDetail.getTableName().trim().isEmpty()) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "数据源表不能为空");
        }
        String conditions = taskDetail.getConditions();
        ColumnsResponse columnsResponse = queryColumns(taskDetail.getTableName(), context.getArchiveGlobal().getDatasourceName(), context.getArchiveGlobal().getDatabaseName(), context.getArchiveGlobal().getAppName());
        validateSqlWhereCondition(conditions, taskDetail.getTableName(), columnsResponse.getColumns());
        List<ColumnRelation> relations = parseRelationConditions(context, taskDetail, parent);
        taskDetail.setColumnRelations(relations);
//        validateRelations(context, taskDetail, null);
        setPrimaryKey(context, taskDetail);
    }


    private void setPrimaryKey(ArchiveUpdateContext context, ArchiveTaskDetailDomain taskDetail) {
        if (StringUtils.hasLength(taskDetail.getPrimaryKeyColumn())) {
            return;
        }
        FetchColumnsRequest fetchColumnsRequest = FetchColumnsRequest.builder()
                .dataSourceName(context.getArchiveGlobal().getDatasourceName()).tableName(taskDetail.getTableName())
                .databaseName(context.getArchiveGlobal().getDatabaseName()).build();
        Result<ColumnsResponse> result = archiveSDKIntegrationApi.fetchColumns(fetchColumnsRequest, context.getArchiveGlobal().getAppName());
        if (result.getData() == null || CollectionUtils.isEmpty(result.getData().getColumns())) {
            log.error("获取表结构失败  fetchColumnsRequest {} {}", fetchColumnsRequest, result);
            throw new BizRuntimeException(ErrorCode.DATASOURCE_CANNOT_FETCH.getCode(), "表结构无法获取-" + taskDetail.getTableName());
        }
        if (!CollectionUtils.isEmpty(result.getData().getIndexes())) { //设置主键
            result.getData().getIndexes().stream().filter(e -> e.isPrimary())
                    .findFirst().ifPresent(v -> taskDetail.setPrimaryKeyColumn(v.getColumns().get(0).getColumn()));
        }
    }

    private ColumnsResponse queryColumns(String tableName, String datasourceName, String databaseName, String appName) {
        return archiveSDKIntegrationApi.fetchColumns(FetchColumnsRequest.builder()
                .dataSourceName(datasourceName).databaseName(databaseName)
                .tableName(tableName)
                .build(), appName).getData();
    }


    /**
     * 校验时间区间是否满足要求
     *
     * @param taskConfig
     */
    private void validateTimeSpan(ArchiveTaskDomain taskConfig) {
        String start = taskConfig.getStart();
        String end = taskConfig.getEnd();
        if (start == null || end == null) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "时间段不能为空");
        }
        // 支持HH:mm和H:mm格式
        java.time.LocalTime startTime, endTime;
        java.time.format.DateTimeFormatter formatterHHmm = java.time.format.DateTimeFormatter.ofPattern("HH:mm");
        java.time.format.DateTimeFormatter formatterHmm = java.time.format.DateTimeFormatter.ofPattern("H:mm");
        boolean parsed = false;
        try {
            startTime = java.time.LocalTime.parse(start, formatterHHmm);
            endTime = java.time.LocalTime.parse(end, formatterHHmm);
            parsed = true;
        } catch (Exception e1) {
            try {
                startTime = java.time.LocalTime.parse(start, formatterHmm);
                endTime = java.time.LocalTime.parse(end, formatterHmm);
                parsed = true;
            } catch (Exception e2) {
                startTime = null;
                endTime = null;
            }
        }
        if (!parsed) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "时间段格式错误，需为H:mm或HH:mm");
        }
        if (startTime.equals(endTime)) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "开始时间和结束时间不能相同");
        }
        // 跨天校验
        int startMinutes = startTime.getHour() * 60 + startTime.getMinute();
        int endMinutes = endTime.getHour() * 60 + endTime.getMinute();
        // 如果开始时间大于等于结束时间，视为跨天
        if (startMinutes >= endMinutes) {
            // 跨天时间段必须是当天12:00及以后到次日12:00之前
            if (startTime.getHour() < 12 || endTime.getHour() >= 12) {
                throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "跨天时间段必须是当天12:00及以后到次日12:00之前");
            }
        }
        // 校验总时长不能超过24小时（即不能跨多天）
        int diff;
        if (startMinutes < endMinutes) {
            diff = endMinutes - startMinutes; // 同一天
        } else {
            diff = (endMinutes + 1440) - startMinutes; // 跨天
        }
        if (diff > 1440) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "时间段不能跨多天");
        }
    }

    /**
     * 将 ArchiveTaskDomain 转换为 TaskConfig
     */
    public TaskConfig convertToTaskConfig(ArchiveTaskDomain domain) {
        if (domain == null) {
            return null;
        }
        TaskConfig config = new TaskConfig();
        config.setTaskName(domain.getTaskName());
        config.setDatasourceName(domain.getDatasourceName());
        config.setDatabaseName(domain.getDatabaseName());
        config.setDataSourceUrl(domain.getDataSourceUrl());
        config.setLimit(domain.getLimit());
        config.setInterval(domain.getInterval());
        config.setStatus(domain.getStatus());
        config.setMode(domain.getMode());
        config.setExecutionType(domain.getExecutionType());
        config.setStart(domain.getStart());
        config.setEnd(domain.getEnd());
        config.setKey(domain.getKey());
        // nextExecuteTime 由调度系统维护，这里不赋值
        if (domain.getTaskDetails() != null) {
            List<TaskDetailConfig> details = domain.getTaskDetails().stream()
                    .map(this::convertToTaskDetailConfig)
                    .collect(Collectors.toList());
            config.setTaskDetails(details);
        }
        return config;
    }

    /**
     * 将 ArchiveTaskDetailDomain 转换为 TaskDetailConfig
     */
    private TaskDetailConfig convertToTaskDetailConfig(ArchiveTaskDetailDomain detailDomain) {
        if (detailDomain == null) {
            return null;
        }
        TaskDetailConfig config = new TaskDetailConfig();
        config.setTableName(detailDomain.getTableName());
        config.setConditions(detailDomain.getConditions());

        // 设置索引相关属性
        config.setOrderBy(detailDomain.getOrderBy());
        config.setIndexColumn(detailDomain.getIndexColumn());
        config.setIndexType(detailDomain.getIndexType());
        config.setIndexStart(detailDomain.getIndexStart());
        config.setIndexEnd(detailDomain.getIndexStart());
        config.setReserveDays(detailDomain.getReserveDays());

        config.setColumnRelations(detailDomain.getColumnRelations());
        config.setPlugins(detailDomain.getPlugins());
        config.setParentTable(detailDomain.getParentTable());
        config.setRelationConditions(detailDomain.getRelationConditions());
        if (!CollectionUtils.isEmpty(detailDomain.getColumnRelations())) {
            config.setColumnRelations(detailDomain.getColumnRelations());
        }
        config.setArchive(detailDomain.getArchive());
        config.setPrimaryKeyColumn(detailDomain.getPrimaryKeyColumn());
        config.setArchiveType(detailDomain.getArchiveType());
        config.setFilterDefaultValue(detailDomain.getFilterDefaultValue());
        config.setProps(detailDomain.getProps());
        config.setEnableScan(detailDomain.isEnableScan());
        // 递归 children
        if (detailDomain.getChildren() != null) {
            List<TaskDetailConfig> children = detailDomain.getChildren().stream()
                    .map(this::convertToTaskDetailConfig)
                    .collect(Collectors.toList());
            config.setChildren(children);
        }
        return config;
    }

    /**
     * relationConditions 字符串转 List<ColumnRelation>
     * 例如: "table1.col1=table2.col2 and table1.col3=table2.col4"
     */
    private List<ColumnRelation> parseRelationConditions(ArchiveUpdateContext context, ArchiveTaskDetailDomain taskDetail, ArchiveTaskDetailDomain parent) {
        if (taskDetail.isRoot()) {
            return Collections.emptyList();
        }
        if (StringUtils.isEmpty(taskDetail.getRelationConditions())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "关联条件不能为空");
        }
        String relationConditions = taskDetail.getRelationConditions();
        // 按and分割多个条件
        String[] conditionParts = relationConditions.split("(?i)\\s+and\\s+");
        if (conditionParts.length == 0) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "无效的关联条件格式");
        }
        for (String condition : conditionParts) {
            condition = condition.trim();
            // 校验每个条件必须包含等号
            if (!condition.contains("=")) {
                throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "关联条件必须使用等值关联: " + condition);
            }
        }
        String datasourceName = context.getArchiveGlobal().getDatasourceName();
        String databaseName = context.getArchiveGlobal().getDatabaseName();
        String appName = context.getArchiveGlobal().getAppName();
        List<ColumnRelation> list = new ArrayList<>();
        String[] relations = relationConditions.split("and");
        for (String relation : relations) {
            String[] parts = relation.trim().split("=");
            if (parts.length == 2) {
                String left = parts[0].trim().replace("`", "");
                String right = parts[1].trim().replace("`", "");
                String[] leftArr = left.split("\\.");
                String[] rightArr = right.split("\\.");
                if (leftArr.length == 2 && rightArr.length == 2) {
                    ColumnsResponse table1 = context.getColumnsResponseMap().computeIfAbsent(leftArr[0], k -> queryColumns(leftArr[0], datasourceName, databaseName, appName));
                    if (table1 == null) {
                        throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "当前节点不支持使用表，或者表不存在 " + leftArr[0]);
                    }
                    ColumnsResponse table2 = context.getColumnsResponseMap().computeIfAbsent(rightArr[0], k -> queryColumns(rightArr[0], datasourceName, databaseName, appName));
                    if (table2 == null) {
                        throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "当前节点不支持使用表，或者表不存在 " + rightArr[0]);
                    }
                    ColumnRelation cr = new ColumnRelation();
                    if (taskDetail.getTableName().equalsIgnoreCase(leftArr[0])
                            && parent.getTableName().equalsIgnoreCase(rightArr[0])) {
                        checkColumnsExists(context, leftArr[0], leftArr[1]);
                        checkColumnsExists(context, rightArr[0], rightArr[1]);
                        cr.setColumnName(leftArr[1]);
                        cr.setRelationColumn(rightArr[1]);
                    } else if (parent.getTableName().equalsIgnoreCase(leftArr[0])
                            && taskDetail.getTableName().equalsIgnoreCase(rightArr[0])) {
                        checkColumnsExists(context, leftArr[0], leftArr[1]);
                        checkColumnsExists(context, rightArr[0], rightArr[1]);
                        cr.setColumnName(rightArr[1]);
                        cr.setRelationColumn(leftArr[1]);
                    } else {
                        throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "关联关系必须包含本表和父表");
                    }
                    list.add(cr);
                }
            }
        }
        return list;
    }


    /**
     * 校验SQL WHERE条件语法和字段引用
     *
     * @param whereCondition SQL WHERE条件（不包含WHERE关键字）
     * @param tableName      表名
     * @param tableColumns   表的列信息
     */
    private void validateSqlWhereCondition(String whereCondition, String tableName,
                                           List<DatabaseColumn> tableColumns) {
        if (whereCondition == null || whereCondition.trim().isEmpty()) {
            return;
        }
        try {
            Expression extra = CCJSqlParserUtil.parseCondExpression(whereCondition);
            Set<String> columns = new HashSet<>();
            extra.accept(new ExpressionDeParser() {
                @Override
                public void visit(Column column) {
                    // 只取列名（不含表名），如需表名可用 column.getFullyQualifiedName()
                    columns.add(column.getColumnName().replace("`", ""));
                }
            });
            Set<String> tableColumnNames = tableColumns.stream().map(DatabaseColumn::getName).collect(Collectors.toSet());
            List<String> columnList = columns.stream().filter(columnName -> !tableColumnNames.contains(columnName)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(columnList)) {
                throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "WHERE条件字段不存在: " + columnList);
            }
        } catch (Exception e) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "WHERE条件语法错误: " + e.getMessage());
        }
    }

    /**
     * 从SQL条件中提取列引用
     * 注意：这个简化实现不处理复杂的SQL函数和子查询，仅处理基本的列引用
     *
     * @param whereCondition SQL WHERE条件
     * @return 列名集合
     */
    private Set<String> extractColumnReferences(String whereCondition) {
        Set<String> columns = new HashSet<>();
        // 使用正则表达式匹配可能的列引用
        // 考虑形如 column, table.column 的引用，但我们只关心列名部分
        String columnPattern = "\\b([a-zA-Z0-9_]+)(?:\\.[a-zA-Z0-9_]+)?\\s*(?:[=<>!]|\\bIN\\b|\\bLIKE\\b|\\bIS\\b|\\bBETWEEN\\b)";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(columnPattern, java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = pattern.matcher(whereCondition);
        while (matcher.find()) {
            // 获取列名部分
            String columnCandidate = matcher.group(1);
            // 跳过SQL关键字
            if (!isSqlKeyword(columnCandidate)) {
                columns.add(columnCandidate);
            }
        }
        // 匹配点号后的列引用，处理 table.column 形式
        String dotColumnPattern = "\\b[a-zA-Z0-9_]+\\.([a-zA-Z0-9_]+)\\b";
        pattern = java.util.regex.Pattern.compile(dotColumnPattern);
        matcher = pattern.matcher(whereCondition);
        while (matcher.find()) {
            columns.add(matcher.group(1));
        }
        return columns;
    }

    /**
     * 检查字符串是否是SQL关键字
     *
     * @param word 待检查的字符串
     * @return 是否是SQL关键字
     */
    private boolean isSqlKeyword(String word) {
        // 常见的SQL关键字列表，不完整，但包含了WHERE条件中常见的关键字
        Set<String> sqlKeywords = new HashSet<>(Arrays.asList(
                "select", "from", "where", "and", "or", "not", "in", "between", "like", "is", "null",
                "true", "false", "case", "when", "then", "else", "end", "as", "on", "join", "inner",
                "outer", "left", "right", "full", "cross", "union", "intersect", "except", "all",
                "distinct", "top", "limit", "offset", "order", "by", "group", "having", "with"
        ));
        return sqlKeywords.contains(word.toLowerCase());
    }


    private void checkColumnsExists(ArchiveUpdateContext context, String table, String column) {
        String datasourceName = context.getArchiveTaskDomain().getDatasourceName();
        String databaseName = context.getArchiveTaskDomain().getDatabaseName();
        String appName = context.getArchiveGlobal().getAppName();
        ColumnsResponse columnsResponse = context.getColumnsResponseMap().computeIfAbsent(table, (s) -> queryColumns(table, datasourceName, databaseName, appName));
        if (columnsResponse == null || columnsResponse.getColumns() == null) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "未查询到表结构: " + table);
        }
        boolean found = columnsResponse.getColumns().stream().anyMatch(col -> col.getName().equalsIgnoreCase(column));
        if (!found) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), String.format("表[%s]中不存在字段[%s]", table, column));
        }
    }


    public List<String> getUncheckedTableNames(ArchiveTaskDomain domain) {
        List<String> result = new ArrayList<>();
        if (domain == null || domain.getTaskDetails() == null) {
            return result;
        }
        for (ArchiveTaskDetailDomain detail : domain.getTaskDetails()) {
            collectUncheckedTableNames(detail, result);
        }
        return result;
    }

    private boolean collectUncheckedTableNames(ArchiveTaskDetailDomain detail, List<String> result) {
        if (detail == null) return false;
        if (!Boolean.TRUE.equals(detail.isChecked())) {
            result.add(detail.getTableName());
            return true;
        }
        if (detail.getChildren() != null) {
            for (ArchiveTaskDetailDomain child : detail.getChildren()) {
                if (collectUncheckedTableNames(child, result)) {
                    result.add(detail.getTableName());
                }
            }
        }
        return false;
    }

    public Result<PreviewResponse> preview(ArchiveTaskDomain archiveTaskDomain, String appName, String currentKey) {
        PreviewRequest previewRequest = new PreviewRequest();
        ArchiveUpdateContext archiveUpdateContext = new ArchiveUpdateContext();
        archiveUpdateContext.setArchiveTaskDomain(archiveTaskDomain);
        if (CollectionUtils.isEmpty(archiveTaskDomain.getTaskDetails())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "缺乏节点配置");
        }
        archiveTaskDomain.getTaskDetails().get(0).setRoot(true);
        ArchiveGlobal archiveGlobal = new ArchiveGlobal().setAppName(appName).setDatasourceName(archiveTaskDomain.getDatasourceName())
                .setDatabaseName(archiveTaskDomain.getDatabaseName());
        archiveUpdateContext.setArchiveGlobal(archiveGlobal);
        setTablesForContext(archiveTaskDomain, archiveGlobal, archiveUpdateContext);
        validateTaskConfig(archiveUpdateContext);
        TaskConfig taskConfig = convertToTaskConfig(archiveTaskDomain);
        previewRequest.setTaskConfig(taskConfig);
        previewRequest.setCurrentKey(currentKey);
        log.info("preview request appName {}  request {}", appName, JSON.toJSONString(previewRequest));
        return archiveSDKIntegrationApi.fetchPreview(appName, previewRequest);
    }

}
