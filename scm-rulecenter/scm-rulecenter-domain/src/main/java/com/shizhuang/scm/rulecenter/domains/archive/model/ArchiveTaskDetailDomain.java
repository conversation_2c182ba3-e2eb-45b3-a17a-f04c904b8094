package com.shizhuang.scm.rulecenter.domains.archive.model;

import com.shizhuang.duapp.scp.framework.admin.sdk.model.ColumnRelation;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ArchiveTaskDetailDomain implements Serializable {

    private String tableName;

    private String conditions;

    private Integer indexType;

    private String indexColumn;

    /**
     * asc desc
     */
    private String orderBy;

    /**
     * 索引字段的值-开始
     */
    private Long indexStart;

    /**
     * 索引字段的值-结束
     */
    private Long indexEnd;

    /**
     * 保留天数
     */
    private Integer reserveDays;

    /**
     * 生效插件
     */
    private List<String> plugins;

    /**
     * 父级节点表
     */
    private String parentTable;

    /**
     * 关联关系
     */
    private String relationConditions;

    /**
     * 子节点
     */
    private List<ArchiveTaskDetailDomain> children;

    /**
     * 是否归档
     */
    private Boolean archive;

    /**
     * 主键字段
     */
    private String primaryKeyColumn;

    /**
     * 1 先归档子节点 2 先归档父节点
     */
    private Integer archiveType;

    /**
     * 默认值过滤 0||null 不过滤 1 过滤
     */
    private Integer filterDefaultValue;

    public String getKey() {
        return getTableName();
    }

    /**
     * 是否为根节点
     */
    private boolean root;

    private String parentKey;

    /**
     * 是否已校验
     */
    private boolean checked = false;

    private List<ColumnRelation> columnRelations;

    private Map<String, Object> props = new HashMap<>();

    /**
     * 是否开启扫描模式
     */
    private boolean enableScan;
}
