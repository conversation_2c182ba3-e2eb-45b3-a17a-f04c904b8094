package com.shizhuang.scm.rulecenter.domains.meta.repository.transfer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.poizon.module.framework.core.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.MapperConfig;

import java.util.List;
import java.util.Objects;

@MapperConfig(componentModel = "spring")
public interface BaseTransfer<S, T> {

    /**
     * json字符串转JSONObject
     *
     * @param jsonString
     * @return
     * @throws Exception
     */
    default JSONObject toJsonObject(String jsonString) {
        if (StringUtils.isNotBlank(jsonString)) {
            return JSONObject.parseObject(jsonString);
        }
        return new JSONObject();
    }


    /**
     * JSONObject 转jsonStr
     *
     * @param json
     * @return
     * @throws Exception
     */
    default String toJSONString(JSONObject json) {
        if (Objects.nonNull(json)) {
            return JSON.toJSONString(json);
        }
        return "{}";
    }

    S dToS(T data);


    T sToT(S entity);


    default List<S> toSourceList(List<T> doList) {
        if (CollectionUtils.isEmpty(doList)) {
            return null;
        }
        return CommonUtils.mapTo(doList, this::dToS);
    }

    default List<T> toTargetList(List<S> doList) {
        if (CollectionUtils.isEmpty(doList)) {
            return null;
        }
        return CommonUtils.mapTo(doList, this::sToT);
    }
}
