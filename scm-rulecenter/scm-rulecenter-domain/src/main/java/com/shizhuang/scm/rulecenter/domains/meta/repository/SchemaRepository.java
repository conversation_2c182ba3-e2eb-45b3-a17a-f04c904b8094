package com.shizhuang.scm.rulecenter.domains.meta.repository;

import com.github.pagehelper.PageInfo;
import com.shizhuang.scm.rulecenter.api.dto.SchemaMetaDTO;
import com.shizhuang.scm.rulecenter.api.query.SchemaPageQuery;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.EntireSchemaVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaDimPriorityEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;

import java.util.List;

/**
 * 方案Repository
 */
public interface SchemaRepository {

    void save(SchemaMetaAggregate schemaMetaAggregate);

    void update(SchemaMetaAggregate schemaMetaAggregate, SchemaMetaAggregate deployedSchemaMetaAggregate);

    void deploySchema(SchemaMetaAggregate schemaMetaAggregate);

    void save(SchemaDimPriorityEditContext schemaDimPriorityEditContext);

    List<DimPriorityVO> querySchemaDimPriority(String schemaCode);

    SchemaEntity getSchemaEntityByCode(String schemaCode, SchemaAggQueryOption option);

    ElementMetaEntity getCustomElementByCode(String schemaCode, String elementCode);
    SchemaMetaAggregate getSchemaAggregateByCode(String schemaCode, SchemaAggQueryOption option);

    PageInfo<SchemaMetaDTO> queryByPage(SchemaPageQuery schemaPageQuery);

    int deleteSchema(String schemaCode);

    List<String> getRelatedSchemaByElement(String schemaCode, String elementCode);

    List<FieldMappingVO> getFieldMappingVOS(String schemaCode,Integer version);

    void override(EntireSchemaVO entireSchemaVO);

    void removeSchemaCache(String schemaCode);
}
