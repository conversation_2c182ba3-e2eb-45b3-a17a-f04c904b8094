package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.ElementMetaAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementCreateContext;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

@Component
public class ElementCreateHandler implements IBasicHandler<ElementCreateContext> {
    @Override
    public void doHandle(ElementCreateContext elementCreateContext) {
        ElementCreateCmd elementCreateCmd = elementCreateContext.getElementCreateCmd();
        elementCreateContext.setElementMetaEntity(ElementMetaAssembler.transEntity(elementCreateCmd));
    }
}
