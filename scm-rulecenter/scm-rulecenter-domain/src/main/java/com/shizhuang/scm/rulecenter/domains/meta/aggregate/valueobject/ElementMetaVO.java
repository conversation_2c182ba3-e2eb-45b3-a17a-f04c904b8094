package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ElementMetaVO {

    @ApiModelProperty("元素code")
    String elementCode;

    @ApiModelProperty("元素名称")
    String elementName;

    @ApiModelProperty("元素来源类型")
    Integer elementSourceType;

    @ApiModelProperty("元素来源类型名称")
    String elementSourceTypeName;

    @ApiModelProperty("元素取值类型")
    Integer valueRangeType;

    @ApiModelProperty("元素取值类型名称")
    String valueRangeTypeName;

}
