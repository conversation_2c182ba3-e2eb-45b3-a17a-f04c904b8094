package com.shizhuang.scm.rulecenter.domains.inst.service;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import lombok.Data;

@Data
public class ParamInstTempDimRes {

    public final String redisDimKey;
    public final ParamInstanceDOWithBLOBs paramInstanceDO;

    public ParamInstTempDimRes(String redisDimKey, ParamInstanceDOWithBLOBs paramInstanceDO) {
        this.redisDimKey = redisDimKey;
        this.paramInstanceDO = paramInstanceDO;
    }

}
