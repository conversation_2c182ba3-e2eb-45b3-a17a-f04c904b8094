package com.shizhuang.scm.rulecenter.domains.meta.context;

import com.shizhuang.scm.rulecenter.api.cmd.SchemaEditCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class SchemaEditContext {

    /**
     * 编辑方案指令
     */
    SchemaEditCmd schemaEditCmd;

    /**
     * 变更操作人
     */
    String optUser;

    /**
     * 本次编辑的方案聚合
     */
    SchemaMetaAggregate editSchemaMetaAggregate;

    /**
     * 原来在线上生效的方案聚合
     */
    SchemaMetaAggregate deployedSchemaMetaAggregate;

    /**
     * 未上线的最大版本方案元素聚合
     */
    SchemaMetaAggregate largestNoDeploySchemaMetaAggregate;

    /**
     * 判断是否有元素被删除
     */
    Boolean hasDeleteElement;

}
