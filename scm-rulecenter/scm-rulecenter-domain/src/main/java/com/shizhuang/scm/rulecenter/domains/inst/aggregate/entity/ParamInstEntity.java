package com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity;


import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ElementInstVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@Setter
public class ParamInstEntity {

    /**
     * 方案code
     */
    private String schemaCode;

    /**
     * 维度,"#"号分割
     */
    private String dimension;

    /**
     * 维度值"#"号分割
     */
    private String dimensionKey;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否生效 0 否 1 是
     */
    private Integer enabled;

    /**
     * 是否删除 0 否 1 是
     */
    private Byte isDel;

    /**
     * 维度元素列表
     */
    List<ElementInstVO> dimensions;

    /**
     * 值元素列表
     */
    List<ElementInstVO> params;


    /**
     * 操作人
     */
    String optUser;


    /**
     * 参数聚合id
     */
    String aggId;

    /**
     * 创建人
     */
    String creator;

    /**
     * 创建时间
     */
    Date ctime;

    /**
     * 最后操作人
     */
    String modifier;

    /**
     * 最后更新时间
     */
    Date mtime;

    public static ParamInstEntity init(ParamInstAddCmd paramInstAddCmd, List<SchemaElementEntity> dimensions, List<SchemaElementEntity> params) {

        Map<String, Object> paramValueMap = paramInstAddCmd.getParamMap().stream()
                .collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));

        List<ElementInstVO> dimensionVOs = extract(dimensions, paramValueMap);
        List<ElementInstVO> paramVOs = extract(params, paramValueMap);
        String schemaCode = paramInstAddCmd.getSchemaCode();
        return buildParamInstEntity(schemaCode, dimensionVOs, paramVOs);
    }

    private static ParamInstEntity buildParamInstEntity(String schemaCode, List<ElementInstVO> dimensionVOs, List<ElementInstVO> paramVOs) {
        ParamInstEntity paramInstEntity = new ParamInstEntity();
        paramInstEntity.setSchemaCode(schemaCode);
        paramInstEntity.setVersion(CommonConstant.ONE);
        paramInstEntity.setEnabled(CommonConstant.ONE);
        paramInstEntity.setIsDel(CommonConstant.ZERO.byteValue());
        paramInstEntity.setDimensions(dimensionVOs);
        paramInstEntity.setParams(paramVOs);
        paramInstEntity.setDimension(dimensionVOs.stream().map(ElementInstVO::getElementCode).collect(Collectors.joining("#")));
        paramInstEntity.setDimensionKey(dimensionVOs.stream().map(ElementInstVO::getElementValue).collect(Collectors.joining("#")));
        return paramInstEntity;
    }

    private static List<ElementInstVO> extract(List<SchemaElementEntity> element, Map<String, Object> paramValueMap) {
        List<ElementInstVO> elementInstVOS = new ArrayList<>();
        for (SchemaElementEntity schemaElement : element) {
            String elementCode = schemaElement.getElementCode();
            if (!paramValueMap.containsKey(elementCode)) {
                continue;
            }
            String elementValue = String.valueOf(paramValueMap.get(elementCode));
            ElementInstVO dimensionVO = ElementInstVO.builder().elementCode(elementCode).elementValue(elementValue).build();
            elementInstVOS.add(dimensionVO);
        }
        return elementInstVOS;
    }

    public List<ElementInstVO> getAllElementInst() {
        if (CollectionUtils.isNotEmpty(params)) {
            return Stream.of(dimensions, params).flatMap(list -> list.stream()).collect(Collectors.toList());
        }
        return dimensions;
    }

}
