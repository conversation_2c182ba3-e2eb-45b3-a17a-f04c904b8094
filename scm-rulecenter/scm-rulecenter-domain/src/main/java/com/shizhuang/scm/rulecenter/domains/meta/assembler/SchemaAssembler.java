package com.shizhuang.scm.rulecenter.domains.meta.assembler;

import com.shizhuang.scm.rulecenter.api.cmd.SchemaCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaDimPriorityEditCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaEditCmd;
import com.shizhuang.scm.rulecenter.api.constant.SchemaStatusEnum;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;

import java.util.List;
import java.util.stream.Collectors;

public class SchemaAssembler {

    public static SchemaEntity toEntity(SchemaCreateCmd schemaCreateCmd){
        SchemaEntity schema = new SchemaEntity();
        schema.setSchemaCode(schemaCreateCmd.getSchemaCode());
        schema.setSchemaName(schemaCreateCmd.getSchemaName());
        schema.setSchemaDesc(schemaCreateCmd.getSchemaDesc());
        schema.setStatus(SchemaStatusEnum.DRAFT.getStatus());
        schema.setVersion(CommonConstant.ONE);
        schema.setCreator(schemaCreateCmd.getCreator());
        schema.setIsLargestVersion(CommonConstant.ONE);
        schema.setIsLatestVersion(CommonConstant.ONE);
        schema.setIsDel(CommonConstant.ZERO);
        schema.setMenu(schemaCreateCmd.getMenu());
        schema.setRedisCacheExpireTime(schemaCreateCmd.getRedisCacheExpireTime());
        schema.setProjectName(schemaCreateCmd.getProjectName());
        schema.setActionFeature(schemaCreateCmd.getActionFeature());
        return schema;
    }

    public static SchemaEntity toEntity(SchemaEditCmd schemaEditCmd){
        SchemaEntity schema = new SchemaEntity();
        schema.setSchemaCode(schemaEditCmd.getSchemaCode());
        schema.setSchemaName(schemaEditCmd.getSchemaName());
        schema.setSchemaDesc(schemaEditCmd.getSchemaDesc());
        schema.setStatus(SchemaStatusEnum.DRAFT.getStatus());
        schema.setIsLargestVersion(CommonConstant.ONE);
        schema.setIsDel(CommonConstant.ZERO);
        schema.setMenu(schemaEditCmd.getMenu());
        schema.setProjectName(schemaEditCmd.getProjectName());
        schema.setRedisCacheExpireTime(schemaEditCmd.getRedisCacheExpireTime());
        schema.setActionFeature(schemaEditCmd.getActionFeature());
        return schema;
    }


    public static List<DimPriorityVO> toVO(SchemaDimPriorityEditCmd schemaDimPriorityEditCmd){
        String schemaCode = schemaDimPriorityEditCmd.getSchemaCode();
        return schemaDimPriorityEditCmd.getPriority().stream().map(dto -> {
            DimPriorityVO dimPriorityVO = new DimPriorityVO();
            dimPriorityVO.setSchemaCode(schemaCode);
            String dimension = String.join("#", dto.getAtomicDimension());
            dimPriorityVO.setDimension(dimension);
            dimPriorityVO.setPriority(dto.getPriority());
            dimPriorityVO.setIsDel(CommonConstant.ZERO.byteValue());
            dimPriorityVO.setVersion(CommonConstant.ONE);
            return dimPriorityVO;
        }).collect(Collectors.toList());
    }


}
