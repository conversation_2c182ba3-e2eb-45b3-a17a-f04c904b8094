package com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;


@Builder
@Getter
@Setter
public class ElementInstVO {

    /**
     * 元素code
     */
    @NotEmpty
    String elementCode;

    /**
     * 元素name，作为入参时不需要传，作为返回值时该字段会返回
     */
    String elementName;

    /**
     * 元素值
     */
    @NotEmpty
    String elementValue;


    /**
     * 元素值描述
     */
    @NotEmpty
    String elementDesc;
}
