package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.assembler.ParamInstAssembler;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstAddOrUpdateContext;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ParamInstAddOrUpdatePreProcessHandler implements IBasicHandler<ParamInstAddOrUpdateContext> {

    @Resource
    SchemaRepository schemaRepository;

    @Override
    public void doHandle(ParamInstAddOrUpdateContext paramInstAddOrUpdateContext) {
        ParamInstAddCmd paramInstAddCmd = paramInstAddOrUpdateContext.getParamInstAddCmd();
        String schemaCode = paramInstAddCmd.getSchemaCode();
        SchemaMetaAggregate schemaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLargestVersion());
        paramInstAddOrUpdateContext.setSchemaAggregate(schemaAggregate);
        List<String> elementCodes = schemaAggregate.getAllElements().stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        List<ElementInstAddCmd> paramMap = paramInstAddCmd.getParamMap();
        paramMap = paramMap.stream()
                .filter(cmd -> elementCodes.contains(cmd.getElementCode()))
                .filter(cmd -> cmd.getElementValue() != null)
                .filter(cmd -> StringUtils.isNotBlank(String.valueOf(cmd.getElementValue())))
                .filter(cmd -> {
                    if (cmd.getElementValue() instanceof List) {
                        return CollectionUtils.isNotEmpty((List) cmd.getElementValue());
                    }
                    return true;
                })
                .collect(Collectors.toList());
        paramInstAddCmd.setParamMap(paramMap);
        String aggId = schemaCode + "_" + System.currentTimeMillis();
        paramInstAddOrUpdateContext.setAggId(aggId);
        List<ParamInstEntity> paramInstEntities = ParamInstAssembler.getParamInstEntities(paramMap, schemaAggregate, aggId);
        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        paramInstEntities.forEach(e -> {
            e.setCreator(operationUser.getUserNameUnBlank());
            e.setModifier(operationUser.getUserNameUnBlank());
            e.setCtime(new Date());
            e.setMtime(new Date());
        });
        paramInstAddOrUpdateContext.setParamInstEntities(paramInstEntities);
        paramInstAddOrUpdateContext.setOperatorId(String.valueOf(operationUser.getUserId()));
        paramInstAddOrUpdateContext.setOperatorName(operationUser.getUserNameUnBlank());
    }
}
