package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ParamDimExtractHandler implements IBasicHandler<ParamInstCreateContext> {

    @Resource
    ParamInstRepository paramInstRepository;



    @Override
    public void doHandle(ParamInstCreateContext paramInstCreateContext) {
        ParamInstAddCmd paramInstAddCmd = paramInstCreateContext.getParamInstAddCmd();
        String schemaCode = paramInstAddCmd.getSchemaCode();




    }
}
