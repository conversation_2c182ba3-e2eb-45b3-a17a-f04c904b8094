package com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity;

import lombok.Data;
import org.javers.core.metamodel.annotation.DiffInclude;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.util.Date;

@Data
public class ParamInstAggEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 方案code
     */
    private String schemaCode;

    /**
     * 维度,"#"号分割
     */
    private String dimension;

    /**
     * 维度值"#"号分割
     */
    private String dimensionKey;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否生效 0 否 1 是
     */
    private Integer enabled;

    /**
     * 是否删除 0 否 1 是
     */
    private Byte isDel;

    /**
     * 参数聚合id
     */
    @Id
    @PropertyName("aggId")
    private String aggId;

    /**
     * 创建人信息
     */
    private String creator;

    /**
     * 维度元素1
     */
    @DiffInclude
    @PropertyName("dimension1")
    private String dimension1;

    /**
     * 维度元素2
     */
    @DiffInclude
    @PropertyName("dimension2")
    private String dimension2;

    /**
     * 维度元素3
     */
    @DiffInclude
    @PropertyName("dimension3")
    private String dimension3;

    /**
     * 维度元素4
     */
    @DiffInclude
    @PropertyName("dimension4")
    private String dimension4;

    /**
     * 维度元素5
     */
    @DiffInclude
    @PropertyName("dimension5")
    private String dimension5;

    /**
     * 维度元素6
     */
    @DiffInclude
    @PropertyName("dimension6")
    private String dimension6;

    /**
     * 维度元素7
     */
    @DiffInclude
    @PropertyName("dimension7")
    private String dimension7;

    /**
     * 维度元素8
     */
    @DiffInclude
    @PropertyName("dimension8")
    private String dimension8;

    /**
     * 维度元素9
     */
    @DiffInclude
    @PropertyName("dimension9")
    private String dimension9;

    /**
     * 维度元素10
     */
    @DiffInclude
    @PropertyName("dimension10")
    private String dimension10;

    /**
     * 参数元素1
     */
    @DiffInclude
    @PropertyName("param1")
    private String param1;

    /**
     * 参数元素2
     */
    @DiffInclude
    @PropertyName("param2")
    private String param2;

    /**
     * 参数元素3
     */
    @DiffInclude
    @PropertyName("param3")
    private String param3;

    /**
     * 参数元素4
     */
    @DiffInclude
    @PropertyName("param4")
    private String param4;

    /**
     * 参数元素5
     */
    @DiffInclude
    @PropertyName("param5")
    private String param5;

    /**
     * 参数元素6
     */
    @DiffInclude
    @PropertyName("param6")
    private String param6;

    /**
     * 参数元素7
     */
    @DiffInclude
    @PropertyName("param7")
    private String param7;

    /**
     * 参数元素8
     */
    @DiffInclude
    @PropertyName("param8")
    private String param8;

    /**
     * 参数元素9
     */
    @DiffInclude
    @PropertyName("param9")
    private String param9;

    /**
     * 参数元素10
     */
    @DiffInclude
    @PropertyName("param10")
    private String param10;

    /**
     * 参数元素11
     */
    @DiffInclude
    @PropertyName("param11")
    private String param11;

    /**
     * 参数元素12
     */
    @DiffInclude
    @PropertyName("param12")
    private String param12;

    /**
     * 参数元素13
     */
    @DiffInclude
    @PropertyName("param13")
    private String param13;

    /**
     * 参数元素14
     */
    @DiffInclude
    @PropertyName("param14")
    private String param14;

    /**
     * 参数元素15
     */
    @DiffInclude
    @PropertyName("param15")
    private String param15;

    /**
     * 参数元素16
     */
    @DiffInclude
    @PropertyName("param16")
    private String param16;

    /**
     * 参数元素17
     */
    @DiffInclude
    @PropertyName("param17")
    private String param17;

    /**
     * 参数元素18
     */
    @DiffInclude
    @PropertyName("param18")
    private String param18;

    /**
     * 参数元素19
     */
    @DiffInclude
    @PropertyName("param19")
    private String param19;

    /**
     * 参数元素20
     */
    @DiffInclude
    @PropertyName("param20")
    private String param20;

}
