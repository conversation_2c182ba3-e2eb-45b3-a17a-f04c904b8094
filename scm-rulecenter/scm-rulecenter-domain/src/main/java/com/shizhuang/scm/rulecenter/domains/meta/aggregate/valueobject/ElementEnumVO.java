package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@NoArgsConstructor
public class ElementEnumVO {

    /**
     * 值
     */
    String value;

    /**
     * 描述
     */
    String desc;

    /**
     * 前端用到的字段 与desc一样
     */
    String lebel;

    /**
     * 级联场景使用，适配前端场景
     */
    List<ElementEnumVO> children;

    public ElementEnumVO(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
