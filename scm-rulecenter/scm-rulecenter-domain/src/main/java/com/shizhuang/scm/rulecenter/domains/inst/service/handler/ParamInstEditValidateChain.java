package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ParamInstElementValueValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.PramInstKeyConflictValidator;
import com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext.*;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ParamInstEditValidateChain implements IBasicHandler<ParamInstEditContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(ParamInstEditContext paramInstEditContext) {
        ComplexResult result = FluentValidator.checkAll()
                .on(paramInstEditContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 1、编辑参数时，维度是不可变的
                ValidatorHandlerFactory.build(ctx -> validateDimImmutable((ParamInstEditContext) ctx)),

                // 2、当前的入参需要满足于元素约定好的取值范围
                ValidatorHandlerFactory.build(ctx -> validateElementValue((ParamInstEditContext) ctx)),

                // 3、编辑参数时，若存在新增场景，则校验之前是否存在唯一键冲突的数据记录
                ValidatorHandlerFactory.build(ctx -> validateUniqueKey((ParamInstEditContext) ctx)),

                // 4、添加方案相关下的自定义校验逻辑
                ValidatorHandlerFactory.build(ctx -> validateCustom((ParamInstEditContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
        SpotCheckConfigPriceValidator spotCheckConfigPriceValidator = applicationContext.getBean(SpotCheckConfigPriceValidator.class);
        SpotCheckConfigRatioRuleValidator spotCheckConfigRatioRuleValidator = applicationContext.getBean(SpotCheckConfigRatioRuleValidator.class);
        SpotCheckConfigNumberRuleValidator spotCheckConfigNumberRuleValidator = applicationContext.getBean(SpotCheckConfigNumberRuleValidator.class);
        extMap.put("spot-check-config", Lists.newArrayList(spotCheckConfigPriceValidator, spotCheckConfigRatioRuleValidator, spotCheckConfigNumberRuleValidator));

        RangeOverLapValidator rangeOverLapValidator = applicationContext.getBean(RangeOverLapValidator.class);
        extMap.put("brand-direct-boxinfo-rule", Lists.newArrayList(rangeOverLapValidator));

    }

    Map<String, List<ParamInstEditCustomValidatorExt>> extMap = new HashMap<>();


    private ValidationError validateCustom(ParamInstEditContext ctx) {
        List<ParamInstEditCustomValidatorExt> paramInstEditCustomValidatorExts = extMap.get(ctx.getParamInstEditCmd().getSchemaCode());
        if (CollectionUtils.isEmpty(paramInstEditCustomValidatorExts)) {
            return null;
        }
        for (ParamInstEditCustomValidatorExt ext : paramInstEditCustomValidatorExts) {
            ValidationError validate = ext.validate(ctx);
            if (validate != null) {
                return validate;
            }
        }
        return null;
    }

    private ValidationError validateUniqueKey(ParamInstEditContext ctx) {
        List<ParamInstanceDOWithBLOBs> oldParamInstanceDOs = ctx.getOldParamInstanceDOs();
        List<ParamInstEntity> paramInstEntities = ctx.getParamInstEntities();
        List<String> collect = oldParamInstanceDOs.stream().map(ParamInstanceDOWithBLOBs::getDimensionKey).collect(Collectors.toList());
        PramInstKeyConflictValidator validator = applicationContext.getBean(PramInstKeyConflictValidator.class);
        for (ParamInstEntity paramInstEntity : paramInstEntities) {
            if (!collect.contains(paramInstEntity.getDimensionKey())) {
                ValidationError validationError = validator.validate(paramInstEntity);
                if (Objects.nonNull(validationError)) {
                    return validationError;
                }
            }
        }
        return null;
    }

    private ValidationError validateElementValue(ParamInstEditContext ctx) {
        ParamInstElementValueValidator validator = applicationContext.getBean(ParamInstElementValueValidator.class);
        ParamInstEditCmd paramInstEditCmd = ctx.getParamInstEditCmd();
        SchemaMetaAggregate schemaMetaAggregate = ctx.getSchemaMetaAggregate();
        List<ElementInstAddCmd> cmdList = paramInstEditCmd.getParamMap();
        Map<String, Object> paramMap = cmdList.stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        Map<String, String> elementCodeToNameMap = schemaMetaAggregate.getElementCodeToNameMap();
        return validator.validate(paramInstEditCmd.getSchemaCode(), paramMap,elementCodeToNameMap);
    }

    private ValidationError validateDimImmutable(ParamInstEditContext ctx) {
//        ParamInstDimensionValidator validator = applicationContext.getBean(ParamInstDimensionValidator.class);
//        String newDimension = ctx.extractDimensionKey();
//        ParamInstanceDO oldParamInstanceDO = ctx.getOldParamInstanceDO();
//        String oldDimension = oldParamInstanceDO.getDimensionKey();
//        return validator.validate(oldDimension, newDimension);
        return null;
    }

}
