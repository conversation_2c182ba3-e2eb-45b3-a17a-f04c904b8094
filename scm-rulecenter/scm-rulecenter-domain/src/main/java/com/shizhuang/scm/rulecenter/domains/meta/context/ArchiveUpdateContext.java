package com.shizhuang.scm.rulecenter.domains.meta.context;

import com.shizhuang.duapp.scp.framework.admin.sdk.response.ColumnsResponse;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveGlobal;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveTaskDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class ArchiveUpdateContext implements Serializable {

    private ArchiveTaskDomain archiveTaskDomain;

    private ArchiveGlobal archiveGlobal;

    private Map<String, ColumnsResponse> columnsResponseMap = new HashMap<>();

}
