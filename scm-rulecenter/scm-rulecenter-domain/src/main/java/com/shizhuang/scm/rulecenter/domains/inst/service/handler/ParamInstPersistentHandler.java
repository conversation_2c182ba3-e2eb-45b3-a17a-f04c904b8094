package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class ParamInstPersistentHandler implements IBasicHandler<ParamInstCreateContext> {

    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    ParamInstDomainService paramInstDomainService;

    @Override
    public void doHandle(ParamInstCreateContext paramInstCreateContext) {

        List<ParamInstEntity> paramInstEntities = paramInstCreateContext.getParamInstEntities();
        //获取元素字段映射map
        SchemaMetaAggregate schemaAggregate = paramInstCreateContext.getSchemaAggregate();
        Map<String, String> elementFieldMap = schemaAggregate.getElementFieldMap();
        paramInstEntities.forEach(p-> paramInstRepository.save(p,elementFieldMap));
        //根据元素字段映射map存储参数实例

        paramInstDomainService.recordCreateInstBizLog(paramInstCreateContext);
    }
}
