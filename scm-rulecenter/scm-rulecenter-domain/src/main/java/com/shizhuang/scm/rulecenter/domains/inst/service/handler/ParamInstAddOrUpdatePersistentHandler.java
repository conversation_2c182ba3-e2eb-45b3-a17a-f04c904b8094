package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstAddOrUpdateContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class ParamInstAddOrUpdatePersistentHandler implements IBasicHandler<ParamInstAddOrUpdateContext> {
    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    ParamInstDomainService paramInstDomainService;

    @Override
    public void doHandle(ParamInstAddOrUpdateContext paramInstAddOrUpdateContext) {

        List<ParamInstEntity> paramInstEntities = paramInstAddOrUpdateContext.getParamInstEntities();
        //获取元素字段映射map
        SchemaMetaAggregate schemaAggregate = paramInstAddOrUpdateContext.getSchemaAggregate();
        Map<String, String> elementFieldMap = schemaAggregate.getElementFieldMap();
        Map<String, String> fieldElementNameMap = schemaAggregate.getFieldElementNameMap();
        paramInstEntities.forEach(p -> {
            ParamInstanceDOWithBLOBs enabledByDim = paramInstRepository.getEnabledByDim(p.getSchemaCode(), p.getDimension(), p.getDimensionKey());
            if (Objects.isNull(enabledByDim)) {
                paramInstRepository.save(p, elementFieldMap);
            } else {
                ParamInstanceDOWithBLOBs newParamInstanceDO = paramInstRepository.initParamInst(p, elementFieldMap);
                newParamInstanceDO.setVersion(p.getVersion());
                paramInstRepository.update(newParamInstanceDO, enabledByDim.getVersion(), paramInstAddOrUpdateContext.getOperatorName(), fieldElementNameMap);
            }
        });
        //根据元素字段映射map存储参数实例

        paramInstDomainService.recordAddOrUpdateInstBizLog(paramInstAddOrUpdateContext);

    }
}
