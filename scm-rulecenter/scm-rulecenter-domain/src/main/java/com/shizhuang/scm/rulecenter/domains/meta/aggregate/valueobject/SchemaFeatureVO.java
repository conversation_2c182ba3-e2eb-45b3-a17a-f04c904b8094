package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SchemaFeatureVO {

    /**
     * 缓存超时时间
     */
    Integer redisCacheExpireTime;


    /**
     * 页面操作配置（用户可以选择是否要导入导出功能等等）
     */
    List<String> actionFeature;


    /**
     * 优先级排序类型 0按自定义优先级排序，1按业务字段排序
     */
    Integer dimPriType;

    /**
     * 排序字段
     */
    String orderField;

    /**
     * 排序类型 asc正序 desc倒序
     */
    String orderType;


    /**
     * 级联配置（用户可以选择是否要导入导出功能等等）
     */
    List<String> cascaderFeature;

}
