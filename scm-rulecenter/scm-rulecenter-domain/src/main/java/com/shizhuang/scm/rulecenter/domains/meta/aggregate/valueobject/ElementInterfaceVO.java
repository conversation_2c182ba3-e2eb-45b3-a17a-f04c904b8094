package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class ElementInterfaceVO {

    /**
     * dubbo接口 格式：{全路径名:版本}  如：com.shizhuang.duapp.mdm.api.dubbo.ScpWarehouseService：1.0.0
     */
    String dubboInterface;

    /**
     * dubbo接口方法
     */
    String dubboMethod;

    /**
     * dubbo接口入参类型,输入类全限定名
     */
    List<String> dubboParamType;

    /**
     * dubbo接口固定入参名称,若单点查询，则其中一个参数需要变成 #{var}
     */
    List<String> dubboParamValue;

    /**
     * 取值范围为dubbo接口时的，获取枚举对应code的取值路径
     */
    String valueCodePath;

    /**
     * 取值范围为dubbo接口时的，获取枚举对应desc的取值路径
     */
    String valueDescPath;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ElementInterfaceVO)) return false;
        ElementInterfaceVO that = (ElementInterfaceVO) o;
        return Objects.equals(dubboInterface, that.dubboInterface) && Objects.equals(dubboMethod, that.dubboMethod) && Objects.equals(dubboParamType, that.dubboParamType) && Objects.equals(dubboParamValue, that.dubboParamValue) && Objects.equals(valueCodePath, that.valueCodePath) && Objects.equals(valueDescPath, that.valueDescPath);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dubboInterface, dubboMethod, dubboParamType, dubboParamValue, valueCodePath, valueDescPath);
    }
}
