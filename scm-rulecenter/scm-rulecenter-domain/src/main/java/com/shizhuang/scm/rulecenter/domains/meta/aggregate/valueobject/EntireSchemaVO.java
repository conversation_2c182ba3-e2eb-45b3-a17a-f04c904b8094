package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 全量方案信息（T1数据更新用）
 */
@Getter
@Setter
public class EntireSchemaVO {

    SchemaDO schemaDO;

    List<SchemaDetailDO> schemaDetailDOS;

    List<SchemaFieldMappingDO> schemaFieldMappingDOS;

    List<DimensionPriorityDO> dimensionPriorityDOS;

    List<ElementDO> elementDOS;

    List<ElementValueRangeDO> elementValueRangeDOS;

}
