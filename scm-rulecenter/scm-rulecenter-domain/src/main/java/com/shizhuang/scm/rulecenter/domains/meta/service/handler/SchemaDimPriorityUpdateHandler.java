package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaDimPriorityEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SchemaDimPriorityUpdateHandler implements IBasicHandler<SchemaDimPriorityEditContext> {

    @Resource
    SchemaRepository schemaRepository;

    @Override
    public void doHandle(SchemaDimPriorityEditContext schemaDimPriorityEditContext) {
        schemaRepository.save(schemaDimPriorityEditContext);
    }
}
