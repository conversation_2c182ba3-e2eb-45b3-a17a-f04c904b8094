package com.shizhuang.scm.rulecenter.domains.meta.service.validator;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.constant.SystemFieldEnum;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.utils.LetterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.regex.Pattern;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.*;

@Slf4j
@Component
public class ElementValidator {

    @Resource
    ElementRepository elementRepository;

    public ValidationError validateRepeatable(String schemaCode, String elementCode) {
        log.info("ElementValidator.validateRepeatable schemaCode: {}, elementCode: {} ", schemaCode, elementCode);
        if (elementRepository.existElement(schemaCode, elementCode)) {
            return ValidationErrorFactory.create(ELEMENT_IS_REPEATABLE);
        }
        return null;
    }

    public ValidationError validateHump(String elementCode) {
        log.info("ElementValidator.validateHump elementCode: {},  ", elementCode);
        String adjustCode = LetterUtil.getMethodName(elementCode);
        if (isRegularJudgment(adjustCode)) {
            return null;
        }
        return ValidationErrorFactory.create(ELEMENT_IS_NOT_HUMP);
    }

    private boolean isRegularJudgment(String field) {
        String pattern = "^([A-Z][a-z0-9]+)+";
        return Pattern.matches(pattern, field);
    }

    public ValidationError validateSystemFieldCode(String elementCode) {
        log.info("ElementValidator.validateSystemFieldCode elementCode: {},  ", elementCode);
        for (SystemFieldEnum systemFieldEnum : SystemFieldEnum.values()) {
            if(Objects.equals(systemFieldEnum.getFieldCode(),elementCode)){
                return ValidationErrorFactory.create(ELEMENT_CODE_CONFLICT,String.format(ELEMENT_CODE_CONFLICT.getErrMsg(),elementCode));

            }
        }
        return null;
    }

}
