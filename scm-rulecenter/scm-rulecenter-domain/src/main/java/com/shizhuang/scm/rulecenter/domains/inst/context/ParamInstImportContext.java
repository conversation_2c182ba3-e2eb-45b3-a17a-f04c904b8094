package com.shizhuang.scm.rulecenter.domains.inst.context;

import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.dewu.scm.lms.api.report.dto.response.ImportExcelResponse;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ParamInstImportExcelVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Builder
@Getter
@Setter
public class ParamInstImportContext {

    /**
     * 原始导入json
     */
    String rowJson;

    /**
     * 反序列化后的导入对象
     */
    List<BaseImportExcel> importRows;

    /**
     * 转化后的导入对象
     */
    List<ParamInstImportExcelVO> paramInstImportExcelVOS;

    /**
     * 导入的维度基于excel中的index，list顺序与方案下元素排序一致
     */
    List<Integer> dimensionIndex;

    /**
     * 导入的维度元素（导入的时候不一定所有维度都可能会有）
     */
    List<SchemaElementEntity> existDimensionElement;

    /**
     * 导入的所有元素的基于excel中的index，list顺序与方案下元素排序一致
     */
    List<Integer> allElementIndex;

    /**
     * 导入的所有元素（导入的时候不一定所有元素都可能会有）
     */
    List<SchemaElementEntity> existAllElement;

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 方案聚合
     */
    SchemaMetaAggregate schemaMetaAggregate;

    /**
     * 元素code与SchemaElementEntity的map
     */
    Map<String,SchemaElementEntity> allElementMap;

    /**
     * 元素code->元素name Map
     */
    Map<String, String> elementCodeToNameMap;

    /**
     * 元素name->元素code Map
     */
    Map<String, String> elementNameToCodeMap;

    /**
     * 元素code-> adsField Map
     */
    Map<String, String> elementFieldMap;

    /**
     * adsField-> 元素code Map
     */
    Map<String,String> fieldElementMap;

    /**
     * 表头map
     */
    Map<Integer, String> headMap;

    /**
     * 导入返回结果对象
     */
    List<ImportExcelResponse> importExcelResponseList;

    /**
     * 操作人
     */
    String optUserNameEn;

    /**
     * 操作人
     */
    String optUserId;

    public String getAdsFieldByIndex(Integer index) {
        String elementName = headMap.get(index);
        String elementCode = elementNameToCodeMap.get(elementName);
        return elementFieldMap.get(elementCode);
    }

    public String getElementCodeByIndex(Integer index) {
        String elementName = headMap.get(index);
        return elementNameToCodeMap.get(elementName);
    }

}
