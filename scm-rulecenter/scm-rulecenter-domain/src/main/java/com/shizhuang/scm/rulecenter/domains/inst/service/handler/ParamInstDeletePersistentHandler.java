package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.shizhuang.scm.rulecenter.api.cmd.ParamInstDeleteCmd;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstDeleteContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ParamInstDeletePersistentHandler implements IBasicHandler<ParamInstDeleteContext> {
    @Resource
    ParamInstRepository paramInstRepository;

    @Override
    public void doHandle(ParamInstDeleteContext paramInstDeleteContext) {
        ParamInstDeleteCmd paramInstDeleteCmd = paramInstDeleteContext.getParamInstDeleteCmd();
        paramInstRepository.delete(paramInstDeleteCmd.getSchemaCode(), paramInstDeleteCmd.getAggId());
    }
}
