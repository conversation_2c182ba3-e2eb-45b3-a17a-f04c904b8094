package com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstDeleteCmd;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstDeleteContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Component
public class BrandSendQualitySolutionValidator implements ParamInstDeleteCustomValidatorExt {

    @Resource
    ParamInstRepository paramInstRepository;

    @Override
    public ValidationError validate(ParamInstDeleteContext paramInstDeleteContext) {
        ParamInstDeleteCmd paramInstDeleteCmd = paramInstDeleteContext.getParamInstDeleteCmd();
        String schemaCode = paramInstDeleteCmd.getSchemaCode();
        String aggId = paramInstDeleteCmd.getAggId();
        List<ParamInstanceDOWithBLOBs> byAggId = paramInstRepository.getByAggId(schemaCode, aggId);
        if (CollectionUtils.isEmpty(byAggId)) {
            return null;
        }
        ParamInstanceDOWithBLOBs paramInstanceDOWithBLOBs = byAggId.get(0);
        String solutionName = paramInstanceDOWithBLOBs.getDimension1();
        InstPageQuery instPageQuery = new InstPageQuery();
        instPageQuery.setPageNum(1);
        instPageQuery.setPageSize(1);
        instPageQuery.setSchemaCode("brand-send-qualify-solution-match");
        ElementInstDTO elementInstDTO = new ElementInstDTO();
        elementInstDTO.setElementCode("solutionName");
        elementInstDTO.setElementValue(solutionName);
        List<ElementInstDTO> queryParams = Lists.newArrayList(elementInstDTO);
        instPageQuery.setQueryParams(queryParams);
        PageInfo<InstDimPageResponse> instDimPageResponsePageInfo = paramInstRepository.queryParamInstPage(instPageQuery);
        if (instDimPageResponsePageInfo != null
                && CollectionUtils.isNotEmpty(instDimPageResponsePageInfo.getList())) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "该方案【" + solutionName + "】已经关联了问题标签，暂不能删除");
        }
        return null;
    }
}
