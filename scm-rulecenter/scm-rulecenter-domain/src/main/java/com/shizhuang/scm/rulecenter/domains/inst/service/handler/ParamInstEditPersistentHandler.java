package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.alibaba.fastjson.JSON;
import com.dw.scp.bizlog.gateway.BizOperationLogGateway;
import com.poizon.fusion.utils.DateUtils;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.api.query.RedisKeyHelper;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.infrastructure.cache.RedisCacheService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.transaction.TransactionUtil;
import com.shizhuang.scm.rulecenter.infrastructure.common.utils.LetterUtil;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ParamInstanceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.REFLECT_ERROR;

@Slf4j
@Component
public class ParamInstEditPersistentHandler implements IBasicHandler<ParamInstEditContext> {

    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    ParamInstDomainService paramInstDomainService;

    @Resource
    TransactionUtil transactionUtil;

    @Override
    public void doHandle(ParamInstEditContext paramInstCreateContext) {
        String aggId = paramInstCreateContext.getAggId();
        SchemaMetaAggregate schemaMetaAggregate = paramInstCreateContext.getSchemaMetaAggregate();
        String schemaCode = schemaMetaAggregate.getSchemaEntity().getSchemaCode();
        ParamInstEditCmd paramInstEditCmd = paramInstCreateContext.getParamInstEditCmd();
        String optUser = paramInstCreateContext.getOptUser();
        Map<String, String> fieldElementNameMap = schemaMetaAggregate.getFieldElementNameMap();
        Integer version = paramInstEditCmd.getVersion() == null ? 1 : paramInstEditCmd.getVersion();
        Map<String, String> elementFieldMap = schemaMetaAggregate.getElementFieldMap();
        List<ParamInstEntity> paramInstEntities = paramInstCreateContext.getParamInstEntities();
        List<ParamInstanceDOWithBLOBs> oldParamInstanceDOs = paramInstCreateContext.getOldParamInstanceDOs();

        List<String> oldDimensionKeys = oldParamInstanceDOs.stream().map(o -> o.getDimension() + "_" + o.getDimensionKey()).collect(Collectors.toList());
        List<String> newDimensionKeys = paramInstEntities.stream().map(o -> o.getDimension() + "_" + o.getDimensionKey()).collect(Collectors.toList());

        //处理新增
        List<ParamInstEntity> addParamInstEntities = paramInstEntities.stream().filter(p -> {
            String dimensionKeyUnion = p.getDimension() + "_" + p.getDimensionKey();
            return !oldDimensionKeys.contains(dimensionKeyUnion);
        }).collect(Collectors.toList());

        //处理更新
        List<ParamInstEntity> updateParamInstEntities = paramInstEntities.stream().filter(p -> {
            String dimensionKeyUnion = p.getDimension() + "_" + p.getDimensionKey();
            return oldDimensionKeys.contains(dimensionKeyUnion);
        }).collect(Collectors.toList());

        //处理删除
        List<ParamInstanceDOWithBLOBs> deleteParamInstDOs = oldParamInstanceDOs.stream()
                .filter(o -> !newDimensionKeys.contains(o.getDimension() + "_" + o.getDimensionKey()))
                .collect(Collectors.toList());

        //包装事务
        transactionUtil.transaction(() -> {
            addParamInstEntities.forEach(p -> paramInstRepository.save(p, elementFieldMap));
            updateParamInstEntities.forEach(e -> {
                ParamInstanceDOWithBLOBs newParamInstanceDO = paramInstRepository.initParamInst(e, elementFieldMap);
                newParamInstanceDO.setVersion(e.getVersion());
                paramInstRepository.update(newParamInstanceDO, version, optUser, fieldElementNameMap);
            });
            delete(schemaCode, aggId, deleteParamInstDOs);
        });

        paramInstDomainService.recordEditInstBizLog(paramInstCreateContext);

    }

    @Resource
    RedisCacheService redisCacheService;

    private void delete(String schemaCode, String aggId, List<ParamInstanceDOWithBLOBs> deleteParamInstDOs) {
        if (CollectionUtils.isEmpty(deleteParamInstDOs)) {
            return;
        }
        // 失效缓存
        deleteParamInstDOs.forEach(o -> {
            String dimKey = RedisKeyHelper.getRedisDimKey(schemaCode, o.getDimension(), o.getDimensionKey());
            redisCacheService.removeCache(dimKey);
        });
        List<String> deleteParamKeys = deleteParamInstDOs.stream().map(ParamInstanceDOWithBLOBs::getDimensionKey).collect(Collectors.toList());
        ParamInstanceDOWithBLOBs deleteParamInstDO = new ParamInstanceDOWithBLOBs();
        deleteParamInstDO.setSchemaCode("DEL_" + DateUtils.parseDateToStr(new Date()) + "_" + schemaCode);
        deleteParamInstDO.setIsDel(CommonConstant.ONE.byteValue());
        ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
        paramInstanceDOExample.createCriteria().andSchemaCodeEqualTo(schemaCode)
                .andAggIdEqualTo(aggId).andDimensionKeyIn(deleteParamKeys);
        paramInstanceMapper.updateByExampleSelective(deleteParamInstDO, paramInstanceDOExample);
    }

    @Resource
    ParamInstanceMapper paramInstanceMapper;

    public static void setFieldValue(ParamInstanceDOWithBLOBs paramInstanceDO, Map<String, String> elementFieldMap, String elementCode, Object elementValue) {
        String fieldCode = elementFieldMap.get(elementCode);
        fieldCode = fieldCode.replace("_", "");
        try {
            Method method = ParamInstanceDOWithBLOBs.class.getMethod("set" + LetterUtil.getMethodName(fieldCode), String.class);
            method.invoke(paramInstanceDO, String.valueOf(elementValue));
        } catch (Exception ex) {
            log.error("ParamInstEditPersistentHandler.setFieldValue reflect error paramInstanceDO:{} elementFieldMap:{} elementCode:{} elementValue:{}"
                    , JSON.toJSONString(paramInstanceDO), JSON.toJSONString(elementFieldMap), elementCode, elementValue, ex);
            throw new BizRuntimeException(REFLECT_ERROR.getCode(), REFLECT_ERROR.getErrMsg());
        }
    }
}
