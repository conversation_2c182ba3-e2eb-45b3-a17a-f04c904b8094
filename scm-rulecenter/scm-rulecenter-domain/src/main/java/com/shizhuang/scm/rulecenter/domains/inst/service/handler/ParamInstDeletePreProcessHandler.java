package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstDeleteContext;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

@Component
public class ParamInstDeletePreProcessHandler implements IBasicHandler<ParamInstDeleteContext> {
    @Override
    public void doHandle(ParamInstDeleteContext paramInstDeleteContext) {

    }
}
