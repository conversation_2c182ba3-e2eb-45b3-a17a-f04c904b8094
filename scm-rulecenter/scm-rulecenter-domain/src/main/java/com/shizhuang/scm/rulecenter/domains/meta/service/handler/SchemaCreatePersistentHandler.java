package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaCreateContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SchemaCreatePersistentHandler implements IBasicHandler<SchemaCreateContext> {

    @Resource
    SchemaRepository schemaRepository;

    @Override
    public void doHandle(SchemaCreateContext schemaCreateContext) {
        SchemaMetaAggregate schemaMetaAggregate = schemaCreateContext.getSchemaMetaAggregate();
        schemaRepository.save(schemaMetaAggregate);
    }
}
