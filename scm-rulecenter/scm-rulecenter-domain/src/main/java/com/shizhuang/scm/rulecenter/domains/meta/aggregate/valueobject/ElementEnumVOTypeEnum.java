package com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject;

public enum ElementEnumVOTypeEnum {

    NORMAL("normal", "正常枚举"),
    CASCADER("cascader", "级联格式枚举"),
    ;
    private String type;

    private String desc;


    ElementEnumVOTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }




}
