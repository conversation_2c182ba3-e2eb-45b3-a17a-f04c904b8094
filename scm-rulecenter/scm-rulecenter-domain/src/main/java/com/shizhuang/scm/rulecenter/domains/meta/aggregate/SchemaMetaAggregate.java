package com.shizhuang.scm.rulecenter.domains.meta.aggregate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.constant.ElementTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.SystemFieldConditionEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.SchemaOptLogVO;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.SchemaAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.SchemaElementAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.spring.ApplicationContextHelper;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 业务参数元数据聚合
 */
@Getter
@Setter
public class SchemaMetaAggregate {

    /**
     * 方案实体
     */
    SchemaEntity schemaEntity;

    /**
     * 维度实体
     */
    List<SchemaElementEntity> dimensions;

    /**
     * 参数实体
     */
    List<SchemaElementEntity> params;

    /**
     * 维度优先级
     */
    List<DimPriorityVO> dimPriority;

    /**
     * 字段映射
     */
    List<FieldMappingVO> fieldMappings;

    /**
     * 历史方案编辑日志
     */
    List<SchemaOptLogVO> schemaOptLogVOs;

    /**
     * 新添方案编辑日志
     */
    List<SchemaOptLogVO> addSchemaOptLogVOs;

    /**
     * 分配字段
     */
    public void assignFieldMap() {
        MetaDomainService metaDomainService = ApplicationContextHelper.getBean(MetaDomainService.class);
        fieldMappings = metaDomainService.fieldMapping(dimensions, params);
    }


    public static SchemaMetaAggregate init(SchemaCreateCmd schemaCreateCmd) {
        SchemaEntity schema = SchemaAssembler.toEntity(schemaCreateCmd);

        List<SchemaElementCreateCmd> dimensionsDto = schemaCreateCmd.getDimension();
        List<SchemaElementCreateCmd> paramsDto = schemaCreateCmd.getParams();
        List<SchemaElementEntity> dimensions = dimensionsDto.stream().map(e -> {
            SchemaElementEntity elementEntity = SchemaElementAssembler.toElementEntity(e);
            elementEntity.setSchemaCode(schema.getSchemaCode());
            elementEntity.setVersion(CommonConstant.ONE);
            elementEntity.setElementType(ElementTypeEnum.DIMENSION.getType());
            return elementEntity;
        }).collect(Collectors.toList());
        List<SchemaElementEntity> params = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(paramsDto)){
            params = paramsDto.stream().map(e -> {
                SchemaElementEntity elementEntity = SchemaElementAssembler.toElementEntity(e);
                elementEntity.setSchemaCode(schema.getSchemaCode());
                elementEntity.setVersion(CommonConstant.ONE);
                elementEntity.setElementType(ElementTypeEnum.PARAM.getType());
                return elementEntity;
            }).collect(Collectors.toList());
        }
        SchemaMetaAggregate schemaMetaAggregate = new SchemaMetaAggregate();
        schemaMetaAggregate.setSchemaEntity(schema);
        schemaMetaAggregate.setDimensions(dimensions);
        schemaMetaAggregate.setParams(params);
        schemaMetaAggregate.assignFieldMap();

        return schemaMetaAggregate;
    }


    @JsonIgnore
    public Map<String, String> getElementFieldMap() {
        Map<String, String> elementFieldMap = fieldMappings.stream().collect(Collectors.toMap(FieldMappingVO::getElementCode, FieldMappingVO::getFieldCode, (o, n) -> n));
        for(SystemFieldConditionEnum systemFieldConditionEnum :SystemFieldConditionEnum.values()){
            //注意：这里需要用原有的code，后续判断sql条件用
            elementFieldMap.put(systemFieldConditionEnum.getConditionCode(),systemFieldConditionEnum.getConditionCode());
        }
        return elementFieldMap;
    }

    @JsonIgnore
    public List<SchemaElementEntity> getAllElements() {
        return Stream.concat(dimensions.stream(), params.stream()).collect(Collectors.toList());
    }

    @JsonIgnore
    public Map<String, String> getElementNameToCodeMap() {
        List<SchemaElementEntity> allElements = Stream.concat(dimensions.stream(), params.stream()).collect(Collectors.toList());
        return allElements.stream().collect(Collectors.toMap(SchemaElementEntity::getElementName, SchemaElementEntity::getElementCode, (o, n) -> n));
    }

    @JsonIgnore
    public Map<String, String> getElementCodeToNameMap() {
        List<SchemaElementEntity> allElements = Stream.concat(dimensions.stream(), params.stream()).collect(Collectors.toList());
        return allElements.stream().collect(Collectors.toMap(SchemaElementEntity::getElementCode, SchemaElementEntity::getElementName, (o, n) -> n));
    }

    @JsonIgnore
    public Map<String, String> getFieldElementNameMap() {
        Map<String, String> elementCodeToNameMap = getElementCodeToNameMap();
        return fieldMappings.stream().collect(Collectors.toMap(FieldMappingVO::getFieldCode, f -> elementCodeToNameMap.get(f.getElementCode()), (o, n) -> n));
    }

    @JsonIgnore
    public Map<String,Byte> getElementValueRangeTypeMap(){
        Map<String, Byte> elementValueRangeMap = getAllElements().stream().collect(Collectors.toMap(SchemaElementEntity::getElementCode, SchemaElementEntity::getValueRangeType));
        for(SystemFieldConditionEnum systemFieldConditionEnum :SystemFieldConditionEnum.values()){
            elementValueRangeMap.put(systemFieldConditionEnum.getConditionCode(), ValueRangeTypeEnum.SYSTEM.getType());
        }
        return elementValueRangeMap;
    }


    @JsonIgnore
    public DimPriorityVO getDefaultDimPriority(){
        List<String> atomicDimension = dimensions.stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        String dim = String.join("#",atomicDimension);
        DimPriorityVO dimPriorityVO = new DimPriorityVO();
        dimPriorityVO.setSchemaCode(schemaEntity.getSchemaCode());
        dimPriorityVO.setDimension(dim);
        dimPriorityVO.setPriority(1);
        dimPriorityVO.setIsDel(CommonConstant.IS_NOT_DELETE);
        dimPriorityVO.setVersion(1);
        dimPriorityVO.setAtomicDimension(atomicDimension);
        return dimPriorityVO;
    }

}
