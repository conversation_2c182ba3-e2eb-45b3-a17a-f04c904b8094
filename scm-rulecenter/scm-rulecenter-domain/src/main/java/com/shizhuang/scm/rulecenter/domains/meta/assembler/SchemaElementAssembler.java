package com.shizhuang.scm.rulecenter.domains.meta.assembler;

import com.shizhuang.scm.rulecenter.api.cmd.SchemaElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.constant.ElementSourceTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.SchemaElementDTO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;

public class SchemaElementAssembler {

    public static SchemaElementEntity toElementEntity(SchemaElementCreateCmd d) {
        SchemaElementEntity elementMetaEntity = new SchemaElementEntity();
        elementMetaEntity.setElementCode(d.getElementCode());
        elementMetaEntity.setElementSourceType(d.getElementSourceType());
        elementMetaEntity.setQueryCondition(d.getQueryCondition());
        elementMetaEntity.setIsRequired(d.getIsRequired());
        elementMetaEntity.setIsMulti(d.getIsMulti());
        elementMetaEntity.setIsDel(CommonConstant.IS_NOT_DELETE);
        elementMetaEntity.setVersion(CommonConstant.ONE);
        return elementMetaEntity;
    }

    public static SchemaElementDTO toDTO(SchemaElementEntity d) {
        SchemaElementDTO schemaElementDTO = new SchemaElementDTO();
        schemaElementDTO.setSchemaCode(d.getSchemaCode());
        schemaElementDTO.setElementCode(d.getElementCode());
        schemaElementDTO.setElementName(d.getElementName());
        schemaElementDTO.setElementType(d.getElementType().intValue());
        schemaElementDTO.setElementSourceType(d.getElementSourceType());
        schemaElementDTO.setPriority(d.getPriority());
        schemaElementDTO.setValueRangeType(d.getValueRangeType());
        schemaElementDTO.setValueRangeTypeName(ValueRangeTypeEnum.of(d.getValueRangeType()).getDesc());
        schemaElementDTO.setQueryCondition(d.getQueryCondition());
        schemaElementDTO.setIsRequired(d.getIsRequired());
        schemaElementDTO.setIsMulti(d.getIsMulti());
        schemaElementDTO.setElementNote(d.getElementNote());
        schemaElementDTO.setElementSourceTypeName(ElementSourceTypeEnum.of(d.getElementSourceType().byteValue()).getDesc());
        schemaElementDTO.setVersion(d.getVersion());
        schemaElementDTO.setFrontComponent(d.getFrontComponent());
        return schemaElementDTO;
    }




}
