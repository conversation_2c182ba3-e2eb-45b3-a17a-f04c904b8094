package com.shizhuang.scm.rulecenter.domains.inst.service.validator;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ElementInstVO;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.PARAM_NOT_EXIST;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.PARAM_VALUE_NOT_EXIST;

/**
 * 校验值域是否正确
 */
@Component
public class ParamInstElementValueValidator {

    @Resource
    MetaDomainService metaDomainService;

    /**
     * 判断输入的参数值是否存在
     * @param schemaCode
     * @param paramMap
     * @return
     */
    public ValidationError validate(String schemaCode, Map<String,Object> paramMap,Map<String, String> elementCodeToNameMap) {

        for (Map.Entry<String,Object> entry : paramMap.entrySet()) {
            String elementCode = entry.getKey();
            Object elementValue = entry.getValue();
            if(elementValue instanceof String){
                String valueDesc = metaDomainService.getFirstElemDesc(schemaCode, elementCode, (String)elementValue,paramMap);
                if (StringUtils.isBlank(valueDesc)) {
                    String elementName = elementCodeToNameMap.get(elementCode);
                    String specificMsg = String.format(PARAM_VALUE_NOT_EXIST.getErrMsg(), elementName, elementValue);
                    return ValidationErrorFactory.create(PARAM_VALUE_NOT_EXIST, specificMsg);
                }
            }
        }
        return null;
    }

    /**
     * 判断输入的值描述是否存在
     * @param schemaCode
     * @param descMap
     * @return
     */
    public ValidationError validateDesc(String schemaCode, List<ElementInstVO> elementInstVOList, Map<String, String> elementCodeToNameMap,Map<String,String> elementCodeValueMap) {

        for (ElementInstVO elementInstVO:elementInstVOList) {
            String elementCode =elementInstVO.getElementCode();
            String elementDesc = elementInstVO.getElementDesc();
            String elementValue = metaDomainService.getElemValue(schemaCode, elementCode, elementDesc, elementCodeValueMap);
            if (StringUtils.isBlank(elementValue)) {
                String elementName = elementCodeToNameMap.get(elementCode);
                String specificMsg = String.format(PARAM_NOT_EXIST.getErrMsg(), elementName);
                return ValidationErrorFactory.create(PARAM_NOT_EXIST, specificMsg);
            }
            elementCodeValueMap.put(elementCode,elementValue);
        }
        return null;
    }

}
