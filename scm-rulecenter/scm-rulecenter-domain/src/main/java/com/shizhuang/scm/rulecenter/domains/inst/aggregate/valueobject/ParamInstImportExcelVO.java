package com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject;

import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ParamInstImportExcelVO {

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * rowId
     */
    private String rowId;


    /**
     * 原始导入数据
     */
    BaseImportExcel baseImportExcel;


    /**
     * 维度
     */
    String dimension;


    /**
     * 维度值
     */
    String dimensionKey;


    /**
     * 转化后的导入数据
     */
    List<ElementInstVO> elementInstVOList;


    /**
     * elementCode:elementDesc map
     */
    Map<String,String> descMap;


}
