package com.shizhuang.scm.rulecenter.domains.support.pmf;

import com.poizon.fusion.common.app.ApplicationContextHolder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;


@Getter
@Setter
public class OperationUserContext implements Serializable {
    /**
     * 统一用cas的userId
     */
    private Long userId = 0L;

    // todo id和人名都存储起来

    /**
     * 登录操作用户名
     */
    private String userName = "";

    /**
     * 登陆用户真实姓名
     */
    private String realName = "";

    /**
     * 操作用户所在仓库
     */
    private String operationRepositoryCode = "";

    private Short operationRepositoryId = 0;
    /**
     * 操作用户所在仓库名称
     */
    private String operationRepositoryName = "";
    /**
     * 工位号，绑定工位的时候需要更新。
     */
    private String stationNo = "0000";
    /**
     * 是否是管理员
     */
    private Boolean isAdmin = false;
    /**
     * 自有用户:1 兼职用户:2
     */
    private Integer userType = 2;
    /**
     * 订餐状态
     * 0:默认值不申请，1:待申请，2:待领取，3:已领取
     */
    private Integer mealStatus;

    /**
     * 订餐状态
     * 0:默认值不申请
     */
    private Integer signStatus;

    private Integer userStatus;

    private Long signId;

    /**
     * app版本号，从请求头里面获取的
     */
    private String appVersion = "";
    /**
     * 用户选择语言
     */
    private String Language = "zh";

    /**
     * 组别code
     */
    private String orgCode;

    /**
     * 组别名称
     */
    private String orgName;

    /**
     * 组别全名称-7级架构
     */
    private String orgFullName;

    /**
     * 是否查询过LMS用户信息接口
     */
    private boolean checkLms = false;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 人员仓库
     **/
    List<WarehouseDto> warehouses;


    public String getStationNo() {
        try {
            Environment environment = ApplicationContextHolder.getEnvironment();
            if (Objects.isNull(environment)) {
                return stationNo;
            }
            String property = environment.getProperty("user.sign.stationCodeMatch", "");
            if (StringUtils.isNotBlank(property)) {
                //匹配不上 特殊字符
                if (!stationNo.matches(property)) {
                    stationNo = "__sno__";
                }
            }
        } catch (Exception e) {
            //ignore
        }
        return stationNo;
    }

    @Data
    public static class WarehouseDto implements Serializable {
        /**
         * 仓库code
         **/
        private String warehouseCode;
        /**
         * 仓库名称
         **/
        private String warehouseName;

        private static final long serialVersionUID = 1L;
    }

    public CreatorVO getCreatorVO() {
        CreatorVO creatorVO = new CreatorVO();
        creatorVO.setUserId(userId);
        creatorVO.setUserName(userName);
        return creatorVO;
    }

    public String getUserNameUnBlank() {
        return StringUtils.isBlank(userName) ? String.valueOf(userId) : userName;
    }
}
