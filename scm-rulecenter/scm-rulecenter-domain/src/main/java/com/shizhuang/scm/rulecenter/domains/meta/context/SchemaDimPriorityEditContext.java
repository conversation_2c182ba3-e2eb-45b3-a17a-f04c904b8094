package com.shizhuang.scm.rulecenter.domains.meta.context;

import com.shizhuang.scm.rulecenter.api.cmd.SchemaDimPriorityEditCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class SchemaDimPriorityEditContext {

    SchemaDimPriorityEditCmd schemaDimPriorityEditCmd;

    List<DimPriorityVO> dimPriorityVOList;

    SchemaMetaAggregate schemaMetaAggregate;

    /**
     * 排序类型 0按自定义优先级排序，1按业务字段排序
     */
    Integer dimPriType;

    /**
     * 排序字段
     */
    String orderField;

    /**
     * 排序类型 asc正序 desc倒序
     */
    String orderType;

}
