package com.shizhuang.scm.rulecenter.domains.support.chain;

import com.shizhuang.scm.rulecenter.infrastructure.common.spring.ApplicationContextHelper;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class HandlerChainDefinition<CONTEXT> {

    String chainName;

    List<Class<? extends IBasicHandler<CONTEXT>>> handlerList;

    public static <CONTEXT> HandlerChainDefinition<CONTEXT> name(String name){
        HandlerChainDefinition<CONTEXT> handlerChainDefinition = new HandlerChainDefinition<>();
        handlerChainDefinition.setChainName(name);
        handlerChainDefinition.setHandlerList(new ArrayList<>());
        return handlerChainDefinition;
    }

    public HandlerChainDefinition<CONTEXT> add(Class<? extends IBasicHandler<CONTEXT>>  iBasicHandlerClazz){
        List<Class<? extends IBasicHandler<CONTEXT>>> basicHandlerClazzList = this.getHandlerList();
        basicHandlerClazzList.add(iBasicHandlerClazz);
        return this;
    }

    public  GenericHandler<CONTEXT> orchestrate(){
        Class<? extends IBasicHandler<CONTEXT>> firstHandler = handlerList.get(0);

        GenericHandler<CONTEXT> firstGenericHandler = new GenericHandler<>();
        IBasicHandler<CONTEXT> bizHandlers = ApplicationContextHelper.getBean(firstHandler);
        firstGenericHandler.setCurHandler(bizHandlers);

        GenericHandler<CONTEXT> preGenericHandler = firstGenericHandler;
        for (int i = 1; i < handlerList.size(); i++) {
            Class<? extends IBasicHandler<CONTEXT>> handler = handlerList.get(i);
            GenericHandler<CONTEXT> nextGenericHandler = new GenericHandler<>();
            nextGenericHandler.setCurHandler(ApplicationContextHelper.getBean(handler));
            preGenericHandler.setNextHandler(nextGenericHandler);

            preGenericHandler = nextGenericHandler;
        }

        return firstGenericHandler;
    }
}
