package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ElementBatchCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementBatchCreateContext;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.DubboInterfaceValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.ElementValidator;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.ValueRepeatableValidator;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ElementBatchCreateValidatorChain implements IBasicHandler<ElementBatchCreateContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Override
    public void doHandle(ElementBatchCreateContext elementBatchCreateContext) {

        ComplexResult result = FluentValidator.checkAll()
                .putAttribute2Context(CommonConstant.APPLICATION_CONTEXT, this.applicationContext)
                .on(elementBatchCreateContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(
                // 1、同方案下（common或者指定方案），输入的元素code与现有的元素code不能相同
                ValidatorHandlerFactory.build(ctx -> validateElementRepeatable((ElementBatchCreateContext) ctx)),

                // 2、元素code需要以驼峰形式命名，
                ValidatorHandlerFactory.build(ctx -> validateElementCodeHump((ElementBatchCreateContext) ctx)),

                // 3、枚举类型里的值域 值或者值描述不能有相同的情况，比如品牌id不能有重复，品牌名称不能有重复
                ValidatorHandlerFactory.build(ctx -> validateValueRepeatable((ElementBatchCreateContext) ctx)),

                // 4、如果是dubbo查询接口类型，需要做基本的字段判空校验
                ValidatorHandlerFactory.build(ctx -> validateDubboInterface((ElementBatchCreateContext) ctx)),

                // 5、如果是全量dubbo查询接口类型，需要做基本的字段判空校验
                ValidatorHandlerFactory.build(ctx -> validateDubboInvokeSuccess((ElementBatchCreateContext) ctx)),

                // 6、需要确定检查如果是dubbo单点查询，需要强制设置有且仅有一个#{var}的变量
                ValidatorHandlerFactory.build(ctx -> validateSearchDubboElement((ElementBatchCreateContext) ctx))
        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }

    private ValidationError validateSearchDubboElement(ElementBatchCreateContext ctx) {
        ElementBatchCreateCmd elementBatchCreateCmd = ctx.getElementBatchCreateCmd();
        List<ElementCreateCmd> createCmdList = elementBatchCreateCmd.getCreateCmdList();
        DubboInterfaceValidator validator = applicationContext.getBean(DubboInterfaceValidator.class);
        return createCmdList.stream()
                .filter(cmd -> validator.acceptSearchDubboInterface(cmd.getValueRangeType()))
                .map(cmd -> validator.validateSearchDubboInterface(cmd.getValueToDescInterface(), cmd.getDescToValueInterface()))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private ValidationError validateDubboInvokeSuccess(ElementBatchCreateContext ctx) {
        ElementBatchCreateCmd elementBatchCreateCmd = ctx.getElementBatchCreateCmd();
        List<ElementCreateCmd> createCmdList = elementBatchCreateCmd.getCreateCmdList();
        DubboInterfaceValidator validator = applicationContext.getBean(DubboInterfaceValidator.class);
        return createCmdList.stream()
                .filter(cmd -> validator.acceptFullDubboInterface(cmd.getValueRangeType()))
                .map(cmd -> validator.validate(cmd.getFullInterface()))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private ValidationError validateDubboInterface(ElementBatchCreateContext ctx) {
        ElementBatchCreateCmd elementBatchCreateCmd = ctx.getElementBatchCreateCmd();
        List<ElementCreateCmd> createCmdList = elementBatchCreateCmd.getCreateCmdList();
        DubboInterfaceValidator validator = applicationContext.getBean(DubboInterfaceValidator.class);
        return createCmdList.stream()
                .filter(cmd -> validator.acceptBaseDubboInterface(cmd.getValueRangeType()))
                .map(cmd -> validator.validate(cmd.getValueRangeType(), cmd.getFullInterface(), cmd.getValueToDescInterface(), cmd.getDescToValueInterface()))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }


    private ValidationError validateValueRepeatable(ElementBatchCreateContext ctx) {
        ElementBatchCreateCmd elementBatchCreateCmd = ctx.getElementBatchCreateCmd();
        List<ElementCreateCmd> createCmdList = elementBatchCreateCmd.getCreateCmdList();
        ValueRepeatableValidator validator = applicationContext.getBean(ValueRepeatableValidator.class);
        return createCmdList.stream()
                .filter(cmd -> validator.accept(cmd.getValueRangeType()))
                .map(cmd -> validator.validate(cmd.getEnums()))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private ValidationError validateElementCodeHump(ElementBatchCreateContext ctx) {
        ElementBatchCreateCmd elementBatchCreateCmd = ctx.getElementBatchCreateCmd();
        List<ElementCreateCmd> createCmdList = elementBatchCreateCmd.getCreateCmdList();
        ElementValidator validator = applicationContext.getBean(ElementValidator.class);

        return createCmdList.stream()
                .map(cmd -> validator.validateHump(cmd.getElementCode()))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private ValidationError validateElementRepeatable(ElementBatchCreateContext ctx) {
        ElementBatchCreateCmd elementBatchCreateCmd = ctx.getElementBatchCreateCmd();
        List<ElementCreateCmd> createCmdList = elementBatchCreateCmd.getCreateCmdList();
        ElementValidator validator = applicationContext.getBean(ElementValidator.class);
        List<ElementCreateCmd> elementEditCmds = new ArrayList<>();
        List<ElementCreateCmd> elementAddCmds = new ArrayList<>();
        for(ElementCreateCmd cmd:createCmdList){
            if(validator.validateRepeatable(cmd.getSchemaCode(), cmd.getElementCode()) != null){
                elementEditCmds.add(cmd);
            }else{
                elementAddCmds.add(cmd);
            }
        }
        ctx.setExecuteEditCmd(elementEditCmds);
        ctx.setExecuteCreateCmd(elementAddCmds);
        return null;
    }

}
