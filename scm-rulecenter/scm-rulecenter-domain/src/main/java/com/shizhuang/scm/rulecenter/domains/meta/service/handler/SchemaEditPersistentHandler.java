package com.shizhuang.scm.rulecenter.domains.meta.service.handler;
import com.google.common.collect.Lists;

import com.shizhuang.scm.rulecenter.api.cmd.SchemaEditCmd;
import com.shizhuang.scm.rulecenter.api.cmd.SchemaElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.constant.ElementTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.SchemaElementDTO;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.CompareIssueVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.SchemaOptLogVO;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.SchemaAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.SchemaElementAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.context.SchemaEditContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.domains.support.pmf.OperationUserContext;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class SchemaEditPersistentHandler implements IBasicHandler<SchemaEditContext> {
    @Resource
    SchemaRepository schemaRepository;

    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    MetaDomainService metaDomainService;

    @Override
    public void doHandle(SchemaEditContext schemaEditContext) {

        SchemaMetaAggregate largestNoDeploySchemaMetaAggregate = schemaEditContext.getLargestNoDeploySchemaMetaAggregate();
        SchemaMetaAggregate deployedSchemaMetaAggregate = schemaEditContext.getDeployedSchemaMetaAggregate();

        SchemaEditCmd schemaEditCmd = schemaEditContext.getSchemaEditCmd();
        SchemaEntity editSchemaEntity = SchemaAssembler.toEntity(schemaEditCmd);
        dealVersion(largestNoDeploySchemaMetaAggregate, editSchemaEntity, deployedSchemaMetaAggregate);

        List<SchemaElementCreateCmd> dimensionsDto = schemaEditCmd.getDimension();
        List<SchemaElementCreateCmd> paramsDto = schemaEditCmd.getParams();
        List<SchemaElementEntity> dimensions = dimensionsDto.stream().map(e -> {
            SchemaElementEntity elementEntity = SchemaElementAssembler.toElementEntity(e);
            elementEntity.setSchemaCode(editSchemaEntity.getSchemaCode());
            elementEntity.setVersion(editSchemaEntity.getVersion());
            elementEntity.setElementType(ElementTypeEnum.DIMENSION.getType());
            return elementEntity;
        }).collect(Collectors.toList());

        List<SchemaElementEntity> params = paramsDto.stream().map(e -> {
            SchemaElementEntity elementEntity = SchemaElementAssembler.toElementEntity(e);
            elementEntity.setSchemaCode(editSchemaEntity.getSchemaCode());
            elementEntity.setVersion(editSchemaEntity.getVersion());
            elementEntity.setElementType(ElementTypeEnum.PARAM.getType());
            return elementEntity;
        }).collect(Collectors.toList());

        SchemaMetaAggregate editschemaMetaAggregate = new SchemaMetaAggregate();
        editschemaMetaAggregate.setSchemaEntity(editSchemaEntity);
        editschemaMetaAggregate.setDimensions(dimensions);
        editschemaMetaAggregate.setParams(params);
        editschemaMetaAggregate.assignFieldMap();

        OperationUserContext operationUser = BizIdentityContext.getOperationUser();
        String userName = operationUser.getUserNameUnBlank();
        SchemaMetaAggregate oldSchemaMetaAggregate = Optional.ofNullable(deployedSchemaMetaAggregate).orElse(largestNoDeploySchemaMetaAggregate);
        List<CompareIssueVO> compareIssueVOList = metaDomainService.compareSchema(editschemaMetaAggregate, oldSchemaMetaAggregate);
        SchemaOptLogVO schemaOptLogVO = SchemaOptLogVO.builder().schemaCode(editSchemaEntity.getSchemaCode())
                .modifier(userName)
                .compareIssueVOList(compareIssueVOList)
                .build();
        editschemaMetaAggregate.setAddSchemaOptLogVOs(Lists.newArrayList(schemaOptLogVO));

        schemaRepository.update(editschemaMetaAggregate,deployedSchemaMetaAggregate);
        if(schemaEditContext.getHasDeleteElement()){
            paramInstRepository.deleteBySchema(schemaEditCmd.getSchemaCode());
        }
    }

    /**
     * 处理版本数据
     * @param largestNoDeploySchemaMetaAggregate
     * @param schemaEntity
     * @param deployedSchemaMetaAggregate
     */
    private static Integer dealVersion(SchemaMetaAggregate largestNoDeploySchemaMetaAggregate, SchemaEntity schemaEntity, SchemaMetaAggregate deployedSchemaMetaAggregate) {
        Integer lastVersion = 0;
        if (largestNoDeploySchemaMetaAggregate != null) {
            lastVersion = largestNoDeploySchemaMetaAggregate.getSchemaEntity().getVersion();
            schemaEntity.setVersion(largestNoDeploySchemaMetaAggregate.getSchemaEntity().getVersion());
            schemaEntity.setIsLatestVersion(CommonConstant.ZERO);
            if (deployedSchemaMetaAggregate == null) {
                schemaEntity.setIsLatestVersion(CommonConstant.ONE);
            }
            schemaEntity.setCreator(largestNoDeploySchemaMetaAggregate.getSchemaEntity().getCreator());
        } else if (deployedSchemaMetaAggregate != null) {
            lastVersion = deployedSchemaMetaAggregate.getSchemaEntity().getVersion();
            schemaEntity.setVersion(deployedSchemaMetaAggregate.getSchemaEntity().getVersion() + 1);
            schemaEntity.setIsLatestVersion(CommonConstant.ZERO);
            schemaEntity.setCreator(deployedSchemaMetaAggregate.getSchemaEntity().getCreator());
        }
        return lastVersion;
    }
}
