package com.shizhuang.scm.rulecenter.domains.inst.service.handler;

import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.request.BaseImportExcel;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstImportContext;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ParamInstImportPreProcessHandler implements IBasicHandler<ParamInstImportContext> {

    @Resource
    SchemaRepository schemaRepository;

    @Override
    public void doHandle(ParamInstImportContext paramInstImportContext) {

        String rowJson = paramInstImportContext.getRowJson();
        List<BaseImportExcel> importRows = JSON.parseArray(rowJson, BaseImportExcel.class);

        BaseImportExcel baseImportExcel = importRows.get(0);
        Map<Integer, String> headMap = baseImportExcel.getHeadMap();
        if(MapUtils.isEmpty(headMap)){
            headMap = new HashMap<>();
            headMap.put(0,"所属版本");
            headMap.put(1,"仓库");
            headMap.put(2,"一级类目");
            headMap.put(3,"二级类目");
            headMap.put(4,"三级类目");
            headMap.put(5,"品牌");
            headMap.put(6,"是否奢品");
            headMap.put(7,"买家区域");
            headMap.put(8,"质检优化配置");
        }
        Map<String, String> businessParamMap = baseImportExcel.getBusinessParamMap();
        String schemaCode = "";
        if(MapUtils.isNotEmpty(businessParamMap)){
            schemaCode = businessParamMap.get(CommonConstant.SCHEMA_CODE);
        }else{
            schemaCode = "category-brand-quality-optimize-config";
        }
        SchemaMetaAggregate schemaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLatestVersion());

        Map<String, String> elementFieldMap = schemaAggregate.getElementFieldMap();
        Map<String, String> elementNameToCodeMap = schemaAggregate.getElementNameToCodeMap();
        Map<String, String> elementCodeToNameMap = schemaAggregate.getElementCodeToNameMap();

        paramInstImportContext.setHeadMap(headMap);
        paramInstImportContext.setImportRows(importRows);
        paramInstImportContext.setSchemaCode(schemaCode);
        paramInstImportContext.setSchemaMetaAggregate(schemaAggregate);
        paramInstImportContext.setElementFieldMap(elementFieldMap);
        paramInstImportContext.setElementNameToCodeMap(elementNameToCodeMap);
        paramInstImportContext.setOptUserNameEn(baseImportExcel.getOperateUserNameEn());
        paramInstImportContext.setOptUserId(String.valueOf(baseImportExcel.getOperateUserId()));
        paramInstImportContext.setElementCodeToNameMap(elementCodeToNameMap);

        Map<String, SchemaElementEntity> allElementMap = schemaAggregate.getAllElements().stream().collect(Collectors.toMap(SchemaElementEntity::getElementCode, e -> e, (o, n) -> n));
        paramInstImportContext.setAllElementMap(allElementMap);

        List<SchemaElementEntity> dimensions = schemaAggregate.getDimensions();
        List<SchemaElementEntity> allElements = schemaAggregate.getAllElements();
        IndexInfo indexInfo = extractIndexInfo(allElements,dimensions, headMap, elementNameToCodeMap);
        paramInstImportContext.setDimensionIndex(indexInfo.getDimensionIndex());
        paramInstImportContext.setExistDimensionElement(indexInfo.getExistDimensionElement());
        paramInstImportContext.setAllElementIndex(indexInfo.getAllElementIndex());
        paramInstImportContext.setExistAllElement(indexInfo.getExistAllElement());
        paramInstImportContext.setImportExcelResponseList(new ArrayList<>());

        log.info("ParamInstImportApiImpl paramInstImportContext :{}",JSON.toJSONString(paramInstImportContext));

    }


    /**
     * 提取导入的excel里哪些列是维度，记录列的index，顺序和方案下维度顺序一致
     * @param dimensions
     * @param headMap
     * @param elementNameToCodeMap
     * @return
     */
    private IndexInfo extractIndexInfo(List<SchemaElementEntity> allElements, List<SchemaElementEntity> dimensions, Map<Integer, String> headMap, Map<String, String> elementNameToCodeMap) {
        IndexInfo indexInfo = new IndexInfo();

        List<Integer> dimensionIndex = new ArrayList<>();
        List<SchemaElementEntity> existDimensionElement = new ArrayList<>();
        for (SchemaElementEntity dimensionElement : dimensions) {
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                String elementName = entry.getValue();
                String elementCode = elementNameToCodeMap.get(elementName);
                if (Objects.equals(dimensionElement.getElementCode(), elementCode)) {
                    dimensionIndex.add(entry.getKey());
                    existDimensionElement.add(dimensionElement);
                    break;
                }
            }
        }
        List<Integer> allElementIndex = new ArrayList<>();
        List<SchemaElementEntity> existAllElement = new ArrayList<>();
        for (SchemaElementEntity dimensionElement : allElements) {
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                String elementName = entry.getValue();
                String elementCode = elementNameToCodeMap.get(elementName);
                if (Objects.equals(dimensionElement.getElementCode(), elementCode)) {
                    allElementIndex.add(entry.getKey());
                    existAllElement.add(dimensionElement);
                    break;
                }
            }
        }
        indexInfo.setDimensionIndex(dimensionIndex);
        indexInfo.setExistDimensionElement(existDimensionElement);
        indexInfo.setAllElementIndex(allElementIndex);
        indexInfo.setExistAllElement(existAllElement);

        return indexInfo;
    }

    @Getter
    @Setter
    class IndexInfo {

        List<Integer> dimensionIndex;

        List<SchemaElementEntity> existDimensionElement;

        List<Integer> allElementIndex;

        List<SchemaElementEntity> existAllElement;

    }

}
