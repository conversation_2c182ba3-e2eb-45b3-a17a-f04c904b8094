package com.shizhuang.scm.rulecenter.domains.meta.repository.transfer;

import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.DimensionPriorityDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface DimPriorityTransfer extends BaseTransfer<DimensionPriorityDO, DimPriorityVO> {

    DimPriorityTransfer INSTANCE = Mappers.getMapper(DimPriorityTransfer.class);



}
