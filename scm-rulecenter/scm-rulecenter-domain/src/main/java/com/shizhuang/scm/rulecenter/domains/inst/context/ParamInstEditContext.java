package com.shizhuang.scm.rulecenter.domains.inst.context;

import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstAggEntity;
import com.shizhuang.scm.rulecenter.domains.inst.aggregate.entity.ParamInstEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Builder
@Getter
@Setter
public class ParamInstEditContext {


    ParamInstEditCmd paramInstEditCmd;


    SchemaMetaAggregate schemaMetaAggregate;


    String optUser;


    ParamInstanceDOWithBLOBs oldParamInstanceDO;


    List<ParamInstanceDOWithBLOBs> oldParamInstanceDOs;


    List<ParamInstEntity> paramInstEntities;

    String aggId;

    //用于记录日志的旧参数实体，与页面上的一致
    ParamInstAggEntity oldAggEntity;

    //用于记录日志的新参数实体，与页面上的一致
    ParamInstAggEntity newAggEntity;

    private String operatorName;

    private String operatorId;

    public String extractDimensionKey() {
        List<SchemaElementEntity> dimensions = schemaMetaAggregate.getDimensions();
        List<ElementInstAddCmd> paramMap = paramInstEditCmd.getParamMap();

        List<String> dimensionCodes = dimensions.stream().map(SchemaElementEntity::getElementCode).collect(Collectors.toList());
        Map<String, ElementInstAddCmd> collect = paramMap.stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, e -> e, (o, n) -> n));

        List<String> extractDimensionList = new ArrayList<>();
        for (String dimension : dimensionCodes) {
            if (collect.containsKey(dimension)) {
                extractDimensionList.add(String.valueOf(collect.get(dimension).getElementValue()));
            }
        }
        return String.join("#", extractDimensionList);
    }


}
