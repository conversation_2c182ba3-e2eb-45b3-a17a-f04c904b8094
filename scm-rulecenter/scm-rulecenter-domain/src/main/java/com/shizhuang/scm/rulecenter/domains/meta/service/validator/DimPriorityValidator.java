package com.shizhuang.scm.rulecenter.domains.meta.service.validator;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.*;

/**
 * 方案优先级重复性校验
 */
@Slf4j
@Component
public class DimPriorityValidator {

    @Resource
    ParamInstRepository paramInstRepository;

    public ValidationError validateRepeatable(List<DimPriorityVO> dimPriorityVOList) {
        log.info("DimPriorityValidator.validateRepeatable param dimPriorityVOList: {} ", JSON.toJSONString(dimPriorityVOList));
        Set<Integer> priSet = new HashSet<>();
        Set<String> dimensionSet = new HashSet<>();
        for (DimPriorityVO dimPriorityVO : dimPriorityVOList) {
            Integer priority = dimPriorityVO.getPriority();
            String dimension = dimPriorityVO.getDimension();
            if (priSet.contains(priority)) {
                return ValidationErrorFactory.create(DIMENSION_PRIORITY_IS_REPEATABLE);
            }
            priSet.add(priority);
            if (dimensionSet.contains(dimension)) {
                String specificMsg = DIMENSION_IS_REPEATABLE.getErrMsg() + dimension;
                return ValidationErrorFactory.create(DIMENSION_IS_REPEATABLE, specificMsg);
            }
            dimensionSet.add(dimension);

        }
        return null;
    }

    public ValidationError validateMissingPart(List<DimPriorityVO> dimPriorityVOList) {
        log.info("DimPriorityValidator.validateMissingPart param dimPriorityVOList: {} ", JSON.toJSONString(dimPriorityVOList));
        DimPriorityVO dimPriorityVO = dimPriorityVOList.get(0);
        String schemaCode = dimPriorityVO.getSchemaCode();
        List<String> allExistDimension = paramInstRepository.queryAllDimension(schemaCode);
        List<String> inputDimensions = dimPriorityVOList.stream().map(DimPriorityVO::getDimension).collect(Collectors.toList());

        for (String dimension : allExistDimension) {
            if (!inputDimensions.contains(dimension)) {
                String specificMsg = String.format(DIMENSION_PRIORITY_IS_MISSING.getErrMsg(), dimension);
                return ValidationErrorFactory.create(DIMENSION_PRIORITY_IS_MISSING, specificMsg);
            }
        }
        return null;
    }

}
