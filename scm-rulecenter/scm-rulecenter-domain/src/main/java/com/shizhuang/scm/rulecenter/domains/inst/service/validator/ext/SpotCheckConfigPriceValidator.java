package com.shizhuang.scm.rulecenter.domains.inst.service.validator.ext;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.shizhuang.scm.rulecenter.api.cmd.ElementInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstEditCmd;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Component
public class SpotCheckConfigPriceValidator implements ParamInstCreateCustomValidatorExt, ParamInstEditCustomValidatorExt {
    @Override
    public ValidationError validate(ParamInstCreateContext paramInstCreateContext) {
        ParamInstAddCmd paramInstAddCmd = paramInstCreateContext.getParamInstAddCmd();
        Map<String, Object> paramInstValueMap = paramInstAddCmd.getParamMap().stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        return getValidationError(paramInstValueMap);
    }

    private static ValidationError getValidationError(Map<String, Object> paramInstValueMap) {
        Object skuPriceStart = paramInstValueMap.get("skuPriceStart");
        Object skuPriceEnd = paramInstValueMap.get("skuPriceEnd");
        BigDecimal skuPriceStartD = new BigDecimal(String.valueOf(skuPriceStart));
        BigDecimal skuPriceEndD = new BigDecimal(String.valueOf(skuPriceEnd));
        if (skuPriceStartD.compareTo(skuPriceEndD) > 0) {
            return new ValidationError().setErrorCode(BIZ_VALIDATE_ERROR.getCode()).setErrorMsg(BIZ_VALIDATE_ERROR.getErrMsg() + "商品开始价格不能大于商品结束价格");
        }

        return null;
    }

    @Override
    public ValidationError validate(ParamInstEditContext paramInstEditContext) {
        ParamInstEditCmd paramInstEditCmd = paramInstEditContext.getParamInstEditCmd();
        Map<String, Object> paramInstValueMap = paramInstEditCmd.getParamMap().stream().collect(Collectors.toMap(ElementInstAddCmd::getElementCode, ElementInstAddCmd::getElementValue, (o, n) -> n));
        return getValidationError(paramInstValueMap);
    }
}
