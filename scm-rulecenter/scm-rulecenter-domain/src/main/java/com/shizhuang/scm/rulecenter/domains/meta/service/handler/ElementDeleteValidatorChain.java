package com.shizhuang.scm.rulecenter.domains.meta.service.handler;

import com.alibaba.fastjson.JSON;
import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.cmd.ElementDeleteCmd;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.context.ElementDeleteContext;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.service.validator.OptUserValidator;
import com.shizhuang.scm.rulecenter.domains.support.chain.IBasicHandler;
import com.shizhuang.scm.rulecenter.domains.support.validator.ValidatorHandlerFactory;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ValidationErrorFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.EXIST_RELATED_SCHEMA;

@Component
public class ElementDeleteValidatorChain implements IBasicHandler<ElementDeleteContext>, ApplicationContextAware {

    ValidatorChain validatorChain;

    ApplicationContext applicationContext;

    @Resource
    ElementRepository elementRepository;

    @Resource
    SchemaRepository schemaRepository;


    @Override
    public void doHandle(ElementDeleteContext elementDeleteContext) {
        ComplexResult result = FluentValidator.checkAll()
                .putAttribute2Context(CommonConstant.APPLICATION_CONTEXT, this.applicationContext)
                .on(elementDeleteContext, validatorChain)
                .failFast()
                .doValidate()
                .result(ResultCollectors.toComplex());
        List<ValidationError> errors = result.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            ValidationError validationError = errors.get(0);
            throw new BizRuntimeException(validationError.getErrorCode(), validationError.getErrorMsg());
        }
    }

    private ValidationError validateOptUserIllegal(ElementDeleteContext ctx) {
        OptUserValidator validator = applicationContext.getBean(OptUserValidator.class);
        ElementDeleteCmd elementDeleteCmd = ctx.getElementDeleteCmd();
        String elementCode = elementDeleteCmd.getElementCode();
        String schemaCode = elementDeleteCmd.getSchemaCode();
        ElementMetaEntity elementMetaEntity = elementRepository.getByCode(schemaCode, elementCode);
        String creator = elementMetaEntity.getCreator();
        return validator.validate(creator);
    }

    private ValidationError validateRelatedSchema(ElementDeleteContext ctx) {
        ElementDeleteCmd elementDeleteCmd = ctx.getElementDeleteCmd();
        String elementCode = elementDeleteCmd.getElementCode();
        String schemaCode = elementDeleteCmd.getSchemaCode();
        List<String> schemaCodes = schemaRepository.getRelatedSchemaByElement(schemaCode, elementCode);
        if (CollectionUtils.isNotEmpty(schemaCodes)) {
            String specificMsg = String.format(EXIST_RELATED_SCHEMA.getErrMsg(), JSON.toJSONString(schemaCodes));
            return ValidationErrorFactory.create(EXIST_RELATED_SCHEMA, specificMsg);
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        List<Validator> validatorHandlers = Lists.newArrayList(

                // 2、需要确定当前编辑人是否为创建人或者白名单
                ValidatorHandlerFactory.build(ctx -> validateOptUserIllegal((ElementDeleteContext) ctx)),

                // 1、元素在删除的时候不能有关联的方案
                ValidatorHandlerFactory.build(ctx -> validateRelatedSchema((ElementDeleteContext) ctx))

        );
        validatorChain = new ValidatorChain();
        validatorChain.setValidators(validatorHandlers);
    }
}
