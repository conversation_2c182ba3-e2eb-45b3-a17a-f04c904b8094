package com.shizhuang.scm.rulecenter.domains.inst.service;

import com.shizhuang.scm.rulecenter.domains.inst.aggregate.valueobject.ElementInstVO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 参数实例提取
 */
@Builder
@Getter
@Setter
public class ParamDimExtractRes {

    /**
     * 维度
     */
    String dimension;

    /**
     * 维度key
     */
    String dimensionKey;

    /**
     * 维度值实例，顺序与方案中默认的顺序相同
     */
    List<ElementInstVO> dimensions;

    /**
     * 参数值实例，顺序与方案中默认的顺序相同
     */
    List<ElementInstVO> params;

    /**
     * 元素code与数据库字段mapping
     */
    Map<String/*elementCode*/,String/*fieldCode*/> elementFieldMap;

}
