package com.shizhuang.scm.rulecenter.domains.meta.assembler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ElementEditCmd;
import com.shizhuang.scm.rulecenter.api.constant.ElementSourceTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.ElementEnumDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import com.shizhuang.scm.rulecenter.api.response.ElementDetailResponse;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceSet;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementInterfaceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
public class ElementMetaAssembler {

    public static ElementMetaEntity transEntity(ElementCreateCmd elementCreateCmd) {
        ElementMetaEntity elementMetaEntity = new ElementMetaEntity();
        String schemaCode = elementCreateCmd.getSchemaCode();
        if (StringUtils.isBlank(schemaCode)) {
            schemaCode = "COMMON";
        }
        elementMetaEntity.setSchemaCode(schemaCode);
        elementMetaEntity.setElementCode(elementCreateCmd.getElementCode());
        elementMetaEntity.setElementName(elementCreateCmd.getElementName());
        elementMetaEntity.setElementSourceType(elementCreateCmd.getElementSourceType());
        elementMetaEntity.setValueRangeType(elementCreateCmd.getValueRangeType());
        elementMetaEntity.setFrontComponent(elementCreateCmd.getFrontComponent());
        ElementInterfaceVO fullInterfaceVO = null;
        ElementInterfaceVO valueToDescInterfaceVO = null;
        ElementInterfaceVO descToValueInterfaceVO = null;
        if (elementCreateCmd.getFullInterface() != null) {
            fullInterfaceVO = BeanCopyUtils.copyProperties(elementCreateCmd.getFullInterface(), ElementInterfaceVO.class);
        }
        if (elementCreateCmd.getValueToDescInterface() != null) {
            valueToDescInterfaceVO = BeanCopyUtils.copyProperties(elementCreateCmd.getValueToDescInterface(), ElementInterfaceVO.class);
        }
        if (elementCreateCmd.getDescToValueInterface() != null) {
            descToValueInterfaceVO = BeanCopyUtils.copyProperties(elementCreateCmd.getDescToValueInterface(), ElementInterfaceVO.class);
        }
        List<ElementEnumVO> elementEnumVOS = transToVO(elementCreateCmd.getEnums());
        elementMetaEntity.setEnums(elementEnumVOS);
        ElementInterfaceSet elementInterfaceSet = new ElementInterfaceSet();
        elementInterfaceSet.setFullInterfaceVO(fullInterfaceVO);
        elementInterfaceSet.setValueToDescInterfaceVO(valueToDescInterfaceVO);
        elementInterfaceSet.setDescToValueInterfaceVO(descToValueInterfaceVO);
        elementMetaEntity.setElementInterfaceSet(elementInterfaceSet);
        elementMetaEntity.setElementNote(elementCreateCmd.getElementNote());
        return elementMetaEntity;
    }

    private static List<ElementEnumVO> transToVO(List<ElementEnumDTO> enums) {
        if (CollectionUtils.isNotEmpty(enums)) {
            return enums.stream().map(e -> {
                        String desc = e.getDesc();
                        if (StringUtils.isBlank(desc)) {
                            desc = e.getValue();
                        }
                        return new ElementEnumVO(e.getValue(), desc);
                    })
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public static ElementMetaEntity transEntity(ElementEditCmd elementEditCmd) {
        ElementMetaEntity elementMetaEntity = new ElementMetaEntity();
        elementMetaEntity.setSchemaCode(elementEditCmd.getSchemaCode());
        elementMetaEntity.setElementCode(elementEditCmd.getElementCode());
        elementMetaEntity.setElementName(elementEditCmd.getElementName());
        elementMetaEntity.setValueRangeType(elementEditCmd.getValueRangeType());
        elementMetaEntity.setElementSourceType(elementEditCmd.getElementSourceType());
        ElementInterfaceVO fullInterfaceVO = null;
        ElementInterfaceVO valueToDescInterfaceVO = null;
        ElementInterfaceVO descToValueInterfaceVO = null;
        if (elementEditCmd.getFullInterface() != null) {
            fullInterfaceVO = BeanCopyUtils.copyProperties(elementEditCmd.getFullInterface(), ElementInterfaceVO.class);
        }
        if (elementEditCmd.getValueToDescInterface() != null) {
            valueToDescInterfaceVO = BeanCopyUtils.copyProperties(elementEditCmd.getValueToDescInterface(), ElementInterfaceVO.class);
        }
        if (elementEditCmd.getDescToValueInterface() != null) {
            descToValueInterfaceVO = BeanCopyUtils.copyProperties(elementEditCmd.getDescToValueInterface(), ElementInterfaceVO.class);
        }
        List<ElementEnumVO> elementEnumVOS = transToVO(elementEditCmd.getEnums());
        elementMetaEntity.setEnums(elementEnumVOS);
        ElementInterfaceSet elementInterfaceSet = new ElementInterfaceSet();
        elementInterfaceSet.setFullInterfaceVO(fullInterfaceVO);
        elementInterfaceSet.setValueToDescInterfaceVO(valueToDescInterfaceVO);
        elementInterfaceSet.setDescToValueInterfaceVO(descToValueInterfaceVO);
        elementMetaEntity.setElementInterfaceSet(elementInterfaceSet);
        elementMetaEntity.setElementNote(elementEditCmd.getElementNote());
        elementMetaEntity.setFrontComponent(elementEditCmd.getFrontComponent());
        return elementMetaEntity;
    }


    public static ElementDetailResponse transDto(ElementMetaEntity elementMetaEntity) {
        log.info("ElementDetailResponse transDto param:{}", JSON.toJSONString(elementMetaEntity));
        ElementDetailResponse elementDetailResponse = new ElementDetailResponse();
        elementDetailResponse.setElementCode(elementMetaEntity.getElementCode());
        elementDetailResponse.setElementName(elementMetaEntity.getElementName());
        elementDetailResponse.setElementSourceType(elementMetaEntity.getElementSourceType());
        elementDetailResponse.setElementSourceTypeName(ElementSourceTypeEnum.of(elementMetaEntity.getElementSourceType().byteValue()).getDesc());
        elementDetailResponse.setValueRangeType(elementMetaEntity.getValueRangeType());
        elementDetailResponse.setValueRangeTypeName(ValueRangeTypeEnum.of(elementMetaEntity.getValueRangeType()).getDesc());
        ElementInterfaceSet elementInterfaceSet = Optional.ofNullable(elementMetaEntity.getElementInterfaceSet()).orElseGet(ElementInterfaceSet::new);
        ElementInterfaceDTO fullInterfaceDTO = null;
        ElementInterfaceDTO valueToDescInterfaceDTO = null;
        ElementInterfaceDTO descToValueInterfaceDTO = null;
        if (elementInterfaceSet.getFullInterfaceVO() != null) {
            fullInterfaceDTO = new ElementInterfaceDTO();
            ElementInterfaceVO fullInterfaceVO = elementInterfaceSet.getFullInterfaceVO();
            fullInterfaceDTO.setDubboInterface(fullInterfaceVO.getDubboInterface());
            fullInterfaceDTO.setDubboMethod(fullInterfaceVO.getDubboMethod());
            fullInterfaceDTO.setDubboParamType(fullInterfaceVO.getDubboParamType());
            fullInterfaceDTO.setDubboParamValue(fullInterfaceVO.getDubboParamValue());
            fullInterfaceDTO.setValueCodePath(fullInterfaceVO.getValueCodePath());
            fullInterfaceDTO.setValueDescPath(fullInterfaceVO.getValueDescPath());
        }
        if (elementInterfaceSet.getValueToDescInterfaceVO() != null) {
            valueToDescInterfaceDTO = BeanCopyUtils.copyProperties(elementInterfaceSet.getValueToDescInterfaceVO(), ElementInterfaceDTO.class);
        }
        if (elementInterfaceSet.getDescToValueInterfaceVO() != null) {
            descToValueInterfaceDTO = BeanCopyUtils.copyProperties(elementInterfaceSet.getDescToValueInterfaceVO(), ElementInterfaceDTO.class);
        }
        elementDetailResponse.setFullInterface(fullInterfaceDTO);
        elementDetailResponse.setValueToDescInterface(valueToDescInterfaceDTO);
        elementDetailResponse.setDescToValueInterface(descToValueInterfaceDTO);

        List<ElementEnumVO> enums = elementMetaEntity.getEnums();
        if (CollectionUtils.isNotEmpty(enums)) {
            List<ElementEnumDTO> elementEnumDTOS = enums.stream().map(e -> {
                ElementEnumDTO elementEnumDTO = new ElementEnumDTO();
                elementEnumDTO.setValue(e.getValue());
                elementEnumDTO.setDesc(e.getDesc());
                // todo 这里是测试结构
                ElementEnumDTO elementEnumDTO2 = new ElementEnumDTO();
                elementEnumDTO2.setValue("2");
                elementEnumDTO2.setDesc("h2");
                elementEnumDTO2.setLabel("h2");
                elementEnumDTO.setChildren(Lists.newArrayList(elementEnumDTO2));
                return elementEnumDTO;
            }).collect(Collectors.toList());

            elementDetailResponse.setEnums(elementEnumDTOS);
        }

        elementDetailResponse.setElementNote(elementMetaEntity.getElementNote());
        elementDetailResponse.setBelongToSchemaCode(elementMetaEntity.getSchemaCode());
        elementDetailResponse.setFrontComponent(elementMetaEntity.getFrontComponent());
        elementDetailResponse.setCreator(elementMetaEntity.getCreator());
        return elementDetailResponse;
    }
}
