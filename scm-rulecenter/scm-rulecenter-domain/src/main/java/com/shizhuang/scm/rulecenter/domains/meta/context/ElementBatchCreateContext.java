package com.shizhuang.scm.rulecenter.domains.meta.context;

import com.shizhuang.scm.rulecenter.api.cmd.ElementBatchCreateCmd;
import com.shizhuang.scm.rulecenter.api.cmd.ElementCreateCmd;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class ElementBatchCreateContext {

    ElementBatchCreateCmd elementBatchCreateCmd;

    List<ElementCreateCmd> executeCreateCmd;

    List<ElementCreateCmd> executeEditCmd;

}
