package com.shizhuang.scm.rulecenter.domains.support.chain;

import com.alibaba.fastjson.JSON;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 组处理器抽象类
 */
@Slf4j
@Getter
@Setter
public class GenericHandler<CONTEXT> implements IHandler<CONTEXT> {

    IBasicHandler<CONTEXT> curHandler;

    IHandler<CONTEXT> nextHandler;


    @Override
    public void doHandle(CONTEXT context) {
        curHandler.doHandle(context);
    }

    @Override
    public void setNextHandler(IHandler<CONTEXT> iHandler) {
        this.nextHandler = iHandler;
    }

    @Override
    public boolean shouldHandle(Object context) {
//        Boolean aBoolean = (Boolean) context.get(CommonConstant.SHOULD_HANDLE);
//        if (aBoolean != null && !aBoolean) {
//            return false;
//        }
        return true;
    }


    @Override
    public void handle(CONTEXT context) {
        if (shouldHandle(context)) {
            try {
                doHandle(context);
            } catch (BizRuntimeException e) {
                handleException(context, e);
            }
        }
        if (nextHandler != null) {
            nextHandler.handle(context);
        }
    }

    /**
     * 默认异常处理
     *
     * @param context
     * @param e
     */
    public void handleException(CONTEXT context, BizRuntimeException e) {
        if (log.isDebugEnabled()) {
            log.debug(JSON.toJSONString(context));
        }
        log.error("curHandler:{}, context :{} unhandled exception: ", curHandler.getClass().getSimpleName(),  context, e);
        throw new BizRuntimeException(e.getErrorCode(), e.getMessage());
    }

}
