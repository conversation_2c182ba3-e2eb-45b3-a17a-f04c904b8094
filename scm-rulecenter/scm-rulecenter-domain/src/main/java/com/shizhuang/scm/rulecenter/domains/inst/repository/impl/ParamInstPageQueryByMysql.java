package com.shizhuang.scm.rulecenter.domains.inst.repository.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.domains.inst.assembler.ParamInstAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.ElementRepositoryImpl;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ParamInstanceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class ParamInstPageQueryByMysql implements ParamInstPageQueryStrategy {

    @Resource
    SchemaRepository schemaRepository;

    @Resource
    ParamInstanceMapper paramInstanceMapper;

    @Resource
    ParamInstAssembler paramInstAssembler;

    @Resource
    private ThreadPoolTaskExecutor asyncServiceExecutor;


    @Resource
    ThreadPoolTaskExecutor customExecutor;

    @Resource
    ElementRepository elementRepository;

    @Override
    public PageInfo<InstDimPageResponse> queryPage(InstPageQuery instPageQuery) {
        String schemaCode = instPageQuery.getSchemaCode();
        Integer pageNum = instPageQuery.getPageNum();
        Integer pageSize = instPageQuery.getPageSize();

        SchemaMetaAggregate schemaAggregateByCode = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLargestVersion());
        List<SchemaElementEntity> allElements = schemaAggregateByCode.getAllElements();
        Map<String, String> elementFieldMap = schemaAggregateByCode.getElementFieldMap();

        List<String> enabled = Lists.newArrayList("1", "0");
        if (CollectionUtils.isNotEmpty(instPageQuery.getQueryParams())) {
            List<String> inputEnabled = instPageQuery.getQueryParams().stream()
                    .filter(q -> "enabled".equals(q.getElementCode())).map(ElementInstDTO::getElementValue).collect(Collectors.toList());
            enabled = CollectionUtils.isNotEmpty(inputEnabled) ? inputEnabled : enabled;
        }

        Long total = paramInstanceMapper.countAggIdByPage(schemaCode, enabled);
        List<String> aggIds;
        String inputOrderBy = instPageQuery.getOrderBy();


        if (StringUtils.isBlank(inputOrderBy)) {
            aggIds = paramInstanceMapper.selectAggIdByPage(schemaCode, enabled, pageSize, (pageNum - 1) * pageSize);
        } else {
            aggIds = paramInstanceMapper.selectAggIdByPageOrder(schemaCode, enabled, pageSize, (pageNum - 1) * pageSize, inputOrderBy);
        }
        List<InstDimPageResponse> instDimPageResponses = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(aggIds)) {
            ParamInstanceDOExample paramInstanceDOExample = new ParamInstanceDOExample();
            String orderByClause = " create_time desc ";
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inputOrderBy)) {
                orderByClause = " " + inputOrderBy + " ";
            }
            paramInstanceDOExample.setOrderByClause(orderByClause);
            ParamInstanceDOExample.Criteria criteria = paramInstanceDOExample.createCriteria()
                    .andSchemaCodeEqualTo(schemaCode).andIsDelEqualTo(CommonConstant.IS_NOT_DELETE).andAggIdIn(aggIds);
            //这里处理非业务的系统条件，比如是否只查询有效的参数
            processSystemCondition(instPageQuery, criteria);
            List<ParamInstanceDOWithBLOBs> paramInstanceDOS = paramInstanceMapper.selectByExampleWithBLOBs(paramInstanceDOExample);
            log.info("paramInstanceMapper.selectByExampleWithBLOBs instPageQuery:{} res:{}", JSON.toJSONString(instPageQuery), JSON.toJSONString(paramInstanceDOS));

            //本地缓存预热，避免大量调用redis
            allElements.forEach(e -> elementRepository.getByCodeSimple(e.getSchemaCode(), e.getElementCode()));

            List<CompletableFuture<InstDimPageResponse>> completableFutures = new ArrayList<>();
            for (int i = 0; i < paramInstanceDOS.size(); i++) {
                int finalI = i;
                CompletableFuture<InstDimPageResponse> completableFuture = CompletableFuture.supplyAsync(() -> paramInstAssembler.toPageResponse(paramInstanceDOS.get(finalI), allElements, elementFieldMap), customExecutor);
                completableFutures.add(completableFuture);
            }

            CompletableFuture<List<InstDimPageResponse>> listCompletableFuture = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> completableFutures.stream().map(fu -> {
                        try {
                            return fu.get();
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).collect(Collectors.toList()));
            try {
                instDimPageResponses = listCompletableFuture.get();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        PageInfo<InstDimPageResponse> paramInstanceDOPageInfo = PageInfo.of(instDimPageResponses);
        paramInstanceDOPageInfo.setPageNum(pageNum);
        paramInstanceDOPageInfo.setPageSize(pageSize);
        paramInstanceDOPageInfo.setTotal(total);
        return paramInstanceDOPageInfo;
    }

    private static void processSystemCondition(InstPageQuery instPageQuery, ParamInstanceDOExample.Criteria criteria) {
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        if (CollectionUtils.isNotEmpty(queryParams)) {
            List<ElementInstDTO> enabledDtos = queryParams.stream().filter(e -> e.getElementCode().equals("enabled")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(enabledDtos)) {
                String elementValue = enabledDtos.get(0).getElementValue();
                criteria.andEnabledEqualTo(Integer.valueOf(elementValue));
            }
        }
    }
}
