{"type": "dialog", "actions": [{"actionType": "cancel", "id": "u:cancel_btn", "label": "取消", "type": "button"}, {"actionType": "submit", "componentId": "u:archive_node_form", "id": "u:submit_btn", "label": "确认", "level": "primary", "type": "button"}], "body": [{"api": {"messages": {"success": "创建成功"}, "method": "post", "url": "/scm/rulecenter/archive-management/create-node"}, "body": [{"columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data == null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}"}, "method": "post", "sendOn": "${databaseName!=null && databaseName!=''}", "url": "/scm/rulecenter/archive/fetch-tables"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择表名"}, "formItemProps": {"rules": [{"message": "请选择表名", "required": true}]}, "id": "u:table_name", "label": "表名", "linkage": [{"event": "onChange", "resetKeys": ["indexColumn"]}], "name": "tableName", "type": "select"}], "id": "u:5e12c4020389"}, {"body": [], "id": "u:47e33681bd30"}], "id": "u:33bda0b60f07", "type": "grid"}, {"columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data !=null && response.data.length > 0){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${appName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-sources"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据源"}, "formItemProps": {"rules": [{"message": "请选择数据源", "required": false}]}, "id": "u:datasource_name", "label": "数据源名称", "linkage": [{"event": "onChange", "resetKeys": ["databaseName"], "setValues": {}}], "name": "datasourceName", "type": "select"}], "id": "u:e5bd8203b822"}, {"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data==null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "datasource": "${datasourceName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-bases"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据库"}, "formItemProps": {"rules": [{"message": "请选择数据库", "required": false}]}, "id": "u:database_name", "label": "数据库名称", "linkage": [{"event": "onChange", "resetKeys": ["tableName"]}], "name": "databaseName", "type": "select"}], "id": "u:fce32884ee30"}], "id": "u:c5ea54c5280e", "type": "grid"}, {"columns": [{"body": [{"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否归档", "required": true}]}, "id": "u:is_archive", "label": "是否归档", "name": "isArchive", "type": "select"}], "id": "u:86c33c47dee2"}, {"body": [{"fieldProps": {"options": [{"label": "先归档子节点", "value": 1}, {"label": "先归档父节点", "value": 2}]}, "hiddenOn": "${!isArchive}", "id": "u:archive_type", "label": "归档顺序", "name": "archiveType", "type": "select"}], "id": "u:73e0ffc4ab60"}], "id": "u:8ddc03b89d06", "type": "grid"}, {"columns": [{"body": [{"fieldProps": {"options": [{"label": "不过滤", "value": 0}, {"label": "过滤", "value": 1}]}, "hiddenOn": "${!isArchive}", "id": "u:filter_default_value", "label": "默认值过滤", "name": "filterDefaultValue", "type": "select"}], "id": "u:181da80b6b7c"}], "id": "u:filter_section", "type": "grid"}, {"id": "u:79a568220011", "title": "分表配置", "type": "divider"}, {"fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否启用分片", "required": true}]}, "id": "u:sharding_enabled", "initialValue": false, "label": "启用分片", "name": "shardingEnabled", "type": "select"}, {"columns": [{"body": [{"fieldProps": {"placeholder": "如：item_id % 128 或 pink_operate_item_${item_id%128+1}"}, "formItemProps": {"rules": [{"message": "分表表达式格式错误，支持格式：字段名 % 数值 或 前缀${字段名%数值+偏移量}", "test": "function(value) {\n  if (!value) return true;\n  const simplePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\\s*[%]\\s*\\d+$/;\n  const templatePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\\$\\{[a-zA-Z_][a-zA-Z0-9_]*[%]\\d+([+\\-]\\d+)?\\}$/;\n  return simplePattern.test(value.trim()) || templatePattern.test(value.trim());\n}"}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:sharding_expression", "label": "分表表达式", "name": "shardingExpression", "type": "text"}], "id": "u:08e689831d09"}, {"body": [{"fieldProps": {"placeholder": "请输入分表插件全类名，如：com.example.ShardingPlugin"}, "formItemProps": {"rules": [{"message": "插件类名格式错误，请输入完整的类名", "pattern": "^[a-zA-Z_][a-zA-Z0-9_]*(\\.[a-zA-Z_][a-zA-Z0-9_]*)*$", "required": false}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:sharding_plugin_class", "label": "分表插件类", "name": "shardingPluginClass", "type": "text"}], "id": "u:0ab1b1f5cf48"}], "id": "u:c3bbac2791bb", "type": "grid"}, {"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data!=null){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}", "tableName": "${tableName}"}, "method": "post", "sendOn": "${tableName!=null && tableName != ''}", "url": "/scm/rulecenter/archive/fetch-columns"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择分表字段"}, "formItemProps": {"rules": [{"message": "请选择分表字段", "required": "${shardingEnabled}"}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:sharding_field", "label": "分片字段", "name": "shardingField", "type": "select"}, {"fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否启用索引扫描", "required": true}]}, "id": "u:enable_scan", "label": "索引扫描", "name": "enableScan", "type": "select", "initialValue": false}, {"hiddenOn": "${!enableScan}", "id": "u:index_related_config", "type": "divider", "title": "索引相关配置"}, {"id": "u:index_column_section", "type": "grid", "columns": [{"body": [{"id": "u:index_column", "label": "索引字段", "name": "indexColumn", "type": "select", "fieldProps": {"placeholder": "请选择索引字段", "labelInValue": false}, "formItemProps": {"rules": [{"message": "请选择索引字段", "required": false}]}, "api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data!=null){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name,type:v.type}})\n}\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}", "tableName": "${tableName}"}, "method": "post", "sendOn": "${tableName!=null && tableName != ''}", "url": "/scm/rulecenter/archive/fetch-columns"}, "linkage": [{"event": "onChange", "resetKeys": ["indexSpan", "reserveDays"], "setValues": {"indexColumType": "${indexColumn.type}"}}]}], "id": "u:cca33985cab3"}, {"body": [{"fieldProps": {"disabled": true}, "id": "u:index_column_type", "label": "索引字段类型", "name": "indexColumType", "type": "text", "formItemProps": {"hidden": false}, "initialValue": "", "trim": true, "proFieldProps": {"mode": "read"}}], "id": "u:b92e627e1c58"}]}, {"id": "u:order_by_section", "type": "grid", "columns": [{"body": [{"fieldProps": {"options": [{"label": "升序", "value": "ASC"}, {"label": "降序", "value": "DESC"}]}, "id": "u:order_by", "label": "排序方式", "name": "orderBy", "type": "select", "initialValue": "ASC"}], "id": "u:54f1dec34f4e"}]}, {"id": "u:reserve_days", "type": "digit", "label": "保留天数", "name": "reserveDays", "fieldProps": {"placeholder": "请输入保留天数，如：30、90、365", "min": 1, "max": 36500}, "hiddenOn": "${!(CONTAINS(indexColumType,\"date\")||CONTAINS(indexColumType,\"datetime\")||CONTAINS(indexColumType,\"timestamp\"))}", "formItemProps": {"rules": [{"required": true, "message": "日期类型字段必须设置保留天数"}]}}, {"id": "u:index_span", "type": "digitRange", "label": "索引值范围", "name": "indexSpan", "startName": "indexStart", "endName": "indexEnd", "fieldProps": {"tooltip": "可只填写开始或结束值，如：1000-9999 或 1000- 或 -9999", "placeholder": ["开始值", "结束值"]}, "hiddenOn": "${!(STARTSWITH(indexColumType,\"bigint\")||STARTSWITH(indexColumType,\"int\")||STARTSWITH(indexColumType,\"tinyint\")||STARTSWITH(indexColumType,\"smallint\")||STARTSWITH(indexColumType,\"mediumint\")||STARTSWITH(indexColumType,\"decimal\")||STARTSWITH(indexColumType,\"float\")||STARTSWITH(indexColumType,\"double\")||STARTSWITH(indexColumType,\"varchar\"))}", "formItemProps": {"rules": [{"message": "索引值范围格式错误，请输入有效的数字范围", "test": "function(value) {\n  if (!value || !Array.isArray(value)) return true;\n  const [start, end] = value;\n  if (start !== undefined && (isNaN(start) || start < 0)) return false;\n  if (end !== undefined && (isNaN(end) || end < 0)) return false;\n  if (start !== undefined && end !== undefined && start >= end) return false;\n  return true;\n}"}]}}, {"fieldProps": {"placeholder": "如：a.id = b.aid，仅支持等值连接，支持多个字段，如：a.id = b.aid and a.type = b.type", "rows": 3}, "formItemProps": {"hidden": false, "rules": [{"message": "关联关系格式错误，请使用等值连接格式：表别名.字段 = 表别名.字段", "test": "function(value) {\n  if (!value) return true;\n  const relationPattern = /^([a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*(\\s+and\\s+[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*)*)$/i;\n  return relationPattern.test(value.trim());\n}"}]}, "hiddenOn": true, "id": "u:relations", "label": "关联关系", "name": "relations", "type": "textarea"}, {"fieldProps": {"placeholder": "请输入SQL查询条件，如：id > 0 and status = 1 and create_time < '2024-01-01'", "rows": 3}, "formItemProps": {"rules": [{"message": "查询条件格式错误，请检查SQL语法", "test": "function(value) {\n  if (!value) return true;\n  // 简单的SQL条件格式校验\n  const sqlPattern = /^(\\s*[a-zA-Z_][a-zA-Z0-9_]*\\s*[=<>!]+\\s*['\"\\w\\s\\-\\.]+\\s*(and|or)?\\s*)*$/i;\n  return sqlPattern.test(value.trim());\n}"}]}, "id": "u:condition", "label": "查询条件", "name": "condition", "type": "textarea"}, {"formItemProps": {"hidden": true}, "id": "u:task_id", "label": "taskId", "name": "taskId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:node_id", "label": "nodeId", "name": "nodeId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:parent_node_id", "label": "parentNodeId", "name": "parentNodeId", "type": "text"}], "id": "u:archive_node_form", "onSubmit": "// 表单提交前的数据处理\nconst formData = values;\n\n// 处理分片配置\nif (!formData.shardingEnabled) {\n  formData.shardingExpression = null;\n  formData.shardingPluginClass = null;\n  formData.shardingField = null;\n} else {\n  // 确保分片表达式和插件类只填写一个\n  if (formData.shardingExpression && formData.shardingPluginClass) {\n    const confirmed = confirm('分片表达式和分片插件类只能填写一个，请选择保留哪个？');\n    if (confirmed) {\n      formData.shardingPluginClass = null;\n    } else {\n      formData.shardingExpression = null;\n    }\n  }\n}\n\n// 处理索引范围\nif (formData.indexSpan) {\n  formData.indexStart = formData.indexSpan[0];\n  formData.indexEnd = formData.indexSpan[1];\n  delete formData.indexSpan;\n}\n\n// 根据索引字段类型自动设置归档类型\nif (formData.indexColumType) {\n  const dateTypes = ['date', 'datetime', 'timestamp'];\n  const numberTypes = ['bigint', 'int', 'tinyint', 'smallint', 'mediumint', 'decimal', 'float', 'double', 'varchar'];\n  \n  const isDateType = dateTypes.some(type => formData.indexColumType.includes(type));\n  const isNumberType = numberTypes.some(type => formData.indexColumType.startsWith(type));\n  \n  if (isDateType) {\n    formData.archiveType = 2; // 保留天数类型\n  } else if (isNumberType) {\n    formData.archiveType = 1; // 区间类型\n  }\n}\n\n// 构建nodeProps对象\nconst nodeProps = {\n  indexColumType: formData.indexColumType,\n  enableScan: formData.enableScan,\n  archiveType: formData.archiveType,\n  filterDefaultValue: formData.filterDefaultValue\n};\n\n// 根据归档类型添加相应字段\nif (formData.archiveType === 2) {\n  // 保留天数类型\n  nodeProps.reserveDays = formData.reserveDays;\n  nodeProps.indexType = 2; // INDEX_TYPE_RESERVE_DAYS\n} else if (formData.archiveType === 1) {\n  // 区间类型\n  nodeProps.indexStart = formData.indexStart;\n  nodeProps.indexEnd = formData.indexEnd;\n  nodeProps.indexType = 1; // INDEX_TYPE_SPAN\n}\n\nformData.nodeProps = nodeProps;\n\n// 设置默认值\nif (formData.status === undefined) {\n  formData.status = 1; // 默认运行状态\n}\nif (formData.isArchive === undefined) {\n  formData.isArchive = true; // 默认归档\n}\nif (formData.archiveType === undefined) {\n  formData.archiveType = 1; // 默认先归档子节点\n}\nif (formData.filterDefaultValue === undefined) {\n  formData.filterDefaultValue = 0; // 默认不过滤\n}\nif (formData.shardingEnabled === undefined) {\n  formData.shardingEnabled = false; // 默认关闭分片\n}\nif (formData.enableScan === undefined) {\n  formData.enableScan = false; // 默认关闭索引扫描\n}\n\n// 确保taskId为数字类型\nif (formData.taskId && typeof formData.taskId === 'string') {\n  formData.taskId = parseInt(formData.taskId, 10);\n}\n\nreturn formData;", "type": "form", "actions": []}], "id": "u:40a8a52c5eba", "size": "lg", "title": "归档节点配置", "width": "1150px"}