#!/bin/bash

# 测试 getTaskConfig 接口是否返回 appName 字段
# 使用方法: ./test_get_task_config_appname.sh [taskId]

BASE_URL="http://localhost:8884/archive-management"
HEADER_USERID="userid:1"

# 默认任务ID，可以通过参数传入
TASK_ID=${1:-123}

echo "测试 getTaskConfig 接口是否返回 appName 字段"
echo "请求参数: taskId=$TASK_ID"
echo "请求URL: $BASE_URL/get-task-config?taskId=$TASK_ID"
echo "=========================================="

# 发送请求并格式化输出
RESPONSE=$(curl -s -X GET "$BASE_URL/get-task-config?taskId=$TASK_ID" -H "$HEADER_USERID")

echo "响应内容:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

echo ""
echo "=========================================="
echo "检查是否包含 appName 字段:"

# 检查响应中是否包含 appName 字段
if echo "$RESPONSE" | grep -q '"appName"'; then
    echo "✅ 响应中包含 appName 字段"
    # 提取 appName 的值
    APP_NAME=$(echo "$RESPONSE" | jq -r '.data.appName' 2>/dev/null)
    if [ "$APP_NAME" != "null" ] && [ -n "$APP_NAME" ]; then
        echo "✅ appName 的值: $APP_NAME"
    else
        echo "⚠️  appName 的值为空或 null"
    fi
else
    echo "❌ 响应中不包含 appName 字段"
fi

echo ""
echo "=========================================="
echo "完整的响应结构:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE" 