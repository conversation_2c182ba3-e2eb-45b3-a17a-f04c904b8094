package com.shizhuang.scm.rulecenter.infrastructure.dal.repository;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ArchiveNodeConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 归档节点配置数据访问层
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public class ArchiveNodeConfigRepository {

    @Autowired
    private ArchiveNodeConfigMapper archiveNodeConfigMapper;

    /**
     * 根据任务ID查询所有节点配置
     *
     * @param taskId 任务ID
     * @return 节点配置列表
     */
    public List<ArchiveNodeConfigDO> findByTaskId(Long taskId) {
        return archiveNodeConfigMapper.selectByTaskId(taskId);
    }


    /**
     * 根据ID查询节点配置
     *
     * @param id 节点配置ID
     * @return 节点配置
     */
    public ArchiveNodeConfigDO findById(Long id) {
        return archiveNodeConfigMapper.selectByPrimaryKey(id);
    }

    /**
     * 保存节点配置
     *
     * @param nodeConfig 节点配置
     * @return 保存后的节点配置
     */
    public ArchiveNodeConfigDO save(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig.getId() == null) {
            archiveNodeConfigMapper.insertSelective(nodeConfig);
        } else {
            archiveNodeConfigMapper.updateByPrimaryKeySelective(nodeConfig);
        }
        return nodeConfig;
    }


    /**
     * 批量插入节点配置
     *
     * @param nodeConfigs 节点配置列表
     * @return 插入数量
     */
    public int batchInsert(List<ArchiveNodeConfigDO> nodeConfigs) {
        if (nodeConfigs == null || nodeConfigs.isEmpty()) {
            return 0;
        }
        return archiveNodeConfigMapper.batchInsert(nodeConfigs);
    }


    /**
     * 根据任务ID删除所有节点配置
     *
     * @param taskId 任务ID
     * @return 删除数量
     */
    public int deleteByTaskId(Long taskId) {
        return archiveNodeConfigMapper.deleteByTaskId(taskId);
    }

    /**
     * 根据节点ID和任务ID查询节点配置
     *
     * @param nodeId 节点ID
     * @param taskId 任务ID
     * @return 节点配置
     */
    public ArchiveNodeConfigDO findByNodeIdAndTaskId(Long nodeId, Long taskId) {
        return archiveNodeConfigMapper.selectByNodeIdAndTaskId(nodeId, taskId);
    }

    /**
     * 根据表名和任务ID查询节点配置列表
     *
     * @param tableName 表名
     * @param taskId 任务ID
     * @return 节点配置列表
     */
    public List<ArchiveNodeConfigDO> findByTableNameAndTaskId(String tableName, Long taskId) {
        return archiveNodeConfigMapper.selectByTableNameAndTaskId(tableName, taskId);
    }

    /**
     * 根据任务ID查询root节点配置
     *
     * @param taskId 任务ID
     * @return root节点配置，如果不存在则返回null
     */
    public ArchiveNodeConfigDO findRootNodeByTaskId(Long taskId) {
        return archiveNodeConfigMapper.selectRootNodeByTaskId(taskId);
    }

}