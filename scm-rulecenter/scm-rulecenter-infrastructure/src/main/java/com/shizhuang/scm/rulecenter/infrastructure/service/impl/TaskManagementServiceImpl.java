package com.shizhuang.scm.rulecenter.infrastructure.service.impl;

import com.shizhuang.duapp.id.client.core.client.IdGenerateClient;
import com.shizhuang.scm.rulecenter.infrastructure.common.ArchiveConfigException;
import com.shizhuang.scm.rulecenter.infrastructure.common.ArchiveConfigValidator;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveAppConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.TaskPropsConfig;
import com.shizhuang.scm.rulecenter.infrastructure.util.TaskPropsConfigUtil;
import com.shizhuang.scm.rulecenter.infrastructure.dal.repository.ArchiveAppConfigRepository;
import com.shizhuang.scm.rulecenter.infrastructure.dal.repository.ArchiveTaskConfigRepository;
import com.shizhuang.scm.rulecenter.infrastructure.dal.repository.ArchiveNodeConfigRepository;
import com.shizhuang.scm.rulecenter.infrastructure.service.TaskManagementService;
import com.shizhuang.scm.rulecenter.infrastructure.service.ConfigPushService;
import com.shizhuang.scm.rulecenter.infrastructure.service.QueryColumnsService;
import com.shizhuang.scm.rulecenter.api.constant.ArchiveConstants;
import com.shizhuang.scm.rulecenter.api.request.ArchiveGlobalConfigRequest;
import com.shizhuang.scm.rulecenter.infrastructure.integration.rpc.ArchiveSDKIntegrationApi;
import com.shizhuang.duapp.scp.framework.admin.sdk.request.FetchColumnsRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.ColumnsResponse;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.DatabaseColumn;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.DatabaseIndex;
import com.poizon.fusion.common.model.Result;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.util.deparser.ExpressionDeParser;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.HashSet;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.api.constant.ArchiveConstants.ARCHIVE_TYPE_CHILDREN;
import static com.shizhuang.scm.rulecenter.api.constant.ArchiveConstants.ARCHIVE_TYPE_PARENT;

/**
 * 任务管理服务实现
 * <p>
 * 提供任务的创建、更新、删除等管理功能
 * 支持应用级别配置，区分不同的接入客户端
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class TaskManagementServiceImpl implements TaskManagementService {

    @Autowired
    private ArchiveAppConfigRepository appConfigRepository;

    @Autowired
    private ArchiveTaskConfigRepository taskConfigRepository;

    @Autowired
    private ArchiveNodeConfigRepository nodeConfigRepository;

    @Autowired(required = false)
    private ConfigPushService configPushService;

    @Resource
    private IdGenerateClient idGenerateClient;

    @Autowired
    private ArchiveSDKIntegrationApi archiveSDKIntegrationApi;

    @Autowired
    private QueryColumnsService queryColumnsService;


    @Override
    @Transactional
    public ArchiveTaskConfigDO createTask(String appName, ArchiveTaskConfigDO taskConfig) {
        try {
            ArchiveConfigValidator.validateAppName(appName, "appName");
            ArchiveConfigValidator.validateNotNull(taskConfig, "taskConfig");
            // 1. 验证应用是否存在并获取应用配置ID
            ArchiveAppConfigDO appEntity = appConfigRepository.findByAppName(appName);
            if (appEntity == null) {
                // 创建默认应用配置
                Date now = new Date();
                appEntity = new ArchiveAppConfigDO();
                appEntity.setAppName(appName);
                appEntity.setEnable(true);
                appEntity.setThreads(1);
                appEntity.setLockExpireSeconds(3600);
                appEntity.setInterval(60);
                appEntity.setMaxThreads(2);
                appEntity.setVersion(now.getTime());
                appEntity.setExt(null);
                appEntity.setCreateTime(now);
                appEntity.setUpdateTime(now);
                appEntity = appConfigRepository.save(appEntity);
            }
            // 2. 生成任务ID
            Long taskId = generateTaskId();
            taskConfig.setTaskId(taskId);
            // 3. 生成版本号
            Long version = generateVersion();
            taskConfig.setVersion(version);
            // 4. 设置应用配置ID
            taskConfig.setAppConfigId(appEntity.getId());
            // 5. 设置时间戳
            Date now = new Date();
            taskConfig.setCreateTime(now);
            taskConfig.setUpdateTime(now);
            // 6. 保存到数据库
            ArchiveTaskConfigDO savedTaskConfig = taskConfigRepository.save(taskConfig);
            return savedTaskConfig;
        } catch (ArchiveConfigException e) {
            log.error("任务创建失败: appName={}, errorCode={}", appName, e.getErrorCode(), e);
            throw e;
        } catch (Exception e) {
            log.error("任务创建失败: appName={}", appName, e);
            throw ArchiveConfigException.databaseError("创建任务", e);
        }
    }

    @Override
    @Transactional
    public ArchiveTaskConfigDO updateTask(Long taskId, ArchiveTaskConfigDO taskConfig) {
        try {
            ArchiveConfigValidator.validateTaskId(taskId, "taskId");
            ArchiveConfigValidator.validateNotNull(taskConfig, "taskConfig");
            // 1. 验证任务是否存在
            ArchiveTaskConfigDO existingEntity = taskConfigRepository.findByTaskId(taskId);
            if (existingEntity == null) {
                throw ArchiveConfigException.taskNotFound(taskId);
            }
            // 2. 生成新版本号
            Long newVersion = generateVersion();
            taskConfig.setVersion(newVersion);
            taskConfig.setTaskId(taskId);
            taskConfig.setAppConfigId(existingEntity.getAppConfigId());
            taskConfig.setCreateTime(existingEntity.getCreateTime());
            taskConfig.setUpdateTime(new Date());
            taskConfig.setId(existingEntity.getId());
            // 3. 更新数据库
            ArchiveTaskConfigDO updatedTaskConfig = taskConfigRepository.save(taskConfig);
            // 4. 推送配置变更到ARK配置中心（在事务中）
            String appName = existingEntity.getAppName();
            String key = String.format(ArchiveConstants.TASK_CONFIG_KEY_PREFIX, taskId);
            configPushService.push(appName, key, taskConfig.getVersion().toString());
            log.info("任务更新成功: taskId={}, version={}", taskId, newVersion);
            return updatedTaskConfig;
        } catch (ArchiveConfigException e) {
            log.error("任务更新失败: taskId={}, errorCode={}", taskId, e.getErrorCode(), e);
            throw e;
        } catch (Exception e) {
            log.error("任务更新失败: taskId={}", taskId, e);
            throw ArchiveConfigException.databaseError("更新任务", e);
        }
    }

    @Override
    @Transactional
    public ArchiveTaskConfigDO updateTaskStatus(Long taskId, Integer status) {
        try {
            ArchiveConfigValidator.validateTaskId(taskId, "taskId");
            ArchiveConfigValidator.validateNotNull(status, "status");
            ArchiveTaskConfigDO entity = taskConfigRepository.findByTaskId(taskId);
            if (entity == null) {
                throw ArchiveConfigException.taskNotFound(taskId);
            }
            entity.setStatus(status);
            entity.setUpdateTime(new Date());
            ArchiveTaskConfigDO updatedTaskConfig = taskConfigRepository.save(entity);
            String appName = updatedTaskConfig.getAppName();
            String key = String.format(ArchiveConstants.TASK_CONFIG_KEY_PREFIX, taskId);
            configPushService.push(appName, key, updatedTaskConfig.getVersion().toString());
            log.info("任务状态更新: taskId={}, status={}", taskId, status);
            return updatedTaskConfig;
        } catch (ArchiveConfigException e) {
            log.error("任务状态更新失败: taskId={}, errorCode={}", taskId, e.getErrorCode(), e);
            throw e;
        } catch (Exception e) {
            log.error("任务状态更新失败: taskId={}, status={}", taskId, status, e);
            throw ArchiveConfigException.databaseError("更新任务状态", e);
        }
    }

    @Override
    @Transactional
    public int deleteTask(Long taskId) {
        try {
            ArchiveConfigValidator.validateTaskId(taskId, "taskId");
            ArchiveTaskConfigDO entity = taskConfigRepository.findByTaskId(taskId);
            if (entity == null) {
                throw ArchiveConfigException.taskNotFound(taskId);
            }
            // 先删除所有节点
            nodeConfigRepository.deleteByTaskId(taskId);
            // 再删除任务本身
            int deleteCount = taskConfigRepository.deleteById(entity.getId());
            // 移除配置中心配置
            String appName = entity.getAppName();
            String key = String.format(ArchiveConstants.TASK_CONFIG_KEY_PREFIX, taskId);
            configPushService.remove(appName, key);
            log.info("任务删除成功: taskId={}, deleteCount={}", taskId, deleteCount);
            return deleteCount;
        } catch (ArchiveConfigException e) {
            log.error("任务删除失败: taskId={}, errorCode={}", taskId, e.getErrorCode(), e);
            throw e;
        } catch (Exception e) {
            log.error("任务删除失败: taskId={}", taskId, e);
            throw ArchiveConfigException.databaseError("删除任务", e);
        }
    }

    @Override
    @Transactional
    public void saveNodeConfig(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null) {
            throw new ArchiveConfigException("节点配置不能为空");
        }

        // 自动获取主键列（如果未设置）
        autoSetPrimaryKeyColumn(nodeConfig);

        // 设置时间戳
        Date now = new Date();
        if (nodeConfig.getCreateTime() == null) {
            nodeConfig.setCreateTime(now);
        }
        nodeConfig.setUpdateTime(now);

        // 保存到数据库
        nodeConfigRepository.save(nodeConfig);

        // 批量更新相关节点的 queryColumns（包括当前节点、父节点、子节点）
        queryColumnsService.updateRelatedNodesQueryColumns(nodeConfig);
        // 获取任务配置以获取appName
        ArchiveTaskConfigDO taskConfig = taskConfigRepository.findByTaskId(nodeConfig.getTaskId());
        String key = String.format(ArchiveConstants.TASK_CONFIG_KEY_PREFIX, nodeConfig.getTaskId());
        configPushService.push(taskConfig.getAppName(), key, taskConfig.getVersion().toString());
    }

    @Override
    @Transactional
    public ArchiveNodeConfigDO createNodeConfig(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null) {
            throw new ArchiveConfigException("节点配置不能为空");
        }

        // 检查是否为root节点，如果是则验证是否已存在root节点
        if (Boolean.TRUE.equals(nodeConfig.getRootNode()) && nodeConfig.getTaskId() != null) {
            ArchiveNodeConfigDO existingRootNode = nodeConfigRepository.findRootNodeByTaskId(nodeConfig.getTaskId());
            if (existingRootNode != null) {
                throw new ArchiveConfigException("任务ID为 " + nodeConfig.getTaskId() + " 的配置中已存在root节点，无法重复创建root节点");
            }
        }

        // 生成节点ID（如果未设置）
        if (nodeConfig.getNodeId() == null) {
            nodeConfig.setNodeId(idGenerateClient.snowId());
        }

        // 自动获取主键列（如果未设置）
        autoSetPrimaryKeyColumn(nodeConfig);
        // 参考 ArchiveController#addTable 的校验逻辑，增加子节点验证
        if (nodeConfig.getTaskId() != null) {
            validateChildNodeConfig(nodeConfig, nodeConfig.getTaskId());
        }
        // 设置时间戳
        Date now = new Date();
        nodeConfig.setCreateTime(now);
        nodeConfig.setUpdateTime(now);

        // 批量更新相关节点的 queryColumns（包括当前节点、父节点、子节点）
        queryColumnsService.updateRelatedNodesQueryColumns(nodeConfig);

        // 保存到数据库
        nodeConfigRepository.save(nodeConfig);

        // 获取任务配置以获取appName并推送配置更新
        ArchiveTaskConfigDO taskConfig = taskConfigRepository.findByTaskId(nodeConfig.getTaskId());
        if (taskConfig != null) {
            String key = String.format(ArchiveConstants.TASK_CONFIG_KEY_PREFIX, nodeConfig.getTaskId());
            configPushService.push(taskConfig.getAppName(), key, taskConfig.getVersion().toString());
        }

        return nodeConfig;
    }

    @Override
    @Transactional
    public ArchiveNodeConfigDO updateNodeConfig(Long nodeId, ArchiveNodeConfigDO nodeConfig) {
        if (nodeId == null) {
            throw new ArchiveConfigException("节点ID不能为空");
        }
        if (nodeConfig == null) {
            throw new ArchiveConfigException("节点配置不能为空");
        }

        // 查询现有节点配置
        ArchiveNodeConfigDO existingNode = nodeConfigRepository.findByNodeIdAndTaskId(nodeId, nodeConfig.getTaskId());
        if (existingNode == null) {
            throw new ArchiveConfigException("节点不存在，节点ID: " + nodeId);
        }
        nodeConfig.setId(existingNode.getId());
        nodeConfig.setRootNode(existingNode.getRootNode());
        if (existingNode.getRootNode()) {
            nodeConfig.setArchiveType(ARCHIVE_TYPE_CHILDREN);
        }
        nodeConfig.setPrimaryKeyColumn(existingNode.getPrimaryKeyColumn());
        // 设置节点ID，确保更新的是正确的节点
        nodeConfig.setNodeId(nodeId);

        // 保留原有的创建时间，更新修改时间
        nodeConfig.setCreateTime(existingNode.getCreateTime());
        nodeConfig.setUpdateTime(new Date());

        // 自动获取主键列（如果未设置）
        autoSetPrimaryKeyColumn(nodeConfig);

        // 验证节点配置
        validateChildNodeConfig(nodeConfig, nodeConfig.getTaskId());

        // 获取应用名称用于验证节点条件
        ArchiveTaskConfigDO taskConfig = taskConfigRepository.findByTaskId(nodeConfig.getTaskId());
        if (taskConfig != null) {
            validateNodeConditions(nodeConfig, taskConfig.getAppName());
        }

        // 保存到数据库
        nodeConfigRepository.save(nodeConfig);

        // 批量更新相关节点的 queryColumns（包括当前节点、父节点、子节点）
        queryColumnsService.updateRelatedNodesQueryColumns(nodeConfig);

        // 推送配置更新
        String key = String.format(ArchiveConstants.TASK_CONFIG_KEY_PREFIX, nodeConfig.getTaskId());
        configPushService.push(taskConfig.getAppName(), key, taskConfig.getVersion().toString());

        return nodeConfig;
    }


    @Override
    public Long generateTaskId() {
        return idGenerateClient.snowId();
    }

    @Override
    public Long generateVersion() {
        return System.currentTimeMillis();
    }

    @Override
    public void validateTaskConfig(ArchiveTaskConfigDO taskConfig) {
        if (taskConfig == null) {
            throw new ArchiveConfigException("任务配置不能为空");
        }
        if (taskConfig.getDatasourceName() == null || taskConfig.getDatasourceName().trim().isEmpty()) {
            throw new ArchiveConfigException("数据源名称不能为空");
        }
        if (taskConfig.getDatabaseName() == null || taskConfig.getDatabaseName().trim().isEmpty()) {
            throw new ArchiveConfigException("数据库名称不能为空");
        }
        if (taskConfig.getTaskName() == null || taskConfig.getTaskName().trim().isEmpty()) {
            throw new ArchiveConfigException("任务名称不能为空");
        }
        if (taskConfig.getLimit() == null || taskConfig.getLimit() <= 0) {
            throw new ArchiveConfigException("扫描数量必须大于0");
        }
        if (taskConfig.getInterval() == null || taskConfig.getInterval() <= 0) {
            throw new ArchiveConfigException("扫描间隔必须大于0");
        }
        // 执行类型为指定时间时校验时间区间
        if (taskConfig.getExecutionType() != null && taskConfig.getExecutionType() == 2) { // 2-指定时间
            TaskPropsConfig timeConfig = null;
            if (taskConfig.getProps() != null) {
                timeConfig = TaskPropsConfigUtil.deserialize(taskConfig.getProps());
            }
            if (timeConfig == null || timeConfig.getStart() == null || timeConfig.getEnd() == null) {
                throw new ArchiveConfigException("时间段不能为空");
            }
            String startStr = timeConfig.getStart();
            String endStr = timeConfig.getEnd();
            java.time.LocalTime startTime = null, endTime = null;
            boolean parsed = false;
            try {
                startTime = java.time.LocalTime.parse(startStr, java.time.format.DateTimeFormatter.ofPattern("HH:mm"));
                endTime = java.time.LocalTime.parse(endStr, java.time.format.DateTimeFormatter.ofPattern("HH:mm"));
                parsed = true;
            } catch (Exception e1) {
                try {
                    startTime = java.time.LocalTime.parse(startStr, java.time.format.DateTimeFormatter.ofPattern("H:mm"));
                    endTime = java.time.LocalTime.parse(endStr, java.time.format.DateTimeFormatter.ofPattern("H:mm"));
                    parsed = true;
                } catch (Exception e2) {
                    // ignore
                }
            }
            if (!parsed) {
                throw new ArchiveConfigException("时间段格式错误，需为H:mm或HH:mm");
            }
            if (startTime.equals(endTime)) {
                throw new ArchiveConfigException("开始时间和结束时间不能相同");
            }
            int startMinutes = startTime.getHour() * 60 + startTime.getMinute();
            int endMinutes = endTime.getHour() * 60 + endTime.getMinute();
            // 如果开始时间大于等于结束时间，视为跨天
            if (startMinutes >= endMinutes) {
                // 跨天时间段必须是当天12:00及以后到次日12:00之前
                if (startTime.getHour() < 12 || endTime.getHour() >= 12) {
                    throw new ArchiveConfigException("跨天时间段必须是当天12:00及以后到次日12:00之前");
                }
            }
            int diff;
            if (startMinutes < endMinutes) {
                diff = endMinutes - startMinutes;
            } else {
                diff = (endMinutes + 1440) - startMinutes;
            }
            if (diff > 1440) {
                throw new ArchiveConfigException("时间段不能跨多天");
            }
        }
        // 其它业务规则校验可在此补充
    }


    @Override
    public void validateNodeConfigs(List<ArchiveNodeConfigDO> nodeConfigs) {
        if (nodeConfigs == null || nodeConfigs.isEmpty()) {
            throw new ArchiveConfigException("节点配置不能为空");
        }
        // 必须有且仅有一个根节点
        long rootCount = nodeConfigs.stream().filter(n -> Boolean.TRUE.equals(n.getRootNode())).count();
        if (rootCount != 1) {
            throw new ArchiveConfigException("必须有且仅有一个根节点");
        }
        for (ArchiveNodeConfigDO node : nodeConfigs) {
            validateNodeRecursive(node, nodeConfigs);
        }
    }

    @Override
    public void validateChildNodeConfig(ArchiveNodeConfigDO nodeConfig, Long taskId) {
        // 参考 ArchiveController#addTable 的校验逻辑，修改为单节点验证
        if (nodeConfig == null) {
            throw new ArchiveConfigException("节点配置不能为空");
        }

        // taskId 必须有值
        if (taskId == null) {
            throw new ArchiveConfigException("任务ID不能为空");
        }

        // 验证任务是否存在
        ArchiveTaskConfigDO taskEntity = taskConfigRepository.findByTaskId(taskId);
        if (taskEntity == null) {
            throw new ArchiveConfigException("任务不存在");
        }

        // 基本字段校验
        validateSingleNodeBasicFields(nodeConfig);

        // 如果不是根节点，parentNodeId 必须有值
        if (!Boolean.TRUE.equals(nodeConfig.getRootNode())) {
            if (nodeConfig.getParentNodeId() == null) {
                throw new ArchiveConfigException("非根节点的父节点ID不能为空");
            }
            // 关联条件不能为空
            if (nodeConfig.getRelations() == null || nodeConfig.getRelations().trim().isEmpty()) {
                throw new ArchiveConfigException("关联条件不能为空");
            }

            // 验证父节点是否存在
            validateParentNodeExists(nodeConfig.getParentNodeId(), taskId);
        }
        // 同一个任务，不允许一个表出现多次
        validateTableNotDuplicate(nodeConfig.getTableName(), taskId, nodeConfig.getNodeId());
    }

    /**
     * 校验单个节点的基本字段
     *
     * @param nodeConfig 节点配置
     */
    private void validateSingleNodeBasicFields(ArchiveNodeConfigDO nodeConfig) {
        validateNodeBasicFields(nodeConfig, false);
    }

    /**
     * 校验节点的基本字段（公共方法）
     *
     * @param nodeConfig  节点配置
     * @param isRecursive 是否为递归校验（用于区分根节点和子节点的校验逻辑）
     */
    private void validateNodeBasicFields(ArchiveNodeConfigDO nodeConfig, boolean isRecursive) {
        if (nodeConfig.getTableName() == null || nodeConfig.getTableName().trim().isEmpty()) {
            throw new ArchiveConfigException("节点表名不能为空");
        }

        // 递归校验时，非根节点必须设置归档顺序
        if (isRecursive && !Boolean.TRUE.equals(nodeConfig.getRootNode()) && nodeConfig.getArchiveType() == null) {
            throw new ArchiveConfigException("归档顺序不能为空");
        }

        // 所有节点都要校验 查询条件和索引字段不能全部为空
        boolean condEmpty = nodeConfig.getCondition() == null || nodeConfig.getCondition().trim().isEmpty();
        boolean idxEmpty = nodeConfig.getIndexColumn() == null || nodeConfig.getIndexColumn().trim().isEmpty() || nodeConfig.getArchiveType() == null;
        if (condEmpty && idxEmpty) {
            throw new ArchiveConfigException("节点的查询条件和索引字段不能全部为空");
        }

        // 校验插件字段格式
        if (nodeConfig.getPlugins() != null && !nodeConfig.getPlugins().trim().isEmpty()) {
            try {
                com.fasterxml.jackson.databind.JsonNode pluginsNode = new com.fasterxml.jackson.databind.ObjectMapper().readTree(nodeConfig.getPlugins());
                if (!pluginsNode.isArray()) {
                    throw new ArchiveConfigException("插件字段必须为JSON数组");
                }
            } catch (Exception e) {
                throw new ArchiveConfigException("插件字段必须为JSON数组");
            }
        }

        // 主键列不能为空或无法识别
        if (nodeConfig.getPrimaryKeyColumn() == null || nodeConfig.getPrimaryKeyColumn().trim().isEmpty()) {
            throw new ArchiveConfigException("节点主键列不能为空或无法识别");
        }
    }

    /**
     * 验证父节点是否存在
     *
     * @param parentNodeId 父节点ID
     * @param taskId       任务ID
     */
    private void validateParentNodeExists(Long parentNodeId, Long taskId) {
        ArchiveNodeConfigDO parentNode = nodeConfigRepository.findByNodeIdAndTaskId(parentNodeId, taskId);
        if (parentNode == null) {
            throw new ArchiveConfigException("父节点不存在，父节点ID: " + parentNodeId);
        }
    }

    /**
     * 验证表名在同一任务中不重复
     *
     * @param tableName     表名
     * @param taskId        任务ID
     * @param currentNodeId 当前节点ID（更新时排除自己）
     */
    private void validateTableNotDuplicate(String tableName, Long taskId, Long currentNodeId) {
        List<ArchiveNodeConfigDO> existingNodes = nodeConfigRepository.findByTableNameAndTaskId(tableName, taskId);
        if (existingNodes != null && !existingNodes.isEmpty()) {
            // 如果是更新操作，排除当前节点
            if (currentNodeId != null) {
                existingNodes = existingNodes.stream()
                        .filter(node -> !currentNodeId.equals(node.getNodeId()))
                        .collect(Collectors.toList());
            }

            if (!existingNodes.isEmpty()) {
                throw new ArchiveConfigException("同一任务中不允许表名重复，表名: " + tableName);
            }
        }
    }


    private void validateNodeRecursive(ArchiveNodeConfigDO node, List<ArchiveNodeConfigDO> allNodes) {
        validateNodeRecursive(node, allNodes, new java.util.HashSet<>());
    }

    private void validateNodeRecursive(ArchiveNodeConfigDO node, List<ArchiveNodeConfigDO> allNodes, java.util.Set<Long> visited) {
        if (node.getNodeId() != null && !visited.add(node.getNodeId())) {
            throw new ArchiveConfigException("节点树存在环，节点ID: " + node.getNodeId());
        }

        // 调用基本字段校验方法，传入true表示递归校验（会对非根节点进行归档顺序校验）
        validateNodeBasicFields(node, true);

        // 非根节点必须有有效的关联条件
        if (!Boolean.TRUE.equals(node.getRootNode())) {
            String relationConditions = getRelationConditions(node);
            if (relationConditions == null || relationConditions.trim().isEmpty()) {
                throw new ArchiveConfigException("非根节点必须有有效的关联条件");
            }
        }

        // 校验 WHERE 条件字段存在与语法
        if (node.getCondition() != null && !node.getCondition().trim().isEmpty()) {
            if (node.getCondition().toLowerCase().contains(";")) {
                throw new ArchiveConfigException("WHERE条件不能包含分号");
            }
        }

        // 校验关联条件（relations 或 props.relationConditions）
        String relationConditions = getRelationConditions(node);
        if (relationConditions != null && !relationConditions.trim().isEmpty()) {
            String[] conds = relationConditions.split("(?i)\\s+and\\s+");
            for (String cond : conds) {
                cond = cond.trim();
                if (!cond.contains("=")) {
                    throw new ArchiveConfigException("关联条件必须为等值关联: " + cond);
                }
                String[] parts = cond.split("=");
                if (parts.length != 2) {
                    throw new ArchiveConfigException("无效的关联条件格式: " + cond);
                }
            }
        }

        // 校验分片字段
        if (Boolean.TRUE.equals(node.getShardingEnabled())) {
            if (node.getShardingField() == null || node.getShardingField().trim().isEmpty()) {
                throw new ArchiveConfigException("启用分片时，分片字段不能为空");
            }
        }

        // 递归校验子节点（如有）
        List<ArchiveNodeConfigDO> children = findChildren(node, allNodes);
        for (ArchiveNodeConfigDO child : children) {
            validateNodeRecursive(child, allNodes, visited);
        }
    }

    // 从 props JSON 中获取 reserveDays
    private Integer getReserveDaysFromProps(String props) {
        if (props == null) return null;
        try {
            com.fasterxml.jackson.databind.JsonNode node = new com.fasterxml.jackson.databind.ObjectMapper().readTree(props);
            if (node.has(ArchiveConstants.PROPS_KEY_RESERVE_DAYS)) {
                return node.get(ArchiveConstants.PROPS_KEY_RESERVE_DAYS).asInt();
            }
        } catch (Exception ignored) {
        }
        return null;
    }

    // 从 props JSON 中获取 indexStart
    private Long getIndexStartFromProps(String props) {
        if (props == null) return null;
        try {
            com.fasterxml.jackson.databind.JsonNode node = new com.fasterxml.jackson.databind.ObjectMapper().readTree(props);
            if (node.has(ArchiveConstants.PROPS_KEY_INDEX_START)) {
                return node.get(ArchiveConstants.PROPS_KEY_INDEX_START).asLong();
            }
        } catch (Exception ignored) {
        }
        return null;
    }

    // 获取关联条件
    private String getRelationConditions(ArchiveNodeConfigDO node) {
        // 优先 relations 字段
        if (node.getRelations() != null && !node.getRelations().trim().isEmpty()) {
            return node.getRelations();
        }
        // 其次 props.relationConditions
        if (node.getProps() != null) {
            try {
                com.fasterxml.jackson.databind.JsonNode propsNode = new com.fasterxml.jackson.databind.ObjectMapper().readTree(node.getProps());
                if (propsNode.has(ArchiveConstants.PROPS_KEY_RELATION_CONDITIONS)) {
                    return propsNode.get(ArchiveConstants.PROPS_KEY_RELATION_CONDITIONS).asText();
                }
            } catch (Exception ignored) {
            }
        }
        return null;
    }

    // 查找子节点
    private List<ArchiveNodeConfigDO> findChildren(ArchiveNodeConfigDO parent, List<ArchiveNodeConfigDO> allNodes) {
        List<ArchiveNodeConfigDO> children = new java.util.ArrayList<>();
        if (parent.getNodeId() == null) return children;
        for (ArchiveNodeConfigDO node : allNodes) {
            if (parent.getNodeId().equals(node.getParentNodeId())) {
                children.add(node);
            }
        }
        return children;
    }

    @Override
    public void globalConfig(ArchiveGlobalConfigRequest request) {
        ArchiveAppConfigDO appConfig = appConfigRepository.findByAppName(request.getAppName());
        if (appConfig == null) {
            appConfig = new ArchiveAppConfigDO();
            appConfig.setAppName(request.getAppName());
            appConfig.setCreateTime(new java.util.Date());
        }
        if (request.getThreads() != null) {
            appConfig.setThreads(request.getThreads());
        }
        if (request.getEnable() != null) {
            appConfig.setEnable(request.getEnable());
        }
        if (request.getInterval() != null) {
            appConfig.setInterval(request.getInterval());
        }
        if (request.getLockExpireSeconds() != null) {
            appConfig.setLockExpireSeconds(request.getLockExpireSeconds());
        }
        appConfig.setUpdateTime(new java.util.Date());
        // 可选：生成新版本号
        appConfig.setVersion(System.currentTimeMillis());
        appConfigRepository.save(appConfig);
    }

    @Override
    public void validateNodeConditions(ArchiveNodeConfigDO nodeConfig, String appName) {
        if (nodeConfig == null) {
            throw new ArchiveConfigException("节点配置不能为空");
        }
        String conditions = nodeConfig.getCondition();
        if (conditions == null || conditions.trim().isEmpty()) {
            // 根节点允许条件为空，但需要检查索引字段
            if (Boolean.TRUE.equals(nodeConfig.getRootNode())) {
                if (nodeConfig.getIndexColumn() == null || nodeConfig.getIndexColumn().trim().isEmpty()) {
                    throw new ArchiveConfigException("根节点的查询条件和索引字段不能全部为空");
                }
            }
            return;
        }

        // 基本语法检查
        if (conditions.toLowerCase().contains(";")) {
            throw new ArchiveConfigException("WHERE条件不能包含分号");
        }

        // 获取表结构信息
        String tableName = nodeConfig.getTableName();
        String datasourceName = nodeConfig.getDatasourceName();
        String databaseName = nodeConfig.getDatabaseName();
        if (tableName == null || tableName.trim().isEmpty()) {
            throw new ArchiveConfigException("表名不能为空");
        }
        try {
            // 获取表结构
            FetchColumnsRequest request = FetchColumnsRequest.builder()
                    .dataSourceName(datasourceName)
                    .databaseName(databaseName)
                    .tableName(tableName)
                    .build();
            ColumnsResponse columnsResponse = archiveSDKIntegrationApi.fetchColumns(request, appName).getData();
            if (columnsResponse == null || CollectionUtils.isEmpty(columnsResponse.getColumns())) {
                throw new ArchiveConfigException("无法获取表结构信息: " + tableName);
            }
            // 验证SQL WHERE条件语法和字段引用
            validateSqlWhereCondition(conditions, tableName, columnsResponse.getColumns());
        } catch (Exception e) {
            if (e instanceof ArchiveConfigException) {
                throw e;
            }
            log.error("验证节点条件失败: tableName={}, conditions={}", tableName, conditions, e);
            throw new ArchiveConfigException("验证节点条件失败: " + e.getMessage());
        }
    }

    /**
     * 校验SQL WHERE条件语法和字段引用
     * 参考 ArchiveDomainService#validateSqlWhereCondition
     *
     * @param whereCondition SQL WHERE条件（不包含WHERE关键字）
     * @param tableName      表名
     * @param tableColumns   表的列信息
     */
    private void validateSqlWhereCondition(String whereCondition, String tableName,
                                           List<DatabaseColumn> tableColumns) {
        if (whereCondition == null || whereCondition.trim().isEmpty()) {
            return;
        }
        try {
            Expression extra = CCJSqlParserUtil.parseCondExpression(whereCondition);
            Set<String> columns = new HashSet<>();
            extra.accept(new ExpressionDeParser() {
                @Override
                public void visit(Column column) {
                    // 只取列名（不含表名），如需表名可用 column.getFullyQualifiedName()
                    columns.add(column.getColumnName().replace("`", ""));
                }
            });
            Set<String> tableColumnNames = tableColumns.stream().map(DatabaseColumn::getName).collect(Collectors.toSet());
            List<String> columnList = columns.stream().filter(columnName -> !tableColumnNames.contains(columnName)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(columnList)) {
                throw new ArchiveConfigException("WHERE条件字段不存在: " + columnList);
            }
        } catch (Exception e) {
            throw new ArchiveConfigException("WHERE条件语法错误: " + e.getMessage());
        }
    }

    /**
     * 根据表名获取主键列（内部方法）
     *
     * @param tableName      表名
     * @param datasourceName 数据源名称
     * @param databaseName   数据库名称
     * @param appName        应用名称
     * @return 主键列名，如果没有主键则返回null
     */
    private String getPrimaryKeyColumn(String tableName, String datasourceName, String databaseName, String appName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            throw new ArchiveConfigException("表名不能为空");
        }
        if (datasourceName == null || datasourceName.trim().isEmpty()) {
            throw new ArchiveConfigException("数据源名称不能为空");
        }
        if (databaseName == null || databaseName.trim().isEmpty()) {
            throw new ArchiveConfigException("数据库名称不能为空");
        }
        if (appName == null || appName.trim().isEmpty()) {
            throw new ArchiveConfigException("应用名称不能为空");
        }

        try {
            // 构建获取表结构的请求
            FetchColumnsRequest request = FetchColumnsRequest.builder()
                    .dataSourceName(datasourceName)
                    .databaseName(databaseName)
                    .tableName(tableName)
                    .build();

            // 调用SDK API获取表结构
            Result<ColumnsResponse> result = archiveSDKIntegrationApi.fetchColumns(request, appName);
            if (result.getData() == null || CollectionUtils.isEmpty(result.getData().getColumns())) {
                log.error("获取表结构失败: tableName={}, datasourceName={}, databaseName={}, appName={}",
                        tableName, datasourceName, databaseName, appName);
                throw new ArchiveConfigException("无法获取表结构信息: " + tableName);
            }

            // 查找主键索引
            if (!CollectionUtils.isEmpty(result.getData().getIndexes())) {
                return result.getData().getIndexes().stream()
                        .filter(DatabaseIndex::isPrimary)
                        .findFirst()
                        .map(index -> index.getColumns().get(0).getColumn())
                        .orElse(null);
            }

            log.warn("表 {} 没有找到主键索引", tableName);
            return null;

        } catch (Exception e) {
            if (e instanceof ArchiveConfigException) {
                throw e;
            }
            log.error("获取主键列失败: tableName={}, datasourceName={}, databaseName={}, appName={}",
                    tableName, datasourceName, databaseName, appName, e);
            throw new ArchiveConfigException("获取主键列失败: " + e.getMessage());
        }
    }

    /**
     * 自动设置主键列（如果未设置）
     *
     * @param nodeConfig 节点配置
     */
    private void autoSetPrimaryKeyColumn(ArchiveNodeConfigDO nodeConfig) {
        // 如果主键列已经设置，则跳过
        if (nodeConfig.getPrimaryKeyColumn() != null && !nodeConfig.getPrimaryKeyColumn().trim().isEmpty()) {
            return;
        }

        // 检查必要参数
        if (nodeConfig.getTableName() == null || nodeConfig.getTableName().trim().isEmpty()) {
            log.warn("节点表名为空，无法获取主键列: nodeId={}", nodeConfig.getNodeId());
            return;
        }

        if (nodeConfig.getDatasourceName() == null || nodeConfig.getDatasourceName().trim().isEmpty()) {
            log.warn("节点数据源为空，无法获取主键列: nodeId={}, tableName={}",
                    nodeConfig.getNodeId(), nodeConfig.getTableName());
            return;
        }

        if (nodeConfig.getDatabaseName() == null || nodeConfig.getDatabaseName().trim().isEmpty()) {
            log.warn("节点数据库为空，无法获取主键列: nodeId={}, tableName={}",
                    nodeConfig.getNodeId(), nodeConfig.getTableName());
            return;
        }

        // 获取应用名称
        String appName = null;
        if (nodeConfig.getTaskId() != null) {
            ArchiveTaskConfigDO taskConfig = taskConfigRepository.findByTaskId(nodeConfig.getTaskId());
            if (taskConfig != null) {
                appName = taskConfig.getAppName();
            }
        }

        if (appName == null || appName.trim().isEmpty()) {
            log.warn("无法获取应用名称，无法获取主键列: nodeId={}, tableName={}",
                    nodeConfig.getNodeId(), nodeConfig.getTableName());
            return;
        }

        try {
            String primaryKeyColumn = getPrimaryKeyColumn(
                    nodeConfig.getTableName(),
                    nodeConfig.getDatasourceName(),
                    nodeConfig.getDatabaseName(),
                    appName);

            if (primaryKeyColumn != null) {
                nodeConfig.setPrimaryKeyColumn(primaryKeyColumn);
                log.info("自动获取主键列成功: nodeId={}, tableName={}, primaryKeyColumn={}",
                        nodeConfig.getNodeId(), nodeConfig.getTableName(), primaryKeyColumn);
            } else {
                log.warn("表 {} 没有找到主键列: nodeId={}",
                        nodeConfig.getTableName(), nodeConfig.getNodeId());
            }
        } catch (Exception e) {
            log.error("自动获取主键列失败: nodeId={}, tableName={}, error={}",
                    nodeConfig.getNodeId(), nodeConfig.getTableName(), e.getMessage(), e);
            // 抛出异常，不吞没错误
            throw e;
        }
    }
}
