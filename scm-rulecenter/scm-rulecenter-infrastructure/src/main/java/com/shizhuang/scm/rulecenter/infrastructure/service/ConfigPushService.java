package com.shizhuang.scm.rulecenter.infrastructure.service;

/**
 * 配置推送服务接口
 * 只需推送和移除配置，参数中必须传递 appName
 */
public interface ConfigPushService {
    /**
     * 推送配置
     * @param appName 应用名称（必须）
     * @param key 配置key
     * @param value 配置内容
     */
    void push(String appName, String key, String value);

    /**
     * 移除配置
     * @param appName 应用名称（必须）
     * @param key 配置key
     */
    void remove(String appName, String key);
}