package com.shizhuang.scm.rulecenter.infrastructure.dal.data;

import java.io.Serializable;
import java.util.List;

/**
 * 归档任务配置（包含节点详情）
 * <p>
 * 继承自 ArchiveTaskConfigDO，增加了 taskDetails 字段来描述节点信息
 * 提供更完整的任务配置信息，包含所有相关的节点配置
 *
 * <AUTHOR> Team
 * @version 1.0
 */
public class ArchiveTaskConfigWithDetailsDO extends ArchiveTaskConfigDO implements Serializable {

    /**
     * 任务节点详情列表
     */
    private List<ArchiveNodeConfigDO> taskDetails;

    private static final long serialVersionUID = 1L;

    public ArchiveTaskConfigWithDetailsDO() {
        super();
    }

    /**
     * 从 ArchiveTaskConfigDO 创建包含详情的对象
     *
     * @param taskConfig  基础任务配置
     * @param nodeConfigs 节点配置列表
     * @return 包含详情的任务配置对象
     */
    public static ArchiveTaskConfigWithDetailsDO from(ArchiveTaskConfigDO taskConfig, List<ArchiveNodeConfigDO> nodeConfigs) {
        ArchiveTaskConfigWithDetailsDO result = new ArchiveTaskConfigWithDetailsDO();
        // 复制基础任务配置的所有属性
        result.setId(taskConfig.getId());
        result.setAppConfigId(taskConfig.getAppConfigId());
        result.setTaskId(taskConfig.getTaskId());
        result.setTaskName(taskConfig.getTaskName());
        result.setDatasourceName(taskConfig.getDatasourceName());
        result.setDatabaseName(taskConfig.getDatabaseName());
        result.setVersion(taskConfig.getVersion());
        result.setStatus(taskConfig.getStatus());
        result.setMode(taskConfig.getMode());
        result.setInterval(taskConfig.getInterval());
        result.setLimit(taskConfig.getLimit());
        result.setExecutionType(taskConfig.getExecutionType());
        result.setProps(taskConfig.getProps());
        result.setCreateTime(taskConfig.getCreateTime());
        result.setUpdateTime(taskConfig.getUpdateTime());
        result.setAppName(taskConfig.getAppName());
        // 设置节点详情
        result.setTaskDetails(nodeConfigs);
        return result;
    }

    public List<ArchiveNodeConfigDO> getTaskDetails() {
        return taskDetails;
    }

    public void setTaskDetails(List<ArchiveNodeConfigDO> taskDetails) {
        this.taskDetails = taskDetails;
    }
} 