package com.shizhuang.scm.rulecenter.infrastructure.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shizhuang.scm.rulecenter.infrastructure.common.ArchiveConfigException;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.repository.ArchiveNodeConfigRepository;
import com.shizhuang.scm.rulecenter.infrastructure.service.QueryColumnsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 查询字段计算服务实现
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
@Service
public class QueryColumnsServiceImpl implements QueryColumnsService {

    @Autowired
    private ArchiveNodeConfigRepository nodeConfigRepository;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void computeQueryColumns(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null) {
            throw new ArchiveConfigException("节点配置不能为空");
        }
        Set<String> queryColumns = new HashSet<>();
        // 1. 添加主键字段
        if (nodeConfig.getPrimaryKeyColumn() != null && !nodeConfig.getPrimaryKeyColumn().trim().isEmpty()) {
            queryColumns.add(nodeConfig.getPrimaryKeyColumn().trim());
        }

        // 2. 添加索引字段
        if (nodeConfig.getIndexColumn() != null && !nodeConfig.getIndexColumn().trim().isEmpty()) {
            queryColumns.add(nodeConfig.getIndexColumn().trim());
        }

        // 3. 添加关联关系中的当前表字段
        addCurrentNodeRelationFields(nodeConfig, queryColumns);

        // 4. 添加子节点关联关系中的关联表字段（relatedColumn）
        addChildNodeRelationFields(nodeConfig, queryColumns);

        // 5. 将Set转换为JSON字符串
        setQueryColumnsJson(nodeConfig, queryColumns);
    }

    @Override
    @Transactional
    public void updateParentQueryColumns(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null || nodeConfig.getParentNodeId() == null || nodeConfig.getTaskId() == null) {
            return;
        }
        // 查询父节点
        ArchiveNodeConfigDO parentNode = nodeConfigRepository.findByNodeIdAndTaskId(
                nodeConfig.getParentNodeId(), nodeConfig.getTaskId());
        if (parentNode != null) {
            // 重新计算父节点的 queryColumns
            computeQueryColumns(parentNode);
            // 更新父节点
            parentNode.setUpdateTime(new Date());
            nodeConfigRepository.save(parentNode);
            log.debug("更新父节点queryColumns完成: parentNodeId={}, queryColumns={}",
                    parentNode.getNodeId(), parentNode.getQueryColumns());
        }
    }

    @Override
    @Transactional
    public void updateRelatedNodesQueryColumns(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null || nodeConfig.getTaskId() == null) {
            return;
        }
        try {
            // 1. 更新当前节点
            computeQueryColumns(nodeConfig);
            // 2. 更新父节点（如果存在）
            updateParentQueryColumns(nodeConfig);
            log.debug("批量更新相关节点queryColumns完成: nodeId={}", nodeConfig.getNodeId());
        } catch (Exception e) {
            log.error("批量更新相关节点queryColumns失败: nodeId={}", nodeConfig.getNodeId(), e);
            throw new ArchiveConfigException("批量更新相关节点queryColumns失败: " + e.getMessage());
        }
    }

    /**
     * 添加当前节点关联关系中的字段
     */
    private void addCurrentNodeRelationFields(ArchiveNodeConfigDO nodeConfig, Set<String> queryColumns) {
        if (nodeConfig.getRelations() == null || nodeConfig.getRelations().trim().isEmpty()) {
            return;
        }

        try {
            List<com.shizhuang.scm.rulecenter.api.dto.ColumnRelation> relations =
                    objectMapper.readValue(nodeConfig.getRelations(),
                            new com.fasterxml.jackson.core.type.TypeReference<List<com.shizhuang.scm.rulecenter.api.dto.ColumnRelation>>() {
                            });

            if (relations != null) {
                for (com.shizhuang.scm.rulecenter.api.dto.ColumnRelation relation : relations) {
                    if (relation.getCurrentColumn() != null && !relation.getCurrentColumn().trim().isEmpty()) {
                        queryColumns.add(relation.getCurrentColumn().trim());
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析当前节点关联关系失败，跳过关联字段计算: relations={}, error={}", nodeConfig.getRelations(), e.getMessage(), e);
            throw new ArchiveConfigException("解析当前节点关联关系失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加子节点关联关系中的字段
     */
    private void addChildNodeRelationFields(ArchiveNodeConfigDO nodeConfig, Set<String> queryColumns) {
        if (nodeConfig.getTaskId() == null) {
            return;
        }
        try {
            // 查询所有子节点
            List<ArchiveNodeConfigDO> allNodes = nodeConfigRepository.findByTaskId(nodeConfig.getTaskId());
            List<ArchiveNodeConfigDO> childNodes = allNodes.stream()
                    .filter(child -> nodeConfig.getNodeId().equals(child.getParentNodeId()))
                    .collect(Collectors.toList());
            for (ArchiveNodeConfigDO childNode : childNodes) {
                addChildNodeRelationFieldsForNode(childNode, nodeConfig, queryColumns);
            }
                } catch (Exception e) {
            log.error("查询子节点失败: nodeId={}, taskId={}, error={}",
                nodeConfig.getNodeId(), nodeConfig.getTaskId(), e.getMessage(), e);
            throw new RuntimeException("查询子节点失败: nodeId=" + nodeConfig.getNodeId() + ", taskId=" + nodeConfig.getTaskId(), e);
        }
    }

    /**
     * 为单个子节点添加关联字段
     */
    private void addChildNodeRelationFieldsForNode(ArchiveNodeConfigDO childNode, ArchiveNodeConfigDO currentNode, Set<String> queryColumns) {
        if (childNode.getRelations() == null || childNode.getRelations().trim().isEmpty()) {
            return;
        }

        try {
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            List<com.shizhuang.scm.rulecenter.api.dto.ColumnRelation> childRelations =
                    objectMapper.readValue(childNode.getRelations(),
                            new com.fasterxml.jackson.core.type.TypeReference<List<com.shizhuang.scm.rulecenter.api.dto.ColumnRelation>>() {
                            });

            if (childRelations != null) {
                for (com.shizhuang.scm.rulecenter.api.dto.ColumnRelation childRelation : childRelations) {
                    // 检查子节点的关联关系是否指向当前节点
                    if (childRelation.getRelatedTable() != null &&
                            childRelation.getRelatedTable().equals(currentNode.getTableName()) &&
                            childRelation.getRelatedColumn() != null &&
                            !childRelation.getRelatedColumn().trim().isEmpty()) {
                        queryColumns.add(childRelation.getRelatedColumn().trim());
                    }
                }
            }
                } catch (Exception e) {
            log.error("解析子节点关联关系失败: childNodeId={}, relations={}, error={}",
                childNode.getNodeId(), childNode.getRelations(), e.getMessage(), e);
            throw new RuntimeException("解析子节点关联关系失败: childNodeId=" + childNode.getNodeId() + ", relations=" + childNode.getRelations(), e);
        }
    }

    /**
     * 设置 queryColumns JSON 字符串
     */
    private void setQueryColumnsJson(ArchiveNodeConfigDO nodeConfig, Set<String> queryColumns) {
        if (!CollectionUtils.isEmpty(queryColumns)) {
            try {
                String queryColumnsJson = objectMapper.writeValueAsString(new ArrayList<>(queryColumns));
                nodeConfig.setQueryColumns(queryColumnsJson);
                log.debug("计算queryColumns完成: nodeId={}, queryColumns={}", nodeConfig.getNodeId(), queryColumnsJson);
            } catch (Exception e) {
                log.error("序列化queryColumns失败: nodeId={}, queryColumns={}", nodeConfig.getNodeId(), queryColumns, e);
                throw new ArchiveConfigException("序列化queryColumns失败: " + e.getMessage());
            }
        } else {
            // 如果没有计算到任何字段，设置为空数组
            nodeConfig.setQueryColumns("[]");
        }
    }
} 