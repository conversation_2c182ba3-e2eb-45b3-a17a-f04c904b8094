package com.shizhuang.scm.rulecenter.infrastructure.dal.mapper;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ArchiveNodeConfigMapper {
    int insert(ArchiveNodeConfigDO record);
    int insertSelective(ArchiveNodeConfigDO record);
    int deleteByPrimaryKey(@Param("id") Long id);
    int updateByPrimaryKeySelective(ArchiveNodeConfigDO record);
    int updateByPrimaryKey(ArchiveNodeConfigDO record);
    ArchiveNodeConfigDO selectByPrimaryKey(@Param("id") Long id);
    List<ArchiveNodeConfigDO> selectAll();
    List<ArchiveNodeConfigDO> selectByTaskId(@Param("taskId") Long taskId);
    ArchiveNodeConfigDO selectByTaskIdAndNodeId(@Param("taskId") Long taskId, @Param("nodeId") Long nodeId);
    
    // 批量操作方法
    int batchInsert(@Param("list") List<ArchiveNodeConfigDO> nodeConfigs);
    int batchUpdate(@Param("list") List<ArchiveNodeConfigDO> nodeConfigs);
    int batchDeleteByIds(@Param("ids") List<Long> ids);
    int deleteByTaskId(@Param("taskId") Long taskId);

    // 新增查询方法
    ArchiveNodeConfigDO selectByNodeIdAndTaskId(@Param("nodeId") Long nodeId, @Param("taskId") Long taskId);
    List<ArchiveNodeConfigDO> selectByTableNameAndTaskId(@Param("tableName") String tableName, @Param("taskId") Long taskId);
    
    // 根据任务ID查询root节点
    ArchiveNodeConfigDO selectRootNodeByTaskId(@Param("taskId") Long taskId);
}