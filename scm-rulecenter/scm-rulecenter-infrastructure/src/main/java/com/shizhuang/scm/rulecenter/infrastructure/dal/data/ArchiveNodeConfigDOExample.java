package com.shizhuang.scm.rulecenter.infrastructure.dal.data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ArchiveNodeConfigDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public ArchiveNodeConfigDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        /**
         * 主键ID
         */
        public Criteria andIdEqualTo(Long value) { addCriterion("id =", value, "id"); return (Criteria) this; }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        /**
         * 任务ID
         */
        public Criteria andTaskIdEqualTo(Long value) { addCriterion("task_id =", value, "taskId"); return (Criteria) this; }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andNodeIdIsNull() {
            addCriterion("node_id is null");
            return (Criteria) this;
        }

        public Criteria andNodeIdIsNotNull() {
            addCriterion("node_id is not null");
            return (Criteria) this;
        }

        /**
         * 节点ID
         */
        public Criteria andNodeIdEqualTo(Long value) { addCriterion("node_id =", value, "nodeId"); return (Criteria) this; }

        public Criteria andNodeIdNotEqualTo(Long value) {
            addCriterion("node_id <>", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdIn(List<Long> values) {
            addCriterion("node_id in", values, "nodeId");
            return (Criteria) this;
        }

        public Criteria andParentNodeIdIsNull() {
            addCriterion("parent_node_id is null");
            return (Criteria) this;
        }

        public Criteria andParentNodeIdIsNotNull() {
            addCriterion("parent_node_id is not null");
            return (Criteria) this;
        }

        /**
         * 父节点ID
         */
        public Criteria andParentNodeIdEqualTo(Long value) { addCriterion("parent_node_id =", value, "parentNodeId"); return (Criteria) this; }

        public Criteria andParentNodeIdNotEqualTo(Long value) {
            addCriterion("parent_node_id <>", value, "parentNodeId");
            return (Criteria) this;
        }

        public Criteria andParentNodeIdIn(List<Long> values) {
            addCriterion("parent_node_id in", values, "parentNodeId");
            return (Criteria) this;
        }

        public Criteria andTableNameIsNull() {
            addCriterion("table_name is null");
            return (Criteria) this;
        }

        public Criteria andTableNameIsNotNull() {
            addCriterion("table_name is not null");
            return (Criteria) this;
        }

        /**
         * 表名
         */
        public Criteria andTableNameEqualTo(String value) { addCriterion("table_name =", value, "tableName"); return (Criteria) this; }

        public Criteria andTableNameNotEqualTo(String value) {
            addCriterion("table_name <>", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameLike(String value) {
            addCriterion("table_name like", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameIn(List<String> values) {
            addCriterion("table_name in", values, "tableName");
            return (Criteria) this;
        }

        public Criteria andConditionIsNull() {
            addCriterion("condition is null");
            return (Criteria) this;
        }

        public Criteria andConditionIsNotNull() {
            addCriterion("condition is not null");
            return (Criteria) this;
        }

        /**
         * 查询条件
         */
        public Criteria andConditionEqualTo(String value) { addCriterion("condition =", value, "condition"); return (Criteria) this; }

        public Criteria andConditionNotEqualTo(String value) {
            addCriterion("condition <>", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionLike(String value) {
            addCriterion("condition like", value, "condition");
            return (Criteria) this;
        }

        public Criteria andDatasourceNameIsNull() {
            addCriterion("datasource_name is null");
            return (Criteria) this;
        }

        public Criteria andDatasourceNameIsNotNull() {
            addCriterion("datasource_name is not null");
            return (Criteria) this;
        }

        /**
         * 节点级数据源名称
         */
        public Criteria andDatasourceNameEqualTo(String value) { addCriterion("datasource_name =", value, "datasourceName"); return (Criteria) this; }

        public Criteria andDatasourceNameNotEqualTo(String value) {
            addCriterion("datasource_name <>", value, "datasourceName");
            return (Criteria) this;
        }

        public Criteria andDatasourceNameLike(String value) {
            addCriterion("datasource_name like", value, "datasourceName");
            return (Criteria) this;
        }

        public Criteria andDatasourceNameIn(List<String> values) {
            addCriterion("datasource_name in", values, "datasourceName");
            return (Criteria) this;
        }

        public Criteria andDatabaseNameIsNull() {
            addCriterion("database_name is null");
            return (Criteria) this;
        }

        public Criteria andDatabaseNameIsNotNull() {
            addCriterion("database_name is not null");
            return (Criteria) this;
        }

        /**
         * 节点级数据库名称
         */
        public Criteria andDatabaseNameEqualTo(String value) { addCriterion("database_name =", value, "databaseName"); return (Criteria) this; }

        public Criteria andDatabaseNameNotEqualTo(String value) {
            addCriterion("database_name <>", value, "databaseName");
            return (Criteria) this;
        }

        public Criteria andDatabaseNameLike(String value) {
            addCriterion("database_name like", value, "databaseName");
            return (Criteria) this;
        }

        public Criteria andDatabaseNameIn(List<String> values) {
            addCriterion("database_name in", values, "databaseName");
            return (Criteria) this;
        }

        public Criteria andIsArchiveIsNull() {
            addCriterion("is_archive is null");
            return (Criteria) this;
        }

        public Criteria andIsArchiveIsNotNull() {
            addCriterion("is_archive is not null");
            return (Criteria) this;
        }

        /**
         * 是否归档
         */
        public Criteria andIsArchiveEqualTo(Boolean value) { addCriterion("is_archive =", value, "isArchive"); return (Criteria) this; }

        public Criteria andIsArchiveNotEqualTo(Boolean value) {
            addCriterion("is_archive <>", value, "isArchive");
            return (Criteria) this;
        }

        public Criteria andPrimaryKeyColumnIsNull() {
            addCriterion("primary_key_column is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryKeyColumnIsNotNull() {
            addCriterion("primary_key_column is not null");
            return (Criteria) this;
        }

        /**
         * 主键列
         */
        public Criteria andPrimaryKeyColumnEqualTo(String value) { addCriterion("primary_key_column =", value, "primaryKeyColumn"); return (Criteria) this; }

        public Criteria andPrimaryKeyColumnNotEqualTo(String value) {
            addCriterion("primary_key_column <>", value, "primaryKeyColumn");
            return (Criteria) this;
        }

        public Criteria andPrimaryKeyColumnLike(String value) {
            addCriterion("primary_key_column like", value, "primaryKeyColumn");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        /**
         * 节点状态
         */
        public Criteria andStatusEqualTo(Integer value) { addCriterion("status =", value, "status"); return (Criteria) this; }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andRootNodeIsNull() {
            addCriterion("root_node is null");
            return (Criteria) this;
        }

        public Criteria andRootNodeIsNotNull() {
            addCriterion("root_node is not null");
            return (Criteria) this;
        }

        /**
         * 是否为根节点
         */
        public Criteria andRootNodeEqualTo(Boolean value) { addCriterion("root_node =", value, "rootNode"); return (Criteria) this; }

        public Criteria andRootNodeNotEqualTo(Boolean value) {
            addCriterion("root_node <>", value, "rootNode");
            return (Criteria) this;
        }

        public Criteria andShardingEnabledIsNull() {
            addCriterion("sharding_enabled is null");
            return (Criteria) this;
        }

        public Criteria andShardingEnabledIsNotNull() {
            addCriterion("sharding_enabled is not null");
            return (Criteria) this;
        }

        /**
         * 是否启用分片
         */
        public Criteria andShardingEnabledEqualTo(Boolean value) { addCriterion("sharding_enabled =", value, "shardingEnabled"); return (Criteria) this; }

        public Criteria andShardingEnabledNotEqualTo(Boolean value) {
            addCriterion("sharding_enabled <>", value, "shardingEnabled");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        /**
         * 创建时间
         */
        public Criteria andCreateTimeEqualTo(Date value) { addCriterion("create_time =", value, "createTime"); return (Criteria) this; }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        /**
         * 更新时间
         */
        public Criteria andUpdateTimeEqualTo(Date value) { addCriterion("update_time =", value, "updateTime"); return (Criteria) this; }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andQueryColumnsIsNull() {
            addCriterion("query_columns is null");
            return (Criteria) this;
        }

        public Criteria andQueryColumnsIsNotNull() {
            addCriterion("query_columns is not null");
            return (Criteria) this;
        }

        /**
         * 查询字段列表（JSON格式）
         */
        public Criteria andQueryColumnsEqualTo(String value) { addCriterion("query_columns =", value, "queryColumns"); return (Criteria) this; }

        public Criteria andQueryColumnsNotEqualTo(String value) {
            addCriterion("query_columns <>", value, "queryColumns");
            return (Criteria) this;
        }

        public Criteria andQueryColumnsGreaterThan(String value) {
            addCriterion("query_columns >", value, "queryColumns");
            return (Criteria) this;
        }

        public Criteria andQueryColumnsLessThan(String value) {
            addCriterion("query_columns <", value, "queryColumns");
            return (Criteria) this;
        }

        public Criteria andQueryColumnsBetween(String value1, String value2) {
            addCriterion("query_columns between", value1, value2, "queryColumns");
            return (Criteria) this;
        }

        public Criteria andQueryColumnsIn(List<String> values) {
            addCriterion("query_columns in", values, "queryColumns");
            return (Criteria) this;
        }

        public Criteria andIndexColumnIsNull() {
            addCriterion("index_column is null");
            return (Criteria) this;
        }

        public Criteria andIndexColumnIsNotNull() {
            addCriterion("index_column is not null");
            return (Criteria) this;
        }

        /**
         * 索引字段
         */
        public Criteria andIndexColumnEqualTo(String value) { addCriterion("index_column =", value, "indexColumn"); return (Criteria) this; }

        public Criteria andIndexColumnNotEqualTo(String value) {
            addCriterion("index_column <>", value, "indexColumn");
            return (Criteria) this;
        }

        public Criteria andIndexColumnGreaterThan(String value) {
            addCriterion("index_column >", value, "indexColumn");
            return (Criteria) this;
        }

        public Criteria andIndexColumnLessThan(String value) {
            addCriterion("index_column <", value, "indexColumn");
            return (Criteria) this;
        }

        public Criteria andIndexColumnBetween(String value1, String value2) {
            addCriterion("index_column between", value1, value2, "indexColumn");
            return (Criteria) this;
        }

        public Criteria andIndexColumnIn(List<String> values) {
            addCriterion("index_column in", values, "indexColumn");
            return (Criteria) this;
        }

        public Criteria andOrderByIsNull() {
            addCriterion("order_by is null");
            return (Criteria) this;
        }

        public Criteria andOrderByIsNotNull() {
            addCriterion("order_by is not null");
            return (Criteria) this;
        }

        /**
         * 排序字段
         */
        public Criteria andOrderByEqualTo(String value) { addCriterion("order_by =", value, "orderBy"); return (Criteria) this; }

        public Criteria andOrderByNotEqualTo(String value) {
            addCriterion("order_by <>", value, "orderBy");
            return (Criteria) this;
        }

        public Criteria andOrderByGreaterThan(String value) {
            addCriterion("order_by >", value, "orderBy");
            return (Criteria) this;
        }

        public Criteria andOrderByLessThan(String value) {
            addCriterion("order_by <", value, "orderBy");
            return (Criteria) this;
        }

        public Criteria andOrderByBetween(String value1, String value2) {
            addCriterion("order_by between", value1, value2, "orderBy");
            return (Criteria) this;
        }

        public Criteria andOrderByIn(List<String> values) {
            addCriterion("order_by in", values, "orderBy");
            return (Criteria) this;
        }

        public Criteria andArchiveTypeIsNull() {
            addCriterion("archive_type is null");
            return (Criteria) this;
        }

        public Criteria andArchiveTypeIsNotNull() {
            addCriterion("archive_type is not null");
            return (Criteria) this;
        }

        /**
         * 归档类型
         */
        public Criteria andArchiveTypeEqualTo(Integer value) { addCriterion("archive_type =", value, "archiveType"); return (Criteria) this; }

        public Criteria andArchiveTypeNotEqualTo(Integer value) {
            addCriterion("archive_type <>", value, "archiveType");
            return (Criteria) this;
        }

        public Criteria andArchiveTypeIn(List<Integer> values) {
            addCriterion("archive_type in", values, "archiveType");
            return (Criteria) this;
        }

        public Criteria andFilterDefaultValueIsNull() {
            addCriterion("filter_default_value is null");
            return (Criteria) this;
        }

        public Criteria andFilterDefaultValueIsNotNull() {
            addCriterion("filter_default_value is not null");
            return (Criteria) this;
        }

        /**
         * 默认值过滤
         */
        public Criteria andFilterDefaultValueEqualTo(Integer value) { addCriterion("filter_default_value =", value, "filterDefaultValue"); return (Criteria) this; }

        public Criteria andFilterDefaultValueNotEqualTo(Integer value) {
            addCriterion("filter_default_value <>", value, "filterDefaultValue");
            return (Criteria) this;
        }

        public Criteria andFilterDefaultValueGreaterThan(Integer value) {
            addCriterion("filter_default_value >", value, "filterDefaultValue");
            return (Criteria) this;
        }

        public Criteria andFilterDefaultValueLessThan(Integer value) {
            addCriterion("filter_default_value <", value, "filterDefaultValue");
            return (Criteria) this;
        }

        public Criteria andFilterDefaultValueBetween(Integer value1, Integer value2) {
            addCriterion("filter_default_value between", value1, value2, "filterDefaultValue");
            return (Criteria) this;
        }

        public Criteria andFilterDefaultValueIn(List<Integer> values) {
            addCriterion("filter_default_value in", values, "filterDefaultValue");
            return (Criteria) this;
        }

        public Criteria andPluginsIsNull() {
            addCriterion("plugins is null");
            return (Criteria) this;
        }

        public Criteria andPluginsIsNotNull() {
            addCriterion("plugins is not null");
            return (Criteria) this;
        }

        /**
         * 插件列表（JSON格式）
         */
        public Criteria andPluginsEqualTo(String value) { addCriterion("plugins =", value, "plugins"); return (Criteria) this; }

        public Criteria andPluginsNotEqualTo(String value) {
            addCriterion("plugins <>", value, "plugins");
            return (Criteria) this;
        }

        public Criteria andPluginsGreaterThan(String value) {
            addCriterion("plugins >", value, "plugins");
            return (Criteria) this;
        }

        public Criteria andPluginsLessThan(String value) {
            addCriterion("plugins <", value, "plugins");
            return (Criteria) this;
        }

        public Criteria andPluginsBetween(String value1, String value2) {
            addCriterion("plugins between", value1, value2, "plugins");
            return (Criteria) this;
        }

        public Criteria andPluginsIn(List<String> values) {
            addCriterion("plugins in", values, "plugins");
            return (Criteria) this;
        }

        public Criteria andRelationsIsNull() {
            addCriterion("relations is null");
            return (Criteria) this;
        }

        public Criteria andRelationsIsNotNull() {
            addCriterion("relations is not null");
            return (Criteria) this;
        }

        /**
         * 关联关系（JSON格式）
         */
        public Criteria andRelationsEqualTo(String value) { addCriterion("relations =", value, "relations"); return (Criteria) this; }

        public Criteria andRelationsNotEqualTo(String value) {
            addCriterion("relations <>", value, "relations");
            return (Criteria) this;
        }

        public Criteria andRelationsGreaterThan(String value) {
            addCriterion("relations >", value, "relations");
            return (Criteria) this;
        }

        public Criteria andRelationsLessThan(String value) {
            addCriterion("relations <", value, "relations");
            return (Criteria) this;
        }

        public Criteria andRelationsBetween(String value1, String value2) {
            addCriterion("relations between", value1, value2, "relations");
            return (Criteria) this;
        }

        public Criteria andRelationsIn(List<String> values) {
            addCriterion("relations in", values, "relations");
            return (Criteria) this;
        }

        public Criteria andPropsIsNull() {
            addCriterion("props is null");
            return (Criteria) this;
        }

        public Criteria andPropsIsNotNull() {
            addCriterion("props is not null");
            return (Criteria) this;
        }

        /**
         * 额外属性（JSON格式）
         */
        public Criteria andPropsEqualTo(String value) { addCriterion("props =", value, "props"); return (Criteria) this; }

        public Criteria andPropsNotEqualTo(String value) {
            addCriterion("props <>", value, "props");
            return (Criteria) this;
        }

        public Criteria andPropsGreaterThan(String value) {
            addCriterion("props >", value, "props");
            return (Criteria) this;
        }

        public Criteria andPropsLessThan(String value) {
            addCriterion("props <", value, "props");
            return (Criteria) this;
        }

        public Criteria andPropsBetween(String value1, String value2) {
            addCriterion("props between", value1, value2, "props");
            return (Criteria) this;
        }

        public Criteria andPropsIn(List<String> values) {
            addCriterion("props in", values, "props");
            return (Criteria) this;
        }

        public Criteria andDebugModeIsNull() {
            addCriterion("debug_mode is null");
            return (Criteria) this;
        }

        public Criteria andDebugModeIsNotNull() {
            addCriterion("debug_mode is not null");
            return (Criteria) this;
        }

        /**
         * 是否为debug模式
         */
        public Criteria andDebugModeEqualTo(Boolean value) { addCriterion("debug_mode =", value, "debugMode"); return (Criteria) this; }

        public Criteria andDebugModeNotEqualTo(Boolean value) {
            addCriterion("debug_mode <>", value, "debugMode");
            return (Criteria) this;
        }

        public Criteria andShardingExpressionIsNull() {
            addCriterion("sharding_expression is null");
            return (Criteria) this;
        }

        public Criteria andShardingExpressionIsNotNull() {
            addCriterion("sharding_expression is not null");
            return (Criteria) this;
        }

        /**
         * 分片表达式
         */
        public Criteria andShardingExpressionEqualTo(String value) { addCriterion("sharding_expression =", value, "shardingExpression"); return (Criteria) this; }

        public Criteria andShardingExpressionNotEqualTo(String value) {
            addCriterion("sharding_expression <>", value, "shardingExpression");
            return (Criteria) this;
        }

        public Criteria andShardingExpressionGreaterThan(String value) {
            addCriterion("sharding_expression >", value, "shardingExpression");
            return (Criteria) this;
        }

        public Criteria andShardingExpressionLessThan(String value) {
            addCriterion("sharding_expression <", value, "shardingExpression");
            return (Criteria) this;
        }

        public Criteria andShardingExpressionBetween(String value1, String value2) {
            addCriterion("sharding_expression between", value1, value2, "shardingExpression");
            return (Criteria) this;
        }

        public Criteria andShardingExpressionIn(List<String> values) {
            addCriterion("sharding_expression in", values, "shardingExpression");
            return (Criteria) this;
        }

        public Criteria andShardingPluginClassIsNull() {
            addCriterion("sharding_plugin_class is null");
            return (Criteria) this;
        }

        public Criteria andShardingPluginClassIsNotNull() {
            addCriterion("sharding_plugin_class is not null");
            return (Criteria) this;
        }

        /**
         * 分片插件全类名
         */
        public Criteria andShardingPluginClassEqualTo(String value) { addCriterion("sharding_plugin_class =", value, "shardingPluginClass"); return (Criteria) this; }

        public Criteria andShardingPluginClassNotEqualTo(String value) {
            addCriterion("sharding_plugin_class <>", value, "shardingPluginClass");
            return (Criteria) this;
        }

        public Criteria andShardingPluginClassGreaterThan(String value) {
            addCriterion("sharding_plugin_class >", value, "shardingPluginClass");
            return (Criteria) this;
        }

        public Criteria andShardingPluginClassLessThan(String value) {
            addCriterion("sharding_plugin_class <", value, "shardingPluginClass");
            return (Criteria) this;
        }

        public Criteria andShardingPluginClassBetween(String value1, String value2) {
            addCriterion("sharding_plugin_class between", value1, value2, "shardingPluginClass");
            return (Criteria) this;
        }

        public Criteria andShardingPluginClassIn(List<String> values) {
            addCriterion("sharding_plugin_class in", values, "shardingPluginClass");
            return (Criteria) this;
        }

        public Criteria andShardingFieldIsNull() {
            addCriterion("sharding_field is null");
            return (Criteria) this;
        }

        public Criteria andShardingFieldIsNotNull() {
            addCriterion("sharding_field is not null");
            return (Criteria) this;
        }

        /**
         * 分片字段名称
         */
        public Criteria andShardingFieldEqualTo(String value) { addCriterion("sharding_field =", value, "shardingField"); return (Criteria) this; }

        public Criteria andShardingFieldNotEqualTo(String value) {
            addCriterion("sharding_field <>", value, "shardingField");
            return (Criteria) this;
        }

        public Criteria andShardingFieldGreaterThan(String value) {
            addCriterion("sharding_field >", value, "shardingField");
            return (Criteria) this;
        }

        public Criteria andShardingFieldLessThan(String value) {
            addCriterion("sharding_field <", value, "shardingField");
            return (Criteria) this;
        }

        public Criteria andShardingFieldBetween(String value1, String value2) {
            addCriterion("sharding_field between", value1, value2, "shardingField");
            return (Criteria) this;
        }

        public Criteria andShardingFieldIn(List<String> values) {
            addCriterion("sharding_field in", values, "shardingField");
            return (Criteria) this;
        }

        public Criteria andShardingPrefixIsNull() {
            addCriterion("sharding_prefix is null");
            return (Criteria) this;
        }

        public Criteria andShardingPrefixIsNotNull() {
            addCriterion("sharding_prefix is not null");
            return (Criteria) this;
        }

        /**
         * 分片前缀
         */
        public Criteria andShardingPrefixEqualTo(String value) { addCriterion("sharding_prefix =", value, "shardingPrefix"); return (Criteria) this; }

        public Criteria andShardingPrefixNotEqualTo(String value) {
            addCriterion("sharding_prefix <>", value, "shardingPrefix");
            return (Criteria) this;
        }

        public Criteria andShardingPrefixGreaterThan(String value) {
            addCriterion("sharding_prefix >", value, "shardingPrefix");
            return (Criteria) this;
        }

        public Criteria andShardingPrefixLessThan(String value) {
            addCriterion("sharding_prefix <", value, "shardingPrefix");
            return (Criteria) this;
        }

        public Criteria andShardingPrefixBetween(String value1, String value2) {
            addCriterion("sharding_prefix between", value1, value2, "shardingPrefix");
            return (Criteria) this;
        }

        public Criteria andShardingPrefixIn(List<String> values) {
            addCriterion("sharding_prefix in", values, "shardingPrefix");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
} 