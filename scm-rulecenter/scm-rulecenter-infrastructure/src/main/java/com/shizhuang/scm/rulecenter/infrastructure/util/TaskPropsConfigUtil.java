package com.shizhuang.scm.rulecenter.infrastructure.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.TaskPropsConfig;
import lombok.extern.slf4j.Slf4j;
import java.io.IOException;

/**
 * 任务属性配置工具类
 * 用于处理 TaskPropsConfig 的序列化和反序列化
 */
@Slf4j
public class TaskPropsConfigUtil {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 将 TaskPropsConfig 对象序列化为 JSON 字符串
     * 
     * @param timeConfig 时间配置对象
     * @return JSON 字符串
     * @throws RuntimeException 序列化失败时抛出
     */
    public static String serialize(TaskPropsConfig timeConfig) {
        if (timeConfig == null) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(timeConfig);
        } catch (JsonProcessingException e) {
            log.error("序列化 TaskPropsConfig 失败: timeConfig={}, error={}", timeConfig, e.getMessage(), e);
            throw new RuntimeException("序列化 TaskPropsConfig 失败: timeConfig=" + timeConfig, e);
        }
    }
    
    /**
     * 将 JSON 字符串反序列化为 TaskPropsConfig 对象
     * 
     * @param json JSON 字符串
     * @return TaskPropsConfig 对象
     * @throws RuntimeException 反序列化失败时抛出
     */
    public static TaskPropsConfig deserialize(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(json, TaskPropsConfig.class);
        } catch (IOException e) {
            log.error("反序列化 TaskPropsConfig 失败: json={}, error={}", json, e.getMessage(), e);
            throw new RuntimeException("反序列化 TaskPropsConfig 失败: json=" + json, e);
        }
    }
    
    /**
     * 验证时间字符串格式
     * 格式：HH:mm
     * 
     * @param timeStr 时间字符串
     * @return 是否有效
     */
    public static boolean isValidTimeString(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            log.debug("时间字符串为空: timeStr={}", timeStr);
            return false;
        }
        
        try {
            String[] parts = timeStr.trim().split(":");
            if (parts.length != 2) {
                log.debug("时间格式错误，缺少冒号分隔符: timeStr={}, parts.length={}", timeStr, parts.length);
                return false;
            }
            
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            
            if (hour < 0 || hour > 23) {
                log.debug("小时值超出范围: timeStr={}, hour={}", timeStr, hour);
                return false;
            }
            
            if (minute < 0 || minute > 59) {
                log.debug("分钟值超出范围: timeStr={}, minute={}", timeStr, minute);
                return false;
            }
            
            return true;
        } catch (NumberFormatException e) {
            log.debug("时间字符串转换为数字失败: timeStr={}, error={}", timeStr, e.getMessage());
            return false;
        }
    }
    
    /**
     * 解析时间范围字符串
     * 格式：HH:mm-HH:mm
     * 
     * @param timeSpan 时间范围字符串
     * @return TaskPropsConfig 对象
     * @throws RuntimeException 解析失败时抛出
     */
    public static TaskPropsConfig parseTimeSpan(String timeSpan) {
        if (timeSpan == null || timeSpan.trim().isEmpty()) {
            return null;
        }
        
        try {
            String[] parts = timeSpan.trim().split("-");
            if (parts.length != 2) {
                log.error("时间范围格式错误: timeSpan={}, parts.length={}", timeSpan, parts.length);
                throw new RuntimeException("时间范围格式错误: timeSpan=" + timeSpan + ", parts.length=" + parts.length);
            }
            String start = parts[0].trim();
            String end = parts[1].trim();
            
            if (!isValidTimeString(start)) {
                log.error("开始时间格式无效: timeSpan={}, start={}", timeSpan, start);
                throw new RuntimeException("开始时间格式无效: timeSpan=" + timeSpan + ", start=" + start);
            }
            
            if (!isValidTimeString(end)) {
                log.error("结束时间格式无效: timeSpan={}, end={}", timeSpan, end);
                throw new RuntimeException("结束时间格式无效: timeSpan=" + timeSpan + ", end=" + end);
            }
            
            return new TaskPropsConfig(start, end, timeSpan);
        } catch (Exception e) {
            log.error("解析时间范围失败: timeSpan={}, error={}", timeSpan, e.getMessage(), e);
            throw new RuntimeException("解析时间范围失败: timeSpan=" + timeSpan, e);
        }
    }
} 