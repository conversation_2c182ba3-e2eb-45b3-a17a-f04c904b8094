package com.shizhuang.scm.rulecenter.infrastructure.service;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;

/**
 * 查询字段计算服务
 * 负责计算节点的 queryColumns 字段，包括主键、索引、关联关系等字段
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public interface QueryColumnsService {

    /**
     * 计算节点的 queryColumns 字段
     * 包含：主键字段、索引字段、关联关系中的当前表字段、子节点关联关系中的关联表字段
     *
     * @param nodeConfig 节点配置
     */
    void computeQueryColumns(ArchiveNodeConfigDO nodeConfig);

    /**
     * 更新父节点的 queryColumns
     * 当子节点保存时，需要更新父节点的 queryColumns 以包含子节点的关联字段
     *
     * @param nodeConfig 当前节点配置
     */
    void updateParentQueryColumns(ArchiveNodeConfigDO nodeConfig);

    /**
     * 批量更新相关节点的 queryColumns
     * 包括当前节点和所有相关节点（父节点、子节点）
     *
     * @param nodeConfig 节点配置
     */
    void updateRelatedNodesQueryColumns(ArchiveNodeConfigDO nodeConfig);
} 