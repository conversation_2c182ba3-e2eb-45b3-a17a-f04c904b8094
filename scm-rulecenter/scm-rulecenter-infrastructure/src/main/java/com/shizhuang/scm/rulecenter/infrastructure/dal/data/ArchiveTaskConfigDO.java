package com.shizhuang.scm.rulecenter.infrastructure.dal.data;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.shizhuang.scm.rulecenter.infrastructure.config.LongToStringSerializer;

import java.io.Serializable;
import java.util.Date;

/**
 * 归档任务配置表
 */
public class ArchiveTaskConfigDO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 应用配置ID
     */
    private Long appConfigId;
    /**
     * 任务ID
     */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 数据源名称
     */
    private String datasourceName;
    /**
     * 数据库名称
     */
    private String databaseName;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 任务状态
     */
    private Integer status;
    /**
     * 运行模式
     */
    private Integer mode;
    /**
     * 扫描间隔
     */
    private Integer interval;
    /**
     * 扫描数量
     */
    private Integer limit;
    /**
     * 执行类型
     */
    private Integer executionType;
    /**
     * 额外属性（JSON格式）
     */
    private String props;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 应用名称
     */
    private String appName;
    private static final long serialVersionUID = 1L;

    // getter/setter
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppConfigId() {
        return appConfigId;
    }

    public void setAppConfigId(Long appConfigId) {
        this.appConfigId = appConfigId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getExecutionType() {
        return executionType;
    }

    public void setExecutionType(Integer executionType) {
        this.executionType = executionType;
    }

    public String getProps() {
        return props;
    }

    public void setProps(String props) {
        this.props = props;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
} 