package com.shizhuang.scm.rulecenter.infrastructure.dal.data;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.shizhuang.scm.rulecenter.infrastructure.config.LongToStringSerializer;

import java.io.Serializable;
import java.util.Date;

/**
 * 归档节点配置表
 */
public class ArchiveNodeConfigDO implements Serializable {
    /** 主键ID */
    private Long id;
    /** 任务ID */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long taskId;
    /** 节点ID */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long nodeId;
    /** 父节点ID */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long parentNodeId;
    /** 表名 */
    private String tableName;
    /** 查询条件 */
    private String condition;
    /** 节点级数据源名称 */
    private String datasourceName;
    /** 节点级数据库名称 */
    private String databaseName;
    /** 是否归档 */
    private Boolean isArchive;
    /** 查询字段列表（JSON格式） */
    private String queryColumns;
    /** 主键列 */
    private String primaryKeyColumn;
    /** 节点状态 */
    private Integer status;
    /** 是否为根节点 */
    private Boolean rootNode;
    /** 索引字段 */
    private String indexColumn;
    /** 排序字段 */
    private String orderBy;
    /** 归档类型 */
    private Integer archiveType;
    /** 默认值过滤 */
    private Integer filterDefaultValue;
    /** 是否启用分片 */
    private Boolean shardingEnabled;
    /** 分片表达式 */
    private String shardingExpression;
    /** 分片插件全类名 */
    private String shardingPluginClass;
    /** 分片字段名称 */
    private String shardingField;
    /** 分片前缀 */
    private String shardingPrefix;
    /** 插件列表（JSON格式） */
    private String plugins;
    /** 关联关系（JSON格式） */
    private String relations;
    /** 额外属性（JSON格式） */
    private String props;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    private static final long serialVersionUID = 1L;
    // getter/setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Long getTaskId() { return taskId; }
    public void setTaskId(Long taskId) { this.taskId = taskId; }
    public Long getNodeId() { return nodeId; }
    public void setNodeId(Long nodeId) { this.nodeId = nodeId; }
    public Long getParentNodeId() { return parentNodeId; }
    public void setParentNodeId(Long parentNodeId) { this.parentNodeId = parentNodeId; }
    public String getTableName() { return tableName; }
    public void setTableName(String tableName) { this.tableName = tableName; }
    public String getCondition() { return condition; }
    public void setCondition(String condition) { this.condition = condition; }
    public String getDatasourceName() { return datasourceName; }
    public void setDatasourceName(String datasourceName) { this.datasourceName = datasourceName; }
    public String getDatabaseName() { return databaseName; }
    public void setDatabaseName(String databaseName) { this.databaseName = databaseName; }
    public Boolean getIsArchive() { return isArchive; }
    public void setIsArchive(Boolean isArchive) { this.isArchive = isArchive; }
    public String getQueryColumns() { return queryColumns; }
    public void setQueryColumns(String queryColumns) { this.queryColumns = queryColumns; }
    public String getPrimaryKeyColumn() { return primaryKeyColumn; }
    public void setPrimaryKeyColumn(String primaryKeyColumn) { this.primaryKeyColumn = primaryKeyColumn; }
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    public Boolean getRootNode() { return rootNode; }
    public void setRootNode(Boolean rootNode) { this.rootNode = rootNode; }
    public String getIndexColumn() { return indexColumn; }
    public void setIndexColumn(String indexColumn) { this.indexColumn = indexColumn; }
    public String getOrderBy() { return orderBy; }
    public void setOrderBy(String orderBy) { this.orderBy = orderBy; }
    public Integer getArchiveType() { return archiveType; }
    public void setArchiveType(Integer archiveType) { this.archiveType = archiveType; }
    public Integer getFilterDefaultValue() { return filterDefaultValue; }
    public void setFilterDefaultValue(Integer filterDefaultValue) { this.filterDefaultValue = filterDefaultValue; }
    public Boolean getShardingEnabled() { return shardingEnabled; }
    public void setShardingEnabled(Boolean shardingEnabled) { this.shardingEnabled = shardingEnabled; }
    public String getShardingExpression() { return shardingExpression; }
    public void setShardingExpression(String shardingExpression) { this.shardingExpression = shardingExpression; }
    public String getShardingPluginClass() { return shardingPluginClass; }
    public void setShardingPluginClass(String shardingPluginClass) { this.shardingPluginClass = shardingPluginClass; }
    public String getShardingField() { return shardingField; }
    public void setShardingField(String shardingField) { this.shardingField = shardingField; }
    public String getShardingPrefix() { return shardingPrefix; }
    public void setShardingPrefix(String shardingPrefix) { this.shardingPrefix = shardingPrefix; }
    public String getPlugins() { return plugins; }
    public void setPlugins(String plugins) { this.plugins = plugins; }
    public String getRelations() { return relations; }
    public void setRelations(String relations) { this.relations = relations; }
    public String getProps() { return props; }
    public void setProps(String props) { this.props = props; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

} 