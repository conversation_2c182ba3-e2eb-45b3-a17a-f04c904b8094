package com.shizhuang.scm.rulecenter.infrastructure.service.impl;

import com.shizhuang.ark.config.client.api.ArkConfigFactory;
import com.shizhuang.ark.config.client.api.ArkConfigService;
import com.shizhuang.ark.config.client.model.ArkConfigServiceProperties;
import com.shizhuang.ark.config.client.model.PublishValueRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.constants.ArchiveConstants;
import com.shizhuang.scm.rulecenter.infrastructure.service.ConfigPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

@Slf4j
@Service
public class ConfigPushServiceImpl implements ConfigPushService {

    private ArkConfigService arkConfigService;

    @Value("${archive.platform.ark.token:}")
    private String arkToken;


    @PostConstruct
    public void init() {
        if (StringUtils.isEmpty(arkToken)) {
            log.warn("Ark token is empty, ArkConfigService will not be initialized.");
            return;
        }
        arkConfigService = getArkConfigServiceToken(arkToken);
    }

    private ArkConfigService getArkConfigServiceToken(String token) {
        String namespace = System.getProperty("spring.cloud.nacos.config.namespace");
        String serverAddr = System.getProperty("spring.cloud.nacos.config.server-addr");
        ArkConfigServiceProperties arkConfigServiceProperties = ArkConfigServiceProperties.builder()
                .serverAddr(serverAddr)
                .namespace(namespace)
                .accessToken(token)
                .build();
        return ArkConfigFactory.createConfigService(arkConfigServiceProperties);
    }

    private String getDataId(String appName) {
        // 这里可根据实际业务调整dataId生成规则
        return appName + ".properties";
    }

    @Override
    public void push(String appName, String key, String value) {
        if (StringUtils.isEmpty(appName) || StringUtils.isEmpty(key)) {
            throw new IllegalArgumentException("appName和key不能为空");
        }
        String dataId = getDataId(appName);
        PublishValueRequest publishValueRequest = PublishValueRequest.builder()
                .dataId(dataId)
                .groupId(ArchiveConstants.ARK_GROUP)
                .key(key)
                .value(value)
                .build();
        boolean result = arkConfigService.publishValue(publishValueRequest);
        if (result) {
            log.info("配置推送成功: appName={}, key={}, dataId={}", appName, key, dataId);
        } else {
            log.error("配置推送失败: appName={}, key={}, dataId={}", appName, key, dataId);
            throw new RuntimeException("配置推送失败: " + dataId);
        }
    }

    @Override
    public void remove(String appName, String key) {
        if (StringUtils.isEmpty(appName) || StringUtils.isEmpty(key)) {
            throw new IllegalArgumentException("appName和key不能为空");
        }
        String dataId = getDataId(appName);
        try {
            PublishValueRequest publishValueRequest = PublishValueRequest.builder()
                    .dataId(dataId)
                    .groupId(ArchiveConstants.ARK_GROUP)
                    .key(key)
                    .value("") // 空字符串表示删除
                    .build();
            boolean result = arkConfigService.publishValue(publishValueRequest);
            if (result) {
                log.info("配置移除成功: appName={}, key={}, dataId={}", appName, key, dataId);
            } else {
                log.error("配置移除失败: appName={}, key={}, dataId={}", appName, key, dataId);
                throw new RuntimeException("配置移除失败: " + dataId);
            }
        } catch (Exception e) {
            log.error("配置移除异常: appName={}, key={}, dataId={}", appName, key, dataId, e);
            throw new RuntimeException("配置移除异常: " + dataId, e);
        }
    }
}