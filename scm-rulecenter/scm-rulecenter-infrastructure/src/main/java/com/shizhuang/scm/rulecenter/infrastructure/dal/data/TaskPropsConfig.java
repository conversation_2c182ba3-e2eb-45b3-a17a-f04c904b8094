package com.shizhuang.scm.rulecenter.infrastructure.dal.data;

import java.io.Serializable;

/**
 * 任务属性配置对象
 * 用于保存任务的属性配置信息
 */
public class TaskPropsConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 开始时间（格式：HH:mm） */
    private String start;
    
    /** 结束时间（格式：HH:mm） */
    private String end;
    
    /** 时间范围字符串（格式：HH:mm-HH:mm） */
    private String timespan;
    
    public TaskPropsConfig() {}
    
    public TaskPropsConfig(String start, String end, String timespan) {
        this.start = start;
        this.end = end;
        this.timespan = timespan;
    }
    
    // getter/setter
    public String getStart() {
        return start;
    }
    
    public void setStart(String start) {
        this.start = start;
    }
    
    public String getEnd() {
        return end;
    }
    
    public void setEnd(String end) {
        this.end = end;
    }
    
    public String getTimespan() {
        return timespan;
    }
    
    public void setTimespan(String timespan) {
        this.timespan = timespan;
    }
    
    @Override
    public String toString() {
        return "TaskPropsConfig{" +
                "start=" + start +
                ", end=" + end +
                ", timespan='" + timespan + '\'' +
                '}';
    }
} 