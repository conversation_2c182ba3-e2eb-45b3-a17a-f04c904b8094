<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ArchiveNodeConfigMapper">
    <resultMap id="BaseResultMap" type="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="node_id" property="nodeId" />
        <result column="parent_node_id" property="parentNodeId" />
        <result column="table_name" property="tableName" />
        <result column="condition" property="condition" />
        <result column="datasource_name" property="datasourceName" />
        <result column="database_name" property="databaseName" />
        <result column="is_archive" property="isArchive" />
        <result column="query_columns" property="queryColumns" />
        <result column="primary_key_column" property="primaryKeyColumn" />
        <result column="status" property="status" />
        <result column="root_node" property="rootNode" />
        <result column="index_column" property="indexColumn" />
        <result column="order_by" property="orderBy" />
        <result column="archive_type" property="archiveType" />
        <result column="filter_default_value" property="filterDefaultValue" />
        <result column="sharding_enabled" property="shardingEnabled" />
        <result column="sharding_expression" property="shardingExpression" />
        <result column="sharding_plugin_class" property="shardingPluginClass" />
        <result column="sharding_field" property="shardingField" />
        <result column="sharding_prefix" property="shardingPrefix" />
        <result column="plugins" property="plugins" />
        <result column="relations" property="relations" />
        <result column="props" property="props" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `task_id`, `node_id`, `parent_node_id`, `table_name`, `condition`, `datasource_name`, `database_name`, `is_archive`, `query_columns`, `primary_key_column`, `status`, `root_node`, `index_column`, `order_by`, `archive_type`, `filter_default_value`, `sharding_enabled`, `sharding_expression`, `sharding_plugin_class`, `sharding_field`, `sharding_prefix`, `plugins`, `relations`, `props`, `create_time`, `update_time`
    </sql>
    <insert id="insert" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO">
        insert into `archive_node_config` (<include refid="Base_Column_List"/>)
        values (#{id}, #{taskId}, #{nodeId}, #{parentNodeId}, #{tableName}, #{condition}, #{datasourceName}, #{databaseName}, #{isArchive}, #{queryColumns}, #{primaryKeyColumn}, #{status}, #{rootNode}, #{indexColumn}, #{orderBy}, #{archiveType}, #{filterDefaultValue}, #{shardingEnabled}, #{shardingExpression}, #{shardingPluginClass}, #{shardingField}, #{shardingPrefix}, #{plugins}, #{relations}, #{props}, #{createTime}, #{updateTime})
    </insert>
    <insert id="insertSelective" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO">
        insert into `archive_node_config`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`id`,</if>
            <if test="taskId != null">`task_id`,</if>
            <if test="nodeId != null">`node_id`,</if>
            <if test="parentNodeId != null">`parent_node_id`,</if>
            <if test="tableName != null">`table_name`,</if>
            <if test="condition != null">`condition`,</if>
            <if test="datasourceName != null">`datasource_name`,</if>
            <if test="databaseName != null">`database_name`,</if>
            <if test="isArchive != null">`is_archive`,</if>
            <if test="queryColumns != null">`query_columns`,</if>
            <if test="primaryKeyColumn != null">`primary_key_column`,</if>
            <if test="status != null">`status`,</if>
            <if test="rootNode != null">`root_node`,</if>
            <if test="indexColumn != null">`index_column`,</if>
            <if test="orderBy != null">`order_by`,</if>
            <if test="archiveType != null">`archive_type`,</if>
            <if test="filterDefaultValue != null">`filter_default_value`,</if>
            <if test="shardingEnabled != null">`sharding_enabled`,</if>
            <if test="shardingExpression != null">`sharding_expression`,</if>
            <if test="shardingPluginClass != null">`sharding_plugin_class`,</if>
            <if test="shardingField != null">`sharding_field`,</if>
            <if test="shardingPrefix != null">`sharding_prefix`,</if>
            <if test="plugins != null">`plugins`,</if>
            <if test="relations != null">`relations`,</if>
            <if test="props != null">`props`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="nodeId != null">#{nodeId},</if>
            <if test="parentNodeId != null">#{parentNodeId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="condition != null">#{condition},</if>
            <if test="datasourceName != null">#{datasourceName},</if>
            <if test="databaseName != null">#{databaseName},</if>
            <if test="isArchive != null">#{isArchive},</if>
            <if test="queryColumns != null">#{queryColumns},</if>
            <if test="primaryKeyColumn != null">#{primaryKeyColumn},</if>
            <if test="status != null">#{status},</if>
            <if test="rootNode != null">#{rootNode},</if>
            <if test="indexColumn != null">#{indexColumn},</if>
            <if test="orderBy != null">#{orderBy},</if>
            <if test="archiveType != null">#{archiveType},</if>
            <if test="filterDefaultValue != null">#{filterDefaultValue},</if>
            <if test="shardingEnabled != null">#{shardingEnabled},</if>
            <if test="shardingExpression != null">#{shardingExpression},</if>
            <if test="shardingPluginClass != null">#{shardingPluginClass},</if>
            <if test="shardingField != null">#{shardingField},</if>
            <if test="shardingPrefix != null">#{shardingPrefix},</if>
            <if test="plugins != null">#{plugins},</if>
            <if test="relations != null">#{relations},</if>
            <if test="props != null">#{props},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <delete id="deleteByPrimaryKey" parameterType="long">
        delete from `archive_node_config` where `id` = #{id}
    </delete>
    <update id="updateByPrimaryKeySelective" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO">
        update `archive_node_config`
        <set>
            <if test="taskId != null">`task_id` = #{taskId},</if>
            <if test="nodeId != null">`node_id` = #{nodeId},</if>
            <if test="parentNodeId != null">`parent_node_id` = #{parentNodeId},</if>
            <if test="tableName != null">`table_name` = #{tableName},</if>
            <if test="condition != null">`condition` = #{condition},</if>
            <if test="datasourceName != null">`datasource_name` = #{datasourceName},</if>
            <if test="databaseName != null">`database_name` = #{databaseName},</if>
            <if test="isArchive != null">`is_archive` = #{isArchive},</if>
            <if test="queryColumns != null">`query_columns` = #{queryColumns},</if>
            <if test="primaryKeyColumn != null">`primary_key_column` = #{primaryKeyColumn},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="rootNode != null">`root_node` = #{rootNode},</if>
            <if test="indexColumn != null">`index_column` = #{indexColumn},</if>
            <if test="orderBy != null">`order_by` = #{orderBy},</if>
            <if test="archiveType != null">`archive_type` = #{archiveType},</if>
            <if test="filterDefaultValue != null">`filter_default_value` = #{filterDefaultValue},</if>
            <if test="shardingEnabled != null">`sharding_enabled` = #{shardingEnabled},</if>
            <if test="shardingExpression != null">`sharding_expression` = #{shardingExpression},</if>
            <if test="shardingPluginClass != null">`sharding_plugin_class` = #{shardingPluginClass},</if>
            <if test="shardingField != null">`sharding_field` = #{shardingField},</if>
            <if test="shardingPrefix != null">`sharding_prefix` = #{shardingPrefix},</if>
            <if test="plugins != null">`plugins` = #{plugins},</if>
            <if test="relations != null">`relations` = #{relations},</if>
            <if test="props != null">`props` = #{props},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
        </set>
        where `id` = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO">
        update `archive_node_config`
        set `task_id` = #{taskId}, `node_id` = #{nodeId}, `parent_node_id` = #{parentNodeId}, `table_name` = #{tableName}, `condition` = #{condition}, `datasource_name` = #{datasourceName}, `database_name` = #{databaseName}, `is_archive` = #{isArchive}, `query_columns` = #{queryColumns}, `primary_key_column` = #{primaryKeyColumn}, `status` = #{status}, `root_node` = #{rootNode}, `index_column` = #{indexColumn}, `order_by` = #{orderBy}, `archive_type` = #{archiveType}, `filter_default_value` = #{filterDefaultValue}, `sharding_enabled` = #{shardingEnabled}, `sharding_expression` = #{shardingExpression}, `sharding_plugin_class` = #{shardingPluginClass}, `sharding_field` = #{shardingField}, `sharding_prefix` = #{shardingPrefix}, `plugins` = #{plugins}, `relations` = #{relations}, `props` = #{props}, `create_time` = #{createTime}, `update_time` = #{updateTime}
        where `id` = #{id}
    </update>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="long">
        select <include refid="Base_Column_List"/> from `archive_node_config` where `id` = #{id}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from `archive_node_config`
    </select>
    <select id="selectByTaskId" resultMap="BaseResultMap" parameterType="long">
        select <include refid="Base_Column_List"/> from `archive_node_config` where `task_id` = #{taskId}
    </select>
    <select id="selectByTaskIdAndNodeId" resultMap="BaseResultMap" parameterType="map">
        select <include refid="Base_Column_List"/> from `archive_node_config` where `task_id` = #{taskId} and `node_id` = #{nodeId}
    </select>
    
    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into `archive_node_config` (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.taskId}, #{item.nodeId}, #{item.parentNodeId}, #{item.tableName}, 
             #{item.condition}, #{item.datasourceName}, #{item.databaseName}, #{item.isArchive}, 
             #{item.queryColumns}, #{item.primaryKeyColumn}, #{item.status}, #{item.rootNode}, 
             #{item.indexColumn}, #{item.orderBy}, #{item.archiveType}, 
             #{item.filterDefaultValue}, #{item.shardingEnabled}, #{item.shardingExpression}, 
             #{item.shardingPluginClass}, #{item.shardingField}, #{item.shardingPrefix}, #{item.plugins}, #{item.relations}, 
             #{item.props}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
    
    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update `archive_node_config` 
            <set>
                <if test="item.taskId != null">`task_id` = #{item.taskId},</if>
                <if test="item.nodeId != null">`node_id` = #{item.nodeId},</if>
                <if test="item.parentNodeId != null">`parent_node_id` = #{item.parentNodeId},</if>
                <if test="item.tableName != null">`table_name` = #{item.tableName},</if>
                <if test="item.condition != null">`condition` = #{item.condition},</if>
                <if test="item.datasourceName != null">`datasource_name` = #{item.datasourceName},</if>
                <if test="item.databaseName != null">`database_name` = #{item.databaseName},</if>
                <if test="item.isArchive != null">`is_archive` = #{item.isArchive},</if>
                <if test="item.queryColumns != null">`query_columns` = #{item.queryColumns},</if>
                <if test="item.primaryKeyColumn != null">`primary_key_column` = #{item.primaryKeyColumn},</if>
                <if test="item.status != null">`status` = #{item.status},</if>
                <if test="item.rootNode != null">`root_node` = #{item.rootNode},</if>
                <if test="item.indexColumn != null">`index_column` = #{item.indexColumn},</if>
                <if test="item.orderBy != null">`order_by` = #{item.orderBy},</if>
                <if test="item.archiveType != null">`archive_type` = #{item.archiveType},</if>
                <if test="item.filterDefaultValue != null">`filter_default_value` = #{item.filterDefaultValue},</if>
                <if test="item.shardingEnabled != null">`sharding_enabled` = #{item.shardingEnabled},</if>
                <if test="item.shardingExpression != null">`sharding_expression` = #{item.shardingExpression},</if>
                <if test="item.shardingPluginClass != null">`sharding_plugin_class` = #{item.shardingPluginClass},</if>
                <if test="item.shardingField != null">`sharding_field` = #{item.shardingField},</if>
                <if test="item.shardingPrefix != null">`sharding_prefix` = #{item.shardingPrefix},</if>
                <if test="item.plugins != null">`plugins` = #{item.plugins},</if>
                <if test="item.relations != null">`relations` = #{item.relations},</if>
                <if test="item.props != null">`props` = #{item.props},</if>
                <if test="item.updateTime != null">`update_time` = #{item.updateTime}</if>
            </set>
            where `id` = #{item.id}
        </foreach>
    </update>
    
    <!-- 批量删除 -->
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        delete from `archive_node_config`
        where `id` in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    
    <!-- 根据任务ID删除 -->
    <delete id="deleteByTaskId" parameterType="java.lang.Long">
        delete from `archive_node_config`
        where `task_id` = #{taskId}
    </delete>

    <!-- 根据节点ID和任务ID查询 -->
    <select id="selectByNodeIdAndTaskId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from `archive_node_config`
        where `node_id` = #{nodeId} and `task_id` = #{taskId}
    </select>

    <!-- 根据表名和任务ID查询 -->
    <select id="selectByTableNameAndTaskId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from `archive_node_config`
        where `table_name` = #{tableName} and `task_id` = #{taskId}
    </select>

    <!-- 根据任务ID查询root节点 -->
    <select id="selectRootNodeByTaskId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from `archive_node_config`
        where `task_id` = #{taskId} and `root_node` = 1
        limit 1
    </select>
</mapper>