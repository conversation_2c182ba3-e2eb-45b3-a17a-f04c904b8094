<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ArchiveTaskConfigMapper">
    <resultMap id="BaseResultMap" type="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO">
        <id column="id" property="id" />
        <result column="app_config_id" property="appConfigId" />
        <result column="task_id" property="taskId" />
        <result column="task_name" property="taskName" />
        <result column="app_name" property="appName" />
        <result column="datasource_name" property="datasourceName" />
        <result column="database_name" property="databaseName" />
        <result column="version" property="version" />
        <result column="status" property="status" />
        <result column="mode" property="mode" />
        <result column="interval" property="interval" />
        <result column="limit" property="limit" />
        <result column="execution_type" property="executionType" />
        <result column="props" property="props" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `app_config_id`, `task_id`, `task_name`, `app_name`, `datasource_name`, `database_name`, `version`, `status`, `mode`, `interval`, `limit`, `execution_type`, `props`, `create_time`, `update_time`
    </sql>
    <insert id="insert" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO">
        insert into `archive_task_config` (<include refid="Base_Column_List"/>)
        values (#{id}, #{appConfigId}, #{taskId}, #{taskName}, #{appName}, #{datasourceName}, #{databaseName}, #{version}, #{status}, #{mode}, #{interval}, #{limit}, #{executionType}, #{props}, #{createTime}, #{updateTime})
    </insert>
    <insert id="insertSelective" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO">
        insert into `archive_task_config`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`id`,</if>
            <if test="appConfigId != null">`app_config_id`,</if>
            <if test="taskId != null">`task_id`,</if>
            <if test="taskName != null">`task_name`,</if>
            <if test="appName != null">`app_name`,</if>
            <if test="datasourceName != null">`datasource_name`,</if>
            <if test="databaseName != null">`database_name`,</if>
            <if test="version != null">`version`,</if>
            <if test="status != null">`status`,</if>
            <if test="mode != null">`mode`,</if>
            <if test="interval != null">`interval`,</if>
            <if test="limit != null">`limit`,</if>
            <if test="executionType != null">`execution_type`,</if>
            <if test="props != null">`props`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="appConfigId != null">#{appConfigId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="appName != null">#{appName},</if>
            <if test="datasourceName != null">#{datasourceName},</if>
            <if test="databaseName != null">#{databaseName},</if>
            <if test="version != null">#{version},</if>
            <if test="status != null">#{status},</if>
            <if test="mode != null">#{mode},</if>
            <if test="interval != null">#{interval},</if>
            <if test="limit != null">#{limit},</if>
            <if test="executionType != null">#{executionType},</if>
            <if test="props != null">#{props},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <delete id="deleteByPrimaryKey" parameterType="long">
        delete from `archive_task_config` where `id` = #{id}
    </delete>
    <update id="updateByPrimaryKeySelective" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO">
        update `archive_task_config`
        <set>
            <if test="appConfigId != null">`app_config_id` = #{appConfigId},</if>
            <if test="taskId != null">`task_id` = #{taskId},</if>
            <if test="taskName != null">`task_name` = #{taskName},</if>
            <if test="appName != null">`app_name` = #{appName},</if>
            <if test="datasourceName != null">`datasource_name` = #{datasourceName},</if>
            <if test="databaseName != null">`database_name` = #{databaseName},</if>
            <if test="version != null">`version` = #{version},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="mode != null">`mode` = #{mode},</if>
            <if test="interval != null">`interval` = #{interval},</if>
            <if test="limit != null">`limit` = #{limit},</if>
            <if test="executionType != null">`execution_type` = #{executionType},</if>
            <if test="props != null">`props` = #{props},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
        </set>
        where `id` = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO">
        update `archive_task_config`
        set `app_config_id` = #{appConfigId}, `task_id` = #{taskId}, `task_name` = #{taskName}, `app_name` = #{appName}, `datasource_name` = #{datasourceName}, `database_name` = #{databaseName}, `version` = #{version}, `status` = #{status}, `mode` = #{mode}, `interval` = #{interval}, `limit` = #{limit}, `execution_type` = #{executionType}, `props` = #{props}, `create_time` = #{createTime}, `update_time` = #{updateTime}
        where `id` = #{id}
    </update>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="long">
        select <include refid="Base_Column_List"/> from `archive_task_config` where `id` = #{id}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from `archive_task_config`
    </select>
    <select id="selectByTaskId" resultMap="BaseResultMap" parameterType="long">
        select <include refid="Base_Column_List"/> from `archive_task_config` where `task_id` = #{taskId}
    </select>
    <select id="selectByAppConfigId" resultMap="BaseResultMap" parameterType="long">
        select <include refid="Base_Column_List"/> from `archive_task_config` where `app_config_id` = #{appConfigId}
    </select>
    
    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into `archive_task_config` (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.appConfigId}, #{item.taskId}, #{item.taskName}, #{item.appName}, #{item.datasourceName}, 
             #{item.databaseName}, #{item.version}, #{item.status}, #{item.mode}, #{item.interval}, 
             #{item.limit}, #{item.executionType}, #{item.start}, #{item.end}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
    
    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update `archive_task_config` 
            <set>
                <if test="item.appConfigId != null">`app_config_id` = #{item.appConfigId},</if>
                <if test="item.taskName != null">`task_name` = #{item.taskName},</if>
                <if test="item.appName != null">`app_name` = #{item.appName},</if>
                <if test="item.datasourceName != null">`datasource_name` = #{item.datasourceName},</if>
                <if test="item.databaseName != null">`database_name` = #{item.databaseName},</if>
                <if test="item.version != null">`version` = #{item.version},</if>
                <if test="item.status != null">`status` = #{item.status},</if>
                <if test="item.mode != null">`mode` = #{item.mode},</if>
                <if test="item.interval != null">`interval` = #{item.interval},</if>
                <if test="item.limit != null">`limit` = #{item.limit},</if>
                <if test="item.executionType != null">`execution_type` = #{item.executionType},</if>
                <if test="item.start != null">`start` = #{item.start},</if>
                <if test="item.end != null">`end` = #{item.end},</if>
                <if test="item.updateTime != null">`update_time` = #{item.updateTime}</if>
            </set>
            where `id` = #{item.id}
        </foreach>
    </update>
    
    <!-- 批量删除 -->
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        delete from `archive_task_config`
        where `id` in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    
    <!-- 根据应用配置ID删除 -->
    <delete id="deleteByAppConfigId" parameterType="java.lang.Long">
        delete from `archive_task_config`
        where `app_config_id` = #{appConfigId}
    </delete>
</mapper> 