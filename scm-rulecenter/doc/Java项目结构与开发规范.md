# scm-rulecenter Java 项目结构与开发规范

## 1. 项目结构

### 1.1 推荐目录结构

```
scm-rulecenter/
  ├── scm-rulecenter-api/           # API接口定义模块（DTO、VO、Feign等）
  ├── scm-rulecenter-application/   # 应用服务层（Service、业务逻辑）
  ├── scm-rulecenter-domain/        # 领域层（实体、聚合、领域服务、仓储接口）
  ├── scm-rulecenter-infrastructure/# 基础设施层（持久化、第三方集成、数据访问实现）
  ├── scm-rulecenter-interfaces/    # 接口层（Controller、Web、RPC、定时任务等）
  ├── scm-rulecenter-sdk/           # SDK模块（对外暴露的客户端工具包）
  ├── scm-rulecenter-sdk-od/        # 其他SDK扩展
  └── ...                           # 其他相关模块
```

#### 1.1.1 各目录用途说明

- **src/main/java**：主代码目录，存放 Java 源码。
- **src/main/resources**：资源文件目录，存放配置文件、Mapper XML、静态资源等。
- **src/test/java**：测试代码目录，存放单元测试、集成测试代码。
- **src/test/resources**：测试资源文件目录，存放测试用配置、数据等。

#### 1.1.2 各层划分与放置位置

- **Controller**：`scm-rulecenter-interfaces/src/main/java/com/xxx/interfaces/controller`
- **Service（应用服务）**：`scm-rulecenter-application/src/main/java/com/xxx/application/service`
- **Repository/DAO**：接口定义在 `domain`，实现放在 `infrastructure`
- **Domain/Entity**：`scm-rulecenter-domain/src/main/java/com/xxx/domain/entity`
- **DTO/VO**：`scm-rulecenter-api/src/main/java/com/xxx/api/dto`、`vo`
- **Util**：`common` 或各模块下的 `util` 包

#### 1.1.3 配置文件放置建议

- **application.yml/properties**：`src/main/resources/`
- **logback.xml/logback-spring.xml**：`src/main/resources/`
- **Dubbo/MyBatis等XML**：`src/main/resources/` 或子目录（如 `dubbo/`, `mapper/`）

---

## 1.2 模块依赖关系

scm-rulecenter 项目采用分层架构，各模块之间依赖关系如下：

- **scm-rulecenter-api**
  - 定义 DTO、VO、接口契约，供其他模块依赖。
  - 被 application、interfaces、domain、infrastructure 等模块依赖。

- **scm-rulecenter-domain**
  - 定义领域模型、仓储接口、领域服务。
  - 依赖 api（用于 DTO/VO），被 application、infrastructure 依赖。

- **scm-rulecenter-application**
  - 实现应用服务、业务逻辑、事务控制。
  - 依赖 domain、api。
  - 被 interfaces 依赖。

- **scm-rulecenter-infrastructure**
  - 实现数据访问、第三方集成、仓储接口实现。
  - 依赖 domain、api。
  - 被 application 依赖。

- **scm-rulecenter-interfaces**
  - 提供 Controller、RPC、定时任务等接口。
  - 依赖 application、api。

- **scm-rulecenter-sdk / scm-rulecenter-sdk-od**
  - 对外暴露的客户端工具包。
  - 依赖 api。

> **依赖方向建议**：
> - 依赖自下而上，禁止出现环状依赖。
> - interfaces → application → domain → api
> - infrastructure 只被 application 依赖，实现 domain 的仓储接口。

---

## 1.2.1 循环依赖与对象抽取规范（修订）

- 如 infrastructure 和 domain 有共同依赖（如 DTO、VO、参数对象、通用枚举等），必须将相关对象定义在 scm-rulecenter-api 模块，由 domain 和 infrastructure 共同依赖 api，禁止直接相互依赖，避免循环依赖。
- 推荐对象类型：DTO、VO、参数对象、通用枚举等。
- 依赖链示例：infrastructure → api ← domain

---

## 2. 依赖关系

### 2.1 推荐主要依赖库

- **Spring Boot**：基础框架，简化配置和开发
- **Spring Cloud**：微服务相关
- **MyBatis/Hibernate/JPA**：ORM与数据访问
- **Lombok**：简化 Java 代码（如 getter/setter、构造器等）
- **JUnit/Mockito**：单元测试
- **Logback**：日志框架
- **MapStruct**：对象映射
- **Swagger/OpenAPI**：接口文档
- **Fastjson/Jackson**：JSON 处理

### 2.2 依赖管理工具

- **Maven**（推荐）：项目依赖与构建管理
- **Gradle**：可选

#### Maven 基本配置示例（pom.xml）

```xml
<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.xxx</groupId>
  <artifactId>scm-rulecenter</artifactId>
  <version>1.0.0</version>
  <modules>
    <module>scm-rulecenter-api</module>
    <module>scm-rulecenter-application</module>
    <!-- ... -->
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>2.7.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!-- 其他依赖 -->
    </dependencies>
  </dependencyManagement>
</project>
```

### 2.3 各层依赖关系说明

- **Controller** 依赖 **Service**
- **Service** 依赖 **Domain/Repository**
- **Repository** 依赖 **Infrastructure**
- **DTO/VO** 可被 Controller、Service、Domain 层引用
- **Util** 可被所有层引用，但应避免业务逻辑

---

## 3. 编码规范

### 3.1 命名规范

- **包名**：全小写，反向域名（如 `com.xxx.module`）
- **类名**：大驼峰（如 `UserController`）
- **方法名**：小驼峰（如 `getUserById`）
- **变量名**：小驼峰（如 `userName`）
- **常量名**：全大写，单词间下划线（如 `MAX_SIZE`）

### 3.2 注释规范

- 类、方法、复杂逻辑必须有注释，采用 Javadoc 风格
- 说明方法用途、参数、返回值、异常

### 3.3 代码风格

- 缩进：4 空格
- 每行最大长度：120 字符
- 运算符两侧、关键字后加空格
- 大括号独占一行
- 方法间空一行

### 3.4 异常处理规范

- 业务异常自定义（如 `BusinessException`），统一处理
- 不向前端暴露系统异常细节，详细信息写日志
- Controller 层捕获异常，返回统一响应结构

### 3.5 日志规范

- 使用 SLF4J + Logback
- 日志分级（info、warn、error、debug）
- 不打印敏感信息
- 记录关键业务操作、异常堆栈

### 3.6 单元测试规范

- 测试类与被测类同包，放在 `src/test/java`
- 使用 JUnit5，命名以 `*Test` 结尾
- 覆盖主要业务逻辑和异常分支
- Mock 外部依赖

---

## 4. 分层职责

### 4.1 Controller

- 接收请求、参数校验、调用 Service、返回统一响应
- 不包含业务逻辑

### 4.2 Service（应用服务）

- 业务逻辑实现、事务控制、聚合调用
- 依赖 Repository/Domain 层

### 4.3 Repository/DAO

- 数据访问接口（定义在 Domain，实现在 Infrastructure）
- 负责与数据库、缓存等数据源交互

### 4.4 Domain/Entity

- 领域模型、实体、聚合根、领域服务
- 只包含与业务相关的属性和方法

### 4.5 DTO/VO

- 数据传输对象（DTO）：用于服务间、前后端数据传递
- 视图对象（VO）：用于前端展示

### 4.6 Util

- 工具类，通用方法
- 不包含业务逻辑

### 4.7 业务逻辑、数据校验、事务控制

- **业务逻辑**：Service 层
- **数据校验**：参数校验在 Controller，业务校验在 Service/Domain
- **事务控制**：Service 层（@Transactional）

---

## 5. 其他建议

### 5.1 统一响应结构

- 设计统一响应体（如 `Result<T>`），包含 code、message、data 字段
- Controller 返回统一结构，便于前端处理

### 5.2 统一异常处理

- 全局异常处理（@ControllerAdvice）
- 业务异常与系统异常分离
- 日志记录详细异常信息

### 5.3 配置管理

- 配置文件分环境管理（application-dev.yml, application-prod.yml）
- 敏感信息加密或使用配置中心

### 5.4 安全规范

- 防止 XSS、SQL 注入（参数校验、ORM、预编译语句）
- 不暴露敏感信息
- 权限校验、接口鉴权

### 5.5 代码审查与版本控制

- 使用 Git，遵循分支管理策略（如 Git Flow）
- 提交信息规范，避免提交 .gitignore 忽略文件
- 代码合并需评审

### 5.6 代码生成工具建议

- **Lombok**：减少样板代码
- **MapStruct**：对象映射
- **MyBatis Generator**：自动生成 Mapper/Entity

---

## 6. 参考示例

### 6.1 统一响应结构

```java
public class Result<T> {
    private int code;
    private String message;
    private T data;
    // getter/setter/constructor
}
```

### 6.2 统一异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException ex) {
        // 记录日志
        return Result.fail(ex.getCode(), ex.getMessage());
    }
    @ExceptionHandler(Exception.class)
    public Result<?> handleException(Exception ex) {
        // 记录详细日志
        return Result.fail(500, "系统异常，请联系管理员");
    }
}
```

---

**本规范适用于 scm-rulecenter 及其子模块，建议所有开发成员遵循。** 