<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.shizhuang.scm</groupId>
    <artifactId>scm-rulecenter-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <properties>
        <revision>5.70.6-SNAPSHOT</revision>
        <sdk-version>1.0.10-RELEASE</sdk-version>
        <fusion.version>1.3.15</fusion.version>
        <java.version>1.8</java.version>
        <encoding>UTF-8</encoding>
        <project.build.sourceEncoding>${encoding}</project.build.sourceEncoding>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.test.skip>true</maven.test.skip>
        <lms-api.version>529.1018.RELEASE</lms-api.version>
        <poizon.module.framework.version>1.0.2.RELEASE</poizon.module.framework.version>
        <scp-framework-i18n.version>0.0.2-SNAPSHOT</scp-framework-i18n.version>
        <lombok.version>1.18.28</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <dynamic-datasource.version>4.1.3</dynamic-datasource.version>
        <scp-user-core-api.version>1.0.2-RELEASE</scp-user-core-api.version>
        <dRedis.version>1.0.2</dRedis.version>
        <bigdata.log.version>1.0.6</bigdata.log.version>
        <archive.sdk.version>1.1.4-RELEASE</archive.sdk.version>
        <qms.api.sdk.version>5.66.10-RELEASE</qms.api.sdk.version>
    </properties>
    <modules>
        <module>scm-rulecenter-api</module>
        <module>scm-rulecenter-application</module>
        <module>scm-rulecenter-domain</module>
        <module>scm-rulecenter-infrastructure</module>
        <module>scm-rulecenter-interfaces</module>
        <module>scm-rulecenter-sdk</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.20</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>scm-rulecenter-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>scm-rulecenter-interfaces</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>scm-rulecenter-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>scm-rulecenter-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>scm-rulecenter-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>scm-rulecenter-sdk</artifactId>
                <version>${sdk-version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>scm-rulecenter-sdk-od</artifactId>
                <version>${sdk-version}</version>
            </dependency>
            <dependency>
                <groupId>com.dewu.scm.lms</groupId>
                <artifactId>lms-api</artifactId>
                <version>${lms-api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>avatar-HA-common</artifactId>
                        <groupId>com.shizhuang.avatar</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>avatar-common</artifactId>
                        <groupId>com.shizhuang.avatar</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.poizon.module.framework</groupId>
                <artifactId>api</artifactId>
                <version>${poizon.module.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.poizon.module.framework</groupId>
                <artifactId>core</artifactId>
                <version>${poizon.module.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shizhuang.duapp</groupId>
                <artifactId>scp-framework-i18n</artifactId>
                <version>${scp-framework-i18n.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shizhuang.duapp</groupId>
                        <artifactId>fusion-dependence-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.10</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.baidu.unbiz</groupId>
                <artifactId>fluent-validator</artifactId>
                <version>1.0.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.poizon</groupId>
                <artifactId>fusion-adapt-avatar</artifactId>
                <version>${fusion.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.poizon</groupId>
                <artifactId>fusion-bgdeploy-feign</artifactId>
                <version>${fusion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.poizon</groupId>
                <artifactId>fusion-bgdeploy-dubbo</artifactId>
                <version>${fusion.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>5.1.0.Final</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dw.scp</groupId>
                <artifactId>scp-bizlog</artifactId>
                <version>5.51.0-RELEASE</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.poizon</groupId>-->
<!--                <artifactId>fusion-alicloud-rocketmq</artifactId>-->
<!--                <version>${fusion.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.shizhuang.duapp</groupId>
                <artifactId>scp-master-data-api</artifactId>
                <version>5.39.1-OAG</version>
            </dependency>
            <!--    ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────-->
            <!--    │   项目中引入依赖时，尽可能不要指定依赖版本号                                                                                                -->
            <!--    │   将常用的二方包、三方包的版本交由fusion-dependencies托管                                                                                  -->
            <!--    │   有如下好处：                                                                                                                         -->
            <!--    │   1. 使用统一版本，从源头上规避了依赖冲突问题                                                                                               -->
            <!--    │   2. 规范使用姿势，降低后续版本升级的成本                                                                                                  -->
            <!--    │   详情可加群咨询：https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=8f0k4d50-545b-45f4-a428-50676739beed             -->
            <!--    └──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────-->
            <dependency>
                <groupId>com.poizon</groupId>
                <artifactId>fusion-dependencies</artifactId>
                <version>${fusion.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.poizon</groupId>
                        <artifactId>fusion-elasticjob</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.shizhuang.duapp</groupId>
                <artifactId>scp-framework-bigdata-log</artifactId>
                <!--上线请使用最新正式包 http://nexus.poizon.com/nexus/content/repositories/releases/com/shizhuang/duapp/scp-framework-bigdata-log/ -->
                <version>${bigdata.log.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shizhuang.duapp</groupId>
                <artifactId>scp-archive-common</artifactId>
                <version>${archive.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dewu.scp</groupId>
                <artifactId>dewu-qms-api-sdk</artifactId>
                <version>${qms.api.sdk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.2.7</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <snapshotRepository>
            <id>poizon-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.shizhuang-inc.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>poizon-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.shizhuang-inc.com/nexus/content/repositories/releases/</url>
        </repository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>central</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>dewu</id>
            <url>https://nexus.shizhuang-inc.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
    </repositories>
</project>
