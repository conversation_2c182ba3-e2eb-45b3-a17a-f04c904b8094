package com.shizhuang.scm.rulecenter.interfaces.converter;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.api.dto.ColumnRelation;
import com.shizhuang.scm.rulecenter.infrastructure.integration.rpc.ArchiveSDKIntegrationApi;
import com.shizhuang.duapp.scp.framework.admin.sdk.request.FetchColumnsRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.ColumnsResponse;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.DatabaseColumn;
import com.poizon.fusion.common.model.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * ArchiveNodeConfigConverter 单元测试
 * 测试关系条件解析的各种场景
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@DisplayName("ArchiveNodeConfigConverter 关系条件解析测试")
class ArchiveNodeConfigConverterTest {

    @Mock
    private ArchiveSDKIntegrationApi archiveSDKIntegrationApi;

    private ArchiveNodeConfigConverter converter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        converter = new ArchiveNodeConfigConverter();
        ReflectionTestUtils.setField(converter, "archiveSDKIntegrationApi", archiveSDKIntegrationApi);
    }

    @Nested
    @DisplayName("合法场景测试")
    class ValidScenariosTest {

        @Test
        @DisplayName("单个关系条件 - 标准格式")
        void testSingleValidRelation() {
            // Given
            String relationConditions = "pink_inbound_detail.id = pink_operate_item.inbound_detail_id";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("pink_inbound_detail", Arrays.asList("id", "name", "type"));
            mockTableStructure("pink_operate_item", Arrays.asList("inbound_detail_id", "item_name"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            
            ColumnRelation relation = result.get(0);
            assertEquals("id", relation.getCurrentColumn());
            assertEquals("inbound_detail_id", relation.getRelatedColumn());
            assertEquals("pink_operate_item", relation.getRelatedTable());
        }

        @Test
        @DisplayName("多个关系条件 - 用and连接")
        void testMultipleValidRelations() {
            // Given
            String relationConditions = "table1.col1 = table2.col2 and table1.col3 = table3.col4";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table1", Arrays.asList("col1", "col3", "name"));
            mockTableStructure("table2", Arrays.asList("col2", "description"));
            mockTableStructure("table3", Arrays.asList("col4", "type"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(2, result.size());
            
            // 第一个关系
            assertEquals("col1", result.get(0).getCurrentColumn());
            assertEquals("col2", result.get(0).getRelatedColumn());
            assertEquals("table2", result.get(0).getRelatedTable());
            
            // 第二个关系
            assertEquals("col3", result.get(1).getCurrentColumn());
            assertEquals("col4", result.get(1).getRelatedColumn());
            assertEquals("table3", result.get(1).getRelatedTable());
        }

        @Test
        @DisplayName("大小写不敏感的and连接")
        void testCaseInsensitiveAnd() {
            // Given
            String relationConditions = "table1.col1 = table2.col2 AND table1.col3 = table3.col4";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table1", Arrays.asList("col1", "col3"));
            mockTableStructure("table2", Arrays.asList("col2"));
            mockTableStructure("table3", Arrays.asList("col4"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(2, result.size());
        }

        @Test
        @DisplayName("灵活的空格处理")
        void testFlexibleSpacing() {
            // Given
            String relationConditions = "table1.col1=table2.col2  and   table1.col3=table3.col4";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table1", Arrays.asList("col1", "col3"));
            mockTableStructure("table2", Arrays.asList("col2"));
            mockTableStructure("table3", Arrays.asList("col4"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(2, result.size());
        }

        @Test
        @DisplayName("带反引号的表名和字段名")
        void testWithBackticks() {
            // Given
            String relationConditions = "`table1`.`col1` = `table2`.`col2`";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table1", Arrays.asList("col1"));
            mockTableStructure("table2", Arrays.asList("col2"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("col1", result.get(0).getCurrentColumn());
            assertEquals("col2", result.get(0).getRelatedColumn());
        }

        @Test
        @DisplayName("下划线开头的表名和字段名")
        void testUnderscorePrefix() {
            // Given
            String relationConditions = "_table._column = _other_table._other_column";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("_table", Arrays.asList("_column"));
            mockTableStructure("_other_table", Arrays.asList("_other_column"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
        }

        @Test
        @DisplayName("数字在表名和字段名中")
        void testNumbersInNames() {
            // Given
            String relationConditions = "table_123.col_456 = other_table_789.col_012";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table_123", Arrays.asList("col_456"));
            mockTableStructure("other_table_789", Arrays.asList("col_012"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
        }
    }

    @Nested
    @DisplayName("不合法场景测试")
    class InvalidScenariosTest {

        @Test
        @DisplayName("空字符串")
        void testEmptyString() {
            // Given
            String relationConditions = "";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNull(result);
        }

        @Test
        @DisplayName("null值")
        void testNullValue() {
            // Given
            String relationConditions = null;
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNull(result);
        }

        @Test
        @DisplayName("只有空白字符")
        void testOnlyWhitespace() {
            // Given
            String relationConditions = "   \t\n  ";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNull(result);
        }

        @Test
        @DisplayName("缺少等号")
        void testMissingEquals() {
            // Given
            String relationConditions = "table1.col1 table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("关联条件必须使用等值关联"));
        }

        @Test
        @DisplayName("多个等号")
        void testMultipleEquals() {
            // Given
            String relationConditions = "table1.col1 = table2.col2 = table3.col3";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("关联条件格式错误，应为 table1.column1 = table2.column2"));
        }

        @Test
        @DisplayName("缺少表名")
        void testMissingTableName() {
            // Given
            String relationConditions = "col1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("关联条件格式错误，应为 table1.column1 = table2.column2"));
        }

        @Test
        @DisplayName("缺少字段名")
        void testMissingColumnName() {
            // Given
            String relationConditions = "table1. = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("关联条件格式错误，应为 table1.column1 = table2.column2"));
        }

        @Test
        @DisplayName("表名以数字开头")
        void testTableNameStartsWithNumber() {
            // Given
            String relationConditions = "1table.col1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("左侧表名格式错误"));
        }

        @Test
        @DisplayName("字段名以数字开头")
        void testColumnNameStartsWithNumber() {
            // Given
            String relationConditions = "table1.1col = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("左侧字段名格式错误"));
        }

        @Test
        @DisplayName("表名包含特殊字符")
        void testTableNameWithSpecialChars() {
            // Given
            String relationConditions = "table-1.col1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("左侧表名格式错误"));
        }

        @Test
        @DisplayName("字段名包含特殊字符")
        void testColumnNameWithSpecialChars() {
            // Given
            String relationConditions = "table1.col-1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("左侧字段名格式错误"));
        }

        @Test
        @DisplayName("使用非等值操作符")
        void testNonEqualsOperator() {
            // Given
            String relationConditions = "table1.col1 > table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("关联条件必须使用等值关联"));
        }

        @Test
        @DisplayName("无效的and连接")
        void testInvalidAndConnection() {
            // Given
            String relationConditions = "table1.col1 = table2.col2 and";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("关联条件格式错误，应为 table1.column1 = table2.column2"));
        }

        @Test
        @DisplayName("and后面缺少条件")
        void testAndWithoutCondition() {
            // Given
            String relationConditions = "table1.col1 = table2.col2 and ";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("关联条件必须使用等值关联"));
        }
    }

    @Nested
    @DisplayName("字段验证测试")
    class FieldValidationTest {

        @Test
        @DisplayName("表不存在")
        void testTableNotExists() {
            // Given
            String relationConditions = "nonexistent_table.col1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableNotExists("nonexistent_table");

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("左侧表不存在或无法获取表结构"));
        }

        @Test
        @DisplayName("字段不存在")
        void testColumnNotExists() {
            // Given
            String relationConditions = "table1.nonexistent_col = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table1", Arrays.asList("col1", "col2")); // 不包含 nonexistent_col
            mockTableStructure("table2", Arrays.asList("col2"));

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("左侧表[table1]中不存在字段[nonexistent_col]"));
        }

        @Test
        @DisplayName("数据源名称为空")
        void testEmptyDatasourceName() {
            // Given
            String relationConditions = "table1.col1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setDatasourceName(null); // 数据源名称为空

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then - 应该跳过字段验证，只做格式验证
            assertNotNull(result);
            assertEquals(1, result.size());
        }

        @Test
        @DisplayName("数据库名称为空")
        void testEmptyDatabaseName() {
            // Given
            String relationConditions = "table1.col1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setDatabaseName(null); // 数据库名称为空

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then - 应该跳过字段验证，只做格式验证
            assertNotNull(result);
            assertEquals(1, result.size());
        }

        @Test
        @DisplayName("SDK API调用失败")
        void testSDKApiFailure() {
            // Given
            String relationConditions = "table1.col1 = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockSDKApiFailure();

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                converter.parseRelationConditions(nodeConfig, relationConditions);
            });
            assertTrue(exception.getMessage().contains("验证表字段失败"));
        }
    }

    @Nested
    @DisplayName("边界场景测试")
    class EdgeCaseTest {

        @Test
        @DisplayName("单个字符的表名和字段名")
        void testSingleCharacterNames() {
            // Given
            String relationConditions = "a.b = c.d";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("a", Arrays.asList("b"));
            mockTableStructure("c", Arrays.asList("d"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
        }

        @Test
        @DisplayName("很长的表名和字段名")
        void testLongNames() {
            // Given
            String longTableName = "very_long_table_name_with_many_characters_123456789";
            String longColumnName = "very_long_column_name_with_many_characters_123456789";
            String relationConditions = longTableName + "." + longColumnName + " = table2.col2";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure(longTableName, Arrays.asList(longColumnName));
            mockTableStructure("table2", Arrays.asList("col2"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
        }

        @Test
        @DisplayName("多个and连接的复杂条件")
        void testComplexMultipleConditions() {
            // Given
            String relationConditions = "table1.col1 = table2.col2 and table3.col3 = table4.col4 and table5.col5 = table6.col6";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table1", Arrays.asList("col1"));
            mockTableStructure("table2", Arrays.asList("col2"));
            mockTableStructure("table3", Arrays.asList("col3"));
            mockTableStructure("table4", Arrays.asList("col4"));
            mockTableStructure("table5", Arrays.asList("col5"));
            mockTableStructure("table6", Arrays.asList("col6"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(3, result.size());
        }

        @Test
        @DisplayName("混合大小写的and连接")
        void testMixedCaseAnd() {
            // Given
            String relationConditions = "table1.col1 = table2.col2 And table3.col3 = table4.col4";
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            mockTableStructure("table1", Arrays.asList("col1"));
            mockTableStructure("table2", Arrays.asList("col2"));
            mockTableStructure("table3", Arrays.asList("col3"));
            mockTableStructure("table4", Arrays.asList("col4"));

            // When
            List<ColumnRelation> result = converter.parseRelationConditions(nodeConfig, relationConditions);

            // Then
            assertNotNull(result);
            assertEquals(2, result.size());
        }
    }

    // 辅助方法
    private ArchiveNodeConfigDO createMockNodeConfig() {
        ArchiveNodeConfigDO nodeConfig = new ArchiveNodeConfigDO();
        nodeConfig.setDatasourceName("testDataSource");
        nodeConfig.setDatabaseName("testDatabase");
        return nodeConfig;
    }

    private void mockTableStructure(String tableName, List<String> columns) {
        ColumnsResponse columnsResponse = new ColumnsResponse();
        columnsResponse.setTableName(tableName);
        
        List<DatabaseColumn> databaseColumns = columns.stream()
                .map(col -> {
                    DatabaseColumn dbCol = new DatabaseColumn();
                    dbCol.setName(col);
                    return dbCol;
                })
                .collect(java.util.stream.Collectors.toList());
        
        columnsResponse.setColumns(databaseColumns);
        
        Result<ColumnsResponse> result = new Result<>();
        result.setData(columnsResponse);
        
        when(archiveSDKIntegrationApi.fetchColumns(any(FetchColumnsRequest.class), eq("default")))
                .thenReturn(result);
    }

    private void mockTableNotExists(String tableName) {
        Result<ColumnsResponse> result = new Result<>();
        result.setData(null);
        
        when(archiveSDKIntegrationApi.fetchColumns(any(FetchColumnsRequest.class), eq("default")))
                .thenReturn(result);
    }

    private void mockSDKApiFailure() {
        when(archiveSDKIntegrationApi.fetchColumns(any(FetchColumnsRequest.class), eq("default")))
                .thenThrow(new RuntimeException("SDK API调用失败"));
    }
} 