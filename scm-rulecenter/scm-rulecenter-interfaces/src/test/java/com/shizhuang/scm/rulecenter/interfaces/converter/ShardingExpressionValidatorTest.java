package com.shizhuang.scm.rulecenter.interfaces.converter;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分片表达式验证器测试
 * 测试分片表达式的各种合法和非法场景
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@DisplayName("分片表达式验证器测试")
class ShardingExpressionValidatorTest {

    private ArchiveNodeConfigConverter converter;

    @BeforeEach
    void setUp() {
        converter = new ArchiveNodeConfigConverter();
    }

    @Nested
    @DisplayName("合法分片表达式测试")
    class ValidShardingExpressionsTest {

        @ParameterizedTest
        @ValueSource(strings = {
            "id + 128",
            "user_id - 100",
            "order_id * 2",
            "item_id / 64",
            "id + 128 - 64",
            "user_id * 2 + 1",
            "order_id / 4 * 2",
            "(id + 128) / 64",
            "id + (128 * 2)",
            "(user_id + 100) * 2 - 50",
            "id",
            "user_id",
            "order_id"
        })
        @DisplayName("测试合法的分片表达式")
        void testValidShardingExpressions(String expression) {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "表达式 '" + expression + "' 应该验证通过");
        }
    }

    @Nested
    @DisplayName("非法分片表达式测试")
    class InvalidShardingExpressionsTest {

        @ParameterizedTest
        @ValueSource(strings = {
            "id % 128",           // 不支持取模运算
            "id ^ 2",             // 不支持幂运算
            "sin(id)",            // 不支持数学函数
            "cos(user_id)",       // 不支持数学函数
            "log(order_id)",      // 不支持数学函数
            "sqrt(item_id)",      // 不支持数学函数
            "abs(id)",            // 不支持数学函数
            "id + + 128",         // 连续运算符
            "id * * 2",           // 连续运算符
            "id / / 64",          // 连续运算符
            "+ id",               // 以运算符开头（非负号）
            "* id",               // 以运算符开头
            "id +",               // 以运算符结尾
            "id *",               // 以运算符结尾
            "id /",               // 以运算符结尾
            "(id + 128",          // 括号不匹配
            "id + 128)",          // 括号不匹配
            "id @ 128",           // 非法字符
            "id # 128",           // 非法字符
            "id $ 128",           // 非法字符
            "id & 128",           // 非法字符
            "id | 128",           // 非法字符
            "id ~ 128",           // 非法字符
            "id ! 128",           // 非法字符
            "id ? 128",           // 非法字符
            "id : 128",           // 非法字符
            "id ; 128",           // 非法字符
            "id , 128",           // 非法字符
            "id . 128",           // 非法字符
            "id = 128",           // 不支持等号
            "id > 128",           // 不支持比较运算符
            "id < 128",           // 不支持比较运算符
            "id >= 128",          // 不支持比较运算符
            "id <= 128",          // 不支持比较运算符
            "id != 128",          // 不支持比较运算符
            "id && 128",          // 不支持逻辑运算符
            "id || 128",          // 不支持逻辑运算符
            "id & 128",           // 不支持位运算符
            "id | 128",           // 不支持位运算符
            "id ^ 128",           // 不支持位运算符
            "id << 128",          // 不支持位移运算符
            "id >> 128",          // 不支持位移运算符
            "id >>> 128"          // 不支持位移运算符
        })
        @DisplayName("测试非法的分片表达式")
        void testInvalidShardingExpressions(String expression) {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression(expression);
            
            // When & Then
            Exception exception = assertThrows(RuntimeException.class, () -> {
                converter.validateShardingExpression(nodeConfig);
            }, "表达式 '" + expression + "' 应该验证失败");
            
            assertTrue(exception.getMessage().contains("分片表达式格式错误"), 
                "错误信息应该包含'分片表达式格式错误'");
        }
    }

    @Nested
    @DisplayName("边界场景测试")
    class EdgeCaseTest {

        @Test
        @DisplayName("空表达式")
        void testEmptyExpression() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("");
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "空表达式应该验证通过");
        }

        @Test
        @DisplayName("null表达式")
        void testNullExpression() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression(null);
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "null表达式应该验证通过");
        }

        @Test
        @DisplayName("只有字段名的表达式")
        void testFieldNameOnly() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("id");
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "只有字段名的表达式应该验证通过");
        }

        @Test
        @DisplayName("复杂但合法的表达式")
        void testComplexValidExpression() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("(user_id + 100) * 2 - 50");
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "复杂但合法的表达式应该验证通过");
        }

        @Test
        @DisplayName("包含下划线的字段名")
        void testFieldNameWithUnderscore() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("user_id + 128");
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "包含下划线的字段名应该验证通过");
        }

        @Test
        @DisplayName("包含数字的字段名")
        void testFieldNameWithNumbers() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("user1_id + 128");
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "包含数字的字段名应该验证通过");
        }

        @Test
        @DisplayName("小数运算")
        void testDecimalOperations() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("id / 2.5 + 1.5");
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "小数运算应该验证通过");
        }

        @Test
        @DisplayName("负号开头的表达式")
        void testNegativeStartExpression() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("-id + 128");
            
            // When & Then
            assertDoesNotThrow(() -> {
                converter.validateShardingExpression(nodeConfig);
            }, "负号开头的表达式应该验证通过");
        }
    }

    @Nested
    @DisplayName("错误信息测试")
    class ErrorMessageTest {

        @Test
        @DisplayName("取模运算错误信息")
        void testModuloOperatorErrorMessage() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("id % 128");
            
            // When & Then
            Exception exception = assertThrows(RuntimeException.class, () -> {
                converter.validateShardingExpression(nodeConfig);
            });
            
            assertTrue(exception.getMessage().contains("不支持取模运算(%)"), 
                "错误信息应该包含'不支持取模运算(%)'");
        }

        @Test
        @DisplayName("幂运算错误信息")
        void testPowerOperatorErrorMessage() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("id ^ 2");
            
            // When & Then
            Exception exception = assertThrows(RuntimeException.class, () -> {
                converter.validateShardingExpression(nodeConfig);
            });
            
            assertTrue(exception.getMessage().contains("不支持幂运算(^)"), 
                "错误信息应该包含'不支持幂运算(^)'");
        }

        @Test
        @DisplayName("数学函数错误信息")
        void testMathFunctionErrorMessage() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("sin(id)");
            
            // When & Then
            Exception exception = assertThrows(RuntimeException.class, () -> {
                converter.validateShardingExpression(nodeConfig);
            });
            
            assertTrue(exception.getMessage().contains("不支持数学函数调用"), 
                "错误信息应该包含'不支持数学函数调用'");
        }

        @Test
        @DisplayName("连续运算符错误信息")
        void testConsecutiveOperatorsErrorMessage() {
            // Given
            ArchiveNodeConfigDO nodeConfig = createMockNodeConfig();
            nodeConfig.setShardingExpression("id + + 128");
            
            // When & Then
            Exception exception = assertThrows(RuntimeException.class, () -> {
                converter.validateShardingExpression(nodeConfig);
            });
            
            assertTrue(exception.getMessage().contains("连续的运算符"), 
                "错误信息应该包含'连续的运算符'");
        }
    }

    /**
     * 创建模拟的节点配置
     */
    private ArchiveNodeConfigDO createMockNodeConfig() {
        ArchiveNodeConfigDO nodeConfig = new ArchiveNodeConfigDO();
        nodeConfig.setNodeId("test-node-001");
        nodeConfig.setTableName("test_table");
        nodeConfig.setDatasourceName("test_datasource");
        nodeConfig.setDatabaseName("test_database");
        nodeConfig.setShardingEnabled(true);
        return nodeConfig;
    }
}
