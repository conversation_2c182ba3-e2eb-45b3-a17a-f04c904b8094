package com.shizhuang.scm.rulecenter.interfaces.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 归档任务更新请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ArchiveTaskUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    /**
     * 任务名称
     */
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

    /**
     * 数据源名称
     */
    @NotEmpty(message = "数据源名称不能为空")
    private String datasourceName;

    /**
     * 数据库名称
     */
    @NotEmpty(message = "数据库名称不能为空")
    private String databaseName;

    /**
     * 扫描数量限制
     */
    @NotNull(message = "扫描数量不能为空")
    private Integer limit;

    /**
     * 扫描间隔（秒）
     */
    @NotNull(message = "扫描间隔不能为空")
    private Integer interval;

    /**
     * 任务状态：0-暂停，1-运行
     */
    private Integer status;

    /**
     * 运行模式：1-debug，2-生产
     */
    private Integer mode;

    /**
     * 执行类型：1-全天，2-指定时间
     */
    private Integer executionType;

    /**
     * 扩展配置（JSON格式）
     */
    private String ext;

    /**
     * 指定运行时间段
     */
    private String timeSpan;

} 