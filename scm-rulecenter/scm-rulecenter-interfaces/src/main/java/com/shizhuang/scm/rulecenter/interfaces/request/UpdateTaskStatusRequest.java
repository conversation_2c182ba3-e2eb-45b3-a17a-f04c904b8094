package com.shizhuang.scm.rulecenter.interfaces.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新任务状态请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class UpdateTaskStatusRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    /**
     * 任务状态：0-暂停，1-运行
     */
    @NotNull(message = "任务状态不能为空")
    private Integer status;
} 