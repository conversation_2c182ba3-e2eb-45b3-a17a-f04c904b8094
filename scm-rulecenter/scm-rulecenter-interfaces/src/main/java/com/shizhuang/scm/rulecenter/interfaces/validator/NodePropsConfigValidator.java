package com.shizhuang.scm.rulecenter.interfaces.validator;

import com.shizhuang.scm.rulecenter.api.constant.ArchiveConstants;
import com.shizhuang.scm.rulecenter.interfaces.dto.NodePropsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 节点属性配置校验器
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
public class NodePropsConfigValidator {

    /**
     * 有效的扫描模式列表
     */
    private static final List<String> VALID_SCAN_MODES = Arrays.asList(
            ArchiveConstants.SCAN_MODE_PRIMARY_KEY,
            ArchiveConstants.SCAN_MODE_CONDITION,
            ArchiveConstants.SCAN_MODE_INDEX
    );

    /**
     * 校验NodePropsConfig对象
     * 
     * @param nodePropsConfig 节点属性配置
     * @throws IllegalArgumentException 校验失败时抛出异常
     */
    public static void validate(NodePropsConfig nodePropsConfig) {
        if (nodePropsConfig == null) {
            return;
        }

        // 校验扫描模式
        validateScanMode(nodePropsConfig.getScanMode());
        
        // 校验索引类型
        validateIndexType(nodePropsConfig.getIndexType());
        
        // 校验保留天数
        validateReserveDays(nodePropsConfig.getReserveDays());
        
        // 校验超时时间
        validateTimeoutSeconds(nodePropsConfig.getTimeoutSeconds());
        
        // 校验最大扫描次数
        validateMaxScanCount(nodePropsConfig.getMaxScanCount());
    }

    /**
     * 校验扫描模式
     * 
     * @param scanMode 扫描模式
     * @throws IllegalArgumentException 校验失败时抛出异常
     */
    public static void validateScanMode(String scanMode) {
        if (!StringUtils.hasText(scanMode)) {
            return; // 允许为空
        }

        if (!VALID_SCAN_MODES.contains(scanMode)) {
            String errorMsg = String.format("无效的扫描模式：%s，有效值：%s", 
                    scanMode, String.join(", ", VALID_SCAN_MODES));
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        log.debug("扫描模式校验通过：{}", scanMode);
    }

    /**
     * 校验索引类型
     * 
     * @param indexType 索引类型
     * @throws IllegalArgumentException 校验失败时抛出异常
     */
    public static void validateIndexType(Integer indexType) {
        if (indexType == null) {
            return; // 允许为空
        }

        if (indexType != ArchiveConstants.INDEX_TYPE_SPAN && 
            indexType != ArchiveConstants.INDEX_TYPE_RESERVE_DAYS) {
            String errorMsg = String.format("无效的索引类型：%d，有效值：%d(区间)、%d(保留天数)", 
                    indexType, ArchiveConstants.INDEX_TYPE_SPAN, ArchiveConstants.INDEX_TYPE_RESERVE_DAYS);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        log.debug("索引类型校验通过：{}", indexType);
    }

    /**
     * 校验保留天数
     * 
     * @param reserveDays 保留天数
     * @throws IllegalArgumentException 校验失败时抛出异常
     */
    public static void validateReserveDays(Integer reserveDays) {
        if (reserveDays == null) {
            return; // 允许为空
        }

        if (reserveDays <= 0 || reserveDays > 36500) { // 最大100年
            String errorMsg = String.format("无效的保留天数：%d，有效范围：1-36500", reserveDays);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        log.debug("保留天数校验通过：{}", reserveDays);
    }

    /**
     * 校验超时时间
     * 
     * @param timeoutSeconds 超时时间（秒）
     * @throws IllegalArgumentException 校验失败时抛出异常
     */
    public static void validateTimeoutSeconds(Integer timeoutSeconds) {
        if (timeoutSeconds == null) {
            return; // 允许为空
        }

        if (timeoutSeconds <= 0 || timeoutSeconds > 86400) { // 最大24小时
            String errorMsg = String.format("无效的超时时间：%d秒，有效范围：1-86400", timeoutSeconds);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        log.debug("超时时间校验通过：{}秒", timeoutSeconds);
    }

    /**
     * 校验最大扫描次数
     * 
     * @param maxScanCount 最大扫描次数
     * @throws IllegalArgumentException 校验失败时抛出异常
     */
    public static void validateMaxScanCount(Integer maxScanCount) {
        if (maxScanCount == null) {
            return; // 允许为空
        }

        if (maxScanCount <= 0 || maxScanCount > 10000) {
            String errorMsg = String.format("无效的最大扫描次数：%d，有效范围：1-10000", maxScanCount);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        log.debug("最大扫描次数校验通过：{}", maxScanCount);
    }

    /**
     * 获取有效的扫描模式列表
     * 
     * @return 有效的扫描模式列表
     */
    public static List<String> getValidScanModes() {
        return VALID_SCAN_MODES;
    }

    /**
     * 检查扫描模式是否有效
     * 
     * @param scanMode 扫描模式
     * @return 是否有效
     */
    public static boolean isValidScanMode(String scanMode) {
        return StringUtils.hasText(scanMode) && VALID_SCAN_MODES.contains(scanMode);
    }
} 