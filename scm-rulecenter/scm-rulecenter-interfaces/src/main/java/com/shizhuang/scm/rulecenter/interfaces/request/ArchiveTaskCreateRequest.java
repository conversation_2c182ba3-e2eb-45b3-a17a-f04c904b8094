package com.shizhuang.scm.rulecenter.interfaces.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;

/**
 * 归档任务创建请求
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class ArchiveTaskCreateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用名称
     */
    @NotEmpty(message = "应用名称不能为空")
    private String appName;

    /**
     * 任务名称
     */
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

    /**
     * 数据源名称
     */
    @NotEmpty(message = "数据源名称不能为空")
    private String datasourceName;

    /**
     * 数据库名称
     */
    @NotEmpty(message = "数据库名称不能为空")
    private String databaseName;

    /**
     * 扫描数量限制
     */
    @NotNull(message = "扫描数量不能为空")
    @Min(value = 1, message = "扫描数量必须大于0")
    @Max(value = 10000, message = "扫描数量不能超过10000")
    private Integer limit;

    /**
     * 扫描间隔（秒）
     */
    @NotNull(message = "扫描间隔不能为空")
    @Min(value = 1, message = "扫描间隔必须大于0")
    @Max(value = 3600, message = "扫描间隔不能超过3600秒")
    private Integer interval;

    /**
     * 任务状态：0-暂停，1-运行
     */
    @Min(value = 0, message = "任务状态值无效")
    @Max(value = 1, message = "任务状态值无效")
    private Integer status = 0;

    /**
     * 运行模式：1-debug，2-生产
     */
    @Min(value = 1, message = "运行模式值无效")
    @Max(value = 2, message = "运行模式值无效")
    private Integer mode = 1;

    /**
     * 执行类型：1-全天，2-指定时间
     */
    @Min(value = 1, message = "执行类型值无效")
    @Max(value = 2, message = "执行类型值无效")
    private Integer executionType = 1;

    /**
     * 时间范围（HH:mm-HH:mm格式）
     */
    @Pattern(regexp = "^(?:[01]?[0-9]|2[0-3]):[0-5][0-9]-(?:[01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "时间范围格式错误，请使用HH:mm-HH:mm格式")
    private String timeSpan;

    /**
     * 扩展配置（JSON格式）
     */
    private String ext;

    public String getAppName() { return appName == null ? null : appName.trim(); }
    public String getTaskName() { return taskName == null ? null : taskName.trim(); }
    public String getDatasourceName() { return datasourceName == null ? null : datasourceName.trim(); }
    public String getDatabaseName() { return databaseName == null ? null : databaseName.trim(); }
    public String getTimeSpan() { return timeSpan == null ? null : timeSpan.trim(); }
    public String getExt() { return ext == null ? null : ext.trim(); }
} 