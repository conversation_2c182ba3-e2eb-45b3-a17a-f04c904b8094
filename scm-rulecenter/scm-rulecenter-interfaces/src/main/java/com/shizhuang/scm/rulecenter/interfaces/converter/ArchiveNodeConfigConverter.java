package com.shizhuang.scm.rulecenter.interfaces.converter;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.interfaces.dto.ArchiveNodeConfigDTO;
import com.shizhuang.scm.rulecenter.interfaces.dto.NodePropsConfig;
import com.shizhuang.scm.rulecenter.interfaces.request.ArchiveTaskCreateRequest;
import com.shizhuang.scm.rulecenter.api.dto.ColumnRelation;
import com.shizhuang.scm.rulecenter.infrastructure.integration.rpc.ArchiveSDKIntegrationApi;
import com.shizhuang.duapp.scp.framework.admin.sdk.request.FetchColumnsRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.ColumnsResponse;
import com.poizon.fusion.common.model.Result;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 归档节点配置转换器
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
@Component
public class ArchiveNodeConfigConverter {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private ArchiveSDKIntegrationApi archiveSDKIntegrationApi;
    
    /**
     * 将ArchiveNodeConfigDO转换为ArchiveNodeConfigDDTO
     * 
     * @param nodeConfigDO 节点配置DO
     * @return 节点配置DTO
     */
    public ArchiveNodeConfigDTO toDisplayDTO(ArchiveNodeConfigDO nodeConfigDO) {
        if (nodeConfigDO == null) {
            return null;
        }
        
        ArchiveNodeConfigDTO displayDTO = new ArchiveNodeConfigDTO();
        
        // 复制基本属性
        displayDTO.setId(nodeConfigDO.getId());
        displayDTO.setTaskId(nodeConfigDO.getTaskId());
        displayDTO.setNodeId(nodeConfigDO.getNodeId());
        displayDTO.setParentNodeId(nodeConfigDO.getParentNodeId());
        displayDTO.setTableName(nodeConfigDO.getTableName());
        displayDTO.setCondition(nodeConfigDO.getCondition());
        displayDTO.setDatasourceName(nodeConfigDO.getDatasourceName());
        displayDTO.setDatabaseName(nodeConfigDO.getDatabaseName());
        displayDTO.setIsArchive(nodeConfigDO.getIsArchive());
        displayDTO.setQueryColumns(nodeConfigDO.getQueryColumns());
        displayDTO.setPrimaryKeyColumn(nodeConfigDO.getPrimaryKeyColumn());
        displayDTO.setRootNode(nodeConfigDO.getRootNode());
        displayDTO.setIndexColumn(nodeConfigDO.getIndexColumn());
        displayDTO.setOrderBy(nodeConfigDO.getOrderBy());
        displayDTO.setArchiveType(nodeConfigDO.getArchiveType());
        displayDTO.setFilterDefaultValue(nodeConfigDO.getFilterDefaultValue());
        displayDTO.setShardingEnabled(nodeConfigDO.getShardingEnabled());
        displayDTO.setShardingExpression(nodeConfigDO.getShardingExpression());
        displayDTO.setShardingPluginClass(nodeConfigDO.getShardingPluginClass());
        displayDTO.setShardingField(nodeConfigDO.getShardingField());
        displayDTO.setShardingPrefix(nodeConfigDO.getShardingPrefix());
        displayDTO.setRelations(nodeConfigDO.getRelations());
        displayDTO.setCreateTime(nodeConfigDO.getCreateTime());
        displayDTO.setUpdateTime(nodeConfigDO.getUpdateTime());
        
        // 解析props为NodePropsConfig对象
        NodePropsConfig nodeProps = parseNodeProps(nodeConfigDO.getProps());
        displayDTO.setNodeProps(nodeProps);
        
        // 将nodeProps中的字段打平到DTO中，优先使用数据库字段
        if (nodeProps != null) {
            // 索引字段类型 - 优先使用数据库字段，如果为空则使用nodeProps
            if (nodeConfigDO.getIndexColumn() != null && nodeProps.getIndexColumType() != null) {
                displayDTO.setIndexColumType(nodeProps.getIndexColumType());
            } else if (nodeProps.getIndexColumType() != null) {
                displayDTO.setIndexColumType(nodeProps.getIndexColumType());
            }
            
            // 索引扫描 - 优先使用数据库字段，如果为空则使用nodeProps
            if (nodeConfigDO.getIndexColumn() != null && nodeProps.getEnableScan() != null) {
                displayDTO.setEnableScan(nodeProps.getEnableScan());
            } else if (nodeProps.getEnableScan() != null) {
                displayDTO.setEnableScan(nodeProps.getEnableScan());
            }
            
            // 索引值范围 - 优先使用数据库字段，如果为空则使用nodeProps
            if (nodeConfigDO.getIndexColumn() != null && (nodeProps.getIndexStart() != null || nodeProps.getIndexEnd() != null)) {
                displayDTO.setIndexStart(nodeProps.getIndexStart());
                displayDTO.setIndexEnd(nodeProps.getIndexEnd());
            } else if (nodeProps.getIndexStart() != null || nodeProps.getIndexEnd() != null) {
                displayDTO.setIndexStart(nodeProps.getIndexStart());
                displayDTO.setIndexEnd(nodeProps.getIndexEnd());
            }
            
            // 保留天数 - 优先使用数据库字段，如果为空则使用nodeProps
            if (nodeConfigDO.getIndexColumn() != null && nodeProps.getReserveDays() != null) {
                displayDTO.setReserveDays(nodeProps.getReserveDays());
            } else if (nodeProps.getReserveDays() != null) {
                displayDTO.setReserveDays(nodeProps.getReserveDays());
            }
        }
        
        // 索引字段 - 直接使用数据库字段
        displayDTO.setIndexColumn(nodeConfigDO.getIndexColumn());
        
        // 构建indexData对象（包含索引字段信息）
        if (nodeConfigDO.getIndexColumn() != null) {
            try {
                // 构建indexData对象，包含label、value、type信息
                String indexColumType = displayDTO.getIndexColumType();
                if (indexColumType == null && nodeProps != null) {
                    indexColumType = nodeProps.getIndexColumType();
                }
                
                Object indexData = objectMapper.createObjectNode()
                    .put("label", nodeConfigDO.getIndexColumn())
                    .put("value", nodeConfigDO.getIndexColumn())
                    .put("type", indexColumType);
                displayDTO.setIndexData(indexData);
            } catch (Exception e) {
                log.warn("构建indexData失败: nodeId={}, error={}", nodeConfigDO.getNodeId(), e.getMessage());
            }
        }
        
        return displayDTO;
    }
    
    /**
     * 将ArchiveNodeConfigDDTO转换为ArchiveNodeConfigDO
     * 
     * @param displayDTO 节点配置DTO
     * @return 节点配置DO
     */
    public ArchiveNodeConfigDO toDO(ArchiveNodeConfigDTO displayDTO) {
        if (displayDTO == null) {
            return null;
        }
        
        ArchiveNodeConfigDO nodeConfigDO = new ArchiveNodeConfigDO();
        
        // 复制基本属性
        nodeConfigDO.setId(displayDTO.getId());
        nodeConfigDO.setTaskId(displayDTO.getTaskId());
        nodeConfigDO.setNodeId(displayDTO.getNodeId());
        nodeConfigDO.setParentNodeId(displayDTO.getParentNodeId());
        nodeConfigDO.setTableName(displayDTO.getTableName());
        nodeConfigDO.setCondition(displayDTO.getCondition());
        nodeConfigDO.setDatasourceName(displayDTO.getDatasourceName());
        nodeConfigDO.setDatabaseName(displayDTO.getDatabaseName());
        nodeConfigDO.setIsArchive(displayDTO.getIsArchive());
        nodeConfigDO.setQueryColumns(displayDTO.getQueryColumns());
        nodeConfigDO.setPrimaryKeyColumn(displayDTO.getPrimaryKeyColumn());
        nodeConfigDO.setRootNode(displayDTO.getRootNode());

        // 处理索引字段 - 优先使用打平字段，如果为空则使用原有字段
        String indexColumn = displayDTO.getIndexColumn();
        if (indexColumn == null && displayDTO.getIndexData() != null) {
            // 从indexData中提取索引字段
            try {
                if (displayDTO.getIndexData() instanceof com.fasterxml.jackson.databind.JsonNode) {
                    com.fasterxml.jackson.databind.JsonNode indexDataNode = (com.fasterxml.jackson.databind.JsonNode) displayDTO.getIndexData();
                    if (indexDataNode.has("value")) {
                        indexColumn = indexDataNode.get("value").asText();
                    }
                }
            } catch (Exception e) {
                log.warn("从indexData提取索引字段失败: error={}", e.getMessage());
            }
        }
        nodeConfigDO.setIndexColumn(indexColumn);
        
        nodeConfigDO.setOrderBy(displayDTO.getOrderBy());
        nodeConfigDO.setArchiveType(displayDTO.getArchiveType());
        nodeConfigDO.setFilterDefaultValue(displayDTO.getFilterDefaultValue());
        nodeConfigDO.setShardingEnabled(displayDTO.getShardingEnabled());
        nodeConfigDO.setShardingExpression(displayDTO.getShardingExpression());
        nodeConfigDO.setShardingPluginClass(displayDTO.getShardingPluginClass());
        nodeConfigDO.setShardingField(displayDTO.getShardingField());


        nodeConfigDO.setShardingPrefix(displayDTO.getShardingPrefix());

        nodeConfigDO.setRelations(displayDTO.getRelations());
        nodeConfigDO.setCreateTime(displayDTO.getCreateTime());
        nodeConfigDO.setUpdateTime(displayDTO.getUpdateTime());
        
        // 将打平的字段重新组装到NodePropsConfig中，优先使用数据库字段
        NodePropsConfig nodeProps = displayDTO.getNodeProps();
        if (nodeProps == null) {
            nodeProps = new NodePropsConfig();
        }

        // 索引字段类型
        if (displayDTO.getIndexColumType() != null) {
            nodeProps.setIndexColumType(displayDTO.getIndexColumType());
        }

        // 索引扫描
        if (displayDTO.getEnableScan() != null) {
            nodeProps.setEnableScan(displayDTO.getEnableScan());
        }

        // 索引值范围
        if (displayDTO.getIndexStart() != null) {
            nodeProps.setIndexStart(displayDTO.getIndexStart());
        }
        if (displayDTO.getIndexEnd() != null) {
            nodeProps.setIndexEnd(displayDTO.getIndexEnd());
        }

        // 保留天数
        if (displayDTO.getReserveDays() != null) {
            nodeProps.setReserveDays(displayDTO.getReserveDays());
        }

        // 修复：处理indexData字段，提取其中的信息到nodeProps
        if (displayDTO.getIndexData() != null) {
            try {
                if (displayDTO.getIndexData() instanceof com.fasterxml.jackson.databind.JsonNode) {
                    com.fasterxml.jackson.databind.JsonNode indexDataNode = (com.fasterxml.jackson.databind.JsonNode) displayDTO.getIndexData();

                    // 提取索引字段类型
                    if (indexDataNode.has("type") && nodeProps.getIndexColumType() == null) {
                        String type = indexDataNode.get("type").asText();
                        if (type != null && !type.trim().isEmpty()) {
                            nodeProps.setIndexColumType(type);
                        }
                    }

                    // 提取索引字段值
                    if (indexDataNode.has("value") && nodeConfigDO.getIndexColumn() == null) {
                        String value = indexDataNode.get("value").asText();
                        if (value != null && !value.trim().isEmpty()) {
                            nodeConfigDO.setIndexColumn(value);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("处理indexData失败: error={}", e.getMessage());
            }
        }

        // 将NodePropsConfig对象序列化为props字符串
        nodeConfigDO.setProps(serializeNodeProps(nodeProps));
        
        return nodeConfigDO;
    }
    
        /**
     * 批量转换DO列表为DTO列表
     * 
     * @param nodeConfigDOList 节点配置DO列表
     * @return 节点配置DTO列表
     */
    public List<ArchiveNodeConfigDTO> toDisplayDTOList(List<ArchiveNodeConfigDO> nodeConfigDOList) {
        if (CollectionUtils.isEmpty(nodeConfigDOList)) {
            return null;
        }
        
        return nodeConfigDOList.stream()
                .map(this::toDisplayDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换DTO列表为DO列表
     * 
     * @param displayDTOList 节点配置DTO列表
     * @return 节点配置DO列表
     */
    public List<ArchiveNodeConfigDO> toDOList(List<ArchiveNodeConfigDTO> displayDTOList) {
        if (CollectionUtils.isEmpty(displayDTOList)) {
            return null;
        }
        
        return displayDTOList.stream()
                .map(this::toDO)
                .collect(Collectors.toList());
    }
    
    /**
     * 解析props字符串为NodePropsConfig对象
     * 
     * @param props props字符串
     * @return NodePropsConfig对象
     */
    private NodePropsConfig parseNodeProps(String props) {
        if (props == null || props.trim().isEmpty()) {
            return new NodePropsConfig();
        }
        
        try {
            return objectMapper.readValue(props, NodePropsConfig.class);
        } catch (Exception e) {
            log.error("解析NodePropsConfig失败: props={}, error={}", props, e.getMessage(), e);
            throw new RuntimeException("解析NodePropsConfig失败: props=" + props, e);
        }
    }
    
    /**
     * 将NodePropsConfig对象序列化为props字符串
     * 
     * @param nodeProps NodePropsConfig对象
     * @return props字符串
     */
    private String serializeNodeProps(NodePropsConfig nodeProps) {
        if (nodeProps == null) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(nodeProps);
        } catch (Exception e) {
            log.error("序列化NodePropsConfig失败: nodeProps={}, error={}", nodeProps, e.getMessage(), e);
            throw new RuntimeException("序列化NodePropsConfig失败: nodeProps=" + nodeProps, e);
        }
    }

    /**
     * 验证relations字段并解析到props中
     * 参考 ArchiveDomainService#parseRelationConditions 的实现
     *
     * @param nodeConfig 节点配置
     */
    public void validateAndParseRelations(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null) {
            return;
        }

        // 校验分片表达式
        validateShardingExpression(nodeConfig);

        String relationConditions = getRelationConditions(nodeConfig);
        if (relationConditions != null && !relationConditions.trim().isEmpty()) {
            try {
                List<ColumnRelation> columnRelations = parseRelationConditions(nodeConfig, relationConditions);
                updatePropsWithColumnRelations(nodeConfig, columnRelations);
            } catch (Exception e) {
                // 如果验证失败，记录详细错误信息并提供调试建议
                log.error("关系条件验证失败: nodeId={}, relationConditions={}, error={}", 
                    nodeConfig.getNodeId(), relationConditions, e.getMessage(), e);
                
                // 提供调试信息
                log.error("调试建议: 请检查以下内容：");
                log.error("1. 数据源名称: {}", nodeConfig.getDatasourceName());
                log.error("2. 数据库名称: {}", nodeConfig.getDatabaseName());
                log.error("3. 表名和字段名是否正确");
                log.error("4. 数据源连接是否正常");
                log.error("5. 是否有访问表的权限");
                
                throw e;
            }
        }
    }

    /**
     * 校验分片表达式
     * 要求：只允许一个数据库字段名，其他是合法的数学运算
     * 示例：item_id%128, item_id/128+1, item_id*2+1 等
     *
     * @param nodeConfig 节点配置
     */
    private void validateShardingExpression(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null || !Boolean.TRUE.equals(nodeConfig.getShardingEnabled())) {
            return;
        }

        String shardingExpression = nodeConfig.getShardingExpression();
        if (!StringUtils.hasText(shardingExpression)) {
            throw new RuntimeException("启用分片时，分片表达式不能为空");
        }

        // 移除所有空白字符
        String expression = shardingExpression.replaceAll("\\s+", "");

        // 校验表达式格式：只允许一个数据库字段名，其他是合法的数学运算
        validateShardingExpressionFormat(expression);

        // 使用数学表达式验证语法正确性
        validateShardingExpressionMathFormat(expression);
    }

    /**
     * 校验分片表达式格式
     * 只允许一个数据库字段名，其他是合法的数学运算
     *
     * @param expression 分片表达式
     */
    private void validateShardingExpressionFormat(String expression) {
        // 数据库字段名正则表达式：字母开头，包含字母、数字、下划线
        Pattern fieldPattern = Pattern.compile("[a-zA-Z_][a-zA-Z0-9_]*");

        // 提取所有可能的字段名
        Matcher fieldMatcher = fieldPattern.matcher(expression);
        java.util.Set<String> fields = new java.util.HashSet<>();
        while (fieldMatcher.find()) {
            String field = fieldMatcher.group();
            // 排除数学函数名和常量
            if (!isMathFunctionOrConstant(field)) {
                fields.add(field);
            }
        }

        // 检查是否只有一个数据库字段名
        if (fields.size() != 1) {
            throw new RuntimeException("分片表达式必须包含且仅包含一个数据库字段名，当前字段：" + fields);
        }

        String fieldName = fields.iterator().next();
        log.debug("分片表达式字段名: {}", fieldName);

        // 验证表达式只包含合法字符：字段名、数字、运算符、括号、空格
        // 允许的字符：字母、数字、下划线、运算符(+ - * / %)、括号、空格、小数点
        String validExpression = expression.replaceAll("[a-zA-Z_][a-zA-Z0-9_]*", "FIELD")
                                        .replaceAll("\\d+(\\.\\d+)?", "NUMBER")
                                        .replaceAll("[+\\-*/%()\\s.]", "OPERATOR");

        // 检查是否包含非法字符（除了FIELD、NUMBER、OPERATOR之外的其他字符）
        String remainingChars = validExpression.replaceAll("[A-Z]+", "");
        if (!remainingChars.isEmpty()) {
            throw new RuntimeException("分片表达式包含非法字符: " + remainingChars +
                "，只允许：字母、数字、下划线、运算符(+ - * / %)、括号、空格、小数点");
        }

        // 基本语法检查：确保表达式结构合理
        validateExpressionStructure(expression);
    }

    /**
     * 验证表达式结构的基本合理性
     *
     * @param expression 分片表达式
     */
    private void validateExpressionStructure(String expression) {
        // 检查括号匹配
        int openBrackets = 0;
        int closeBrackets = 0;
        for (char c : expression.toCharArray()) {
            if (c == '(') {
                openBrackets++;
            } else if (c == ')') {
                closeBrackets++;
            }
        }
        if (openBrackets != closeBrackets) {
            throw new RuntimeException("分片表达式括号不匹配");
        }

        // 检查连续的运算符（除了负号）
        for (int i = 0; i < expression.length() - 1; i++) {
            char current = expression.charAt(i);
            char next = expression.charAt(i + 1);
            if (isOperator(current) && isOperator(next) && current != '-' && next != '-') {
                throw new RuntimeException("分片表达式包含连续的运算符: " + current + next);
            }
        }

        // 检查表达式不能以运算符结尾（除了右括号）
        char lastChar = expression.charAt(expression.length() - 1);
        if (isOperator(lastChar) && lastChar != ')') {
            throw new RuntimeException("分片表达式不能以运算符结尾");
        }
    }

    /**
     * 判断字符是否为运算符
     * 只支持四则运算：+ - * /
     *
     * @param c 字符
     * @return 是否为运算符
     */
    private boolean isOperator(char c) {
        return c == '+' || c == '-' || c == '*' || c == '/';
    }

    /**
     * 判断是否为数学函数名或常量
     *
     * @param name 名称
     * @return 是否为数学函数名或常量
     */
    private boolean isMathFunctionOrConstant(String name) {
        String[] mathFunctions = {"abs", "ceil", "floor", "round", "sqrt", "pow", "max", "min", "sin", "cos", "tan"};
        String[] constants = {"pi", "e"};

        String lowerName = name.toLowerCase();
        for (String func : mathFunctions) {
            if (lowerName.equals(func)) {
                return true;
            }
        }
        for (String constant : constants) {
            if (lowerName.equals(constant)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证分片表达式的数学运算格式
     * 支持格式：字段名 + 简单数学运算，如 id + 128, id - 128, id * 128, id / 128
     * 不支持：取模(%)、幂运算(^)、复杂函数等
     *
     * @param expression 分片表达式
     */
    private void validateShardingExpressionMathFormat(String expression) {
        try {
            // 移除所有空白字符
            String cleanExpression = expression.replaceAll("\\s+", "");
            
            // 提取字段名
            String fieldName = extractFieldNameFromExpression(cleanExpression);
            if (fieldName == null) {
                throw new RuntimeException("分片表达式必须包含一个有效的数据库字段名");
            }
            
            // 验证表达式只包含允许的字符和操作
            validateExpressionCharacters(cleanExpression, fieldName);
            
            // 验证数学运算的合理性
            validateMathOperations(cleanExpression, fieldName);
            
            log.info("分片表达式验证成功: expression={}, fieldName={}", expression, fieldName);
            
        } catch (Exception e) {
            log.error("分片表达式验证失败: expression={}, error={}", expression, e.getMessage(), e);
            throw new RuntimeException("分片表达式格式错误: " + expression + ", 错误信息: " + e.getMessage());
        }
    }

    /**
     * 从分片表达式中提取字段名
     *
     * @param expression 分片表达式
     * @return 字段名
     */
    private String extractFieldNameFromExpression(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return null;
        }

        // 数据库字段名正则表达式：字母开头，包含字母、数字、下划线
        Pattern fieldPattern = Pattern.compile("[a-zA-Z_][a-zA-Z0-9_]*");
        Matcher fieldMatcher = fieldPattern.matcher(expression);
        
        while (fieldMatcher.find()) {
            String field = fieldMatcher.group();
            // 排除数学函数名和常量
            if (!isMathFunctionOrConstant(field)) {
                return field;
            }
        }
        
        return null;
    }

    /**
     * 验证表达式只包含允许的字符和操作
     *
     * @param expression 清理后的表达式
     * @param fieldName 字段名
     */
    private void validateExpressionCharacters(String expression, String fieldName) {
        // 只允许：字段名、数字、运算符(+ - * /)、括号、小数点
        String allowedPattern = "[a-zA-Z_][a-zA-Z0-9_]*|\\d+(\\.\\d+)?|[+\\-*/()]";
        String remainingExpression = expression.replaceAll(allowedPattern, "");
        
        if (!remainingExpression.isEmpty()) {
            throw new RuntimeException("分片表达式包含非法字符: " + remainingExpression + 
                "，只允许：字段名、数字、运算符(+ - * /)、括号、小数点");
        }
    }

    /**
     * 验证数学运算的合理性
     * 只支持简单的四则运算：+ - * /
     *
     * @param expression 清理后的表达式
     * @param fieldName 字段名
     */
    private void validateMathOperations(String expression, String fieldName) {
        // 检查括号匹配
        int openBrackets = 0;
        int closeBrackets = 0;
        for (char c : expression.toCharArray()) {
            if (c == '(') {
                openBrackets++;
            } else if (c == ')') {
                closeBrackets++;
            }
        }
        if (openBrackets != closeBrackets) {
            throw new RuntimeException("分片表达式括号不匹配");
        }

        // 检查连续的运算符（除了负号）
        for (int i = 0; i < expression.length() - 1; i++) {
            char current = expression.charAt(i);
            char next = expression.charAt(i + 1);
            if (isOperator(current) && isOperator(next) && current != '-' && next != '-') {
                throw new RuntimeException("分片表达式包含连续的运算符: " + current + next);
            }
        }

        // 检查表达式不能以运算符结尾（除了右括号）
        char lastChar = expression.charAt(expression.length() - 1);
        if (isOperator(lastChar) && lastChar != ')') {
            throw new RuntimeException("分片表达式不能以运算符结尾");
        }

        // 检查表达式不能以运算符开头（除了负号）
        char firstChar = expression.charAt(0);
        if (isOperator(firstChar) && firstChar != '-') {
            throw new RuntimeException("分片表达式不能以运算符开头（负号除外）");
        }
        
        // 检查是否包含不支持的运算符
        if (expression.contains("%")) {
            throw new RuntimeException("分片表达式不支持取模运算(%)，只支持四则运算(+ - * /)");
        }
        
        if (expression.contains("^")) {
            throw new RuntimeException("分片表达式不支持幂运算(^)，只支持四则运算(+ - * /)");
        }
        
        // 检查是否包含数学函数调用
        if (expression.contains("(") && !expression.contains(")")) {
            // 检查是否有函数调用，如 sin(, cos(, log( 等
            String[] functionPatterns = {"sin(", "cos(", "tan(", "log(", "ln(", "sqrt(", "abs("};
            for (String func : functionPatterns) {
                if (expression.contains(func)) {
                    throw new RuntimeException("分片表达式不支持数学函数调用(" + func + ")，只支持四则运算(+ - * /)");
                }
            }
        }
    }

    /**
     * 解析关系条件字符串为ColumnRelation列表
     * 直接解析SQL格式：table1.column1 = table2.column2
     * 支持多个条件用 and 连接
     * 
     * @param nodeConfig 节点配置
     * @param relationConditions 关系条件字符串
     * @return ColumnRelation列表
     */
    private List<ColumnRelation> parseRelationConditions(ArchiveNodeConfigDO nodeConfig, String relationConditions) {
        if (relationConditions == null || relationConditions.trim().isEmpty()) {
            return null;
        }
        
        // 直接解析SQL格式
        return parseSqlRelationConditions(relationConditions, nodeConfig);
    }

    /**
     * 解析SQL格式的关系条件
     * 格式：table1.column1 = table2.column2
     * 支持多个条件用 and 连接
     * 参考 ArchiveDomainService.parseRelationConditions 的实现
     * 
     * @param relationConditions SQL格式的关系条件
     * @param nodeConfig 节点配置，用于获取数据源信息进行字段验证
     * @return ColumnRelation列表
     */
    private List<ColumnRelation> parseSqlRelationConditions(String relationConditions, ArchiveNodeConfigDO nodeConfig) {
        if (relationConditions == null || relationConditions.trim().isEmpty()) {
            return null;
        }
        
        try {
            List<ColumnRelation> relations = new java.util.ArrayList<>();
            
            // 按and分割多个条件，大小写不敏感，支持空格
            String[] conditionParts = relationConditions.split("(?i)\\s+and\\s+");
            if (conditionParts.length == 0) {
                throw new RuntimeException("无效的关联条件格式");
            }
            
            // 验证每个条件的基本格式
            for (String condition : conditionParts) {
                condition = condition.trim();
                // 校验每个条件必须包含等号
                if (!condition.contains("=")) {
                    throw new RuntimeException("关联条件必须使用等值关联: " + condition);
                }
            }
            
            // 解析每个条件
            for (String condition : conditionParts) {
                condition = condition.trim();
                
                // 解析单个条件
                String[] parts = condition.split("=");
                if (parts.length != 2) {
                    throw new RuntimeException("关联条件格式错误，应为 table1.column1 = table2.column2: " + condition);
                }
                
                String left = parts[0].trim().replace("`", "");
                String right = parts[1].trim().replace("`", "");
                
                // 解析表名和字段名
                String[] leftArr = left.split("\\.");
                String[] rightArr = right.split("\\.");
                
                if (leftArr.length != 2 || rightArr.length != 2) {
                    throw new RuntimeException("关联条件格式错误，应为 table1.column1 = table2.column2: " + condition);
                }
                
                // 验证表名和字段名格式
                validateTableAndColumnFormat(leftArr[0], leftArr[1], "左侧");
                validateTableAndColumnFormat(rightArr[0], rightArr[1], "右侧");
                
                // 验证表存在性和字段存在性（可选）
                if (nodeConfig != null && isFieldValidationEnabled(nodeConfig)) {
                    validateTableAndColumnExists(leftArr[0], leftArr[1], nodeConfig, "左侧");
                    validateTableAndColumnExists(rightArr[0], rightArr[1], nodeConfig, "右侧");
                } else if (nodeConfig != null) {
                    log.info("跳过字段验证: 数据源信息不完整或字段验证被禁用");
                }
                
                // 创建ColumnRelation对象
                ColumnRelation relation = new ColumnRelation();
                relation.setCurrentColumn(leftArr[1]);      // 当前表字段
                relation.setRelatedColumn(rightArr[1]);    // 关联表字段
                relation.setRelatedTable(rightArr[0]);     // 关联表名
                
                relations.add(relation);
            }
            
            log.debug("SQL格式关系条件解析成功: relationConditions={}, relations={}", 
                relationConditions, relations.size());
            
            return relations;
            
        } catch (Exception e) {
            log.error("SQL格式关系条件解析失败: relationConditions={}, error={}", 
                relationConditions, e.getMessage(), e);
            throw new RuntimeException("SQL格式关系条件解析失败: " + relationConditions + ", 错误信息: " + e.getMessage());
        }
    }

    /**
     * 验证表名和字段名格式
     * 参考前端表单的正则表达式验证逻辑
     * 
     * @param tableName 表名
     * @param columnName 字段名
     * @param side 左侧或右侧标识
     */
    private void validateTableAndColumnFormat(String tableName, String columnName, String side) {
        // 表名格式：字母开头，包含字母、数字、下划线
        if (!tableName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            throw new RuntimeException(side + "表名格式错误: " + tableName + "，应为字母开头，包含字母、数字、下划线");
        }
        
        // 字段名格式：字母开头，包含字母、数字、下划线
        if (!columnName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            throw new RuntimeException(side + "字段名格式错误: " + columnName + "，应为字母开头，包含字母、数字、下划线");
        }
    }

    /**
     * 判断是否启用字段验证
     * 
     * @param nodeConfig 节点配置
     * @return 是否启用字段验证
     */
    private boolean isFieldValidationEnabled(ArchiveNodeConfigDO nodeConfig) {
        if (nodeConfig == null) {
            return false;
        }
        
        // 检查数据源信息是否完整
        String datasourceName = nodeConfig.getDatasourceName();
        String databaseName = nodeConfig.getDatabaseName();
        
        if (datasourceName == null || datasourceName.trim().isEmpty()) {
            log.info("数据源名称为空，禁用字段验证");
            return false;
        }
        
        if (databaseName == null || databaseName.trim().isEmpty()) {
            log.info("数据库名称为空，禁用字段验证");
            return false;
        }
        
        // 可以在这里添加其他条件，比如配置开关
        return true;
    }

    /**
     * 验证表存在性和字段存在性
     * 参考 ArchiveDomainService.checkColumnsExists 的实现
     * 
     * @param tableName 表名
     * @param columnName 字段名
     * @param nodeConfig 节点配置
     * @param side 左侧或右侧标识
     */
    private void validateTableAndColumnExists(String tableName, String columnName, ArchiveNodeConfigDO nodeConfig, String side) {
        try {
            // 获取数据源信息
            String datasourceName = nodeConfig.getDatasourceName();
            String databaseName = nodeConfig.getDatabaseName();
            
            // 记录详细的调试信息
            log.info("开始验证表字段: side={}, tableName={}, columnName={}, datasourceName={}, databaseName={}", 
                side, tableName, columnName, datasourceName, databaseName);
            
            if (datasourceName == null || datasourceName.trim().isEmpty()) {
                log.warn("数据源名称为空，跳过表字段验证: tableName={}, columnName={}", tableName, columnName);
                return;
            }
            
            if (databaseName == null || databaseName.trim().isEmpty()) {
                log.warn("数据库名称为空，跳过表字段验证: tableName={}, columnName={}", tableName, columnName);
                return;
            }
            
            // 构建查询表结构的请求
            FetchColumnsRequest request = FetchColumnsRequest.builder()
                    .dataSourceName(datasourceName)
                    .databaseName(databaseName)
                    .tableName(tableName)
                    .build();
            
            log.info("构建查询请求: request={}", request);
            
            // 调用SDK API获取表结构
            Result<ColumnsResponse> result = archiveSDKIntegrationApi.fetchColumns(request, "default"); // 使用默认appName

            log.info("SDK API调用结果: result={}   request {}", JSON.toJSONString(result), JSON.toJSONString(request));
            
            if (result == null) {
                throw new RuntimeException(side + "SDK API返回null结果，无法获取表结构: " + tableName);
            }
            
            if (result.getData() == null) {
                throw new RuntimeException(side + "SDK API返回数据为null，表可能不存在: " + tableName + 
                    "，请检查数据源[" + datasourceName + "]、数据库[" + databaseName + "]和表名[" + tableName + "]是否正确");
            }
            
            if (result.getData().getColumns() == null) {
                throw new RuntimeException(side + "表[" + tableName + "]结构信息为空，请检查表是否存在或是否有权限访问");
            }
            
            // 记录表结构信息用于调试
            log.info("表结构信息: tableName={}, columnsCount={}, columns={}", 
                tableName, result.getData().getColumns().size(), 
                result.getData().getColumns().stream().map(col -> col.getName()).collect(java.util.stream.Collectors.toList()));
            
            // 验证字段是否存在
            boolean columnExists = result.getData().getColumns().stream()
                    .anyMatch(col -> col.getName() != null && col.getName().equalsIgnoreCase(columnName));
            
            if (!columnExists) {
                // 提供更详细的错误信息，包括可用的字段列表
                List<String> availableColumns = result.getData().getColumns().stream()
                        .map(col -> col.getName())
                        .filter(name -> name != null)
                        .collect(java.util.stream.Collectors.toList());
                
                throw new RuntimeException(side + "表[" + tableName + "]中不存在字段[" + columnName + 
                    "]，可用字段: " + availableColumns + 
                    "，请检查字段名是否正确（注意大小写敏感性）");
            }
            
            log.debug("表字段验证成功: tableName={}, columnName={}", tableName, columnName);
            
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw e;
            }
            log.error("验证表字段失败: side={}, tableName={}, columnName={}, datasourceName={}, databaseName={}, error={}", 
                side, tableName, columnName, 
                nodeConfig.getDatasourceName(), nodeConfig.getDatabaseName(), e.getMessage(), e);
            throw new RuntimeException(side + "验证表字段失败: " + tableName + "." + columnName + 
                "，数据源[" + nodeConfig.getDatasourceName() + "]，数据库[" + nodeConfig.getDatabaseName() + 
                "]，错误信息: " + e.getMessage());
        }
    }
    
    /**
     * 更新props中的列关系信息
     * 
     * @param nodeConfig 节点配置
     * @param columnRelations 列关系列表
     */
    private void updatePropsWithColumnRelations(ArchiveNodeConfigDO nodeConfig, List<ColumnRelation> columnRelations) {
        if (columnRelations == null || columnRelations.isEmpty()) {
            return;
        }
        
        try {
            // 将列关系序列化为JSON字符串并设置到relations字段
            String relationsJson = objectMapper.writeValueAsString(columnRelations);
            nodeConfig.setRelations(relationsJson);
            
            log.debug("更新节点relations字段: nodeId={}, columnRelations={}", 
                nodeConfig.getNodeId(), columnRelations.size());
        } catch (Exception e) {
            log.error("更新节点relations字段失败: nodeId={}, error={}", 
                nodeConfig.getNodeId(), e.getMessage(), e);
            throw new RuntimeException("更新节点relations字段失败: nodeId=" + nodeConfig.getNodeId(), e);
        }
    }
    
    /**
     * 获取关系条件字符串
     * 
     * @param node 节点配置
     * @return 关系条件字符串
     */
    private String getRelationConditions(ArchiveNodeConfigDO node) {
        if (node == null) {
            return null;
        }
        
        // 从relations字段获取
        if (node.getRelations() != null && !node.getRelations().trim().isEmpty()) {
            return node.getRelations();
        }
        
        return null;
    }
} 