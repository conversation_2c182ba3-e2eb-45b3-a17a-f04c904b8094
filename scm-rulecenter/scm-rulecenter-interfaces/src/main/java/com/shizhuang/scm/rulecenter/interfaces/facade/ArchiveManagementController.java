package com.shizhuang.scm.rulecenter.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveAppConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.service.ArchiveConfigService;
import com.shizhuang.scm.rulecenter.infrastructure.service.TaskManagementService;
import com.shizhuang.scm.rulecenter.interfaces.request.ArchiveTaskCreateRequest;
import com.shizhuang.scm.rulecenter.interfaces.request.ArchiveTaskUpdateRequest;
import com.shizhuang.scm.rulecenter.interfaces.converter.ArchiveTaskConverter;
import com.shizhuang.scm.rulecenter.interfaces.converter.ArchiveNodeConfigConverter;
import com.shizhuang.scm.rulecenter.interfaces.dto.ArchiveNodeConfigDTO;
import com.shizhuang.scm.rulecenter.interfaces.dto.ArchiveTaskConfigWithDetailDTO;
import com.shizhuang.scm.rulecenter.interfaces.validator.NodePropsConfigValidator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

import com.shizhuang.scm.rulecenter.api.dto.TaskConfigsQueryRequest;
import com.shizhuang.scm.rulecenter.interfaces.request.UpdateTaskStatusRequest;

import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.PARAM_ERROR;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.SERVER_ERROR;

/**
 * SCP-Archive 配置管理控制器
 * <p>
 * 基于数据库层实现，提供配置查询和任务管理功能
 * 与现有的 ArchiveController 形成互补，专注于配置管理
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("archive-management")
@Api(tags = "SCP-Archive 配置管理", description = "提供配置查询和任务管理功能")
public class ArchiveManagementController {

    @Autowired
    private ArchiveConfigService archiveConfigService;

    @Autowired
    private TaskManagementService taskManagementService;

    @Autowired
    private ArchiveTaskConverter archiveTaskConverter;

    @Autowired
    private ArchiveNodeConfigConverter archiveNodeConfigConverter;

    // ==================== 应用配置管理 ====================

    /**
     * 获取应用配置
     *
     * @param appName 应用名称
     * @return 应用配置信息
     */
    @ApiOperation("获取应用配置")
    @GetMapping("get-app-config")
    public Result<ArchiveAppConfigDO> getAppConfig(
            @ApiParam(value = "应用名称", required = true, example = "order-service")
            @RequestParam("appName") @NotEmpty String appName) {
        log.info("获取应用配置: appName={}", appName);
        ArchiveAppConfigDO appConfig = archiveConfigService.getAppConfig(appName);
        return Result.ofSuccess(appConfig);
    }

    /**
     * 创建或更新应用配置
     * <p>
     * 根据appName自动判断操作类型：
     * - 当appName不存在时，执行创建操作
     * - 当appName已存在时，执行更新操作
     *
     * @param request 应用配置请求对象
     * @return 创建或更新后的应用配置
     */
    @ApiOperation("创建或更新应用配置")
    @PostMapping("save-app-config")
    public Result<ArchiveAppConfigDO> createAppConfig(@RequestBody @Valid ArchiveAppConfigDO request) {
        log.info("创建或更新应用配置: {}", JSON.toJSONString(request));
        ArchiveAppConfigDO result = archiveConfigService.createAppConfig(request);
        return Result.ofSuccess(result);
    }

    /**
     * 获取单个任务配置（包含所有节点信息）
     *
     * @param taskId 任务ID
     * @return 任务配置信息和所有节点信息
     */
    @ApiOperation("获取单个任务配置（包含所有节点信息）")
    @GetMapping("get-task-config")
    public Result<ArchiveTaskConfigWithDetailDTO> getTaskConfig(@RequestParam("taskId") @NotNull Long taskId) {
        log.info("获取任务配置:  taskId={}", taskId);

        // 获取任务配置
        ArchiveTaskConfigDO taskConfig = archiveConfigService.getTaskConfig(taskId);
        if (taskConfig == null) {
            return Result.ofSuccess(null);
        }
        // 获取所有节点配置
        List<ArchiveNodeConfigDO> nodeConfigs = archiveConfigService.getTaskNodeConfigs(taskId);

        // 转换为DTO，包含NodePropsConfig信息
        ArchiveTaskConfigWithDetailDTO result = archiveTaskConverter.toDetailDTO(taskConfig, nodeConfigs);

        return Result.ofSuccess(result);
    }

    /**
     * 获取任务配置（支持批量查询和全量查询，支持分页）
     * 仅返回列表数据，不包含详细的节点信息
     *
     * @param query 查询参数 {appName, pageNum, pageSize, taskIds}
     * @return 任务配置列表（仅基本信息）
     */
    @ApiOperation("获取任务配置（支持批量查询和全量查询，支持分页）")
    @PostMapping("list-task-configs")
    public Result<List<ArchiveTaskConfigDO>> getTaskConfigs(@RequestBody @Valid TaskConfigsQueryRequest query) {
        log.info("获取任务配置列表: {}", JSON.toJSONString(query));

        // 类型转换
        com.shizhuang.scm.rulecenter.api.dto.TaskConfigsQueryRequest infraQuery = new com.shizhuang.scm.rulecenter.api.dto.TaskConfigsQueryRequest();
        infraQuery.setAppName(query.getAppName());
        infraQuery.setTaskIds(query.getTaskIds());
        infraQuery.setPageNum(query.getPageNum());
        infraQuery.setPageSize(query.getPageSize());

        // 获取任务配置列表（仅基本信息，不包含节点信息）
        List<ArchiveTaskConfigDO> taskConfigs = archiveConfigService.queryTaskConfigs(infraQuery);

        // 对于列表查询，仅返回任务基本信息，不包含节点详情
        // 这样可以提高查询性能，减少数据传输量
        return Result.ofSuccess(taskConfigs);
    }


    // ==================== 任务管理 ====================

    /**
     * 创建新任务
     *
     * @param request 任务创建请求
     * @return 创建后的任务配置
     */
    @ApiOperation("创建新任务")
    @PostMapping("create-task")
    public Result<ArchiveTaskConfigDO> createTask(@RequestBody @Valid ArchiveTaskCreateRequest request) {
        log.info("创建任务: request={}", JSON.toJSONString(request));
        
        // 校验执行类型和时间范围
        validateExecutionTypeAndTimeSpan(request.getExecutionType(), request.getTimeSpan());
        
        // 使用转换器转换为DO对象
        ArchiveTaskConfigDO taskConfig = archiveTaskConverter.toDO(request);
        // 创建任务
        ArchiveTaskConfigDO createdTask = taskManagementService.createTask(request.getAppName(), taskConfig);
        return Result.ofSuccess(createdTask);
    }



    /**
     * 更新任务
     *
     * @param request 任务更新请求
     * @return 更新后的任务配置
     */
    @ApiOperation("更新任务")
    @PostMapping("update-task")
    public Result<ArchiveTaskConfigDO> updateTask(@RequestBody @Valid ArchiveTaskUpdateRequest request) {
        log.info("更新任务: taskId={}, taskName={}", request.getTaskId(), request.getTaskName());

        // 校验执行类型和时间范围
        validateExecutionTypeAndTimeSpan(request.getExecutionType(), request.getTimeSpan());

        // 使用转换器转换为DO对象
        ArchiveTaskConfigDO taskConfig = archiveTaskConverter.toDO(request);

        ArchiveTaskConfigDO updatedTask = taskManagementService.updateTask(request.getTaskId(), taskConfig);
        return Result.ofSuccess(updatedTask);
    }

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    @ApiOperation("删除任务")
    @PostMapping("delete-task")
    public Result<Integer> deleteTask(@RequestParam("taskId") @NotNull Long taskId) {
        log.info("删除任务: taskId={}", taskId);
        int deletedCount = taskManagementService.deleteTask(taskId);
        return Result.ofSuccess(deletedCount);
    }

    /**
     * 更新任务状态
     *
     * @param request 更新任务状态请求
     * @return 更新后的任务配置
     */
    @ApiOperation("更新任务状态")
    @PostMapping("update-task-status")
    public Result<ArchiveTaskConfigDO> updateTaskStatus(@RequestBody @Valid UpdateTaskStatusRequest request) {
        log.info("更新任务状态: taskId={}, status={}", request.getTaskId(), request.getStatus());
        ArchiveTaskConfigDO updatedTask = taskManagementService.updateTaskStatus(request.getTaskId(), request.getStatus());
        return Result.ofSuccess(updatedTask);
    }

    /**
     * 创建节点配置
     * 仅支持单个创建，保留所有的校验逻辑
     *
     * @param archiveNodeConfigDDTO 节点配置DTO
     * @return 创建后的节点配置DTO
     */
    @ApiOperation("创建节点配置")
    @PostMapping("create-node")
    public Result<ArchiveNodeConfigDTO> createNodeConfig(@RequestBody @Valid ArchiveNodeConfigDTO archiveNodeConfigDDTO) {
        log.info("创建节点配置: archiveNodeConfigDDTO={}", JSON.toJSONString(archiveNodeConfigDDTO));
        try {
            // 转换为DO对象
            ArchiveNodeConfigDO archiveNodeConfigDO = archiveNodeConfigConverter.toDO(archiveNodeConfigDDTO);
            
            // 校验NodePropsConfig
            if (archiveNodeConfigDDTO.getNodeProps() != null) {
                NodePropsConfigValidator.validate(archiveNodeConfigDDTO.getNodeProps());
            }
            
            // 获取应用名称
            String appName = null;
            if (archiveNodeConfigDO.getTaskId() != null) {
                ArchiveTaskConfigDO taskConfig = archiveConfigService.getTaskConfig(archiveNodeConfigDO.getTaskId());
                if (taskConfig != null) {
                    appName = taskConfig.getAppName();
                }
            }

            // 验证节点conditions
            if (appName != null) {
                taskManagementService.validateNodeConditions(archiveNodeConfigDO, appName);
            }

            // 验证relations字段并解析到props中
            archiveNodeConfigConverter.validateAndParseRelations(archiveNodeConfigDO);

            // 参考 ArchiveController#addTable 的校验逻辑，增加子节点验证
            if (archiveNodeConfigDO.getTaskId() != null) {
                taskManagementService.validateChildNodeConfig(archiveNodeConfigDO, archiveNodeConfigDO.getTaskId());
            }
            
            // 创建节点配置
            ArchiveNodeConfigDO createdNode = taskManagementService.createNodeConfig(archiveNodeConfigDO);
            
            // 转换为DTO返回
            ArchiveNodeConfigDTO createdDTO = archiveNodeConfigConverter.toDisplayDTO(createdNode);
            return Result.ofSuccess(createdDTO);
        } catch (Exception e) {
            log.error("创建节点配置失败", e);
            return Result.ofError(500, "创建节点配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新节点配置
     * 更新当前节点信息，更新后需要进行数据验证
     *
     * @param archiveNodeConfigDDTO 节点配置DTO（包含节点ID）
     * @return 更新后的节点配置DTO
     */
    @ApiOperation("更新节点配置")
    @PostMapping("update-node")
    public Result<ArchiveNodeConfigDTO> updateNodeConfig(@RequestBody @Valid ArchiveNodeConfigDTO archiveNodeConfigDDTO) {
        log.info("更新节点配置: archiveNodeConfigDDTO={}", JSON.toJSONString(archiveNodeConfigDDTO));
        try {
            // 验证节点ID
            if (archiveNodeConfigDDTO.getNodeId() == null) {
                return Result.ofError(PARAM_ERROR.getCode(), "节点ID不能为空");
            }

            // 转换为DO对象
            ArchiveNodeConfigDO archiveNodeConfigDO = archiveNodeConfigConverter.toDO(archiveNodeConfigDDTO);

            // 校验NodePropsConfig
            if (archiveNodeConfigDDTO.getNodeProps() != null) {
                NodePropsConfigValidator.validate(archiveNodeConfigDDTO.getNodeProps());
            }
            
            // 校验打平字段
            validateFlattenedFields(archiveNodeConfigDDTO);

            // 验证relations字段并解析到props中
            archiveNodeConfigConverter.validateAndParseRelations(archiveNodeConfigDO);

            // 更新节点配置（包含验证逻辑）
            ArchiveNodeConfigDO updatedNode = taskManagementService.updateNodeConfig(archiveNodeConfigDO.getNodeId(), archiveNodeConfigDO);
            
            // 转换为DTO返回
            ArchiveNodeConfigDTO updatedDTO = archiveNodeConfigConverter.toDisplayDTO(updatedNode);
            return Result.ofSuccess(updatedDTO);
        } catch (Exception e) {
            log.error("更新节点配置失败: nodeId={}", archiveNodeConfigDDTO.getNodeId(), e);
            return Result.ofError(SERVER_ERROR.getCode(), "更新节点配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证打平字段
     * 
     * @param archiveNodeConfigDTO 节点配置DTO
     */
    private void validateFlattenedFields(ArchiveNodeConfigDTO archiveNodeConfigDTO) {
        // 验证索引字段
        if (archiveNodeConfigDTO.getIndexColumn() != null && archiveNodeConfigDTO.getIndexColumn().trim().isEmpty()) {
            throw new IllegalArgumentException("索引字段不能为空字符串");
        }
        
        // 验证索引字段类型
        if (archiveNodeConfigDTO.getIndexColumType() != null && archiveNodeConfigDTO.getIndexColumType().trim().isEmpty()) {
            throw new IllegalArgumentException("索引字段类型不能为空字符串");
        }
        
        // 验证保留天数
        if (archiveNodeConfigDTO.getReserveDays() != null && archiveNodeConfigDTO.getReserveDays() < 0) {
            throw new IllegalArgumentException("保留天数不能为负数");
        }
        
        // 验证索引值范围
        if (archiveNodeConfigDTO.getIndexStart() != null && archiveNodeConfigDTO.getIndexEnd() != null) {
            if (archiveNodeConfigDTO.getIndexStart() > archiveNodeConfigDTO.getIndexEnd()) {
                throw new IllegalArgumentException("索引开始值不能大于结束值");
            }
        }
        
        // 验证indexData对象
        if (archiveNodeConfigDTO.getIndexData() != null) {
            try {
                if (archiveNodeConfigDTO.getIndexData() instanceof com.fasterxml.jackson.databind.JsonNode) {
                    com.fasterxml.jackson.databind.JsonNode indexDataNode = (com.fasterxml.jackson.databind.JsonNode) archiveNodeConfigDTO.getIndexData();
                    if (indexDataNode.has("value") && indexDataNode.get("value").asText().trim().isEmpty()) {
                        throw new IllegalArgumentException("indexData中的value不能为空");
                    }
                }
            } catch (Exception e) {
                log.warn("验证indexData失败: error={}", e.getMessage());
            }
        }
    }

    /**
     * 校验执行类型和时间范围
     * 
     * @param executionType 执行类型：1-全天，2-指定时间
     * @param timeSpan 时间范围（HH:mm-HH:mm格式）
     */
    private void validateExecutionTypeAndTimeSpan(Integer executionType, String timeSpan) {
        // 校验执行类型
        if (executionType == null) {
            throw new IllegalArgumentException("执行类型不能为空");
        }
        
        if (executionType != 1 && executionType != 2) {
            throw new IllegalArgumentException("执行类型只能为1（全天）或2（指定时间）");
        }
        
        // 当执行类型为2（指定时间）时，校验时间范围
        if (executionType == 2) {
            if (timeSpan == null || timeSpan.trim().isEmpty()) {
                throw new IllegalArgumentException("执行类型为指定时间时，时间范围不能为空");
            }
        }
    }


}