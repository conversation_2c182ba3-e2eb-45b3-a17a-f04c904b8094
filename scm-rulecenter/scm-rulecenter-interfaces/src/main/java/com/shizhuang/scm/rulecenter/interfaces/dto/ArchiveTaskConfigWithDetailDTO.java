package com.shizhuang.scm.rulecenter.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.shizhuang.scm.rulecenter.infrastructure.config.LongToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 归档任务配置DTO（包含节点详情）
 * 用于页面展示，包含ArchiveNodeConfigDTO列表
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArchiveTaskConfigWithDetailDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键ID */
    private Long id;
    
    /** 应用配置ID */
    private Long appConfigId;
    
    /** 任务ID */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long taskId;
    
    /** 任务名称 */
    private String taskName;
    
    /** 数据源名称 */
    private String datasourceName;
    
    /** 数据库名称 */
    private String databaseName;
    
    /** 版本号 */
    private Long version;
    
    /** 任务状态 */
    private Integer status;
    
    /** 运行模式 */
    private Integer mode;
    
    /** 扫描间隔 */
    private Integer interval;
    
    /** 扫描数量 */
    private Integer limit;
    
    /** 执行类型 */
    private Integer executionType;
    
    /** 额外属性（JSON格式） */
    private String props;
    
    /** 时间范围字符串（格式：HH:mm-HH:mm） */
    private String timeSpan;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;
    
    /** 应用名称 */
    private String appName;
    
    /** 任务节点详情列表（使用DTO） */
    private List<ArchiveNodeConfigDTO> taskDetails;
} 