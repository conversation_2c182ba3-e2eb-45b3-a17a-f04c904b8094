package com.shizhuang.scm.rulecenter.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.shizhuang.scm.rulecenter.infrastructure.config.LongToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 归档节点配置DTO
 * 用于页面展示，包含NodePropsConfig对象
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArchiveNodeConfigDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键ID */
    private Long id;
    
    /** 任务ID */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long taskId;
    
    /** 节点ID */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long nodeId;
    
    /** 父节点ID */
    @JsonSerialize(using = LongToStringSerializer.class)
    private Long parentNodeId;
    
    /** 表名 */
    private String tableName;
    
    /** 查询条件 */
    private String condition;
    
    /** 节点级数据源名称 */
    private String datasourceName;
    
    /** 节点级数据库名称 */
    private String databaseName;
    
    /** 是否归档 */
    private Boolean isArchive;
    
    /** 查询字段列表（JSON格式） */
    private String queryColumns;
    
    /** 主键列 */
    private String primaryKeyColumn;
    
    /** 节点状态 */
    private Integer status;
    
    /** 是否为根节点 */
    private Boolean rootNode;
    
    /** 索引字段 */
    private String indexColumn;
    
    /** 排序字段 */
    private String orderBy;
    
    /** 归档类型 */
    private Integer archiveType;
    
    /** 默认值过滤 */
    private Integer filterDefaultValue;
    
    /** 是否启用分片 */
    private Boolean shardingEnabled;
    
    /** 分片表达式 */
    private String shardingExpression;
    
    /** 分片插件全类名 */
    private String shardingPluginClass;
    
    /** 分片字段名称 */
    private String shardingField;

    /**
     * 分表前缀 例如 t_order_
     */
    private String shardingPrefix;
    
    /** 插件列表（JSON格式） */
    private String plugins;
    
    /** 关联关系（JSON格式） */
    private String relations;
    
    /** 节点属性配置对象 */
    private NodePropsConfig nodeProps;
    
    // ==================== 从nodeProps打平的字段 ====================
    
    /** 索引数据（包含索引字段信息） */
    private Object indexData;
    
    /** 索引字段类型 */
    private String indexColumType;
    
    /** 索引扫描 */
    private Boolean enableScan;
    
    /** 索引值范围开始 */
    private Long indexStart;
    
    /** 索引值范围结束 */
    private Long indexEnd;
    
    /** 保留天数 */
    private Integer reserveDays;
    
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;
    
    /** 子节点列表 */
    private List<ArchiveNodeConfigDTO> children;
} 