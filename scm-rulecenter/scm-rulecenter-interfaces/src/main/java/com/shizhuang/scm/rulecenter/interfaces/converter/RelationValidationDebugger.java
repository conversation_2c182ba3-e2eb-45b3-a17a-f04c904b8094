package com.shizhuang.scm.rulecenter.interfaces.converter;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.integration.rpc.ArchiveSDKIntegrationApi;
import com.shizhuang.duapp.scp.framework.admin.sdk.request.FetchColumnsRequest;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.ColumnsResponse;
import com.poizon.fusion.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 关系条件验证调试工具
 * 用于诊断关系条件验证过程中的问题
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
@Component
public class RelationValidationDebugger {

    @Autowired
    private ArchiveSDKIntegrationApi archiveSDKIntegrationApi;

    /**
     * 诊断关系条件验证问题
     * 
     * @param relationConditions 关系条件字符串
     * @param nodeConfig 节点配置
     * @return 诊断报告
     */
    public String diagnoseRelationValidation(String relationConditions, ArchiveNodeConfigDO nodeConfig) {
        StringBuilder report = new StringBuilder();
        report.append("=== 关系条件验证诊断报告 ===\n");
        
        // 1. 基本信息
        report.append("关系条件: ").append(relationConditions).append("\n");
        report.append("节点ID: ").append(nodeConfig.getNodeId()).append("\n");
        report.append("表名: ").append(nodeConfig.getTableName()).append("\n");
        
        // 2. 数据源信息
        report.append("\n--- 数据源信息 ---\n");
        String datasourceName = nodeConfig.getDatasourceName();
        String databaseName = nodeConfig.getDatabaseName();
        report.append("数据源名称: ").append(datasourceName).append("\n");
        report.append("数据库名称: ").append(databaseName).append("\n");
        
        if (datasourceName == null || datasourceName.trim().isEmpty()) {
            report.append("❌ 数据源名称为空\n");
        } else {
            report.append("✅ 数据源名称正常\n");
        }
        
        if (databaseName == null || databaseName.trim().isEmpty()) {
            report.append("❌ 数据库名称为空\n");
        } else {
            report.append("✅ 数据库名称正常\n");
        }
        
        // 3. 解析关系条件
        report.append("\n--- 关系条件解析 ---\n");
        try {
            String[] conditionParts = relationConditions.split("(?i)\\s+and\\s+");
            report.append("条件数量: ").append(conditionParts.length).append("\n");
            
            for (int i = 0; i < conditionParts.length; i++) {
                String condition = conditionParts[i].trim();
                report.append("条件").append(i + 1).append(": ").append(condition).append("\n");
                
                if (!condition.contains("=")) {
                    report.append("❌ 缺少等号\n");
                    continue;
                }
                
                String[] parts = condition.split("=");
                if (parts.length != 2) {
                    report.append("❌ 等号数量不正确\n");
                    continue;
                }
                
                String left = parts[0].trim().replace("`", "");
                String right = parts[1].trim().replace("`", "");
                
                String[] leftArr = left.split("\\.");
                String[] rightArr = right.split("\\.");
                
                if (leftArr.length != 2 || rightArr.length != 2) {
                    report.append("❌ 格式不正确，应为 table.column\n");
                    continue;
                }
                
                report.append("  左侧: 表=").append(leftArr[0]).append(", 字段=").append(leftArr[1]).append("\n");
                report.append("  右侧: 表=").append(rightArr[0]).append(", 字段=").append(rightArr[1]).append("\n");
                
                // 验证表名和字段名格式
                if (!leftArr[0].matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
                    report.append("❌ 左侧表名格式错误: ").append(leftArr[0]).append("\n");
                } else {
                    report.append("✅ 左侧表名格式正确\n");
                }
                
                if (!leftArr[1].matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
                    report.append("❌ 左侧字段名格式错误: ").append(leftArr[1]).append("\n");
                } else {
                    report.append("✅ 左侧字段名格式正确\n");
                }
                
                if (!rightArr[0].matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
                    report.append("❌ 右侧表名格式错误: ").append(rightArr[0]).append("\n");
                } else {
                    report.append("✅ 右侧表名格式正确\n");
                }
                
                if (!rightArr[1].matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
                    report.append("❌ 右侧字段名格式错误: ").append(rightArr[1]).append("\n");
                } else {
                    report.append("✅ 右侧字段名格式正确\n");
                }
            }
        } catch (Exception e) {
            report.append("❌ 解析关系条件失败: ").append(e.getMessage()).append("\n");
        }
        
        // 4. 表结构验证
        if (datasourceName != null && !datasourceName.trim().isEmpty() && 
            databaseName != null && !databaseName.trim().isEmpty()) {
            report.append("\n--- 表结构验证 ---\n");
            
            // 提取所有表名
            String[] conditionParts = relationConditions.split("(?i)\\s+and\\s+");
            for (String condition : conditionParts) {
                String[] parts = condition.trim().split("=");
                if (parts.length == 2) {
                    String left = parts[0].trim().replace("`", "");
                    String right = parts[1].trim().replace("`", "");
                    
                    String[] leftArr = left.split("\\.");
                    String[] rightArr = right.split("\\.");
                    
                    if (leftArr.length == 2) {
                        validateTableStructure(report, leftArr[0], datasourceName, databaseName, "左侧");
                    }
                    
                    if (rightArr.length == 2) {
                        validateTableStructure(report, rightArr[0], datasourceName, databaseName, "右侧");
                    }
                }
            }
        }
        
        // 5. 建议
        report.append("\n--- 建议 ---\n");
        if (datasourceName == null || datasourceName.trim().isEmpty()) {
            report.append("1. 检查节点配置中的 datasourceName 字段\n");
        }
        
        if (databaseName == null || databaseName.trim().isEmpty()) {
            report.append("2. 检查节点配置中的 databaseName 字段\n");
        }
        
        report.append("3. 确认表名和字段名的大小写是否正确\n");
        report.append("4. 检查数据源连接是否正常\n");
        report.append("5. 确认是否有访问表的权限\n");
        
        return report.toString();
    }
    
    /**
     * 验证表结构
     */
    private void validateTableStructure(StringBuilder report, String tableName, String datasourceName, String databaseName, String side) {
        try {
            report.append(side).append("表[").append(tableName).append("]: ");
            
            FetchColumnsRequest request = FetchColumnsRequest.builder()
                    .dataSourceName(datasourceName)
                    .databaseName(databaseName)
                    .tableName(tableName)
                    .build();
            
            Result<ColumnsResponse> result = archiveSDKIntegrationApi.fetchColumns(request, "default");
            
            if (result == null) {
                report.append("❌ SDK API返回null\n");
                return;
            }
            
            if (result.getData() == null) {
                report.append("❌ 表不存在或无法访问\n");
                return;
            }
            
            if (result.getData().getColumns() == null) {
                report.append("❌ 表结构信息为空\n");
                return;
            }
            
            List<String> columns = result.getData().getColumns().stream()
                    .map(col -> col.getName())
                    .filter(name -> name != null)
                    .collect(java.util.stream.Collectors.toList());
            
            report.append("✅ 表存在，字段数量: ").append(columns.size()).append("\n");
            report.append("  字段列表: ").append(columns).append("\n");
            
        } catch (Exception e) {
            report.append("❌ 验证失败: ").append(e.getMessage()).append("\n");
        }
    }
}
