package com.shizhuang.scm.rulecenter.interfaces.converter;

import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.TaskPropsConfig;
import com.shizhuang.scm.rulecenter.infrastructure.util.TaskPropsConfigUtil;
import com.shizhuang.scm.rulecenter.interfaces.request.ArchiveTaskCreateRequest;
import com.shizhuang.scm.rulecenter.interfaces.request.ArchiveTaskUpdateRequest;
import com.shizhuang.scm.rulecenter.interfaces.dto.ArchiveTaskConfigWithDetailDTO;
import com.shizhuang.scm.rulecenter.interfaces.dto.ArchiveNodeConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import java.util.Date;

/**
 * 归档任务配置转换器
 * 
 * 负责在请求DTO和DO对象之间进行转换
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class ArchiveTaskConverter {

    /**
     * 将创建请求转换为DO对象
     * 
     * @param request 创建请求
     * @return DO对象
     */
    public ArchiveTaskConfigDO toDO(ArchiveTaskCreateRequest request) {
        if (request == null) {
            return null;
        }

        ArchiveTaskConfigDO taskConfig = new ArchiveTaskConfigDO();
        
        // 基础信息
        taskConfig.setTaskName(request.getTaskName());
        taskConfig.setDatasourceName(request.getDatasourceName());
        taskConfig.setDatabaseName(request.getDatabaseName());
        taskConfig.setAppName(request.getAppName());
        
        // 配置参数
        taskConfig.setLimit(request.getLimit());
        taskConfig.setInterval(request.getInterval());
        taskConfig.setStatus(request.getStatus());
        taskConfig.setMode(request.getMode());
        taskConfig.setExecutionType(request.getExecutionType());
        
        // 时间配置 - 将HH:mm-HH:mm格式转换为TaskPropsConfig并序列化为props
        if (request.getTimeSpan() != null) {
            TaskPropsConfig timeConfig = TaskPropsConfigUtil.parseTimeSpan(request.getTimeSpan());
            if (timeConfig != null) {
                taskConfig.setProps(TaskPropsConfigUtil.serialize(timeConfig));
            }
        }
        // 时间戳
        Date now = new Date();
        taskConfig.setCreateTime(now);
        taskConfig.setUpdateTime(now);
        return taskConfig;
    }



    /**
     * 将更新请求转换为DO对象
     * 
     * @param request 更新请求
     * @return DO对象
     */
    public ArchiveTaskConfigDO toDO(ArchiveTaskUpdateRequest request) {
        if (request == null) {
            return null;
        }

        ArchiveTaskConfigDO taskConfig = new ArchiveTaskConfigDO();
        
        // 基础信息
        taskConfig.setTaskName(request.getTaskName());
        taskConfig.setDatasourceName(request.getDatasourceName());
        taskConfig.setDatabaseName(request.getDatabaseName());
        
        // 配置参数
        taskConfig.setLimit(request.getLimit());
        taskConfig.setInterval(request.getInterval());
        taskConfig.setStatus(request.getStatus());
        taskConfig.setMode(request.getMode());
        taskConfig.setExecutionType(request.getExecutionType());

        // 时间配置 - 兼容老的startTime/endTime逻辑
        if (request.getTimeSpan() != null) {
            TaskPropsConfig timeConfig = TaskPropsConfigUtil.parseTimeSpan(request.getTimeSpan());
            if (timeConfig != null) {
                taskConfig.setProps(TaskPropsConfigUtil.serialize(timeConfig));
            }
        }
        
        // 扩展配置
        // 已移除所有 setConfigData/getConfigData 相关代码，确保与最新 DO 字段一致
        
        // 更新时间戳
        taskConfig.setUpdateTime(new Date());
        
        return taskConfig;
    }

    @Autowired
    private ArchiveNodeConfigConverter archiveNodeConfigConverter;
    
    /**
     * 将任务配置和节点配置转换为DetailDTO
     * 
     * @param taskConfig 任务配置
     * @param nodeConfigs 节点配置列表
     * @return 包含NodePropsConfig的DetailDTO
     */
    public ArchiveTaskConfigWithDetailDTO toDetailDTO(ArchiveTaskConfigDO taskConfig, List<ArchiveNodeConfigDO> nodeConfigs) {
        if (taskConfig == null) {
            return null;
        }
        
        ArchiveTaskConfigWithDetailDTO detailDTO = new ArchiveTaskConfigWithDetailDTO();
        
        // 复制任务配置的基本属性
        detailDTO.setId(taskConfig.getId());
        detailDTO.setAppConfigId(taskConfig.getAppConfigId());
        detailDTO.setTaskId(taskConfig.getTaskId());
        detailDTO.setTaskName(taskConfig.getTaskName());
        detailDTO.setDatasourceName(taskConfig.getDatasourceName());
        detailDTO.setDatabaseName(taskConfig.getDatabaseName());
        detailDTO.setVersion(taskConfig.getVersion());
        detailDTO.setStatus(taskConfig.getStatus());
        detailDTO.setMode(taskConfig.getMode());
        detailDTO.setInterval(taskConfig.getInterval());
        detailDTO.setLimit(taskConfig.getLimit());
        detailDTO.setExecutionType(taskConfig.getExecutionType());
        detailDTO.setProps(taskConfig.getProps());
        
        // 从 props 中提取 timespan
        if (taskConfig.getProps() != null && !taskConfig.getProps().trim().isEmpty()) {
            try {
                TaskPropsConfig timeConfig = TaskPropsConfigUtil.deserialize(taskConfig.getProps());
                if (timeConfig != null && timeConfig.getTimespan() != null) {
                    detailDTO.setTimeSpan(timeConfig.getTimespan());
                }
            } catch (Exception e) {
                log.warn("从 props 中提取 timespan 失败: props={}, error={}", taskConfig.getProps(), e.getMessage());
            }
        }
        
        detailDTO.setCreateTime(taskConfig.getCreateTime());
        detailDTO.setUpdateTime(taskConfig.getUpdateTime());
        detailDTO.setAppName(taskConfig.getAppName());
        
        // 转换节点配置为DTO列表
        if (!CollectionUtils.isEmpty(nodeConfigs)) {
            List<ArchiveNodeConfigDTO> nodeDTOs = archiveNodeConfigConverter.toDisplayDTOList(nodeConfigs);
            detailDTO.setTaskDetails(nodeDTOs);
        }
        
        return detailDTO;
    }

}