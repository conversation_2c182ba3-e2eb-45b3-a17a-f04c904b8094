<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.shizhuang.scm</groupId>
        <artifactId>scm-rulecenter-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scm-rulecenter-interfaces</artifactId>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>scm-rulecenter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>scm-rulecenter-application</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>scm-rulecenter-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shizhuang.duapp</groupId>
            <artifactId>scp-framework-bigdata-log</artifactId>
        </dependency>
        <!--fusion各功能模块示例代码-->
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-example-lock</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-example-dynamic-redis</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-example-mq</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--fusion启动引导模块-->
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-warning</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-webmvc-swagger</artifactId>
        </dependency>
        <!--ark配置中心-->
        <dependency>
            <groupId>com.shizhuang</groupId>
            <artifactId>ark-config-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>avatar-HA-common</artifactId>
                    <groupId>com.shizhuang.avatar</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--应用名注册，用于监控采集、网关转发-->
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-registry-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-registry-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-ha-common</artifactId>
        </dependency>
        <!--用于feign调用或feign迁移到dubbo-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shizhuang.duapp</groupId>
            <artifactId>scp-framework-i18n</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>scm-rulecenter</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.13.RELEASE</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
