package com.shizhuang.scm.rulecenter.application.service.component;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.duapp.mdm.api.dubbo.CategoryService;
import com.shizhuang.duapp.mdm.api.response.ScpCategoryResponse;
import com.shizhuang.scm.rulecenter.api.service.component.RcCategoryApi;
import com.shizhuang.scm.rulecenter.api.service.component.RcCategoryCascaderResponse;
import com.shizhuang.scm.rulecenter.api.service.component.RcCategoryResponse;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService(accesslog = "true", version = "1.0.0")
public class RcCategoryApiImpl implements RcCategoryApi {

    @DubboReference(timeout = 1500, check = false)
    CategoryService categoryService;

    LoadingCache<String, List<RcCategoryResponse>> listChildrenCategoryCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).maximumSize(10000)
            .build(new CacheLoader<String, List<RcCategoryResponse>>() {
                @Override
                public List<RcCategoryResponse> load(@NonNull String key) {
                    String[] split = key.split("_");
                    String tenantId = split[0];
                    String categoryId = split[1];
                    List<ScpCategoryResponse> data = categoryCache.getUnchecked(tenantId);
                    if(CollectionUtils.isEmpty(data)){
                        return new ArrayList<>();
                    }
                    Map<String, List<ScpCategoryResponse>> collect = data.stream().collect(Collectors.groupingBy(ScpCategoryResponse::getPid));
                    List<ScpCategoryResponse> scpCategoryResponses = collect.get(categoryId);
                    if(CollectionUtils.isEmpty(scpCategoryResponses)){
                        return new ArrayList<>();
                    }
                    return scpCategoryResponses.stream()
                            .filter(res->Objects.equals(res.getStatus(), (byte)1))
                            .map(res -> {
                                RcCategoryResponse rcCategoryResponse = new RcCategoryResponse();
                                rcCategoryResponse.setTenantId(tenantId);
                                rcCategoryResponse.setPid(categoryId);
                                rcCategoryResponse.setCategoryId(res.getCategoryId());
                                rcCategoryResponse.setCategoryName(res.getCategoryName());
                                rcCategoryResponse.setLevel(res.getLevel());
                                return rcCategoryResponse;
                            }).collect(Collectors.toList());
                }
            });


    LoadingCache<String, List<ScpCategoryResponse>> categoryCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).maximumSize(10000)
            .build(new CacheLoader<String, List<ScpCategoryResponse>>() {
                @Override
                public List<ScpCategoryResponse> load(@NonNull String tenantId) {
                    return categoryService.queryAllCategoryV2(tenantId).getData();
                }
            });

    @Override
    public Result<List<RcCategoryResponse>> listChildrenCategory(String tenantId, String categoryId) {
        if (StringUtils.isEmpty(categoryId)) {
            return Result.ofSuccess(new ArrayList<>());
        }
        List<RcCategoryResponse> responses = listChildrenCategoryCache.getUnchecked(tenantId+"_"+categoryId);
        return Result.ofSuccess(responses);
    }


    @Override
    public Result<List<RcCategoryCascaderResponse>> listCategoryCascader(String tenantId) {
        Result<List<ScpCategoryResponse>> listResult = categoryService.queryAllCategoryV2(tenantId);
        List<ScpCategoryResponse> data = listResult.getData().stream().filter(e -> Objects.equals((byte) 1, e.getStatus())).collect(Collectors.toList());
        Map<String, List<ScpCategoryResponse>> childrenMap = data.stream().collect(Collectors.groupingBy(ScpCategoryResponse::getPid));
        String pid = "0";
        return Result.ofSuccess(getListResult(data, childrenMap, pid));
    }

    private static List<RcCategoryCascaderResponse> getListResult(List<ScpCategoryResponse> data, Map<String, List<ScpCategoryResponse>> childrenMap, String pid) {
        List<RcCategoryCascaderResponse> cascaderResponses = new ArrayList<>();
        for (ScpCategoryResponse scpCategoryResponse : data) {
            if (Objects.equals(scpCategoryResponse.getPid(), pid)) {
                RcCategoryCascaderResponse rcCategoryCascaderResponse = new RcCategoryCascaderResponse();
                rcCategoryCascaderResponse.setTenantId(scpCategoryResponse.getTenantId());
                rcCategoryCascaderResponse.setPid(scpCategoryResponse.getPid());
                rcCategoryCascaderResponse.setValue(scpCategoryResponse.getCategoryId());
                rcCategoryCascaderResponse.setLabel(scpCategoryResponse.getCategoryName());
                rcCategoryCascaderResponse.setLevel(scpCategoryResponse.getLevel());
                if (childrenMap.containsKey(scpCategoryResponse.getCategoryId())) {
                    rcCategoryCascaderResponse.setChildren(getListResult(data, childrenMap, scpCategoryResponse.getCategoryId()));
                }
                cascaderResponses.add(rcCategoryCascaderResponse);
            }
        }
        return cascaderResponses;
    }
}
