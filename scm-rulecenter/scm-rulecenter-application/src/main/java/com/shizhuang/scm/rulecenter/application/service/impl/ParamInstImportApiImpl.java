package com.shizhuang.scm.rulecenter.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.response.ImportExcelResponse;
import com.shizhuang.avatar.common.model.Result;
import com.shizhuang.scm.rulecenter.api.cmd.ParamInstAddCmd;
import com.shizhuang.scm.rulecenter.api.service.ParamInstImportApi;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstImportContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstImportPersistentHandler;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstImportPreProcessHandler;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstImportValidateChain;
import com.shizhuang.scm.rulecenter.domains.support.chain.HandlerChainDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@DubboService(accesslog = "true", version = "1.0.0")
public class ParamInstImportApiImpl implements ParamInstImportApi {

    /**
     * 1、通过excelHeader 将原始excel数据转换成elementCode
     * 2、再通过fieldMapping将elementCode转换成adsFieldCode
     * 3、再通过insert语句将数据存储到mysql中
     *
     * @param rowJson
     * @return
     */
    @Override
    public Result<List<ImportExcelResponse>> importParamInst(String rowJson) {

        ParamInstImportContext paramInstImportContext = ParamInstImportContext.builder().rowJson(rowJson).build();
        log.info("importParamInst rowJson:{}", rowJson);

        HandlerChainDefinition<ParamInstImportContext> importParamInstChain = HandlerChainDefinition
                .name(ParamInstAddCmd.class.getSimpleName());

        importParamInstChain.add(ParamInstImportPreProcessHandler.class)
                .add(ParamInstImportValidateChain.class)
                .add(ParamInstImportPersistentHandler.class)
                .orchestrate()
                .handle(paramInstImportContext);

        log.info("ParamInstImportApiImpl.importParamInst res:{}", JSON.toJSONString(paramInstImportContext.getImportExcelResponseList()));
        return Result.success(paramInstImportContext.getImportExcelResponseList());
    }


}
