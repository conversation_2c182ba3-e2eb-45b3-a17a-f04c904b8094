package com.shizhuang.scm.rulecenter.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.poizon.fusion.common.exception.biz.BizRuntimeException;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.dto.DimensionKeyDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstDimQuery;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.query.RedisKeyHelper;
import com.shizhuang.scm.rulecenter.api.query.SchemaDetailQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.InstDimResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;
import com.shizhuang.scm.rulecenter.api.response.SchemaDetailResponse;
import com.shizhuang.scm.rulecenter.api.service.MetaApplicationQueryService;
import com.shizhuang.scm.rulecenter.api.service.ParamInstQueryApi;
import com.shizhuang.scm.rulecenter.domains.inst.assembler.ParamInstAssembler;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstTempDimRes;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimensionKeyVO;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.infrastructure.cache.RedisCacheService;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import com.shizhuang.scm.rulecenter.sdk.util.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 参数实例查询 dubbo Api实现
 */
@Slf4j
@Service
@DubboService(accesslog = "true", version = "1.0.0")
public class ParamInstQueryApiImpl implements ParamInstQueryApi {

    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    SchemaRepository schemaRepository;

    @Resource
    MetaApplicationQueryService metaApplicationQueryService;

    @Resource
    ParamInstAssembler paramInstAssembler;

    @Resource
    RedisCacheService redisCacheService;

    @Resource
    ParamInstDomainService paramInstDomainService;

    /**
     * 根据schemaCode查询
     *
     * @param schemaDetailQuery
     * @return
     */
    @Override
    public Result<SchemaDetailResponse> querySchemaDetail(SchemaDetailQuery schemaDetailQuery) {
        return metaApplicationQueryService.querySchemaDetail(schemaDetailQuery);
    }

    /**
     * 指定维度查询参数实例
     * <p>
     *
     * @param instDimQuery
     * @return
     */
    @Override
    public Result<InstDimResponse> queryByDimension(InstDimQuery instDimQuery) {
        log.info("ParamInstQueryApiImpl.queryByDimension instDimQuery:{}", JSON.toJSONString(instDimQuery));
        String schemaCode = instDimQuery.getSchemaCode();
        List<ElementInstDTO> queryElements = instDimQuery.getQueryDimension();
        String redisCustomDimKey = getRedisCustomDimKey(schemaCode, queryElements);
        DimensionKeyDTO dimKeyUnion = (DimensionKeyDTO) redisCacheService.getUnCheck(redisCustomDimKey);
        InstDimResponse instDimResponse = null;
        if (Objects.isNull(dimKeyUnion)
                || Objects.isNull(dimKeyUnion.getSchemaCode())
                || Objects.isNull(dimKeyUnion.getDimension())
                || Objects.isNull(dimKeyUnion.getDimensionKey())) {
            instDimResponse = queryByInstWithNoCache(redisCustomDimKey, instDimQuery);
        } else {
            String dimension = dimKeyUnion.getDimension();
            String dimensionKey = dimKeyUnion.getDimensionKey();
            String redisDimKey = RedisKeyHelper.getRedisDimKey(schemaCode, dimension, dimensionKey);
            instDimResponse = Optional.ofNullable((InstDimResponse) redisCacheService.getUnCheck(redisDimKey))
                    .orElseGet(() -> queryByDimensionKeyWithNoCache(schemaCode, dimension, dimensionKey));
        }
        log.info("ParamInstQueryApiImpl.queryByDimension instDimResponse:{}", JSON.toJSONString(instDimResponse));
        return Result.ofSuccess(instDimResponse);
    }


    @Override
    public Result<Map<InstDimQuery, InstDimResponse>> batchQueryByDimension(List<InstDimQuery> instDimQueries) {
        Map<InstDimQuery, InstDimResponse> resultMap = instDimQueries.parallelStream().collect(Collectors.toMap(q -> q, q -> {
            String redisCustomDimKey = getRedisCustomDimKey(q.getSchemaCode(), q.getQueryDimension());
            return Optional.ofNullable(queryByInstWithNoCache(redisCustomDimKey, q)).orElseGet(InstDimResponse::new);
        }, (o, n) -> n));
        return Result.ofSuccess(resultMap);
    }


    private InstDimResponse queryByDimensionKeyWithNoCache(String schemaCode, String dimension, String dimensionKey) {
        SchemaMetaAggregate schemaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needDeployed());
        if (schemaAggregate == null) {
            throw new BizRuntimeException(ErrorCode.SCHEMA_CODE_IS_NULL.getCode(), ErrorCode.SCHEMA_CODE_IS_NULL.getErrMsg());
        }
        ParamInstanceDOWithBLOBs paramInstanceDO = paramInstRepository.getEnabledByDim(schemaCode, dimension, dimensionKey);
        List<SchemaElementEntity> allElements = schemaAggregate.getAllElements();
        Map<String, String> elementFieldMap = schemaAggregate.getElementFieldMap();
        InstDimResponse response = paramInstAssembler.toResponse(paramInstanceDO, allElements, elementFieldMap);
        String redisDimKey = RedisKeyHelper.getRedisDimKey(schemaCode, dimension, dimensionKey);
        Long cacheExpireTime = schemaAggregate.getSchemaEntity().calCacheExpireTime();
        redisCacheService.putToValue(redisDimKey, response, cacheExpireTime, TimeUnit.SECONDS);
        return response;
    }


    public InstDimResponse queryByInstWithNoCache(String redisCustomDimKey, InstDimQuery instDimQuery) {
        String schemaCode = instDimQuery.getSchemaCode();
        List<ElementInstDTO> queryElements = instDimQuery.getQueryDimension();
        Map<String, ElementInstDTO> queryElementMap = queryElements.stream().collect(Collectors.toMap(ElementInstDTO::getElementCode, e -> e, (o, n) -> n));
        SchemaMetaAggregate schemaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needDeployed());
        Long cacheExpireTime = schemaAggregate.getSchemaEntity().calCacheExpireTime();
        Integer dimPriType = schemaAggregate.getSchemaEntity().getDimPriTypeVO().getDimPriType();
        //1、提取入参中的维度
        List<String> queryDimensionCode = extractDimension(schemaAggregate, queryElements);

        //2、提取方案中下各个维度的优先级，若没有优先级则做全匹配
        List<DimPriorityVO> orderedDimPriorityVOS = new ArrayList<>();
        List<DimPriorityVO> dimPriorityVOS = schemaRepository.querySchemaDimPriority(schemaCode);
        if (CollectionUtils.isNotEmpty(dimPriorityVOS)) {
            //3、优先级由高到底，寻找出可能命中的dimensionKey
            orderedDimPriorityVOS = dimPriorityVOS.stream().sorted((Comparator.comparing(DimPriorityVO::getPriority))).collect(Collectors.toList());
        }

        List<DimensionKeyVO> hitDimensionKeys = ParamInstDomainService.getDimensionKeyVOs(orderedDimPriorityVOS, queryDimensionCode, queryElementMap);
        ParamInstTempDimRes result = paramInstDomainService.getParamInstDimRes(redisCustomDimKey, hitDimensionKeys, schemaCode, cacheExpireTime, dimPriType);

        //若未查到空结果则也缓存空对象，避免网络调用+数据库io
        if (Objects.isNull(result.paramInstanceDO)) {
            //假设内网应用不会恶意攻击业务参数中心
            redisCacheService.putToValue(redisCustomDimKey, new DimensionKeyDTO(), cacheExpireTime, TimeUnit.SECONDS);
            return null;
        }

        List<SchemaElementEntity> allElements = schemaAggregate.getAllElements();
        Map<String, String> elementFieldMap = schemaAggregate.getElementFieldMap();
        InstDimResponse instDimResponse = paramInstAssembler.toResponse(result.paramInstanceDO, allElements, elementFieldMap);
        redisCacheService.putToValue(result.redisDimKey, instDimResponse, cacheExpireTime, TimeUnit.SECONDS);
        return instDimResponse;
    }


    private static List<String> extractDimension(SchemaMetaAggregate schemaAggregate, List<ElementInstDTO> queryElements) {
        List<SchemaElementEntity> dimensions = schemaAggregate.getDimensions();
        Map<String, ElementInstDTO> queryElementsMap = queryElements.stream().collect(Collectors.toMap(ElementInstDTO::getElementCode, e -> e, (o, n) -> n));
        List<String> queryDimension = new ArrayList<>();
        dimensions.forEach(e->{
            if(queryElementsMap.containsKey(e.getElementCode())){
                queryDimension.add(e.getElementCode());
            }
        });
        return queryDimension;
    }


    private static String getRedisCustomDimKey(String schemaCode, List<ElementInstDTO> queryElements) {
        String elementCodes = queryElements.stream().map(ElementInstDTO::getElementCode).collect(Collectors.joining("#"));
        String elementValues = queryElements.stream().map(ElementInstDTO::getElementValue).collect(Collectors.joining("#"));
        return RedisKeyHelper.getCustomDimKey(schemaCode, elementCodes, elementValues);
    }

    @Override
    public Result<PageInfoResponse<InstDimPageResponse>> queryParamInstPage(InstPageQuery instPageQuery) {
        addPreprocessingParam(instPageQuery);
        PageInfo<InstDimPageResponse> instDimResponsePageInfo = paramInstRepository.queryParamInstPage(instPageQuery);

        PageInfoResponse<InstDimPageResponse> pageInfoResponse = new PageInfoResponse<>();
        BeanUtils.copyProperties(instDimResponsePageInfo, pageInfoResponse);
        log.info("ParamInstQueryApi.queryParamInstPage res:{} ", JSON.toJSONString(pageInfoResponse));
        return Result.ofSuccess(pageInfoResponse);
    }

    private static void addPreprocessingParam(InstPageQuery instPageQuery) {
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        ElementInstDTO elementInstDTO = new ElementInstDTO();
        elementInstDTO.setElementCode("enabled");
        elementInstDTO.setElementValue("1");
        if (CollectionUtils.isNotEmpty(queryParams)) {
            queryParams.add(elementInstDTO);
        } else {
            queryParams = Lists.newArrayList(elementInstDTO);
            instPageQuery.setQueryParams(queryParams);
        }
    }
}
