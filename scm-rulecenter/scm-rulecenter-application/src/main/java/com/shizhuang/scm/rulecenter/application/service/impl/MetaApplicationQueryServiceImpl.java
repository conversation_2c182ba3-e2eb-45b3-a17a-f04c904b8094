package com.shizhuang.scm.rulecenter.application.service.impl;

import com.github.pagehelper.PageInfo;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.constant.ElementSourceTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.ElementTypeEnum;
import com.shizhuang.scm.rulecenter.api.constant.SchemaStatusEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.*;
import com.shizhuang.scm.rulecenter.api.query.*;
import com.shizhuang.scm.rulecenter.api.response.ElementDetailResponse;
import com.shizhuang.scm.rulecenter.api.response.EntireSchemaResponse;
import com.shizhuang.scm.rulecenter.api.response.MetaConstantResponse;
import com.shizhuang.scm.rulecenter.api.response.SchemaDetailResponse;
import com.shizhuang.scm.rulecenter.api.service.MetaApplicationQueryService;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.ElementMetaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaEntity;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.DimPriorityVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.ElementEnumVO;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.FieldMappingVO;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.DimPriorityAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.ElementMetaAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.SchemaElementAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 元数据查询服务（读逻辑）
 */
@Service
public class MetaApplicationQueryServiceImpl implements MetaApplicationQueryService {

    @Resource
    SchemaRepository schemaRepository;

    @Resource
    ElementRepository elementRepository;

    @Override
    public Result<PageInfo<ElementMetaDTO>> queryElementPage(ElementPageQuery elementPageQuery) {
        return Result.ofSuccess(elementRepository.queryByPage(elementPageQuery));
    }

    @Override
    public Result<ElementDetailResponse> queryElementDetail(ElementDetailQuery elementDetailQuery) {
        String schemaCode = elementDetailQuery.getSchemaCode();
        String elementCode = elementDetailQuery.getElementCode();
        Map<String, Object> extParam = elementDetailQuery.getExtParam();
        if (StringUtils.isBlank(schemaCode)) {
            schemaCode = CommonConstant.COMMON;
        }
        ElementMetaEntity elementMetaEntity = elementRepository.getByCodeWithEnum(schemaCode, elementCode, extParam);
        ElementDetailResponse elementDetailResponse = ElementMetaAssembler.transDto(elementMetaEntity);
        return Result.ofSuccess(elementDetailResponse);
    }

    @Override
    public Result<PageInfo<SchemaMetaDTO>> querySchemaPage(SchemaPageQuery schemaPageQuery) {
        return Result.ofSuccess(schemaRepository.queryByPage(schemaPageQuery));
    }

    @Override
    public Result<SchemaDetailResponse> querySchemaDetail(SchemaDetailQuery schemaDetailQuery) {
        String schemaCode = schemaDetailQuery.getSchemaCode();
        SchemaAggQueryOption option = SchemaAggQueryOption.needDeployed();

        if (schemaDetailQuery.getIsLargestVersion() == null || schemaDetailQuery.getIsLargestVersion()) {
            option = SchemaAggQueryOption.needLargestVersion();
        }
        SchemaMetaAggregate schemaAggregate = schemaRepository.getSchemaAggregateByCode(schemaCode, option);
        SchemaEntity schemaEntity = schemaAggregate.getSchemaEntity();
        List<SchemaElementEntity> dimensions = schemaAggregate.getDimensions();
        List<SchemaElementEntity> params = schemaAggregate.getParams();
        List<SchemaElementDTO> dimensionsDTOs = dimensions.stream().map(SchemaElementAssembler::toDTO).collect(Collectors.toList());
        List<SchemaElementDTO> paramsDTOs = params.stream().map(SchemaElementAssembler::toDTO).collect(Collectors.toList());

        List<DimPriorityVO> dimPriority = schemaAggregate.getDimPriority();
        List<DimPriorityDTO> dimPriorityDTOS = dimPriority.stream().map(DimPriorityAssembler::toDTO).collect(Collectors.toList());

        SchemaDetailResponse schemaDetailResponse = new SchemaDetailResponse();
        schemaDetailResponse.setSchemaCode(schemaEntity.getSchemaCode());
        schemaDetailResponse.setSchemaName(schemaEntity.getSchemaName());
        schemaDetailResponse.setSchemaDesc(schemaEntity.getSchemaDesc());
        schemaDetailResponse.setCreator(schemaEntity.getCreator());
        schemaDetailResponse.setStatus(schemaEntity.getStatus());
        schemaDetailResponse.setDimension(dimensionsDTOs);
        schemaDetailResponse.setParams(paramsDTOs);
        schemaDetailResponse.setPriority(dimPriorityDTOS);
        schemaDetailResponse.setMenu(schemaEntity.getMenu());
        schemaDetailResponse.setProjectName(schemaEntity.getProjectName());
        schemaDetailResponse.setRedisCacheExpireTime(schemaEntity.getRedisCacheExpireTime());
        schemaDetailResponse.setVersion(schemaEntity.getVersion());
        schemaDetailResponse.setActionFeature(schemaEntity.getActionFeature());
        return Result.ofSuccess(schemaDetailResponse);
    }

    @Override
    public Result<MetaConstantResponse> getMetaConstant() {
        List<ConstantDTO> elementSourceTypeConstants = Arrays.stream(ElementSourceTypeEnum.values()).map(en -> {
            ConstantDTO constantDTO = new ConstantDTO();
            constantDTO.setValue(en.getType());
            constantDTO.setDesc(en.getDesc());
            return constantDTO;
        }).collect(Collectors.toList());

        List<ConstantDTO> elementTypeConstants = Arrays.stream(ElementTypeEnum.values()).map(en -> {
            ConstantDTO constantDTO = new ConstantDTO();
            constantDTO.setValue(en.getType());
            constantDTO.setDesc(en.getDesc());
            return constantDTO;
        }).collect(Collectors.toList());

        List<ConstantDTO> schemaStatusConstants = Arrays.stream(SchemaStatusEnum.values()).map(en -> {
            ConstantDTO constantDTO = new ConstantDTO();
            constantDTO.setValue(en.getStatus());
            constantDTO.setDesc(en.getDesc());
            return constantDTO;
        }).collect(Collectors.toList());

        List<ConstantDTO> valueRangeTypeConstants = Arrays.stream(ValueRangeTypeEnum.values())
                .filter(e -> !Objects.equals(ValueRangeTypeEnum.SYSTEM.getType(), e.getType()))
                .map(en -> {
                    ConstantDTO constantDTO = new ConstantDTO();
                    constantDTO.setValue(en.getType());
                    constantDTO.setDesc(en.getDesc());
                    return constantDTO;
                }).collect(Collectors.toList());

        MetaConstantResponse metaConstantResponse = new MetaConstantResponse();
        metaConstantResponse.setElementSourceTypeEnums(elementSourceTypeConstants);
        metaConstantResponse.setElementTypeEnums(elementTypeConstants);
        metaConstantResponse.setSchemaStatusEnums(schemaStatusConstants);
        metaConstantResponse.setValueRangeTypeEnums(valueRangeTypeConstants);
        return Result.ofSuccess(metaConstantResponse);
    }

    @Resource
    MetaDomainService metaDomainService;

    @Override
    public Result<List<ElementInstDTO>> queryElementInstByDesc(ElementInstQuery elementInstQuery) {
        String schemaCode = elementInstQuery.getSchemaCode();
        String elementCode = elementInstQuery.getElementCode();
        String elementDesc = StringUtils.isEmpty(elementInstQuery.getKeyWords()) ? elementInstQuery.getElementDesc() : elementInstQuery.getKeyWords();
        List<ElementEnumVO> pageElemDesc = metaDomainService.getPageElemDesc(schemaCode, elementCode, elementDesc);
        if (CollectionUtils.isEmpty(pageElemDesc)) {
            return Result.ofSuccess(new ArrayList<>());
        }
        List<ElementInstDTO> elementInstDTOS = pageElemDesc.stream().map(e -> {
            ElementInstDTO elementInstDTO = new ElementInstDTO();
            elementInstDTO.setElementCode(elementCode);
            elementInstDTO.setElementValue(e.getValue());
            elementInstDTO.setElementDesc(e.getDesc());
            return elementInstDTO;
        }).collect(Collectors.toList());
        return Result.ofSuccess(elementInstDTOS);
    }

    @Override
    public Result<EntireSchemaResponse> getEntireSchemaInfo(String schemaCode) {
        SchemaDetailQuery schemaDetailQuery = new SchemaDetailQuery();
        schemaDetailQuery.setSchemaCode(schemaCode);
        schemaDetailQuery.setIsLargestVersion(true);
        Result<SchemaDetailResponse> schemaDetailResponseResult = querySchemaDetail(schemaDetailQuery);
        SchemaDetailResponse schemaDetail = schemaDetailResponseResult.getData();
        List<SchemaElementDTO> dimension = schemaDetail.getDimension();
        List<SchemaElementDTO> params = Optional.ofNullable(schemaDetail.getParams()).orElse(new ArrayList<>());
        List<ElementDetailResponse> elementList = Stream.concat(dimension.stream(), params.stream()).parallel().map(d -> {
            ElementDetailQuery elementDetailQuery = new ElementDetailQuery();
            elementDetailQuery.setSchemaCode(schemaCode);
            elementDetailQuery.setElementCode(d.getElementCode());
            return queryElementDetail(elementDetailQuery).getData();
        }).collect(Collectors.toList());
        EntireSchemaResponse entireSchemaResponse = new EntireSchemaResponse();
        entireSchemaResponse.setElementDetailList(elementList);
        entireSchemaResponse.setSchemaDetailResponse(schemaDetail);
        List<FieldMappingVO> fieldMappingVOS = schemaRepository.getFieldMappingVOS(schemaCode, schemaDetail.getVersion());
        Map<String, String> schemaFildMap = fieldMappingVOS.stream().collect(Collectors.toMap(FieldMappingVO::getElementCode, FieldMappingVO::getFieldCode, (o, n) -> n));
        SchemaFieldMappingDTO schemaFieldMappingDTO = new SchemaFieldMappingDTO();
        schemaFieldMappingDTO.setSchemaFieldMap(schemaFildMap);
        schemaFieldMappingDTO.setSchemaCode(fieldMappingVOS.get(0).getSchemaCode());
        schemaFieldMappingDTO.setVersion(fieldMappingVOS.get(0).getVersion());
        entireSchemaResponse.setSchemaFieldMappingDTO(schemaFieldMappingDTO);
        return Result.ofSuccess(entireSchemaResponse);
    }
}
