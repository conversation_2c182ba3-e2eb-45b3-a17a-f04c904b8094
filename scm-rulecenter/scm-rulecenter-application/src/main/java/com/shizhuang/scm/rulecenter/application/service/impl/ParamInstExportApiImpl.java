package com.shizhuang.scm.rulecenter.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.dewu.scm.lms.api.report.dto.request.ReportPageRequest;
import com.dewu.scm.lms.api.report.dto.response.ReportPageResponse;
import com.shizhuang.avatar.common.model.Result;
import com.shizhuang.scm.rulecenter.api.query.ParamInstExportRequest;
import com.shizhuang.scm.rulecenter.api.service.ParamInstExportApi;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstExportContext;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstExportExcelHandler;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstExportPreProcessHandler;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstExportQueryHandler;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.ParamInstExportValidateChain;
import com.shizhuang.scm.rulecenter.domains.support.chain.HandlerChainDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@DubboService(accesslog = "true", version = "1.0.0")
public class ParamInstExportApiImpl implements ParamInstExportApi {

    @Override
    public Result<ReportPageResponse> exportParamInst(ReportPageRequest<ParamInstExportRequest> pageRequest) {

        log.info("ParamInstExportApiImpl.exportParamInst request:{}", JSON.toJSONString(pageRequest));
        ParamInstExportContext paramInstImportContext = ParamInstExportContext.builder().pageRequest(pageRequest).build();

        HandlerChainDefinition<ParamInstExportContext> exportParamInstChain = HandlerChainDefinition
                .name(ParamInstExportApi.class.getSimpleName());

        exportParamInstChain
                //1、数据预处理，准备导出分页请求，以及方案聚合
                .add(ParamInstExportPreProcessHandler.class)
                //2、导出校验，校验参数输入是否合法
                .add(ParamInstExportValidateChain.class)
                //3、实际执行查询，返回内部业务对象的分页信息
                .add(ParamInstExportQueryHandler.class)
                //4、将业务对象信息封装成LMS导出框架需要的格式
                .add(ParamInstExportExcelHandler.class)
                .orchestrate()
                .handle(paramInstImportContext);

        return Result.success(paramInstImportContext.getReportPageResponse());
    }

}
