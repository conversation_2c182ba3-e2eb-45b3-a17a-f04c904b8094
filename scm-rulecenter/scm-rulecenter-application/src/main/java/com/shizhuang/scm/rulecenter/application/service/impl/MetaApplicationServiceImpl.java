package com.shizhuang.scm.rulecenter.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.cmd.*;
import com.shizhuang.scm.rulecenter.api.constant.SchemaStatusEnum;
import com.shizhuang.scm.rulecenter.api.constant.ValueRangeTypeEnum;
import com.shizhuang.scm.rulecenter.api.dto.DimPriorityDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementEnumDTO;
import com.shizhuang.scm.rulecenter.api.dto.SchemaElementDTO;
import com.shizhuang.scm.rulecenter.api.dto.SchemaFieldMappingDTO;
import com.shizhuang.scm.rulecenter.api.response.ElementDetailResponse;
import com.shizhuang.scm.rulecenter.api.response.EntireSchemaResponse;
import com.shizhuang.scm.rulecenter.api.response.SchemaDetailResponse;
import com.shizhuang.scm.rulecenter.api.service.MetaApplicationService;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.valueobject.EntireSchemaVO;
import com.shizhuang.scm.rulecenter.domains.meta.assembler.EntireSchemaAssembler;
import com.shizhuang.scm.rulecenter.domains.meta.context.*;
import com.shizhuang.scm.rulecenter.domains.meta.repository.ElementRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.domains.meta.service.handler.*;
import com.shizhuang.scm.rulecenter.domains.support.chain.HandlerChainDefinition;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import com.shizhuang.scm.rulecenter.infrastructure.common.annotation.ConcurrentLock;
import com.shizhuang.scm.rulecenter.infrastructure.common.annotation.KeyParam;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant.PARAM_SCHEMA;

/**
 * 元数据管理服务（写逻辑）
 */
@Slf4j
@Service
public class MetaApplicationServiceImpl implements MetaApplicationService {

    @Resource
    ElementRepository elementRepository;

    @Resource
    SchemaRepository schemaRepository;

    @Override
    public Result<String> createElement(ElementCreateCmd elementCreateCmd) {

        log.info("MetaApplicationServiceImpl.createElement cmd:{}", JSON.toJSONString(elementCreateCmd));
        ElementCreateContext elementCreateContext = ElementCreateContext.builder().elementCreateCmd(elementCreateCmd).build();

        HandlerChainDefinition<ElementCreateContext> elementCreateChain = HandlerChainDefinition
                .name(ElementCreateCmd.class.getSimpleName());

        elementCreateChain
                .add(ElementCreateHandler.class)
                .add(ElementCreateValidatorChain.class)
                .add(ElementCreatePersistentHandler.class)
                .orchestrate()
                .handle(elementCreateContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);
    }


    @Override
    public Result<String> batchCreateElement(ElementBatchCreateCmd elementBatchCreateCmd) {

        log.info("MetaApplicationServiceImpl.batchCreateElement cmd:{}", JSON.toJSONString(elementBatchCreateCmd));
        ElementBatchCreateContext elementBatchCreateContext = ElementBatchCreateContext.builder().elementBatchCreateCmd(elementBatchCreateCmd).build();

        HandlerChainDefinition<ElementBatchCreateContext> elementBatchCreateChain = HandlerChainDefinition
                .name(ElementBatchCreateCmd.class.getSimpleName());

        elementBatchCreateChain.add(ElementBatchCreatePreProcessHandler.class)
                .add(ElementBatchCreateValidatorChain.class)
                .orchestrate()
                .handle(elementBatchCreateContext);

        elementBatchCreateContext.getExecuteCreateCmd().forEach(this::createElement);
        elementBatchCreateContext.getExecuteEditCmd().forEach(cmd -> {
            ElementEditCmd elementEditCmd = new ElementEditCmd();
            BeanUtils.copyProperties(cmd, elementEditCmd);
            this.editElement(elementEditCmd);
        });
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    //编辑元素
    @ConcurrentLock("editElementLock")
    @Override
    public Result<String> editElement(@KeyParam({"schemaCode", "elementCode"}) ElementEditCmd elementEditCmd) {

        log.info("MetaApplicationServiceImpl.editElement cmd:{}", JSON.toJSONString(elementEditCmd));
        ElementEditContext elementEditContext = ElementEditContext.builder().elementEditCmd(elementEditCmd).build();

        HandlerChainDefinition<ElementEditContext> elementEditChain = HandlerChainDefinition
                .name(ElementEditContext.class.getSimpleName());

        elementEditChain
                .add(ElementEditHandler.class)
                .add(ElementEditValidatorChain.class)
                .add(ElementEditPersistentHandler.class)
                .orchestrate()
                .handle(elementEditContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> deleteElement(ElementDeleteCmd elementDeleteCmd) {

        log.info("MetaApplicationServiceImpl.editElement cmd:{}", JSON.toJSONString(elementDeleteCmd));
        ElementDeleteContext elementDeleteContext = ElementDeleteContext.builder().elementDeleteCmd(elementDeleteCmd).build();

        HandlerChainDefinition<ElementDeleteContext> elementEditChain = HandlerChainDefinition
                .name(ElementDeleteCmd.class.getSimpleName());
        elementEditChain.add(ElementDeleteValidatorChain.class)
                .orchestrate()
                .handle(elementDeleteContext);

        elementRepository.delete(elementDeleteCmd.getSchemaCode(), elementDeleteCmd.getElementCode());
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> createSchemaCode() {
        return Result.ofSuccess(PARAM_SCHEMA + "_" + System.currentTimeMillis());
    }

    @Override
    public Result<String> createSchema(SchemaCreateCmd schemaCreateCmd) {

        log.info("MetaApplicationServiceImpl.createSchema cmd:{}", JSON.toJSONString(schemaCreateCmd));
        SchemaCreateContext schemaCreateContext = SchemaCreateContext.builder().schemaCreateCmd(schemaCreateCmd).build();

        HandlerChainDefinition<SchemaCreateContext> schemaCreateChain = HandlerChainDefinition
                .name(SchemaCreateCmd.class.getSimpleName());

        schemaCreateChain
                .add(SchemaAggCreateHandler.class)
                .add(SchemaCreateValidatorChain.class)
                .add(SchemaCreatePersistentHandler.class)
                .orchestrate()
                .handle(schemaCreateContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @ConcurrentLock("editSchemaPriLock")
    @Override
    public Result<String> editSchemaDimPriority(@KeyParam("schemaCode") SchemaDimPriorityEditCmd schemaDimPriorityEditCmd) {

        log.info("MetaApplicationServiceImpl.editSchemaDimPriority cmd:{}", JSON.toJSONString(schemaDimPriorityEditCmd));
        SchemaDimPriorityEditContext schemaDimPriorityEditContext = SchemaDimPriorityEditContext.builder().schemaDimPriorityEditCmd(schemaDimPriorityEditCmd).build();

        HandlerChainDefinition<SchemaDimPriorityEditContext> SchemaDimPriorityEditChain = HandlerChainDefinition
                .name(SchemaCreateCmd.class.getSimpleName());

        SchemaDimPriorityEditChain
                .add(SchemaDimPriorityInitHandler.class)
                .add(SchemaDimPriorityValidatorChain.class)
                .add(SchemaDimPriorityUpdateHandler.class)
                .orchestrate()
                .handle(schemaDimPriorityEditContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    /**
     * 方案编辑
     *
     * @param schemaEditCmd
     * @return
     */
    @ConcurrentLock("editSchemaLock")
    @Override
    public Result<String> editSchema(@KeyParam("schemaCode") SchemaEditCmd schemaEditCmd) {

        log.info("MetaApplicationServiceImpl.editSchema cmd:{}", JSON.toJSONString(schemaEditCmd));
        SchemaEditContext schemaCreateContext = SchemaEditContext.builder().schemaEditCmd(schemaEditCmd).build();

        HandlerChainDefinition<SchemaEditContext> schemaEditChain = HandlerChainDefinition
                .name(SchemaEditCmd.class.getSimpleName());

        schemaEditChain
                .add(SchemaEditPreProcessHandler.class)
                .add(SchemaEditValidatorChain.class)
                .add(SchemaEditPersistentHandler.class)
                .orchestrate()
                .handle(schemaCreateContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);
    }


    @ConcurrentLock("deploySchemaLock")
    @Override
    public Result<String> deploySchema(SchemaDeployCmd schemaDeployCmd) {
        SchemaMetaAggregate schemaAggregateByCode = schemaRepository.getSchemaAggregateByCode(schemaDeployCmd.getSchemaCode(), SchemaAggQueryOption.needLargestVersion());
        schemaRepository.deploySchema(schemaAggregateByCode);
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> deleteSchema(SchemaDeleteCmd schemaDeleteCmd) {
        schemaRepository.deleteSchema(schemaDeleteCmd.getSchemaCode());
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Resource
    EntireSchemaAssembler entireSchemaAssembler;

    @Resource
    MetaDomainService metaDomainService;

    @Override
    public Result<String> overrideEntireSchemaInfo(EntireSchemaResponse entireSchemaResponse) {
        EntireSchemaVO entireSchemaVO = new EntireSchemaVO();
        //1、构建方案信息
        SchemaDetailResponse schemaDetailResponse = entireSchemaResponse.getSchemaDetailResponse();
        SchemaDO schemaDO = entireSchemaAssembler.toSchemaDO(schemaDetailResponse);
        schemaDO.setStatus(SchemaStatusEnum.ONLINE.getStatus().longValue());
        entireSchemaVO.setSchemaDO(schemaDO);

        //2、构建方案-元素绑定信息
        List<SchemaElementDTO> dimension = schemaDetailResponse.getDimension();
        List<SchemaElementDTO> params = schemaDetailResponse.getParams();
        if (CollectionUtils.isNotEmpty(params)) {
            dimension.addAll(params);
        }
        List<SchemaDetailDO> schemaDetailDOS = entireSchemaAssembler.toSchemaDetailDO(dimension);
        entireSchemaVO.setSchemaDetailDOS(schemaDetailDOS);

        //3、构建字段分配信息
        SchemaFieldMappingDTO schemaFieldMappingDTO = entireSchemaResponse.getSchemaFieldMappingDTO();
        List<SchemaFieldMappingDO> schemaFieldMappingDO = entireSchemaAssembler.toSchemaFieldMappingDO(schemaFieldMappingDTO);
        entireSchemaVO.setSchemaFieldMappingDOS(schemaFieldMappingDO);

        //4、构建方案优先级信息
        List<DimPriorityDTO> priority = schemaDetailResponse.getPriority();
        List<DimensionPriorityDO> dimensionPriorityDOS = entireSchemaAssembler.toDimensionPriorityDO(priority);
        entireSchemaVO.setDimensionPriorityDOS(dimensionPriorityDOS);

        //5、元素信息
        List<ElementDetailResponse> elementDetailList = entireSchemaResponse.getElementDetailList();
        List<ElementDO> elementDOS = entireSchemaAssembler.toElementDO(elementDetailList);
        entireSchemaVO.setElementDOS(elementDOS);

        //6、元素枚举取值信息
        Map<String, List<ElementEnumDTO>> elementEnumMap = elementDetailList.stream()
                .filter(e -> ValueRangeTypeEnum.ENUM.getType().equals(e.getValueRangeType()))
                .collect(Collectors.toMap(ElementDetailResponse::getElementCode, ElementDetailResponse::getEnums, (o, n) -> n));
        List<ElementValueRangeDO> elementValueRangeDOS = new ArrayList<>();
        elementEnumMap.forEach((key, elementEnumDTOS) -> elementEnumDTOS.forEach(dto -> {
            ElementValueRangeDO elementValueRangeDO = new ElementValueRangeDO();
            elementValueRangeDO.setElementCode(key);
            elementValueRangeDO.setValue(dto.getValue());
            elementValueRangeDO.setDesc(dto.getDesc());
            elementValueRangeDO.setIsDel((byte) 0);
            elementValueRangeDO.setBelongToSchemaCode(schemaDetailResponse.getSchemaCode());
            elementValueRangeDOS.add(elementValueRangeDO);
        }));
        entireSchemaVO.setElementValueRangeDOS(elementValueRangeDOS);

        schemaRepository.override(entireSchemaVO);
        schemaRepository.removeSchemaCache(schemaDO.getSchemaCode());
        metaDomainService.invalidCache();
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }
}
