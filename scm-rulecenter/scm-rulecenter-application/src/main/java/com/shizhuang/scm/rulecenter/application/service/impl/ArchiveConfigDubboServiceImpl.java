package com.shizhuang.scm.rulecenter.application.service.impl;

import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.dto.AppConfig;
import com.shizhuang.scm.rulecenter.api.dto.ArchiveNode;
import com.shizhuang.scm.rulecenter.api.dto.ArchivePlugin;
import com.shizhuang.scm.rulecenter.api.dto.ColumnRelation;
import com.shizhuang.scm.rulecenter.api.dto.TaskConfig;
import com.shizhuang.scm.rulecenter.api.service.ArchiveConfigDubboService;
import com.shizhuang.scm.rulecenter.api.constant.ArchiveConstants;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveAppConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveNodeConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ArchiveTaskConfigDO;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.TaskPropsConfig;
import com.shizhuang.scm.rulecenter.infrastructure.util.TaskPropsConfigUtil;
import com.shizhuang.scm.rulecenter.infrastructure.service.ArchiveConfigService;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.poizon.fusion.common.exception.biz.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SCP-Archive配置查询Dubbo服务实现
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
@Component("archiveConfigDubboServiceImpl")
public class ArchiveConfigDubboServiceImpl implements ArchiveConfigDubboService {

    @Autowired
    private ArchiveConfigService archiveConfigService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    @Override
    public Result<TaskConfig> getTaskConfig(Long taskId) {
        log.info("获取任务配置: taskId={}", taskId);
        // 获取任务配置
        ArchiveTaskConfigDO taskConfigDO = archiveConfigService.getTaskConfig(taskId);
        if (taskConfigDO == null) {
            log.warn("任务配置不存在: taskId={}", taskId);
            return null;
        }
        // 获取节点配置
        List<ArchiveNodeConfigDO> nodeConfigs = archiveConfigService.getTaskNodeConfigs(taskId);
        // 转换为DTO
        return Result.ofSuccess(convertToTaskConfig(taskConfigDO, nodeConfigs));
    }

    @Override
    public Result<List<TaskConfig>> getAppTaskConfigs(String appName) {
        log.info("获取应用任务配置: appName={}", appName);

        if (StringUtils.isEmpty(appName)) {
            throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "应用名称不能为空");
        }

        // 获取应用下的所有任务配置
        List<ArchiveTaskConfigDO> taskConfigs = archiveConfigService.getAppTaskConfigs(appName);
        if (CollectionUtils.isEmpty(taskConfigs)) {
            log.info("应用下没有任务配置: appName={}", appName);
            return Result.ofSuccess(Collections.emptyList());
        }

        // 转换为DTO列表
        List<TaskConfig> result = new ArrayList<>();
        for (ArchiveTaskConfigDO taskConfig : taskConfigs) {
            List<ArchiveNodeConfigDO> nodeConfigs = archiveConfigService.getTaskNodeConfigs(taskConfig.getTaskId());
            TaskConfig dto = convertToTaskConfig(taskConfig, nodeConfigs);
            if (dto != null) {
                result.add(dto);
            }
        }

        return Result.ofSuccess(result);
    }

    @Override
    public Result<AppConfig> getAppConfig(String appName) {
        log.info("获取应用配置: appName={}", appName);

        if (StringUtils.isEmpty(appName)) {
            throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "应用名称不能为空");
        }

        ArchiveAppConfigDO appConfigDO = archiveConfigService.getAppConfig(appName);
        if (appConfigDO == null) {
            log.warn("应用配置不存在: appName={}", appName);
            return null;
        }

        return Result.ofSuccess(convertToAppConfig(appConfigDO));
    }

    /**
     * 转换任务配置DO为DTO
     */
    private TaskConfig convertToTaskConfig(ArchiveTaskConfigDO taskConfigDO, List<ArchiveNodeConfigDO> nodeConfigs) {
        if (taskConfigDO == null) {
            return null;
        }

        TaskConfig taskConfig = new TaskConfig();

        // 基本信息
        taskConfig.setTaskId(taskConfigDO.getTaskId());
        taskConfig.setTaskName(taskConfigDO.getTaskName());
        taskConfig.setStatus(taskConfigDO.getStatus());
        taskConfig.setMode(taskConfigDO.getMode());
        taskConfig.setVersion(taskConfigDO.getVersion() != null ? taskConfigDO.getVersion().toString() : null);
        taskConfig.setDatasourceName(taskConfigDO.getDatasourceName());
        taskConfig.setDatabaseName(taskConfigDO.getDatabaseName());
        taskConfig.setInterval(taskConfigDO.getInterval());
        taskConfig.setLimit(taskConfigDO.getLimit());
        taskConfig.setExecutionType(taskConfigDO.getExecutionType());

        // 时间信息
        if (taskConfigDO.getCreateTime() != null) {
            taskConfig.setCreateTime(dateFormat.format(taskConfigDO.getCreateTime()));
        }
        if (taskConfigDO.getUpdateTime() != null) {
            taskConfig.setLastUpdateTime(dateFormat.format(taskConfigDO.getUpdateTime()));
        }

        // 时间范围 - 从 props 中解析时间配置
        if (taskConfigDO.getProps() != null) {
            TaskPropsConfig timeConfig = TaskPropsConfigUtil.deserialize(taskConfigDO.getProps());
            if (timeConfig != null) {
                if (timeConfig.getStart() != null) {
                    taskConfig.setStart(timeConfig.getStart());
                }
                if (timeConfig.getEnd() != null) {
                    taskConfig.setEnd(timeConfig.getEnd());
                }
            }
        }
        // 查找根节点
        if (CollectionUtils.isNotEmpty(nodeConfigs)) {
            ArchiveNodeConfigDO rootNodeDO = nodeConfigs.stream()
                    .filter(node -> Boolean.TRUE.equals(node.getRootNode()))
                    .findFirst()
                    .orElse(null);

            if (rootNodeDO != null) {
                taskConfig.setBaseTable(rootNodeDO.getTableName());
                taskConfig.setRootNode(convertToArchiveNode(rootNodeDO, nodeConfigs));
            }
        }

        return taskConfig;
    }

    /**
     * 转换应用配置DO为DTO
     */
    private AppConfig convertToAppConfig(ArchiveAppConfigDO appConfigDO) {
        if (appConfigDO == null) {
            return null;
        }

        AppConfig appConfig = new AppConfig();
        appConfig.setAppName(appConfigDO.getAppName());
        appConfig.setEnable(appConfigDO.getEnable());
        appConfig.setThreads(appConfigDO.getThreads());
        appConfig.setLockExpireSeconds(appConfigDO.getLockExpireSeconds());
        appConfig.setInterval(appConfigDO.getInterval());
        appConfig.setMaxThreads(appConfigDO.getMaxThreads());
        appConfig.setVersion(appConfigDO.getVersion());

        if (appConfigDO.getCreateTime() != null) {
            appConfig.setCreateTime(dateFormat.format(appConfigDO.getCreateTime()));
        }
        if (appConfigDO.getUpdateTime() != null) {
            appConfig.setUpdateTime(dateFormat.format(appConfigDO.getUpdateTime()));
        }

        return appConfig;
    }

    /**
     * 转换节点配置DO为DTO
     */
    private ArchiveNode convertToArchiveNode(ArchiveNodeConfigDO nodeConfigDO, List<ArchiveNodeConfigDO> allNodes) {
        if (nodeConfigDO == null) {
            return null;
        }

        ArchiveNode node = new ArchiveNode();

        // 基本信息
        node.setNodeId(nodeConfigDO.getNodeId());
        node.setTaskId(nodeConfigDO.getTaskId());
        node.setParentNodeId(nodeConfigDO.getParentNodeId());
        node.setTableName(nodeConfigDO.getTableName());
        node.setCondition(nodeConfigDO.getCondition());
        node.setIsArchive(nodeConfigDO.getIsArchive());
        node.setPrimaryKeyColumn(nodeConfigDO.getPrimaryKeyColumn());
        node.setStatus(nodeConfigDO.getStatus());
        node.setRootNode(nodeConfigDO.getRootNode());
        node.setIndexColumn(nodeConfigDO.getIndexColumn());
        node.setOrderBy(nodeConfigDO.getOrderBy());
        node.setShardingEnabled(nodeConfigDO.getShardingEnabled());
        node.setShardingExpression(nodeConfigDO.getShardingExpression());
        node.setShardingPluginClass(nodeConfigDO.getShardingPluginClass());
        node.setShardingField(nodeConfigDO.getShardingField());
        node.setShardingPrefix(nodeConfigDO.getShardingPrefix());

        // 新增字段
        node.setDatasourceName(nodeConfigDO.getDatasourceName());
        node.setDatabaseName(nodeConfigDO.getDatabaseName());
        node.setArchiveType(nodeConfigDO.getArchiveType());
        node.setFilterDefaultValue(nodeConfigDO.getFilterDefaultValue());
        node.setCreateTime(nodeConfigDO.getCreateTime());
        node.setUpdateTime(nodeConfigDO.getUpdateTime());

        // 解析JSON字段 - 让全局异常处理机制处理JSON解析异常
        try {
            if (StringUtils.isNotEmpty(nodeConfigDO.getQueryColumns())) {
                List<String> queryColumns = objectMapper.readValue(nodeConfigDO.getQueryColumns(), new TypeReference<List<String>>() {
                });
                node.setQueryColumns(queryColumns);
            }
        } catch (Exception e) {
            throw new BizRuntimeException(ErrorCode.DATABASE_ERROR.getCode(), "解析queryColumns失败: " + nodeConfigDO.getQueryColumns());
        }

        try {
            if (StringUtils.isNotEmpty(nodeConfigDO.getPlugins())) {
                List<ArchivePlugin> plugins = objectMapper.readValue(nodeConfigDO.getPlugins(), new TypeReference<List<ArchivePlugin>>() {
                });
                node.setPlugins(plugins);
            }
        } catch (Exception e) {
            throw new BizRuntimeException(ErrorCode.DATABASE_ERROR.getCode(), "解析plugins失败: " + nodeConfigDO.getPlugins());
        }

        try {
            if (StringUtils.isNotEmpty(nodeConfigDO.getProps())) {
                Map<String, Object> props = objectMapper.readValue(nodeConfigDO.getProps(), new TypeReference<Map<String, Object>>() {
                });
                node.setProps(props);
                
                // 从props中解析相关字段
                if (props != null) {
                    // 解析indexType
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_INDEX_TYPE)) {
                        Object indexTypeObj = props.get(ArchiveConstants.PROPS_KEY_INDEX_TYPE);
                        if (indexTypeObj instanceof Number) {
                            node.setIndexType(((Number) indexTypeObj).intValue());
                        }
                    }
                    
                    // 解析indexStart
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_INDEX_START)) {
                        Object indexStartObj = props.get(ArchiveConstants.PROPS_KEY_INDEX_START);
                        if (indexStartObj instanceof Number) {
                            node.setIndexStart(((Number) indexStartObj).longValue());
                        }
                    }
                    
                    // 解析indexEnd
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_INDEX_END)) {
                        Object indexEndObj = props.get(ArchiveConstants.PROPS_KEY_INDEX_END);
                        if (indexEndObj instanceof Number) {
                            node.setIndexEnd(((Number) indexEndObj).longValue());
                        }
                    }
                    
                    // 解析reserveDays
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_RESERVE_DAYS)) {
                        Object reserveDaysObj = props.get(ArchiveConstants.PROPS_KEY_RESERVE_DAYS);
                        if (reserveDaysObj instanceof Number) {
                            node.setReserveDays(((Number) reserveDaysObj).intValue());
                        }
                    }
                    
                    // 解析shardingPluginConfig
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_SHARDING_PLUGIN_CONFIG)) {
                        Object shardingPluginConfigObj = props.get(ArchiveConstants.PROPS_KEY_SHARDING_PLUGIN_CONFIG);
                        if (shardingPluginConfigObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> shardingPluginConfig = (Map<String, Object>) shardingPluginConfigObj;
                            node.setShardingPluginConfig(shardingPluginConfig);
                        }
                    }
                    
                    // 解析shardCount
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_SHARD_COUNT)) {
                        Object shardCountObj = props.get(ArchiveConstants.PROPS_KEY_SHARD_COUNT);
                        if (shardCountObj instanceof Number) {
                            node.setShardCount(((Number) shardCountObj).intValue());
                        }
                    }
                    
                    // 解析shardingTableTemplate
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_SHARDING_TABLE_TEMPLATE)) {
                        Object shardingTableTemplateObj = props.get(ArchiveConstants.PROPS_KEY_SHARDING_TABLE_TEMPLATE);
                        if (shardingTableTemplateObj instanceof String) {
                            node.setShardingTableTemplate((String) shardingTableTemplateObj);
                        }
                    }
                    
                    // 解析enableScan
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_ENABLE_SCAN)) {
                        Object enableScanObj = props.get(ArchiveConstants.PROPS_KEY_ENABLE_SCAN);
                        if (enableScanObj instanceof Boolean) {
                            node.setEnableScan((Boolean) enableScanObj);
                        }
                    }
                    
                    // 解析minPrimaryKey
                    if (props.containsKey(ArchiveConstants.PROPS_KEY_MIN_PRIMARY_KEY)) {
                        Object minPrimaryKeyObj = props.get(ArchiveConstants.PROPS_KEY_MIN_PRIMARY_KEY);
                        if (minPrimaryKeyObj instanceof String) {
                            node.setMinPrimaryKey((String) minPrimaryKeyObj);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new BizRuntimeException(ErrorCode.DATABASE_ERROR.getCode(), "解析props失败: " + nodeConfigDO.getProps());
        }

        try {
            if (StringUtils.isNotEmpty(nodeConfigDO.getRelations())) {
                List<ColumnRelation> relations = objectMapper.readValue(nodeConfigDO.getRelations(), new TypeReference<List<ColumnRelation>>() {
                });
                node.setRelations(relations);
            }
        } catch (Exception e) {
            throw new BizRuntimeException(ErrorCode.DATABASE_ERROR.getCode(), "解析relations失败: " + nodeConfigDO.getRelations());
        }

        // 查找子节点
        if (CollectionUtils.isNotEmpty(allNodes)) {
            List<ArchiveNode> children = allNodes.stream()
                    .filter(child -> nodeConfigDO.getNodeId().equals(child.getParentNodeId()))
                    .map(child -> convertToArchiveNode(child, allNodes))
                    .collect(Collectors.toList());
            node.setChildren(children);
        }

        return node;
    }

}