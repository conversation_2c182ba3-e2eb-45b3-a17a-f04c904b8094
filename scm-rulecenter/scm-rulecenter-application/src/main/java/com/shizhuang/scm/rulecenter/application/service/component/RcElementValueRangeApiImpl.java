package com.shizhuang.scm.rulecenter.application.service.component;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;
import com.shizhuang.scm.rulecenter.api.service.ParamInstApplicationService;
import com.shizhuang.scm.rulecenter.api.service.component.RcElementValueRangeApi;
import com.shizhuang.scm.rulecenter.api.service.component.RcValueRangeResponse;
import com.shizhuang.scm.rulecenter.domains.meta.service.MetaDomainService;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOExample;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.ParamInstanceDOWithBLOBs;
import com.shizhuang.scm.rulecenter.infrastructure.dal.mapper.ParamInstanceMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@DubboService(accesslog = "true", version = "1.0.0")
public class RcElementValueRangeApiImpl implements RcElementValueRangeApi {

    @Resource
    ParamInstApplicationService paramInstApplicationService;

    @Override
    public Result<List<RcValueRangeResponse>> getValueRange(String schemaCode, String value, String label) {

        InstPageQuery instPageQuery = new InstPageQuery();
        instPageQuery.setPageNum(1);
        instPageQuery.setPageSize(1000);
        instPageQuery.setSchemaCode(schemaCode);

        return getListResult(value, label, instPageQuery);
    }

    @Override
    public Result<List<RcValueRangeResponse>> getValueRangeWithParam(String schemaCode, String value, String label, String param) {

        if (StringUtils.isBlank(param) || !param.startsWith("[")) {
            return Result.ofSuccess(new ArrayList<>());
        }

        List<ElementQueryParam> elementQueryParams = JSON.parseArray(param, ElementQueryParam.class);
        List<ElementInstDTO> queryParams = elementQueryParams.stream().map(e -> {
            ElementInstDTO elementInstDTO = new ElementInstDTO();
            elementInstDTO.setElementCode(e.getKey());
            elementInstDTO.setElementValue(e.getValue());
            return elementInstDTO;
        }).collect(Collectors.toList());

        ElementInstDTO enabledInst = new ElementInstDTO();
        enabledInst.setElementCode("enabled");
        enabledInst.setElementValue("1");
        queryParams.add(enabledInst);

        InstPageQuery instPageQuery = new InstPageQuery();
        instPageQuery.setPageNum(1);
        instPageQuery.setPageSize(1000);
        instPageQuery.setSchemaCode(schemaCode);
        instPageQuery.setQueryParams(queryParams);

        return getListResult(value, label, instPageQuery);
    }

    private Result<List<RcValueRangeResponse>> getListResult(String value, String label, InstPageQuery instPageQuery) {

        ElementInstDTO enabledInst = new ElementInstDTO();
        enabledInst.setElementCode("enabled");
        enabledInst.setElementValue("1");
        List<ElementInstDTO> queryParams = new ArrayList<>();
        queryParams.add(enabledInst);
        instPageQuery.setQueryParams(queryParams);

        Result<PageInfoResponse<InstDimPageResponse>> pageInfoResponseResult = paramInstApplicationService.queryParamInstPage(instPageQuery);

        if (pageInfoResponseResult == null
                || pageInfoResponseResult.getData() == null
                || CollectionUtils.isEmpty(pageInfoResponseResult.getData().getList())) {
            return Result.ofSuccess(new ArrayList<>());
        }

        PageInfoResponse<InstDimPageResponse> data = pageInfoResponseResult.getData();
        List<InstDimPageResponse> list = data.getList();

        List<RcValueRangeResponse> rcValueRangeResponses = list.stream().map(e -> {
            Map<String, Object> elementDescMap = e.getElementDescMap();
            Map<String, Object> elementValueMap = e.getElementValueMap();
            String[] split = value.split("_");
            Object valueObj = elementDescMap.get(split[0]);
            if (split.length > 1 && split[1].equals("value")) {
                valueObj = elementValueMap.get(split[0]);
            }
            String[] labelSplit = label.split("_");
            Object labelObj = elementDescMap.get(labelSplit[0]);
            if (labelSplit.length > 1 && labelSplit[1].equals("value")) {
                labelObj = elementValueMap.get(labelSplit[0]);
            }
            RcValueRangeResponse rcValueRangeResponse = new RcValueRangeResponse();
            rcValueRangeResponse.setValue(String.valueOf(valueObj));
            rcValueRangeResponse.setLabel(String.valueOf(labelObj));
            rcValueRangeResponse.setElementValueMap(elementValueMap);
            rcValueRangeResponse.setElementDescMap(elementDescMap);

            return rcValueRangeResponse;
        }).collect(Collectors.toList());

        return Result.ofSuccess(rcValueRangeResponses);
    }

}
