package com.shizhuang.scm.rulecenter.application.transfer;

import com.shizhuang.duapp.scp.framework.admin.sdk.model.TaskConfig;
import com.shizhuang.scm.rulecenter.api.request.ArchivePreviewRequest;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveTaskDomain;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveTaskDetailDomain;
import com.shizhuang.scm.rulecenter.api.request.TaskInfoDetailRequest;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.shizhuang.duapp.scp.framework.admin.sdk.constants.ArchiveConstants.ARCHIVE_TYPE_PARENT;
import static com.shizhuang.duapp.scp.framework.admin.sdk.constants.ArchiveConstants.FILTER_DEFAULT_VALUE;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ArchiveTaskConfigTransfer {

    ArchiveTaskConfigTransfer INSTANCE = org.mapstruct.factory.Mappers.getMapper(ArchiveTaskConfigTransfer.class);

    @Mapping(target = "taskName", ignore = true)
    @Mapping(target = "datasourceName", ignore = true)
    @Mapping(target = "dataSourceUrl", ignore = true)
    @Mapping(target = "nextExecuteTime", ignore = true)
    @Mapping(target = "databaseName", ignore = true)
    void toTarget(TaskConfig source, @MappingTarget TaskConfig target);

    @Mappings({
            @Mapping(target = "taskName", source = "taskName"),
            @Mapping(target = "datasourceName", source = "datasourceName"),
            @Mapping(target = "databaseName", source = "databaseName"),
            @Mapping(target = "dataSourceUrl", source = "datasourceUrl"),
            @Mapping(target = "taskDetails", expression = "java(mapTaskDetails(request.getTaskDetails()))")
    })
    void archivePreviewRequestTODomain(ArchivePreviewRequest request, @MappingTarget ArchiveTaskDomain domain);

    default List<ArchiveTaskDetailDomain> mapTaskDetails(List<TaskInfoDetailRequest> details) {
        if (details == null) return null;
        List<ArchiveTaskDetailDomain> result = new java.util.ArrayList<>();
        for (TaskInfoDetailRequest d : details) {
            ArchiveTaskDetailDomain domain = new ArchiveTaskDetailDomain();
            domain.setTableName(d.getTableName());
            domain.setConditions(d.getConditions());
            // 设置索引相关属性
            domain.setIndexColumn(d.getIndexColumn());
            domain.setIndexType(d.getIndexType());
            domain.setOrderBy(d.getOrderBy());
            domain.setIndexStart(d.getIndexStart());
            domain.setIndexEnd(d.getIndexEnd());
            domain.setReserveDays(d.getReserveDays());

            List<String> plugins = new ArrayList<>();
            if (Objects.equals(domain.getArchiveType(), ARCHIVE_TYPE_PARENT)) { // 默认先归档子节点，需要先归档父节点则启用插件
                plugins.add("com.shizhuang.duapp.scp.framework.archive.core.plugin.impl.ParentFirstArchivePlugin");
            }
            if (Objects.equals(domain.getFilterDefaultValue(), FILTER_DEFAULT_VALUE)) {
                plugins.add("com.shizhuang.duapp.scp.framework.archive.core.plugin.impl.SafeDeletePlugin");
            }
            domain.setPlugins(plugins);
            domain.setArchiveType(d.getArchiveType());
            domain.setFilterDefaultValue(d.getFilterDefaultValue());
            domain.setRelationConditions(d.getRelationConditions());
            domain.setArchive(d.getArchive());
            if (d.getChildren() != null && !d.getChildren().isEmpty()) {
                domain.setChildren(mapTaskDetails(d.getChildren()));
            }
            result.add(domain);
        }
        return result;
    }
}
