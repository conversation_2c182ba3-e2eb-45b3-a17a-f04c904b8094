package com.shizhuang.scm.rulecenter.application.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.cmd.*;
import com.shizhuang.scm.rulecenter.api.constant.SystemFieldEnum;
import com.shizhuang.scm.rulecenter.api.dto.DimensionKeyDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.dto.TableHeadDTO;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;
import com.shizhuang.scm.rulecenter.api.service.ParamInstApplicationService;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstAddOrUpdateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstCreateContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstDeleteContext;
import com.shizhuang.scm.rulecenter.domains.inst.context.ParamInstEditContext;
import com.shizhuang.scm.rulecenter.domains.inst.repository.ParamInstRepository;
import com.shizhuang.scm.rulecenter.domains.inst.service.ParamInstDomainService;
import com.shizhuang.scm.rulecenter.domains.inst.service.handler.*;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.SchemaMetaAggregate;
import com.shizhuang.scm.rulecenter.domains.meta.aggregate.entity.SchemaElementEntity;
import com.shizhuang.scm.rulecenter.domains.meta.repository.SchemaRepository;
import com.shizhuang.scm.rulecenter.domains.meta.repository.impl.SchemaAggQueryOption;
import com.shizhuang.scm.rulecenter.domains.support.chain.HandlerChainDefinition;
import com.shizhuang.scm.rulecenter.domains.support.pmf.BizIdentityContext;
import com.shizhuang.scm.rulecenter.infrastructure.common.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 参数实例管理服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService(accesslog = "true", version = "1.0.0")
public class ParamInstApplicationServiceImpl implements ParamInstApplicationService {

    @Resource
    ParamInstRepository paramInstRepository;

    @Resource
    SchemaRepository schemaRepository;

    @Resource
    ParamInstDomainService paramInstDomainService;

    @Override
    public Result<PageInfoResponse<InstDimPageResponse>> queryParamInstPage(InstPageQuery instPageQuery) {
        SchemaMetaAggregate schemaAggregateByCode = schemaRepository.getSchemaAggregateByCode(instPageQuery.getSchemaCode(), SchemaAggQueryOption.needLargestVersion());
        List<ElementInstDTO> queryParams = instPageQuery.getQueryParams();
        if (CollectionUtils.isNotEmpty(queryParams)) {
            Map<String, String> elementCodeToNameMap = schemaAggregateByCode.getElementCodeToNameMap();
            queryParams = queryParams.stream()
                    .filter(q -> SystemFieldEnum.getAllCode().contains(q.getElementCode()) || elementCodeToNameMap.containsKey(q.getElementCode()))
                    .collect(Collectors.toList());
            instPageQuery.setQueryParams(queryParams);
        }
        paramInstDomainService.checkAuthUserWarehouser(instPageQuery,null);

        PageInfo<InstDimPageResponse> instDimResponsePageInfo = paramInstRepository.queryParamInstPage(instPageQuery);
        PageInfoResponse<InstDimPageResponse> pageInfoResponse = new PageInfoResponse<>();
        BeanUtils.copyProperties(instDimResponsePageInfo, pageInfoResponse);
        List<TableHeadDTO> tableHeadDTOS = ParamInstDomainService.getTableHeadDTOS(schemaAggregateByCode);
        pageInfoResponse.setTableHead(tableHeadDTOS);
        return Result.ofSuccess(pageInfoResponse);
    }

    @Override
    public Result<String> addParamInst(ParamInstAddCmd paramInstAddCmd) {

        //初始化责任链
        log.info("ParamInstApplicationServiceImpl.addParamInst cmd:{}", JSON.toJSONString(paramInstAddCmd));
        ParamInstCreateContext paramInstCreateContext = ParamInstCreateContext.builder().paramInstAddCmd(paramInstAddCmd).build();

        HandlerChainDefinition<ParamInstCreateContext> addParamInstChain = HandlerChainDefinition
                .name(ParamInstAddCmd.class.getSimpleName());

        addParamInstChain
                //1、初始化参数实例实体
                .add(ParamInstEntityInitHandler.class)
                //2、引入新增参数实例校验链
                .add(ParamInstCreateValidateChain.class)
                //3、持久化逻辑
                .add(ParamInstPersistentHandler.class)
                //组织责任链
                .orchestrate()
                //执行
                .handle(paramInstCreateContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> editParamInst(ParamInstEditCmd paramInstEditCmd) {

        log.info("ParamInstApplicationServiceImpl.editParamInst cmd:{}", JSON.toJSONString(paramInstEditCmd));
        ParamInstEditContext paramInstCreateContext = ParamInstEditContext.builder().paramInstEditCmd(paramInstEditCmd).build();

        HandlerChainDefinition<ParamInstEditContext> addParamInstChain = HandlerChainDefinition
                .name(ParamInstEditCmd.class.getSimpleName());

        addParamInstChain
                .add(ParamInstEditPreProcessHandler.class)
                .add(ParamInstEditValidateChain.class)
                .add(ParamInstEditPersistentHandler.class)
                .orchestrate()
                .handle(paramInstCreateContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> deleteParamInst(ParamInstDeleteCmd paramInstDeleteCmd) {
        log.info("ParamInstApplicationServiceImpl.deleteParamInst cmd:{}", JSON.toJSONString(paramInstDeleteCmd));
        ParamInstDeleteContext paramInstDeleteContext = ParamInstDeleteContext.builder().paramInstDeleteCmd(paramInstDeleteCmd).build();

        HandlerChainDefinition<ParamInstDeleteContext> deleteParamInstChain = HandlerChainDefinition
                .name(ParamInstDeleteCmd.class.getSimpleName());

        deleteParamInstChain
                .add(ParamInstDeletePreProcessHandler.class)
                .add(ParamInstDeleteValidateChain.class)
                .add(ParamInstDeletePersistentHandler.class)
                .orchestrate()
                .handle(paramInstDeleteContext);

        paramInstRepository.delete(paramInstDeleteCmd.getSchemaCode(), paramInstDeleteCmd.getAggId());
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> batchDeleteParamInst(ParamInstBatchDeleteCmd paramInstBatchDeleteCmd) {
        log.info("ParamInstApplicationServiceImpl.paramInstBatchDeleteCmd cmd:{}", JSON.toJSONString(paramInstBatchDeleteCmd));
        paramInstRepository.batchDelete(paramInstBatchDeleteCmd.getSchemaCode(), paramInstBatchDeleteCmd.getAggIds());
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> disabledParamInst(ParamInstAbleSwitchCmd paramInstAbleSwitchCmd) {
        log.info("ParamInstApplicationServiceImpl.disabledParamInst cmd:{}", JSON.toJSONString(paramInstAbleSwitchCmd));
        String optUser = BizIdentityContext.getOperationUser().getUserNameUnBlank();
        paramInstRepository.disable(paramInstAbleSwitchCmd.getSchemaCode(), paramInstAbleSwitchCmd.getAggId(), optUser);
        paramInstDomainService.recordDisableInstBizLog(paramInstAbleSwitchCmd);
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> enabledParamInst(ParamInstAbleSwitchCmd paramInstAbleSwitchCmd) {
        log.info("ParamInstApplicationServiceImpl.enabledParamInst cmd:{}", JSON.toJSONString(paramInstAbleSwitchCmd));
        String optUser = BizIdentityContext.getOperationUser().getUserNameUnBlank();
        paramInstRepository.enable(paramInstAbleSwitchCmd.getSchemaCode(), paramInstAbleSwitchCmd.getAggId(), optUser);
        paramInstDomainService.recordEnableInstBizLog(paramInstAbleSwitchCmd);
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public byte[] generateImportExcel(String schemaCode) {
        SchemaMetaAggregate schemaAggregateByCode = schemaRepository.getSchemaAggregateByCode(schemaCode, SchemaAggQueryOption.needLatestVersion());
        List<SchemaElementEntity> allElements = schemaAggregateByCode.getAllElements();
        List<String> elementNames = allElements.stream().map(SchemaElementEntity::getElementName).collect(Collectors.toList());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        List<List<String>> excelHeader = new ArrayList<>();
        excelHeader.add(elementNames);
        EasyExcel.write(outputStream).excelType(ExcelTypeEnum.XLSX).needHead(false)
                .sheet("Sheet1").doWrite(excelHeader);
        return outputStream.toByteArray();
    }

    @Override
    public Result<String> batchDisabledParamInst(ParamInstBatchAbleSwitchCmd paramInstBatchAbleSwitchCmd) {
        log.info("ParamInstApplicationServiceImpl.batchDisabledParamInst cmd:{}", JSON.toJSONString(paramInstBatchAbleSwitchCmd));
        List<DimensionKeyDTO> dimensionKeys = paramInstBatchAbleSwitchCmd.getDimensionKeys();
        String optUser = paramInstBatchAbleSwitchCmd.getOptUser();
        String schemaCode = dimensionKeys.get(0).getSchemaCode();
        List<String> aggIds = paramInstBatchAbleSwitchCmd.getAggIds();
        paramInstRepository.batchDisable(schemaCode, aggIds, optUser);
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }

    @Override
    public Result<String> batchEnabledParamInst(ParamInstBatchAbleSwitchCmd paramInstBatchAbleSwitchCmd) {
        log.info("ParamInstApplicationServiceImpl.batchEnabledParamInst cmd:{}", JSON.toJSONString(paramInstBatchAbleSwitchCmd));
        List<DimensionKeyDTO> dimensionKeys = paramInstBatchAbleSwitchCmd.getDimensionKeys();
        String optUser = paramInstBatchAbleSwitchCmd.getOptUser();
        String schemaCode = dimensionKeys.get(0).getSchemaCode();
        List<String> aggIds = paramInstBatchAbleSwitchCmd.getAggIds();
        paramInstRepository.batchEnable(schemaCode, aggIds, optUser);
        return Result.ofSuccess(CommonConstant.SUCCESS);
    }


    @Override
    public Result<String> addOrUpdateParamInst(ParamInstAddCmd paramInstAddCmd) {

        //初始化责任链
        log.info("ParamInstApplicationServiceImpl.addOrUpdateParamInst cmd:{}", JSON.toJSONString(paramInstAddCmd));
        ParamInstAddOrUpdateContext paramInstCreateContext = ParamInstAddOrUpdateContext.builder().paramInstAddCmd(paramInstAddCmd).build();

        HandlerChainDefinition<ParamInstAddOrUpdateContext> addParamInstChain = HandlerChainDefinition
                .name(ParamInstAddCmd.class.getSimpleName());

        addParamInstChain
                //1、初始化参数实例实体
                .add(ParamInstAddOrUpdatePreProcessHandler.class)
                //3、持久化逻辑
                .add(ParamInstAddOrUpdatePersistentHandler.class)
                //组织责任链
                .orchestrate()
                //执行
                .handle(paramInstCreateContext);

        return Result.ofSuccess(CommonConstant.SUCCESS);

    }

    @Transactional
    @Override
    public Result<String> combineEditParamInst(ParamsCombineEditCmd paramsCombineEditCmd) {

        log.info("ParamInstApplicationServiceImpl.combineEditParamInst cmd:{}", JSON.toJSONString(paramsCombineEditCmd));

        List<ParamInstAddCmd> addCmdList = paramsCombineEditCmd.getAddCmdList();
        List<ParamInstEditCmd> editCmdList = paramsCombineEditCmd.getEditCmdList();
        List<ParamInstDeleteCmd> deleteCmdList = paramsCombineEditCmd.getDeleteCmdList();

        if(CollectionUtils.isNotEmpty(addCmdList)){
            addCmdList.forEach(this::addParamInst);
        }

        if(CollectionUtils.isNotEmpty(editCmdList)){
            editCmdList.forEach(this::editParamInst);
        }

        if(CollectionUtils.isNotEmpty(deleteCmdList)){
            deleteCmdList.forEach(this::deleteParamInst);
        }
        return  Result.ofSuccess(CommonConstant.SUCCESS);
    }
}
