package com.shizhuang.scm.rulecenter.application.service.impl;

import com.poizon.fusion.common.model.Result;
import com.shizhuang.ark.config.client.annotation.ArkJsonValue;
import com.shizhuang.duapp.scp.framework.admin.sdk.constants.ArchiveConstants;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.ArchiveDataConfig;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.TaskConfig;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.TaskDetailConfig;
import com.shizhuang.duapp.scp.framework.admin.sdk.response.PreviewResponse;
import com.shizhuang.scm.rulecenter.api.request.*;
import com.shizhuang.scm.rulecenter.api.response.*;
import com.shizhuang.scm.rulecenter.application.transfer.ArchiveTaskConfigTransfer;
import com.shizhuang.scm.rulecenter.domains.archive.service.ArchiveDomainService;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveGlobal;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveTaskDetailDomain;
import com.shizhuang.scm.rulecenter.domains.archive.model.ArchiveTaskDomain;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.BizRuntimeException;
import com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode;
import com.shizhuang.scm.rulecenter.infrastructure.integration.rpc.ArchiveDataPersistService;
import com.shizhuang.scm.rulecenter.infrastructure.dal.data.TaskPropsConfig;
import com.shizhuang.scm.rulecenter.infrastructure.util.TaskPropsConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.shizhuang.duapp.scp.framework.admin.sdk.constants.ArchiveConstants.*;
import static com.shizhuang.scm.rulecenter.infrastructure.common.exception.ErrorCode.BIZ_VALIDATE_ERROR;

@Slf4j
@Service
public class ArchiveAdminService {

    @Resource
    private ArchiveDataPersistService archiveDataPersistService;

    @Resource
    private ArchiveDomainService archiveDomainService;

    private static final String INDEX_COLUMN_TYPE_KEY = "index_column_type";

    /**
     * 数据库日期类型
     */
    @ArkJsonValue("${archive.type.date:[\"date\",\"datetime\",\"timestamp\"]}")
    private List<String> dateTypes;

    /**
     * 数据库日期类型
     */
    @ArkJsonValue("${archive.type.number:[\"tinyint\",\"smallint\",\"mediumint\",\"int\",\"bigint\",\"decimal\",\"float\",\"double\",\"bit\",\"varchar\"]}")
    private List<String> numberTypes;

    public List<TaskInfoResponse> taskList(String appName) {
        List<ArchiveTaskDomain> archiveTaskDomainList = archiveDomainService.getTaskList(appName);
        if (CollectionUtils.isEmpty(archiveTaskDomainList)) {
            return Collections.emptyList();
        }
        return archiveTaskDomainList.stream().map(taskConfig -> {
                    List<ArchiveTaskDetailResponse> taskDetails = Collections.emptyList();
                    if (!CollectionUtils.isEmpty(taskConfig.getTaskDetails())) {
                        taskDetails = taskConfig.getTaskDetails().stream().map(v -> convertFromTaskDetail(v)).collect(Collectors.toList());
                    }
                    return new TaskInfoResponse().setTaskName(taskConfig.getTaskName()).setDataSourceName(taskConfig.getDatasourceName())
                            .setLimit(taskConfig.getLimit()).setTaskDetails(taskDetails)
                            .setInterval(taskConfig.getInterval()).setAppName(appName)
                            .setStatus(taskConfig.getStatus()).setDatabaseName(taskConfig.getDatabaseName())
                            .setMode(taskConfig.getMode()).setKey(taskConfig.getKey())
                            .setExecutionType(taskConfig.getExecutionType())
                            .setTimeSpan(buildTimeSpanFromStartEnd(taskConfig.getStart(), taskConfig.getEnd()));
                }
        ).collect(Collectors.toList());
    }

    private ArchiveTaskDetailResponse convertFromTaskDetail(ArchiveTaskDetailDomain taskDetailConfig) {
        ArchiveTaskDetailResponse archiveTaskDetailResponse = new ArchiveTaskDetailResponse();
        BeanUtils.copyProperties(taskDetailConfig, archiveTaskDetailResponse);
        Optional.ofNullable(taskDetailConfig.getProps().get(INDEX_COLUMN_TYPE_KEY)).ifPresent(v -> archiveTaskDetailResponse.setIndexColumType(v.toString()));
        if (!CollectionUtils.isEmpty(taskDetailConfig.getChildren())) {
            archiveTaskDetailResponse.setChildren(taskDetailConfig.getChildren().stream().map(v -> {
                ArchiveTaskDetailResponse response = convertFromTaskDetail(v);
                response.setParentKey(archiveTaskDetailResponse.getKey());
                return response;
            }).collect(Collectors.toList()));
        }
        return archiveTaskDetailResponse;
    }

    public TaskCreateResponse createTask(TaskInfoRequest request) {
        validateIndexColumn(request);
        ArchiveTaskDomain createTask = convertTaskConfigFromRequest(request);
        if (CollectionUtils.isEmpty(createTask.getTaskDetails())) {
            return new TaskCreateResponse().setSuccess(false).setWarnings("未配置任何表");
        }
        String key = UUID.randomUUID().toString().replace("-", "");
        ArchiveDataConfig archiveDataConfig = getTaskConfig(request.getAppName());
        createTask.getTaskDetails().get(0).setRoot(true);
        createTask.setKey(key);
        if (archiveDataConfig != null) {
            if (!CollectionUtils.isEmpty(archiveDataConfig.getTasks())) {
                TaskConfig taskConfig = archiveDataConfig.getTasks().stream().filter(taskConfig1 -> taskConfig1.getTaskName().equals(request.getTaskName()))
                        .findFirst().orElse(null);
                if (taskConfig != null) {
                    throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "任务名称已经存在");
                }
            }
        }
        ArchiveGlobal archiveGlobal = new ArchiveGlobal().setAppName(request.getAppName()).setDatasourceName(request.getDataSourceName())
                .setDatabaseName(request.getDatabaseName());
        boolean result = archiveDomainService.saveOrUpdate(createTask, archiveGlobal);
        return new TaskCreateResponse().setSuccess(result);
    }

    private void validateIndexColumn(TaskInfoRequest request) {
        if (StringUtils.hasLength(request.getIndexColumType())) {
            String type = request.getIndexColumType().trim();
            if (request.getIndexColumType().contains("(")) {
                type = type.substring(0, type.indexOf("(")).trim();
            }
            if (dateTypes.contains(type)) {
                request.setIndexType(INDEX_TYPE_RESERVE_DAYS);
                if (StringUtils.isEmpty(request.getConditions()) && (request.getReserveDays() == null || request.getReserveDays() <= 0)) {
                    throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "没有过滤条件时，保留天数必填");
                }
            } else if (numberTypes.contains(type)) {
                request.setIndexType(INDEX_TYPE_SPAN);
                if ((request.getIndexStart() == null || request.getIndexStart() <= 0) && (request.getIndexEnd() == null || request.getIndexEnd() <= 0)) {
                    if (StringUtils.isEmpty(request.getConditions())) {
                        throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "区间值过滤时，过滤条件必填，避免误删");
                    }
                }
                request.setIndexValue((request.getIndexStart() == null ? "" : request.getIndexStart()) + "-" + (request.getIndexEnd() == null ? "" : request.getIndexEnd()));
            } else {
                throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "索引列类型不支持");
            }
        }
    }

    private ArchiveTaskDomain convertTaskConfigFromRequest(TaskInfoRequest request) {
        ArchiveTaskDomain taskConfig = new ArchiveTaskDomain();
        taskConfig.setTaskName(request.getTaskName());
        taskConfig.setDatasourceName(request.getDataSourceName());
        taskConfig.setLimit(request.getLimit());
        taskConfig.setInterval(request.getInterval());
        taskConfig.setStatus(0);
        taskConfig.setDatabaseName(request.getDatabaseName());
        taskConfig.setMode(ArchiveConstants.MODE_DEBUG);
        taskConfig.setExecutionType(request.getExecutionType());
        if (Objects.equals(ARCHIVE_TYPE_SPECIFIC_TIME, request.getExecutionType())) {
            setTimeSpan(request.getTimeSpan(), taskConfig);
        }
        taskConfig.setDataSourceUrl(request.getDatasourceUrl());
        ArchiveTaskDetailDomain taskDetailConfig = new ArchiveTaskDetailDomain();
        taskDetailConfig.setTableName(request.getTableName());
        taskDetailConfig.setArchive(request.getArchive());
        taskDetailConfig.setConditions(request.getConditions());
        // 设置索引相关属性
        taskDetailConfig.setIndexColumn(request.getIndexColumn());
        taskDetailConfig.setOrderBy(request.getOrderBy());
        taskDetailConfig.setIndexType(request.getIndexType());
        taskDetailConfig.setIndexStart(request.getIndexStart());
        taskDetailConfig.setIndexEnd(request.getIndexEnd());
        taskDetailConfig.setReserveDays(request.getReserveDays());
        taskDetailConfig.setFilterDefaultValue(request.getFilterDefaultValue());
        taskDetailConfig.setArchiveType(request.getArchiveType());
        taskDetailConfig.setEnableScan(request.isEnableScan());
        taskDetailConfig.getProps().put(INDEX_COLUMN_TYPE_KEY, request.getIndexColumType());
        List<String> plugins = new ArrayList<>();
        if (Objects.equals(request.getArchiveType(), ARCHIVE_TYPE_PARENT)) { // 默认先归档子节点，需要先归档父节点则启用插件
            plugins.add("com.shizhuang.duapp.scp.framework.archive.core.plugin.impl.ParentFirstArchivePlugin");
        }
        if (Objects.equals(request.getFilterDefaultValue(), FILTER_DEFAULT_VALUE)) {
            plugins.add("com.shizhuang.duapp.scp.framework.archive.core.plugin.impl.SafeDeletePlugin");
        }
        taskDetailConfig.setPlugins(plugins);
        taskDetailConfig.setRelationConditions(request.getRelationConditions());
        List<ArchiveTaskDetailDomain> configList = Arrays.asList(taskDetailConfig);
        taskConfig.setTaskDetails(configList);
        return taskConfig;
    }

    private ArchiveTaskDomain convertTaskConfigFromRequest(ModifyTaskRequest request) {
        ArchiveTaskDomain taskConfig = new ArchiveTaskDomain();
        taskConfig.setTaskName(request.getTaskName());
        taskConfig.setDatasourceName(request.getDataSourceName());
        taskConfig.setDatabaseName(request.getDatabaseName());
        taskConfig.setLimit(request.getLimit());
        taskConfig.setInterval(request.getInterval());
        taskConfig.setStatus(request.getStatus());
        taskConfig.setMode(request.getMode());
        taskConfig.setExecutionType(request.getExecutionType());
        if (ARCHIVE_TYPE_SPECIFIC_TIME.equals(request.getExecutionType())) {
            setTimeSpan(request.getTimeSpan(), taskConfig);
        } else {
            taskConfig.setStart(null);
            taskConfig.setEnd(null);
        }
        taskConfig.setDataSourceUrl(request.getDatasourceUrl());
        return taskConfig;
    }

    private void setTimeSpan(String timespan, ArchiveTaskDomain taskConfig) {
        String[] array = timespan.split("-");
        taskConfig.setStart(array[0]);
        taskConfig.setEnd(array[1]);
    }

    public ArchiveDataConfig getTaskConfig(String appName) {
        return archiveDataPersistService.getArchiveDataConfig(appName);
    }

    public TaskCreateResponse modifyTask(@Valid ModifyTaskRequest request) {
        ArchiveTaskDomain taskConfig = convertTaskConfigFromRequest(request);
        List<ArchiveTaskDomain> taskDomains = archiveDomainService.getTaskList(request.getAppName());
        ArchiveTaskDomain existTask = CollectionUtils.isEmpty(taskDomains) ? null : taskDomains.stream().filter(task -> Objects.equals(task.getKey(), request.getKey())).findFirst().orElse(null);
        if (existTask == null) {
            return new TaskCreateResponse().setSuccess(false).setWarnings("未查询到任务配置，请检查项目配置");
        }
        if (!request.isIgnoreWarnings()) {
            if (!StringUtils.isEmpty(request.getDatasourceUrl()))
                if (!existTask.getDataSourceUrl().equals(request.getDatasourceUrl())) {
                    return new TaskCreateResponse().setSuccess(false).setWarnings("数据源地址与已有任务不一致，请务必核实，以免造成事故");
                }
        }
        ArchiveGlobal archiveGlobal = new ArchiveGlobal()
                .setDatasourceName(request.getDataSourceName())
                .setDatabaseName(request.getDatabaseName())
                .setAppName(request.getAppName());
        BeanUtils.copyProperties(taskConfig, existTask, "taskDetails", "key");
        boolean result = archiveDomainService.saveOrUpdate(existTask, archiveGlobal);
        return new TaskCreateResponse().setSuccess(result);
    }

    public void updateStatus(@Valid UpdateTaskStatusRequest request) {
        if (request.isDelete()) {
            ArchiveDataConfig archiveDataConfig = archiveDataPersistService.getArchiveDataConfig(request.getAppName());
            if (archiveDataConfig == null || CollectionUtils.isEmpty(archiveDataConfig.getTasks())) {
                return;
            }
            List<TaskConfig> tasks = archiveDataConfig.getTasks().stream().filter(taskConfig -> !Objects.equals(taskConfig.getTaskName(), request.getTaskName()))
                    .collect(Collectors.toList());
            archiveDataConfig.setTasks(tasks);
            archiveDataPersistService.saveConfig(archiveDataConfig, request.getAppName());
            return;
        }
        ArchiveTaskDomain archiveTaskDomain = archiveDomainService.getTask(request.getAppName(), request.getTaskName());
        if (request.getStatus() != null) {
            archiveTaskDomain.setStatus(request.getStatus());
        }
        if (request.getMode() != null) {
            archiveTaskDomain.setMode(request.getMode());
        }
        if (request.getAllDay() != null) {
            if (request.getAllDay()) {
                archiveTaskDomain.setExecutionType(ARCHIVE_TYPE_ALL_DAY);
            } else if (StringUtils.hasLength(request.getTimeSpan())) {
                setTimeSpan(request.getTimeSpan(), archiveTaskDomain);
            }
        }

        ArchiveGlobal archiveGlobal = new ArchiveGlobal()
                .setDatasourceName(archiveTaskDomain.getDatasourceName())
                .setDatabaseName(archiveTaskDomain.getDatabaseName())
                .setAppName(request.getAppName());
        archiveDomainService.saveOrUpdate(archiveTaskDomain, archiveGlobal);
    }

    public TaskInfoResponse getTaskInfo(String appName, String taskName) {
        List<TaskInfoResponse> taskInfoResponses = taskList(appName);
        if (CollectionUtils.isEmpty(taskInfoResponses)) {
            return null;
        }
        return taskInfoResponses.stream().filter(e -> e.getTaskName().equals(taskName))
                .findFirst().orElse(null);
    }

    public ArchivePreviewResponse preview(ArchivePreviewRequest request) {
        ArchiveTaskDomain archiveTaskDomain = new ArchiveTaskDomain();
        ArchiveTaskConfigTransfer.INSTANCE.archivePreviewRequestTODomain(request, archiveTaskDomain);
        Result<PreviewResponse> result = archiveDomainService.preview(archiveTaskDomain, request.getAppName(), request.getCurrentKey());
        ArchivePreviewResponse re = new ArchivePreviewResponse();
        BeanUtils.copyProperties(result.getData(), re);
        return re;
    }

    public void globalConfig(@Valid ArchiveGlobalConfigRequest request) {
        ArchiveDataConfig archiveDataConfig = getTaskConfig(request.getAppName());
        if (archiveDataConfig == null) {
            archiveDataConfig = new ArchiveDataConfig();
        }
        if (request.getThreads() != null) {
            archiveDataConfig.setThreads(request.getThreads());
        }
        if (request.getEnable() != null) {
            archiveDataConfig.setEnable(request.getEnable());
        }
        if (request.getInterval() != null) {
            archiveDataConfig.setInterval(request.getInterval());
        }
        if (request.getLockExpireSeconds() != null) {
            archiveDataConfig.setLockExpireSeconds(request.getLockExpireSeconds());
        }
        archiveDataPersistService.saveConfig(archiveDataConfig, request.getAppName());
    }

    public ArchiveGlobalConfigResponse getGlobalConfig(String appName) {
        ArchiveDataConfig archiveDataConfig = getTaskConfig(appName);
        ArchiveGlobalConfigResponse response = new ArchiveGlobalConfigResponse();
        if (archiveDataConfig != null) {
            response.setThreads(archiveDataConfig.getThreads());
            response.setEnable(archiveDataConfig.isEnable());
            response.setInterval(archiveDataConfig.getInterval());
            response.setLockExpireSeconds(archiveDataConfig.getLockExpireSeconds());
        }
        return response;
    }

    public ArchiveTaskDetailResponse addTable(AddTableRequest request) {
        ArchiveTaskDomain archiveTaskDomain = archiveDomainService.getTask(request.getAppName(), request.getTaskName());
        if (archiveTaskDomain == null || CollectionUtils.isEmpty(archiveTaskDomain.getTaskDetails())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "任务不存在");
        }
        if (!StringUtils.hasLength(request.getKey()) && isTableExistsInTask(archiveTaskDomain.getTaskDetails(), request.getTableName())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "任务中已经存在该表");
        }
        ArchiveTaskDetailDomain parentNode = null;
        String relationConditions = request.getRelationConditions();
        if (request.getRoot() == null || !request.getRoot()) {
            parentNode = getNodeByKey(archiveTaskDomain.getTaskDetails(), request.getParentKey());
            if (parentNode == null) {
                throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "父级节点不存在");
            }
            // 校验关联条件
            if (relationConditions == null || relationConditions.trim().isEmpty()) {
                throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "关联条件不能为空");
            }
        }
        validateIndexColumn(request);
        // 将request转换为TaskDetailConfig
        ArchiveTaskDetailDomain newTaskDetail = createTaskDetailConfigFromRequest(request, null);
        if (request.getRoot() != null && request.getRoot()) {
            if (CollectionUtils.isEmpty(archiveTaskDomain.getTaskDetails())) {
                if (archiveTaskDomain.getTaskDetails() == null) {
                    archiveTaskDomain.setTaskDetails(new ArrayList<>());
                }
                archiveTaskDomain.getTaskDetails().add(newTaskDetail);
            } else {
                BeanUtils.copyProperties(newTaskDetail, archiveTaskDomain.getTaskDetails().get(0), "children");
            }
        } else {
            // 添加到父节点的children中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            if (StringUtils.hasLength(request.getKey())) {
                ArchiveTaskDetailDomain current = parentNode.getChildren().stream().filter(t -> Objects.equals(t.getTableName(), request.getKey())).findFirst().orElse(null);
                if (current == null) {
                    throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "编辑失败，当前节点不存在或者已经删除");
                }
                BeanUtils.copyProperties(newTaskDetail, current);
            } else {
                parentNode.getChildren().add(newTaskDetail);
            }
        }
        ArchiveGlobal archiveGlobal = new ArchiveGlobal().setAppName(request.getAppName()).setDatasourceName(archiveTaskDomain.getDatasourceName())
                .setDatabaseName(archiveTaskDomain.getDatabaseName());
        archiveDomainService.saveOrUpdate(archiveTaskDomain, archiveGlobal);
        return convertFromTaskDetail(newTaskDetail);
    }

    private void validateIndexColumn(AddTableRequest request) {
        if (request.getRoot() == null || !request.getRoot()) {
            return;
        }
        if (StringUtils.hasLength(request.getIndexColumType())) {
            String type = request.getIndexColumType().trim();
            if (request.getIndexColumType().contains("(")) {
                type = type.substring(0, type.indexOf("(")).trim();
            }
            request.setIndexColumType(type);
            if (dateTypes.contains(type)) {
                request.setIndexType(INDEX_TYPE_RESERVE_DAYS);
            } else if (numberTypes.contains(type)) {
                request.setIndexType(INDEX_TYPE_SPAN);
            } else {
                throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "索引列类型不支持");
            }
        } else if (StringUtils.isEmpty(request.getConditions())) {
            throw new BizRuntimeException(ErrorCode.PARAM_ERROR.getCode(), "索引和过滤条件不能全部为空");
        }
    }

    private boolean isTableExistsInTask(List<ArchiveTaskDetailDomain> taskDetails, String tableName) {
        for (ArchiveTaskDetailDomain taskDetail : taskDetails) {
            if (tableName.equalsIgnoreCase(taskDetail.getTableName())) {
                return true;
            }
            if (!CollectionUtils.isEmpty(taskDetail.getChildren()) && isTableExistsInTask(taskDetail.getChildren(), tableName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从AddTableRequest创建TaskDetailConfig
     *
     * @param request          添加表的请求
     * @param primaryKeyColumn 主键列名（如有）
     * @return TaskDetailConfig对象
     */
    private ArchiveTaskDetailDomain createTaskDetailConfigFromRequest(AddTableRequest request, String primaryKeyColumn) {
        ArchiveTaskDetailDomain taskDetailConfig = new ArchiveTaskDetailDomain();
        // 设置基本属性
        taskDetailConfig.setTableName(request.getTableName());
        taskDetailConfig.setConditions(request.getConditions());
        taskDetailConfig.setRelationConditions(request.getRelationConditions());
        // 设置主键
        if (primaryKeyColumn != null) {
            taskDetailConfig.setPrimaryKeyColumn(primaryKeyColumn);
        }
        // 设置索引相关属性
        taskDetailConfig.setIndexColumn(request.getIndexColumn());
        taskDetailConfig.setOrderBy(request.getOrderBy());
        taskDetailConfig.setIndexType(request.getIndexType());
        taskDetailConfig.setIndexStart(request.getIndexStart());
        taskDetailConfig.setIndexEnd(request.getIndexEnd());
        taskDetailConfig.setReserveDays(request.getReserveDays());
        // 设置归档相关属性
        taskDetailConfig.setArchive(request.getArchive());
        taskDetailConfig.setArchiveType(request.getArchiveType());
        taskDetailConfig.setFilterDefaultValue(request.getFilterDefaultValue());
        taskDetailConfig.setRoot(request.getRoot() == null ? false : request.getRoot());
        taskDetailConfig.setEnableScan(request.isEnableScan());
        if (StringUtils.hasLength(request.getIndexColumType())) {
            taskDetailConfig.getProps().put(INDEX_COLUMN_TYPE_KEY, request.getIndexColumType());
        }
        // 设置插件
        List<String> plugins = new ArrayList<>();
        if (Objects.equals(request.getArchiveType(), ARCHIVE_TYPE_PARENT)) {
            // 默认先归档子节点，需要先归档父节点则启用插件
            plugins.add("com.shizhuang.duapp.scp.framework.archive.core.plugin.impl.ParentFirstArchivePlugin");
        }
        if (Objects.equals(request.getFilterDefaultValue(), FILTER_DEFAULT_VALUE)) {
            plugins.add("com.shizhuang.duapp.scp.framework.archive.core.plugin.impl.SafeDeletePlugin");
        }
        taskDetailConfig.setPlugins(plugins);
        return taskDetailConfig;
    }

    private ArchiveTaskDetailDomain getNodeByKey(List<ArchiveTaskDetailDomain> taskDetails, String key) {
        if (CollectionUtils.isEmpty(taskDetails)) {
            return null;
        }
        for (ArchiveTaskDetailDomain taskDetail : taskDetails) {
            if (Objects.equals(taskDetail.getTableName(), key)) {
                return taskDetail;
            }
            if (!CollectionUtils.isEmpty(taskDetail.getChildren())) {
                ArchiveTaskDetailDomain sub = getNodeByKey(taskDetail.getChildren(), key);
                if (sub != null) {
                    return sub;
                }
            }
        }
        return null;
    }

    public void deleteTable(@Valid DeleteTableRequest request) {
        ArchiveDataConfig archiveDataConfig = getTaskConfig(request.getAppName());
        if (archiveDataConfig == null) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "未找到任务配置");
        }
        if (CollectionUtils.isEmpty(archiveDataConfig.getTasks())) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "未找到任务配置");
        }
        TaskConfig taskConfig = archiveDataConfig.getTasks().stream().filter(t -> t.getTaskName().equals(request.getTaskName()))
                .findFirst().orElse(null);
        if (taskConfig == null) {
            throw new BizRuntimeException(BIZ_VALIDATE_ERROR.getCode(), "未找到任务配置");
        }
        List<TaskDetailConfig> taskDetails = taskConfig.getTaskDetails();
        deleteByKey(taskDetails, request.getKey());
        archiveDataPersistService.saveConfig(archiveDataConfig, request.getAppName());
    }

    private void deleteByKey(List<TaskDetailConfig> taskDetails, String key) {
        for (TaskDetailConfig taskDetail : taskDetails) {
            if (taskDetail.getTableName().equals(key)) {
                taskDetails.remove(taskDetail);
                return;
            }
            if (taskDetail.getChildren() != null && taskDetail.getChildren().size() > 0) {
                deleteByKey(taskDetail.getChildren(), key);
            }
        }
    }
    
    /**
     * 从 start 和 end 字段构建时间范围字符串
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间范围字符串，格式：HH:mm-HH:mm
     */
    private String buildTimeSpanFromStartEnd(String start, String end) {
        if (StringUtils.hasLength(start) && StringUtils.hasLength(end)) {
            return start + "-" + end;
        }
        return "";
    }
    
    /**
     * 从 props 字段中提取时间范围字符串
     * 
     * @param props JSON 格式的 props 字段
     * @return 时间范围字符串，格式：HH:mm-HH:mm
     */
    private String extractTimeSpanFromProps(String props) {
        if (props == null || props.trim().isEmpty()) {
            return "";
        }
        
        try {
            TaskPropsConfig timeConfig = TaskPropsConfigUtil.deserialize(props);
            if (timeConfig != null && timeConfig.getTimespan() != null) {
                return timeConfig.getTimespan();
            }
            // 如果没有 timespan，但有 start 和 end，则拼接
            if (timeConfig != null && timeConfig.getStart() != null && timeConfig.getEnd() != null) {
                return timeConfig.getStart() + "-" + timeConfig.getEnd();
            }
        } catch (Exception e) {
            log.error("解析时间配置失败: props={}, error={}", props, e.getMessage(), e);
            throw new RuntimeException("解析时间配置失败: props=" + props, e);
        }
        
        return "";
    }
}
