<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.shizhuang.scm</groupId>
        <artifactId>scm-rulecenter-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scm-rulecenter-application</artifactId>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.shizhuang.scm</groupId>
            <artifactId>scm-rulecenter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shizhuang.scm</groupId>
            <artifactId>scm-rulecenter-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shizhuang.scm</groupId>
            <artifactId>scm-rulecenter-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-adapt-avatar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shizhuang.duapp</groupId>
            <artifactId>scp-archive-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shizhuang.duapp</groupId>
            <artifactId>scp-master-data-api</artifactId>
        </dependency>
    </dependencies>
</project>
