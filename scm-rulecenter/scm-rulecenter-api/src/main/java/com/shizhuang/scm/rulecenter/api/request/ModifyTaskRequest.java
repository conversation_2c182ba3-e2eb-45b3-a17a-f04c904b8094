package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

@Data
public class ModifyTaskRequest implements Serializable {

    private String appName;

    /**
     * 任务名称
     */
    @Length(max = 64, message = "任务名称长度不能超过64")
    private String taskName;

    /**
     * 数据源名称
     */
    @NotEmpty(message = "数据源名称不能为空")
    private String dataSourceName;

    @NotEmpty(message = "数据库名称不能为空")
    private String databaseName;

    /**
     * 数据源连接
     */
    private String datasourceUrl;

    /**
     * 扫描数量 默认100条
     */
    private Integer limit = 100;

    /**
     * 扫描间隔 默认30秒
     */
    private Integer interval = 30;

    /**
     * 0:暂停 1:运行
     */
    private Integer status = 0;

    /**
     * 1 debug 2 生产
     */
    private Integer mode;

    /**
     * 自定义执行时间段 如：21:00-6:00
     */
    @Pattern(regexp = "^(?:[01]?[0-9]|2[0-3]):[0-5][0-9]-(?:[01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "格式错误，示例:22:00-7:00")
    private String timeSpan;

    /**
     * 忽略警告
     */
    private boolean ignoreWarnings;

    private Integer executionType;

    @NotEmpty(message = "任务Key不能为空")
    private String key;
}
