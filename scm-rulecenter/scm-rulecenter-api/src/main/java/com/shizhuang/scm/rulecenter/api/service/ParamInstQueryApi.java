package com.shizhuang.scm.rulecenter.api.service;

import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.query.InstDimQuery;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.query.SchemaDetailQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.InstDimResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;
import com.shizhuang.scm.rulecenter.api.response.SchemaDetailResponse;

import java.util.List;
import java.util.Map;

/**
 * 业务参数配置中心参数实例服务
 */
public interface ParamInstQueryApi {

    /**
     * 查询方案明细
     * @param schemaDetailQuery
     * @return
     */
    Result<SchemaDetailResponse> querySchemaDetail(SchemaDetailQuery schemaDetailQuery);

    /**
     * 查询参数实例-单点 带缓存，性能好
     * @param instDimQuery
     * @return
     */
    Result<InstDimResponse> queryByDimension(InstDimQuery instDimQuery);


    /**
     * 批量查询参数实例
     * @param instDimQueries
     * @return
     */
    Result<Map<InstDimQuery,InstDimResponse>> batchQueryByDimension(List<InstDimQuery> instDimQueries);

    /**
     * 查询参数实例-分页
     * @param instPageQuery
     * @return
     */
    Result<PageInfoResponse<InstDimPageResponse>> queryParamInstPage(InstPageQuery instPageQuery);

}
