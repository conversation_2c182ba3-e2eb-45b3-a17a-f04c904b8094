package com.shizhuang.scm.rulecenter.api.constant;

public enum ParamInstEnableEnum {

    ENABLED(1, "生效"),
    DISABLED(0, "失效");
    private Integer type;

    private String desc;

    ParamInstEnableEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ParamInstEnableEnum of(Integer type) {
        if (type == 1) {
            return ParamInstEnableEnum.ENABLED;
        } else if (type == 0) {
            return ParamInstEnableEnum.DISABLED;
        }
        return null;
    }

}
