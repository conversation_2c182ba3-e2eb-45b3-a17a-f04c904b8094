package com.shizhuang.scm.rulecenter.api.query;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 元素分页查询
 */
@Getter
@Setter
public class ElementPageQuery implements Serializable {

    /**
     * 页码
     */
    Integer  pageNum;

    /**
     * 页大小
     */
    Integer  pageSize;

    /**
     * 元素code
     */
    String elementCode;

    /**
     * 元素名称
     */
    String elementName;


    /**
     * 元素来源类型  1公共元素 2自定义方案元素
     */
    Byte elementSourceType;

}
