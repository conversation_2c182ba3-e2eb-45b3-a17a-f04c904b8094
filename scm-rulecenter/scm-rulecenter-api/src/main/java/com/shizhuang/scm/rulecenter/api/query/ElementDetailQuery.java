package com.shizhuang.scm.rulecenter.api.query;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
public class ElementDetailQuery implements Serializable {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 元素code
     */
    @NotEmpty
    String elementCode;

    /**
     * 查询元素可能用到的外部参数
     */
    Map<String,Object> extParam;

}
