package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class SchemaElementCreateCmd {

    /**
     * 元素code
     */
    String elementCode;

    /**
     * 元素来源类型  1公共元素 2自定义方案元素
     */
    @NotNull(message = "元素来源类型不能为空")
    Integer elementSourceType;

    /**
     * 是否为搜索条件 0否 1是
     */
    Integer queryCondition;

    /**
     * 是否为必填 0否 1是
     */
    Integer isRequired;

    /**
     * 是否为多选 0否 1是
     */
    Integer isMulti;

}
