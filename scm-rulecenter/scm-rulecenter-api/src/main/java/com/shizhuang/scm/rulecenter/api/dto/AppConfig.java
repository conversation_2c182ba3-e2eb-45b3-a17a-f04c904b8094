package com.shizhuang.scm.rulecenter.api.dto;

import java.io.Serializable;

/**
 * 应用配置DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AppConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 是否启用归档功能
     */
    private Boolean enable;

    /**
     * 归档执行线程数
     */
    private Integer threads;

    /**
     * 分布式锁过期时间（秒）
     */
    private Integer lockExpireSeconds;

    /**
     * 任务扫描间隔（秒）
     */
    private Integer interval;

    /**
     * 最大线程数
     */
    private Integer maxThreads;

    /**
     * 配置版本号
     */
    private Long version;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    // Getters and setters

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getThreads() {
        return threads;
    }

    public void setThreads(Integer threads) {
        this.threads = threads;
    }

    public Integer getLockExpireSeconds() {
        return lockExpireSeconds;
    }

    public void setLockExpireSeconds(Integer lockExpireSeconds) {
        this.lockExpireSeconds = lockExpireSeconds;
    }

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public Integer getMaxThreads() {
        return maxThreads;
    }

    public void setMaxThreads(Integer maxThreads) {
        this.maxThreads = maxThreads;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}