package com.shizhuang.scm.rulecenter.api.response;

import com.shizhuang.scm.rulecenter.api.dto.ConstantDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class MetaConstantResponse implements Serializable {

    /**
     * 元素来源类型枚举 1公共元素 2自定义
     */
    List<ConstantDTO> elementSourceTypeEnums;

    /**
     * 元素类型枚举 1参数 2维度
     */
    List<ConstantDTO> elementTypeEnums;

    /**
     * 方案状态枚举 0草稿 1已上线 2已下线
     */
    List<ConstantDTO> schemaStatusEnums;

    /**
     * 值范围枚举 1枚举 2-dubbo全量 21-dubbo单点 3-自定义文本
     */
    List<ConstantDTO> valueRangeTypeEnums;

}
