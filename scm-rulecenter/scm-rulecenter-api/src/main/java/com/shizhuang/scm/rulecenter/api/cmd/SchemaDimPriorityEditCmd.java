package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 方案维度优先级编辑
 */
@Getter
@Setter
public class SchemaDimPriorityEditCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;

    /**
     * 方案下维度优先级
     */
    @NotEmpty(message = "方案下维度优先级不能为空")
    List<DimPriorityCreateCmd> priority;


    /**
     * 排序类型 0按自定义优先级排序，1按业务字段排序
     */
    Integer dimPriType;


    /**
     * 排序字段
     */
    String orderField;

    /**
     * 排序类型 asc正序 desc倒序
     */
    String orderType;

}
