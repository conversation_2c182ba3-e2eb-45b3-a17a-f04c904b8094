package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class DeleteTableRequest implements Serializable {

    @NotEmpty(message = "app名称不能为空")
    private String appName;

    /**
     * 任务名称
     */
    @Length(max = 64, message = "任务名称长度不能超过64")
    private String taskName;

    /**
     * 父级节点
     */
    @NotEmpty(message = "删除节点不能为空")
    private String key;

}
