package com.shizhuang.scm.rulecenter.api.constant;

public enum ParamInstOptLogEnum {

    CREATE((byte)1, "创建参数"),
    EDIT((byte)2, "修改参数"),
    ENABLED((byte)4, "生效参数方案"),
    DISABLED((byte)3, "失效参数方案")

    ;
    private Byte type;

    private String desc;

    ParamInstOptLogEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

}
