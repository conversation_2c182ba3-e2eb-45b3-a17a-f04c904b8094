package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量创建元素
 */
@Getter
@Setter
public class ElementBatchCreateCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull
    String schemaCode;

    /**
     * 批量创建元素列表
     */
    @NotEmpty
    List<ElementCreateCmd> createCmdList;

}
