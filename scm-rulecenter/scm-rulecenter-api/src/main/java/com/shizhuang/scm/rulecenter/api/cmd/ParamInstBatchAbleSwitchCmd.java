package com.shizhuang.scm.rulecenter.api.cmd;

import com.shizhuang.scm.rulecenter.api.dto.DimensionKeyDTO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Getter
@Setter
public class ParamInstBatchAbleSwitchCmd implements Serializable {

    /**
     * 批量dimensionKey
     */
    List<DimensionKeyDTO> dimensionKeys;


    /**
     * 参数聚合id
     */
    List<String> aggIds;

    /**
     * 操作人
     */
    String optUser;

}
