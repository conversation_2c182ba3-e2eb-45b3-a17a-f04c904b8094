package com.shizhuang.scm.rulecenter.api.query;

import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 根据维度查询参数实例请求体
 */
@Getter
@Setter
public class InstDimQuery implements Serializable {

    /**
     * 方案code
     */
    @NotEmpty
    String schemaCode;

    /**
     * 查询维度+维度值
     */
    @NotEmpty
    List<ElementInstDTO> queryDimension;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InstDimQuery)) return false;
        InstDimQuery that = (InstDimQuery) o;
        return Objects.equals(schemaCode, that.schemaCode) && Objects.equals(queryDimension, that.queryDimension);
    }

    @Override
    public int hashCode() {
        return Objects.hash(schemaCode, queryDimension);
    }
}
