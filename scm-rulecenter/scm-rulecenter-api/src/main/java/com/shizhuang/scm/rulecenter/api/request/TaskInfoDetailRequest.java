package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

@Data
public class TaskInfoDetailRequest implements Serializable {

    @NotEmpty(message = "表名不能为空")
    private String tableName;

    private String conditions;

    private Integer indexType;

    private String indexColumn;

    /**
     * asc desc
     */
    private String orderBy;

    /**
     * 索引字段的值-开始
     */
    private Long indexStart;

    /**
     * 索引字段的值-结束
     */
    private Long indexEnd;

    /**
     * 保留天数
     */
    private Integer reserveDays;

    /**
     * 1 先归档子节点 2 先归档父节点
     */
    private Integer archiveType;

    /**
     * 默认值过滤 0||null 不过滤 1 过滤
     */
    private Integer filterDefaultValue;

    /**
     * 关联关系
     */
    @Pattern(regexp = "^\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*\\.\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*=\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*\\.\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*$", message = "关联关系请使用格式 pink_operate_item.outbound_detail_id = pink_outbound_detail.id")
    private String relationConditions;

    private List<TaskInfoDetailRequest> children;

    /**
     * 是否归档
     */
    private Boolean archive;
}