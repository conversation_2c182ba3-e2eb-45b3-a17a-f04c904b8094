package com.shizhuang.scm.rulecenter.api.query;

import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 参数实例查询请求
 */
@Getter
@Setter
public class InstPageQuery implements Serializable {

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    Integer  pageNum;

    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    Integer  pageSize;

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;

    /**
     * 查询参数
     */
    List<ElementInstDTO> queryParams;

    /**
     * 排序字段
     */
    String orderBy;


    /**
     * 是否需要缺省参数，默认为0 不查字段为null的参数实例，如果为1则查字段为null的情况
     * ，因为某些条件下，null代表全部，符合某些业务场景
     */
    Integer needDefaultParam = 0;

    /**
     * 是否需要校验用户关联仓库查询条件
     */
    Boolean checkAuthUserWarehouse = false;

}
