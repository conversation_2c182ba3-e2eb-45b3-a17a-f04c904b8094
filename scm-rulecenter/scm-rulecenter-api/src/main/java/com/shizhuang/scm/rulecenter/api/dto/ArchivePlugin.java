package com.shizhuang.scm.rulecenter.api.dto;

import java.io.Serializable;

/**
 * 归档插件DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ArchivePlugin implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 插件名称
     */
    private String pluginName;

    /**
     * 插件优先级
     */
    private Integer priority;

    // Getters and setters

    public String getPluginName() {
        return pluginName;
    }

    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
}