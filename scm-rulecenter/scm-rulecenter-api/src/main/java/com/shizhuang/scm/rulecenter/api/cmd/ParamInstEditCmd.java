package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ParamInstEditCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;


    /**
     * 参数聚合id
     */
    String aggId;


    /**
     * 编辑参数版本
     */
    Integer version;

    /**
     * 参数实例入参
     */
    List<ElementInstAddCmd> paramMap;

}
