package com.shizhuang.scm.rulecenter.api.query;

public class RedisKeyHelper {

    public static final String ELEMENT_ENTITY_KEY_PREFIX = "s_rc_ee_%s_%s";
    public static final String ELEMENT_ENTITY_WITH_ENUM_KEY_PREFIX = "s_rc_eee_%s_%s";
    public static final String CUSTOM_DIM_KEY_PREFIX = "s_rc_cdk_v1_%s_%s_%s";
    public static final String DIM_KEY_PREFIX = "s_rc_dk_%s_%s_%s";
    public static final String SCHEMA_DETAIL_KEY_PREFIX = "s_dt_k1_%s_%s";

    public static String getElementEntityKey(String schemaCode,String elementCode){
        return String.format(ELEMENT_ENTITY_KEY_PREFIX,schemaCode,elementCode);
    }

    public static String getElementEntityWithEnumKey(String schemaCode,String elementCode){
        return String.format(ELEMENT_ENTITY_WITH_ENUM_KEY_PREFIX,schemaCode,elementCode);
    }

    public static String getCustomDimKey(String schemaCode,String elementCodes,String elementValues){
        return String.format(CUSTOM_DIM_KEY_PREFIX,schemaCode,elementCodes,elementValues);
    }

    public static String getRedisDimKey(String schemaCode, String dimension, String dimensionKey){
        return String.format(DIM_KEY_PREFIX,schemaCode,dimension,dimensionKey);
    }

    public static String getSchemaDetailKey(String schemaCode, String isLargestVersion) {
        return String.format(SCHEMA_DETAIL_KEY_PREFIX,schemaCode,isLargestVersion);
    }
}
