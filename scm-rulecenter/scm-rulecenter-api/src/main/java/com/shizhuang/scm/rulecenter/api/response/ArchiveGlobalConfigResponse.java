package com.shizhuang.scm.rulecenter.api.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class ArchiveGlobalConfigResponse implements Serializable {

    private String appName;

    private int threads;

    /**
     * 锁时间，默认300秒
     */
    private Integer lockExpireSeconds;

    /**
     * 全局开关
     */
    private boolean enable;

    /**
     * 任务扫描间隔，查询下一个等待执行的任务
     */
    private Integer interval;

}
