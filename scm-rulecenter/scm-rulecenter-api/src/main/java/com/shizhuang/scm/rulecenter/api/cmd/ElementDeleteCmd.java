package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Getter
@Setter
public class ElementDeleteCmd implements Serializable {

    /**
     * 方案code
     */
    @NotBlank(message = "方案code不能为空")
    String schemaCode;

    /**
     * 元素code
     */
    @NotBlank(message = "元素code不能为空")
    String elementCode;

}
