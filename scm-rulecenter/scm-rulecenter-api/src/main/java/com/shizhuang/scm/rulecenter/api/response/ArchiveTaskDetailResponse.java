package com.shizhuang.scm.rulecenter.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ArchiveTaskDetailResponse implements Serializable {

    private String tableName;

    private String conditions;

    private String indexColumn;

    private Integer indexType;

    private Long indexEnd;

    private Long indexStart;

    private String indexColumType;

    private Integer reserveDays;

    /**
     * 生效插件
     */
    private List<String> plugins;

    /**
     * 父级节点表
     */
    private String parentTable;

    /**
     * 关联关系
     */
    private String relationConditions;

    /**
     * 子节点
     */
    private List<ArchiveTaskDetailResponse> children;

    /**
     * 是否归档
     */
    private Boolean archive;

    /**
     * 主键字段
     */
    private String primaryKeyColumn;

    /**
     * 1 先归档子节点 2 先归档父节点
     */
    private Integer archiveType;

    /**
     * 默认值过滤 0||null 不过滤 1 过滤
     */
    private Integer filterDefaultValue;

    public String getKey() {
        return getTableName();
    }

    /**
     * 是否为根节点
     */
    private boolean root;

    private String parentKey;

    /**
     * 是否开启扫描模式
     */
    private boolean enableScan;

}
