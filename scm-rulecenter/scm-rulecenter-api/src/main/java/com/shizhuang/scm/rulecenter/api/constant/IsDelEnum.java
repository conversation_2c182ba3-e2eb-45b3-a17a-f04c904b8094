package com.shizhuang.scm.rulecenter.api.constant;

public enum IsDelEnum {

    YES((byte) 1, "是"),
    NO((byte) 2, "否"),
    ;
    private Byte type;

    private String desc;


    IsDelEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Byte getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static IsDelEnum of(Byte type) {
        if (type == (byte) 1) {
            return IsDelEnum.YES;
        } else if (type == (byte) 0) {
            return IsDelEnum.NO;
        }
        return null;
    }

}
