package com.shizhuang.scm.rulecenter.api.response;

import com.shizhuang.scm.rulecenter.api.dto.DimPriorityDTO;
import com.shizhuang.scm.rulecenter.api.dto.SchemaElementDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 方案明细返回对象
 */
@Getter
@Setter
public class SchemaDetailResponse implements Serializable {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 方案名称
     */
    String schemaName;

    /**
     * 方案描述
     */
    String schemaDesc;

    /**
     * 创建人
     */
    String creator;

    /**
     * 方案状态
     */
    Integer status;

    /**
     * 方案状态名称
     */
    String statusName;

    /**
     * 方案下维度
     */
    List<SchemaElementDTO> dimension;

    /**
     * 方案下参数
     */
    List<SchemaElementDTO> params;

    /**
     * 方案下维度优先级
     */
    List<DimPriorityDTO> priority;

    /**
     * 方案版本
     */
    Integer version;

    /**
     * 菜单
     */
    String menu;

    /**
     * 缓存超时时间
     */
    Integer redisCacheExpireTime;


    /**
     * 业务域名称
     */
    String projectName;

    /**
     * 页面操作配置（用户可以选择是否要导入导出功能等等）
     */
    List<String> actionFeature;

}
