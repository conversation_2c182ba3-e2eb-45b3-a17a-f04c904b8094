package com.shizhuang.scm.rulecenter.api.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum SystemFieldConditionEnum {

    CTIME_START("ctimeStart","创建时间开始","ctime",">=","'%s'"),
    CTIME_END("ctimeEnd","创建时间结束","ctime","<=","'%s'"),
    MTIME_START("mtimeStart","更新时间开始","ctime",">=","'%s'"),
    MTIME_END("mtimeEnd","更新时间结束","ctime","<=","'%s'"),
    ENABLED("enabled","是否生效","enabled","=","%s"),
    CREATOR("creator","创建人","creator","like","'%%%s%%'"),
    MODIFIER("modifier","最后修改人","modifier","like","'%%%s%%'"),

    ;

    @Getter
    String conditionCode;

    @Getter
    String conditionName;

    @Getter
    String fieldCode;

    @Getter
    String condition;

    @Getter
    String stringFormat;


    SystemFieldConditionEnum(String conditionCode, String conditionName, String fieldCode, String condition, String stringFormat) {
        this.conditionCode = conditionCode;
        this.conditionName = conditionName;
        this.fieldCode = fieldCode;
        this.condition = condition;
        this.stringFormat = stringFormat;
    }

    public static final List<String> conditionCodes = Arrays.stream(SystemFieldConditionEnum.values()).map(SystemFieldConditionEnum::getConditionCode).collect(Collectors.toList());

    public static boolean isSystemCondition(String condition){
        return conditionCodes.contains(condition);
    }

    public static SystemFieldConditionEnum of(String conditionCode){
        for (SystemFieldConditionEnum e : SystemFieldConditionEnum.values()) {
            if(Objects.equals(conditionCode,e.getConditionCode())){
                return e;
            }
        }
        return null;
    }

}
