package com.shizhuang.scm.rulecenter.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class SchemaElementDTO implements Serializable {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 元素code
     */
    String elementCode;

    /**
     * 元素名称
     */
    String elementName;


    /**
     * 元素来源类型  1维度 2参数
     */
    Integer elementType;


    /**
     * 元素来源类型  1公共元素 2自定义方案元素
     */
    Integer elementSourceType;

    /**
     * 元素来源类型名称
     */
    String elementSourceTypeName;

    /**
     * 元素取值类型 1枚举，2url，3自定义文本
     */
    Byte valueRangeType;

    /**
     * 元素取值类型名称
     */
    String valueRangeTypeName;

    /**
     * 是否为搜索条件 0否 1是
     */
    Integer queryCondition;

    /**
     * 是否为必填 0否 1是
     */
    Integer isRequired;

    /**
     * 是否为多选 0否 1是
     */
    Integer isMulti;

    /**
     * 元素创建时间
     */
    String createTime;

    /**
     * 元素修改时间
     */
    String modifyTime;

    /**
     * 元素描述
     */
    String elementNote;

    /**
     * 方案内部元素顺序
     */
    String priority;

    /**
     * 版本
     */
    Integer version;

    /**
     * 前端组件
     */
    String frontComponent;

}
