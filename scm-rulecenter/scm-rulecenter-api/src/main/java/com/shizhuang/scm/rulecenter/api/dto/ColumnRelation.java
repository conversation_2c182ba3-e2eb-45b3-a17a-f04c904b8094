package com.shizhuang.scm.rulecenter.api.dto;

import java.io.Serializable;

/**
 * 列关联关系DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ColumnRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前表字段
     */
    private String currentColumn;

    /**
     * 关联表字段
     */
    private String relatedColumn;

    /**
     * 关联表名
     */
    private String relatedTable;

    // Getters and setters

    public String getCurrentColumn() {
        return currentColumn;
    }

    public void setCurrentColumn(String currentColumn) {
        this.currentColumn = currentColumn;
    }

    public String getRelatedColumn() {
        return relatedColumn;
    }

    public void setRelatedColumn(String relatedColumn) {
        this.relatedColumn = relatedColumn;
    }

    public String getRelatedTable() {
        return relatedTable;
    }

    public void setRelatedTable(String relatedTable) {
        this.relatedTable = relatedTable;
    }
}