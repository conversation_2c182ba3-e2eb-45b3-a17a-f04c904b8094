package com.shizhuang.scm.rulecenter.api.dto;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.util.Objects;


@Getter
@Setter
public class ElementInstDTO implements Serializable {

    /**
     * 元素code
     */
    @NotEmpty
    String elementCode;

    /**
     * 元素name，作为入参时不需要传，作为返回值时该字段会返回
     */
    String elementName;

    /**
     * 元素值
     */
    @NotEmpty
    String elementValue;

    /**
     * 元素值
     */
    String elementDesc;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ElementInstDTO)) return false;
        ElementInstDTO that = (ElementInstDTO) o;
        return Objects.equals(elementCode, that.elementCode) && Objects.equals(elementName, that.elementName) && Objects.equals(elementValue, that.elementValue) && Objects.equals(elementDesc, that.elementDesc);
    }

    @Override
    public int hashCode() {
        return Objects.hash(elementCode, elementName, elementValue, elementDesc);
    }
}
