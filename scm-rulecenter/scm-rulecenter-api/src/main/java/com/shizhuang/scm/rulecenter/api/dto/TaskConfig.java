package com.shizhuang.scm.rulecenter.api.dto;

import java.io.Serializable;

/**
 * 任务配置DTO
 *
 * <AUTHOR> Team
 * @version 1.0
 */
public class TaskConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务唯一ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 运行模式
     */
    private Integer mode;

    /**
     * 版本号
     */
    private String version;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 数据源名称
     */
    private String datasourceName;

    /**
     * 数据库名称
     */
    private String databaseName;

    /**
     * 扫描间隔（秒）
     */
    private Integer interval;

    /**
     * 扫描数量限制
     */
    private Integer limit;

    /**
     * 执行类型
     */
    private Integer executionType;

    /**
     * 开始时间
     */
    private String start;

    /**
     * 结束时间
     */
    private String end;

    /**
     * 主表名
     */
    private String baseTable;

    /**
     * 根节点配置
     */
    private ArchiveNode rootNode;


    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getExecutionType() {
        return executionType;
    }

    public void setExecutionType(Integer executionType) {
        this.executionType = executionType;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public String getBaseTable() {
        return baseTable;
    }

    public void setBaseTable(String baseTable) {
        this.baseTable = baseTable;
    }

    public ArchiveNode getRootNode() {
        return rootNode;
    }

    public void setRootNode(ArchiveNode rootNode) {
        this.rootNode = rootNode;
    }

}