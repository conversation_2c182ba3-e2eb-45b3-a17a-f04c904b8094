package com.shizhuang.scm.rulecenter.api.constant;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 业务参数中心固有字段，外部元素code不可与固有字段相冲突
 */
public enum SystemFieldEnum {

    ENABLE("enabled", "是否生效"),

    IS_DEL("is_del", "是否删除"),

    CREATOR("creator", "创建人"),

    CTIME("ctime", "创建时间"),

    MODIFIER("modifier", "最后修改人"),

    MTIME("mtime", "最后修改时间"),
    ;

    String fieldCode;

    String fieldName;

    SystemFieldEnum(String fieldCode, String fieldName) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public String getFieldName() {
        return fieldName;
    }

    public static Set<String> getAllCode() {
        return Arrays.stream(SystemFieldEnum.values()).map(SystemFieldEnum::getFieldCode).collect(Collectors.toSet());
    }
}
