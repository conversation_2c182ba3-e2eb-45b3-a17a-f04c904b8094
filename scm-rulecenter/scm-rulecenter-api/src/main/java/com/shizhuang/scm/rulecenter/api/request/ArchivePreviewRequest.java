package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class ArchivePreviewRequest implements Serializable {

    @NotEmpty(message = "app不能为空")
    private String appName;

    /**
     * 任务名称
     */
    @Length(max = 64, message = "任务名称长度不能超过64")
    private String taskName;

    /**
     * 数据源名称
     */
    @NotEmpty(message = "数据源名称不能为空")
    private String datasourceName;

    @NotEmpty(message = "数据库名称不能为空")
    private String databaseName;

    /**
     * 数据源连接
     */
    private String datasourceUrl;

    @Valid
    private List<TaskInfoDetailRequest> taskDetails;

    private String currentKey;

}
