package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class AddTableRequest implements Serializable {

    @NotEmpty(message = "app名称不能为空")
    private String appName;

    private String key;

    /**
     * 任务名称
     */
    @Length(max = 64, message = "任务名称长度不能超过64")
    private String taskName;

    @NotEmpty(message = "parentKey名称不能为空")
    private String parentKey;

    @NotEmpty(message = "表名不能为空")
    private String tableName;

    private String conditions;

    private String indexColumn;

    /**
     * 索引类型 1 区间 2 保留天数
     * 如果是数字类型，支持填写一个区间，则只在这个区间进行查询，如果是日期，
     * 支持填写保留天数，查询语句将限制在这个时间段内
     */
    private Integer indexType;

    /**
     * asc desc
     */
    private String orderBy;

    /**
     * 1 先归档子节点 2 先归档父节点
     */
    private Integer archiveType;

    /**
     * 默认值过滤 0||null 不过滤 1 过滤
     */
    private Integer filterDefaultValue;

    /**
     * 关联关系
     */
    @Pattern(regexp = "^\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*\\.\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*=\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*\\.\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*$", message = "关联关系请使用格式 pink_operate_item.outbound_detail_id = pink_outbound_detail.id")
    private String relationConditions;

    private Boolean archive;

    private Boolean root = false;


    private String timeSpan;

    private Long indexEnd;

    private Long indexStart;

    private String indexColumType;

    private Integer reserveDays;

    /**
     * 是否开启扫描模式
     */
    private boolean enableScan;
}
