package com.shizhuang.scm.rulecenter.api.service;

import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.cmd.*;
import com.shizhuang.scm.rulecenter.api.response.EntireSchemaResponse;

/**
 * 业务参数配置中心元数据服务
 */
public interface MetaApplicationService {

    /**
     * 元素新增服务
     */
    Result<String> createElement(ElementCreateCmd elementCreateCmd);


    /**
     * 批量元素新增服务
     */
    Result<String> batchCreateElement(ElementBatchCreateCmd elementCreateCmd);


    /**
     * 元素编辑服务
     */
    Result<String> editElement(ElementEditCmd elementEditCmd);

    /**
     * 元素删除服务
     */
    Result<String> deleteElement(ElementDeleteCmd elementDeleteCmd);

    /**
     * 方案编码创建
     */
    Result<String> createSchemaCode();

    /**
     * 方案创建服务
     */
    Result<String> createSchema(SchemaCreateCmd schemaCreateCmd);

    /**
     * 方案编辑服务
     */
    Result<String> editSchema(SchemaEditCmd schemaEditCmd);


    /**
     * 方案优先级编辑服务
     * @param schemaDimPriorityEditCmd
     * @return
     */
    Result<String> editSchemaDimPriority(SchemaDimPriorityEditCmd schemaDimPriorityEditCmd);


    /**
     * 方案上线
     * @param schemaDeployCmd
     * @return
     */
    Result<String> deploySchema(SchemaDeployCmd schemaDeployCmd);


    /**
     * 删除方案
     * @param schemaDeleteCmd
     * @return
     */
    Result<String> deleteSchema(SchemaDeleteCmd schemaDeleteCmd);


    /**
     * 覆写全量方案信息
     * @param entireSchemaResponse
     * @return
     */
    Result<String> overrideEntireSchemaInfo(EntireSchemaResponse entireSchemaResponse);
}
