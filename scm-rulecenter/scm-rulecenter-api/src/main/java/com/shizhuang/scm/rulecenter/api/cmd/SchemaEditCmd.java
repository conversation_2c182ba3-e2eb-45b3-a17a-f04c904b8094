package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 方案修改
 */
@Getter
@Setter
public class SchemaEditCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;

    /**
     * 方案名称
     */
    @NotNull(message = "方案名称不能为空")
    String schemaName;

    /**
     * 方案描述
     */
    String schemaDesc;

    /**
     * 方案下维度
     */
    @NotEmpty(message = "方案下维度不能为空")
    @Size(min = 1, max = 10, message = "最多支持设置10个维度")
    List<SchemaElementCreateCmd> dimension;

    /**
     * 方案下参数
     */
    @Size(max = 20, message = "最多支持设置20个参数")
    List<SchemaElementCreateCmd> params;

    /**
     * 关联菜单路径
     */
    String menu;

    /**
     * 缓存超时时间
     */
    Integer redisCacheExpireTime;

    /**
     * 业务域名称
     */
    String projectName;

    /**
     * 页面操作配置（用户可以选择是否要导入导出功能等等）
     */
    List<String> actionFeature;

}
