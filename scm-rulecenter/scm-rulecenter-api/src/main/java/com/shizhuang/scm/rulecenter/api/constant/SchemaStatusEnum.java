package com.shizhuang.scm.rulecenter.api.constant;

import java.util.Objects;

public enum SchemaStatusEnum {

    DRAFT(0, "草稿"),
    ONLINE(1, "已上线"),
    OFFLINE(2, "已下线"),

    ;
    private int status;

    private String desc;


    SchemaStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static SchemaStatusEnum of(Integer status) {
        for (SchemaStatusEnum schemaStatusEnum : SchemaStatusEnum.values()) {
            if (Objects.equals(schemaStatusEnum.status, status)) {
                return schemaStatusEnum;
            }
        }
        return null;
    }


}
