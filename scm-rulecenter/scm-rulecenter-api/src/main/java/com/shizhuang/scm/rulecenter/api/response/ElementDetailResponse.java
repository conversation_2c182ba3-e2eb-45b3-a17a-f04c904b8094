package com.shizhuang.scm.rulecenter.api.response;

import com.shizhuang.scm.rulecenter.api.dto.ElementEnumDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ElementDetailResponse implements Serializable {

    /**
     * 元素code
     */
    String elementCode;

    /**
     * 元素名称
     */
    String elementName;

    /**
     * 元素来源类型 1公共元素 2自定义方案元素
     */
    Integer elementSourceType;

    /**
     * 元素来源类型名称
     */
    String elementSourceTypeName;

    /**
     * 取值范围类型 1枚举，2url，3自定义文本
     */
    Byte valueRangeType;

    /**
     * 取值范围类型名称
     */
    String valueRangeTypeName;

    /**
     * 全量获取接口 valueRangeType=2时填写该变量
     */
    ElementInterfaceDTO fullInterface;

    /**
     * 单点通过code获取描述接口，导出、新建校验使用 valueRangeType=21时填写该变量
     */
    ElementInterfaceDTO valueToDescInterface;

    /**
     * 单点通过描述获取code接口，导入使用 valueRangeType=21时填写该变量
     */
    ElementInterfaceDTO descToValueInterface;

    /**
     * 元素取值枚举
     */
    List<ElementEnumDTO> enums;


    /**
     * 元素描述
     */
    String elementNote;


    /**
     * 元素归属方案
     */
    String belongToSchemaCode;

    /**
     * 前端组件
     */
    private String frontComponent;


    /**
     * 创建人
     */
    private String creator;

}
