package com.shizhuang.scm.rulecenter.api.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class TaskInfoResponse implements Serializable {

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 数据库名称
     */
    private String databaseName;

    /**
     * 扫描数量 默认100条
     */
    private Integer limit = 100;

    /**
     * 扫描间隔 默认30秒
     */
    private Integer interval = 30;

    /**
     * 0:暂停 1:运行
     */
    private Integer status = 0;

    /**
     * 1 debug 2 生产
     */
    private Integer mode;

    /**
     * 执行 1 全天 2 指定时间
     */
    private Integer executionType;

    private String timeSpan;

    private List<ArchiveTaskDetailResponse> taskDetails;

    private String appName;

    private String key;
}
