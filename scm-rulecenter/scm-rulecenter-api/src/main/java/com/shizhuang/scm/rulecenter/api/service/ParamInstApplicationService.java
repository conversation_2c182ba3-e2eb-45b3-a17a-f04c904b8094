package com.shizhuang.scm.rulecenter.api.service;


import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.cmd.*;
import com.shizhuang.scm.rulecenter.api.query.InstPageQuery;
import com.shizhuang.scm.rulecenter.api.response.InstDimPageResponse;
import com.shizhuang.scm.rulecenter.api.response.PageInfoResponse;

/**
 * 参数实例应用服务
 */
public interface ParamInstApplicationService {

    /**
     * 分页查询参数实例
     * @param instPageQuery
     * @return
     */
    Result<PageInfoResponse<InstDimPageResponse>> queryParamInstPage(InstPageQuery instPageQuery);

    /**
     * 参数实例新增
     * @return
     */
    Result<String> addParamInst(ParamInstAddCmd paramInstAddCmd);

    /**
     * 参数实例修改
     * @return
     */
    Result<String> editParamInst(ParamInstEditCmd paramInstEditCmd);


    /**
     * 参数实例删除
     * @return
     */
    Result<String> deleteParamInst(ParamInstDeleteCmd paramInstDeleteCmd);


    /**
     * 参数实例批量删除
     * @return
     */
    Result<String> batchDeleteParamInst(ParamInstBatchDeleteCmd paramInstBatchDeleteCmd);


    /**
     * 参数实例失效
     * @param paramInstAbleSwitchCmd
     * @return
     */
    Result<String> disabledParamInst(ParamInstAbleSwitchCmd paramInstAbleSwitchCmd);


    /**
     * 参数实例生效
     * @param paramInstAbleSwitchCmd
     * @return
     */
    Result<String> enabledParamInst(ParamInstAbleSwitchCmd paramInstAbleSwitchCmd);


    /**
     * 根据方案code生成方案导入模板
     * @param schemaCode
     * @return
     */
    byte[] generateImportExcel(String schemaCode);

    /**
     * 批量失效参数
     * @param paramInstBatchAbleSwitchCmd
     * @return
     */
    Result<String> batchDisabledParamInst(ParamInstBatchAbleSwitchCmd paramInstBatchAbleSwitchCmd);


    /**
     * 批量生效参数
     * @param paramInstAbleSwitchCmd
     * @return
     */
    Result<String> batchEnabledParamInst(ParamInstBatchAbleSwitchCmd paramInstAbleSwitchCmd);


    /**
     * 参数实例新增
     * @return
     */
    Result<String> addOrUpdateParamInst(ParamInstAddCmd paramInstAddCmd);


    /**
     * 综合编辑参数实例（包含新增，修改，删除）
     * @return
     */
    Result<String> combineEditParamInst(ParamsCombineEditCmd paramsCombineEditCmd);
}