package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SchemaSyncCmd {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 方案名称
     */
    String schemaName;

    /**
     * 方案描述
     */
    String schemaDesc;

    /**
     * 方案下维度
     */
    List<SchemaElementCreateCmd> dimension;

    /**
     * 方案下参数
     */
    List<SchemaElementCreateCmd> params;

    /**
     * 创建人
     */
    String creator;

    /**
     * 关联菜单路径
     */
    String menu;

    /**
     * 方案下维度优先级
     */
    List<DimPriorityCreateCmd> priority;

}
