package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

@Data
public class UpdateTaskStatusRequest implements Serializable {

    @NotEmpty(message = "app名称不能为空")
    private String appName;

    /**
     * 任务名称
     */
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

    /**
     * 0:暂停 1:运行
     */
    private Integer status;

    /**
     * 1 debug 2 生产
     */
    private Integer mode;

    /**
     * 全天运行
     */
    private Boolean allDay;

    /**
     * 自定义执行时间段 如：21:00-6:00
     */
    @Pattern(regexp = "^(?:[01]?[0-9]|2[0-3]):[0-5][0-9]-(?:[01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "格式错误，示例:22:00-7:00")
    private String timeSpan;

    /**
     * 删除操作
     */
    private boolean delete;
}
