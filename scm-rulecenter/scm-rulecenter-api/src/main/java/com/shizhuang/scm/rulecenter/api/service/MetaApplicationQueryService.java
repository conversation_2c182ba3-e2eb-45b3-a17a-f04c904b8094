package com.shizhuang.scm.rulecenter.api.service;


import com.github.pagehelper.PageInfo;
import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementMetaDTO;
import com.shizhuang.scm.rulecenter.api.dto.SchemaMetaDTO;
import com.shizhuang.scm.rulecenter.api.query.*;
import com.shizhuang.scm.rulecenter.api.response.ElementDetailResponse;
import com.shizhuang.scm.rulecenter.api.response.EntireSchemaResponse;
import com.shizhuang.scm.rulecenter.api.response.MetaConstantResponse;
import com.shizhuang.scm.rulecenter.api.response.SchemaDetailResponse;

import java.util.List;

/**
 * 元数据查询服务
 */
public interface MetaApplicationQueryService {

    /**
     * 元素列表查询服务
     */
    Result<PageInfo<ElementMetaDTO>> queryElementPage(ElementPageQuery elementPageQuery);

    /**
     * 方案明细查询
     */
    Result<ElementDetailResponse> queryElementDetail(ElementDetailQuery elementDetailQuery);

    /**
     * 方案列表查询服务
     */
    Result<PageInfo<SchemaMetaDTO>> querySchemaPage(SchemaPageQuery schemaPageQuery);

    /**
     * 方案明细查询
     */
    Result<SchemaDetailResponse> querySchemaDetail(SchemaDetailQuery schemaDetailQuery);


    Result<MetaConstantResponse> getMetaConstant();


    Result<List<ElementInstDTO>> queryElementInstByDesc(ElementInstQuery elementInstQuery);


    public Result<EntireSchemaResponse> getEntireSchemaInfo(String schemaCode);

}
