package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
public class ElementInstAddCmd implements Serializable {

    /**
     * 元素code
     */
    @NotEmpty
    String elementCode;


    /**
     * 元素值
     */
    @NotNull
    Object elementValue;

}
