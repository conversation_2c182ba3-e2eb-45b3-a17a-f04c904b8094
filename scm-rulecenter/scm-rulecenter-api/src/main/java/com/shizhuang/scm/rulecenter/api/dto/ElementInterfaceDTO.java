package com.shizhuang.scm.rulecenter.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ElementInterfaceDTO implements Serializable {

    /**
     * dubbo接口 格式：{全路径名:版本}  如：com.shizhuang.duapp.mdm.api.dubbo.ScpWarehouseService：1.0.0
     */
    String dubboInterface;

    /**
     * dubbo接口方法
     */
    String dubboMethod;

    /**
     * dubbo接口入参类型,输入类全限定名
     */
    List<String> dubboParamType;

    /**
     * dubbo接口固定入参名称,若单点查询，则其中一个参数需要变成 #{var}
     */
    List<String> dubboParamValue;

    /**
     * 取值范围为dubbo接口时的，获取枚举对应code的取值路径
     */
    String valueCodePath;

    /**
     * 取值范围为dubbo接口时的，获取枚举对应desc的取值路径
     */
    String valueDescPath;

}
