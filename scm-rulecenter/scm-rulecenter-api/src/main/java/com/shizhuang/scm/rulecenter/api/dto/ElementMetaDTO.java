package com.shizhuang.scm.rulecenter.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ElementMetaDTO implements Serializable {

    /**
     * 归属方案code
     */
    String belongToSchemaCode;

    /**
     * 元素code
     */
    String elementCode;

    /**
     * 元素名称
     */
    String elementName;

    /**
     * 元素来源类型  1公共元素 2自定义方案元素
     */
    Integer elementSourceType;

    /**
     * 元素来源类型名称
     */
    String elementSourceTypeName;

    /**
     * 元素取值类型 1枚举，2url，3自定义文本
     */
    Byte valueRangeType;

    /**
     * 元素取值类型名称
     */
    String valueRangeTypeName;

    /**
     * 接口信息
     */
    String valueRangeFeature;

    /**
     * 元素取值路径
     */
    String valuePath;

    /**
     * 关联方案，格式：“${schemaCode}；${schemaName}”
     */
    List<String> relatedSchema;

    /**
     * 元素创建时间
     */
    String createTime;

    /**
     * 元素修改时间
     */
    String modifyTime;


    /**
     * 元素描述
     */
    String elementNote;


    /**
     * 创建人
     */
    String creator;

}
