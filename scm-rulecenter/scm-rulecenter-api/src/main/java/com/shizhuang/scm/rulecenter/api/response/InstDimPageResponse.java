package com.shizhuang.scm.rulecenter.api.response;

import com.shizhuang.scm.rulecenter.api.dto.TableHeadDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class InstDimPageResponse {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 业务维度code，#号分隔，如 categoryId#store
     */
    String dimension;

    /**
     * 业务维度值，如 1008#D1
     */
    String dimensionKey;

    /**
     * 参数版本
     */
    Integer version;


    /**
     * 参数聚合id
     */
    String aggId;

    /**
     * 表头
     */
    List<TableHeadDTO> tableHead;


    /**
     * 数据值
     */
    Map<String,Object> elementValueMap;


    /**
     * 数据文案
     */
    Map<String,Object> elementDescMap;

}
