package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class ArchiveGlobalConfigRequest implements Serializable {

    @NotEmpty(message = "app不能为空")
    private String appName;

    @Max(value = 10, message = "线程数不可大于10")
    @Min(value = 1, message = "线程数不可小于1")
    private Integer threads;

    /**
     * 锁时间，默认300秒
     */
    @Min(value = 1, message = "锁时间不可少于1S")
    @Max(value = 21600, message = "锁时间不可大于6小时")
    private Integer lockExpireSeconds;

    /**
     * 全局开关
     */
    private Boolean enable;

    /**
     * 任务扫描间隔，查询下一个等待执行的任务
     */
    @Min(value = 1, message = "锁时间不可少于1S")
    @Max(value = 3600, message = "锁时间不可大于1小时")
    private Integer interval;

}
