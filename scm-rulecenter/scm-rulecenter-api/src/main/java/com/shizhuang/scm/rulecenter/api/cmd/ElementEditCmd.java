package com.shizhuang.scm.rulecenter.api.cmd;

import com.shizhuang.scm.rulecenter.api.dto.ElementEnumDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 元素编辑
 */
@Getter
@Setter
public class ElementEditCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;

    /**
     * 元素code
     */
    @NotNull(message = "元素code不能为空")
    String elementCode;

    /**
     * 元素名称
     */
    String elementName;

    /**
     * 取值范围类型 1枚举，2dubbo全量接口，21dubbo单点查询，3自定义文本
     */
    @NotNull(message = "元素取值范围类型不能为空")
    Byte valueRangeType;

    /**
     * 元素来源类型 1公共元素 2自定义方案元素
     */
    @NotNull(message = "元素来源类型不能为空")
    @Max(2)
    @Min(1)
    Integer elementSourceType;

    /**
     * 全量获取接口 valueRangeType=2时填写该变量
     */
    ElementInterfaceDTO fullInterface;

    /**
     * 单点通过code获取描述接口，导出、新建校验使用 valueRangeType=21时填写该变量
     */
    ElementInterfaceDTO valueToDescInterface;

    /**
     * 单点通过描述获取code接口，导入使用 valueRangeType=21时填写该变量
     */
    ElementInterfaceDTO descToValueInterface;

    /**
     * 元素取值枚举
     */
    List<ElementEnumDTO> enums;

    /**
     * 元素描述
     */
    String elementNote;

    /**
     * 前端组件
     */
    String frontComponent;

}
