package com.shizhuang.scm.rulecenter.api.response;

import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class InstDimResponse implements Serializable {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 业务维度code，#号分隔，如 categoryId#store
     */
    String dimension;

    /**
     * 业务维度值，如 1008#D1
     */
    String dimensionKey;

    /**
     * 元素取值
     */
    List<ElementInstDTO> elements;

    /**
     * 创建时间
     */
    Date createTime;

    /**
     * 修改时间
     */
    Date modifyTime;

    public String getSchemaCode() {
        return schemaCode;
    }

    public void setSchemaCode(String schemaCode) {
        this.schemaCode = schemaCode;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionKey() {
        return dimensionKey;
    }

    public void setDimensionKey(String dimensionKey) {
        this.dimensionKey = dimensionKey;
    }

    public List<ElementInstDTO> getElements() {
        return elements;
    }

    public void setElements(List<ElementInstDTO> elements) {
        this.elements = elements;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}
