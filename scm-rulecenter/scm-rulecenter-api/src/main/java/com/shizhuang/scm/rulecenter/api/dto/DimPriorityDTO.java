package com.shizhuang.scm.rulecenter.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 维度优先级
 */
@Getter
@Setter
public class DimPriorityDTO implements Serializable {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * "维度code集合，#号分隔"
     */
    String dimension;

    /**
     * 优先级
     */
    Integer priority;

    /**
     * 是否删除 0 否 1 是
     */
    Byte isDel;

    /**
     * 版本
     */
    Integer version;

    /**
     * 记录原子维度
     */
    List<String> atomicDimension;

}
