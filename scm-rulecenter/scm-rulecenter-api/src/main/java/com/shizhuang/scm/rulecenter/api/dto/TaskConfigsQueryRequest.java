package com.shizhuang.scm.rulecenter.api.dto;

import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TaskConfigsQueryRequest {
    @NotEmpty(message = "应用名称不能为空")
    private String appName;

    private List<Long> taskIds;

    @NotNull(message = "pageNum不能为空")
    private Integer pageNum = 1;

    @NotNull(message = "pageSize不能为空")
    private Integer pageSize = 20;
} 