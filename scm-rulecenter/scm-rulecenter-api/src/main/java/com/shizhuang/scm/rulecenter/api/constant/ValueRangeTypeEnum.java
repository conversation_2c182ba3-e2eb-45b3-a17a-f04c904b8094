package com.shizhuang.scm.rulecenter.api.constant;

import java.util.Objects;

public enum ValueRangeTypeEnum {

    SYSTEM((byte) 0, "系统字段"),
    ENUM((byte) 1, "枚举"),
    DUBBO_FULL((byte) 2, "dubbo全量接口"),
    DUBBO_SEARCH((byte) 21, "dubbo单点搜索接口"),
    CUSTOM_TEXT((byte) 3, "自定义文本")

    ;

    private Byte type;

    private String desc;


    ValueRangeTypeEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Byte getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ValueRangeTypeEnum of(Byte type) {
        for (ValueRangeTypeEnum valueRangeTypeEnum : ValueRangeTypeEnum.values()) {
            if (Objects.equals(valueRangeTypeEnum.type, type)) {
                return valueRangeTypeEnum;
            }
        }
        return null;
    }

    public static boolean isDubboInterface(Byte type) {
        return ValueRangeTypeEnum.DUBBO_FULL.getType().equals(type)
                || ValueRangeTypeEnum.DUBBO_SEARCH.getType().equals(type);
    }

}
