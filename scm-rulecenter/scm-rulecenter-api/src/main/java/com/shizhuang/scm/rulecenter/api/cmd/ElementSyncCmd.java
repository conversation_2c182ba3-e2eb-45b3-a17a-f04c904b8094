package com.shizhuang.scm.rulecenter.api.cmd;

import com.shizhuang.scm.rulecenter.api.dto.ElementEnumDTO;
import com.shizhuang.scm.rulecenter.api.dto.ElementInterfaceDTO;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Getter
@Setter
public class ElementSyncCmd {

    /**
     * 方案code 非必填，跟着方案一起创建的元素需要带上schemaCode
     */
    String schemaCode;

    /**
     * 元素code
     */
    @NotEmpty(message = "元素code不能为空")
    String elementCode;

    /**
     * 元素名称
     */
    @NotEmpty(message = "元素名称不能为空")
    String elementName;

    /**
     * 元素来源类型 1公共元素 2自定义方案元素
     */
    Integer elementSourceType;

    /**
     * 取值范围类型 1枚举，2dubbo全量接口，21dubbo单点查询，3自定义文本
     */
    Byte valueRangeType;

    /**
     * 全量获取接口 valueRangeType=2时填写该变量
     */
    ElementInterfaceDTO fullInterface;

    /**
     * 单点通过code获取描述接口，导出、新建校验使用 valueRangeType=21时填写该变量
     */
    ElementInterfaceDTO valueToDescInterface;

    /**
     * 单点通过描述获取code接口，导入使用 valueRangeType=21时填写该变量
     */
    ElementInterfaceDTO descToValueInterface;

    /**
     * 元素取值枚举
     */
    List<ElementEnumDTO> enums;

    /**
     * 关联前端组件
     */
    String frontComponent;


}
