package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
public class ParamInstAbleSwitchCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;

    /**
     * 业务维度code，#号分隔,如 categoryId#store
     */
    String dimension;

    /**
     * 业务维度值 如：1008#D1
     */
    String dimensionKey;


    /**
     * 参数聚合id
     */
    String aggId;

    /**
     * 操作人
     */
    String optUser;


    /**
     * 1生效 0失效
     */
    Integer enableFlag;

}
