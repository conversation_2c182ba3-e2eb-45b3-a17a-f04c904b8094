package com.shizhuang.scm.rulecenter.api.constant;

import java.util.Objects;

public enum ElementSourceTypeEnum {

    COMMON((byte)1, "公共元素"),
    CUSTOM((byte)2, "自定义方案元素")

    ;

    private Byte type;

    private String desc;


    ElementSourceTypeEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Byte getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ElementSourceTypeEnum of(Byte type) {
        for (ElementSourceTypeEnum elementSourceTypeEnum : ElementSourceTypeEnum.values()) {
            if (Objects.equals(elementSourceTypeEnum.type, type)) {
                return elementSourceTypeEnum;
            }
        }
        return null;
    }

}
