package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 参数实例新增指令
 */
@Getter
@Setter
public class ParamInstAddCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;

    /**
     * 元素值列表
     */
    @NotEmpty
    List<ElementInstAddCmd> paramMap;

}
