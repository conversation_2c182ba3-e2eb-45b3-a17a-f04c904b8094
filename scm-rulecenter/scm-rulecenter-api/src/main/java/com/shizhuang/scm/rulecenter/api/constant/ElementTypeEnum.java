package com.shizhuang.scm.rulecenter.api.constant;

public enum ElementTypeEnum {

    DIMENSION((byte)1, "维度"),
    PARAM((byte)2, "参数"),
    ;
    private Byte type;

    private String desc;


    ElementTypeEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Byte getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
