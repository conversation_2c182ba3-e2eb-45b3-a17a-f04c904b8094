package com.shizhuang.scm.rulecenter.api.constant;

public enum IsRequiredEnum {


    YES(1, "是"),
    NO(2, "否"),
    ;
    private Integer type;

    private String desc;


    IsRequiredEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static IsRequiredEnum of(Byte type) {
        if (type == (byte) 1) {
            return IsRequiredEnum.YES;
        } else if (type == (byte) 0) {
            return IsRequiredEnum.NO;
        }
        return null;
    }

}
