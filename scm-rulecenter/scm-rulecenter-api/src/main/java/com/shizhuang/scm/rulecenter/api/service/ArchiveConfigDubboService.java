package com.shizhuang.scm.rulecenter.api.service;

import com.poizon.fusion.common.model.Result;
import com.shizhuang.scm.rulecenter.api.dto.AppConfig;
import com.shizhuang.scm.rulecenter.api.dto.TaskConfig;

import java.util.List;

/**
 * SCP-Archive配置查询Dubbo服务接口
 * 
 * 提供给admin端的任务配置查询能力，支持按应用、任务ID等维度获取详细归档任务配置
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public interface ArchiveConfigDubboService {

    /**
     * 获取单个任务配置
     * 
     * @param appName 应用名称
     * @param taskId 任务ID
     * @return 任务配置（TaskConfig），不存在返回null
     */
    Result<TaskConfig> getTaskConfig(Long taskId);

    /**
     * 获取应用下所有任务配置
     * 
     * @param appName 应用名称
     * @return 任务配置列表
     */
    Result<List<TaskConfig>> getAppTaskConfigs(String appName);

    /**
     * 获取全局配置（应用级别配置，不包含任务明细）
     * 
     * @param appName 应用名称
     * @return 全局配置（AppConfig），不存在返回null
     */
    Result<AppConfig> getAppConfig(String appName);
}