package com.shizhuang.scm.rulecenter.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class TaskInfoRequest implements Serializable {

    @NotEmpty(message = "app不能为空")
    private String appName;

    /**
     * 任务名称
     */
    @Length(max = 64, message = "任务名称长度不能超过64")
    private String taskName;

    /**
     * 数据源名称
     */
    @NotEmpty(message = "数据源名称不能为空")
    private String dataSourceName;

    @NotEmpty(message = "数据库名称不能为空")
    private String databaseName;

    /**
     * 数据源连接
     */
    private String datasourceUrl;

    /**
     * 扫描数量 默认100条
     */
    private Integer limit = 100;

    /**
     * 扫描间隔 默认30秒
     */
    private Integer interval = 30;

    /**
     * 1 全天运行 2 指定时间段运行
     */
    private Integer executionType;

    @NotEmpty(message = "表名不能为空")
    private String tableName;

    private String conditions;

    private String indexColumn;

    /**
     * 索引类型 1 区间 2 保留天数
     * 如果是数字类型，支持填写一个区间，则只在这个区间进行查询，如果是日期，
     * 支持填写保留天数，查询语句将限制在这个时间段内
     */
    private Integer indexType;

    /**
     * asc desc
     */
    private String orderBy;

    /**
     * 索引字段的值,如果是区间，则为0-1，如果是日期，则为数字
     */
    private String indexValue;

    /**
     * 1 先归档子节点 2 先归档父节点
     */
    private Integer archiveType;

    /**
     * 默认值过滤 0||null 不过滤 1 过滤
     */
    private Integer filterDefaultValue;

    /**
     * 关联关系
     */
    @Pattern(regexp = "^\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*\\.\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*=\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*\\.\\s*((?:[a-zA-Z_]\\w*|`(?:``|[^`])+`))\\s*$", message = "关联关系请使用格式 pink_operate_item.outbound_detail_id = pink_outbound_detail.id")
    private String relationConditions;

    /**
     * 是否归档
     */
    private Boolean archive;

    /**
     * 忽略警告
     */
    private boolean ignoreWarnings;

    private String timeSpan;

    private Long indexEnd;

    private Long indexStart;

    private String indexColumType;

    private Integer reserveDays;

    public String getConditions() {
        return conditions == null ? null : conditions.trim();
    }

    /**
     * 是否开启扫描模式
     */
    private boolean enableScan;
}
