package com.shizhuang.scm.rulecenter.api.cmd;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
public class ParamInstDeleteCmd implements Serializable {

    /**
     * 方案code
     */
    @NotNull(message = "方案code不能为空")
    String schemaCode;

    /**
     * 业务维度code，#号分隔,如 categoryId#store
     */
    //@NotNull(message = "业务维度code不能为空")
    String dimension;

    /**
     * 业务维度值 如 1008#D1
     */
    //@NotNull(message = "业务维度值不能为空")
    String dimensionKey;


    /**
     * 参数聚合id
     */
    @NotNull(message = "参数聚合id不能为空")
    String aggId;

}
