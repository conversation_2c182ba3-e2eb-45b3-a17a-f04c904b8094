package com.shizhuang.scm.rulecenter.api.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * 归档节点DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ArchiveNode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 节点唯一ID
     */
    private Long nodeId;

    /**
     * 归属任务ID
     */
    private Long taskId;

    /**
     * 父节点ID，根节点为0或null
     */
    private Long parentNodeId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 查询条件
     */
    private String condition;

    /**
     * 节点级数据源名称
     */
    private String datasourceName;

    /**
     * 节点级数据库名称
     */
    private String databaseName;

    /**
     * 子节点列表
     */
    private List<ArchiveNode> children;

    /**
     * 是否归档
     */
    private Boolean isArchive;

    /**
     * 查询字段列表
     */
    private List<String> queryColumns;

    /**
     * 主键列
     */
    private String primaryKeyColumn;

    /**
     * 节点状态
     */
    private Integer status;

    /**
     * 是否为根节点
     */
    private Boolean rootNode;

    /**
     * 是否为调试模式
     */
    private Boolean debugMode;

    /**
     * 插件列表
     */
    private List<ArchivePlugin> plugins;

    /**
     * 索引字段
     */
    private String indexColumn;

    /**
     * 索引类型
     */
    private Integer indexType;

    /**
     * 索引结束值
     */
    private Long indexEnd;

    /**
     * 索引开始值
     */
    private Long indexStart;

    /**
     * 排序方式
     */
    private String orderBy;

    /**
     * 归档类型
     */
    private Integer archiveType;

    /**
     * 默认值过滤
     */
    private Integer filterDefaultValue;

    /**
     * 保留天数
     */
    private Integer reserveDays;

    /**
     * 属性配置
     */
    private Map<String, Object> props;

    /**
     * 关联关系
     */
    private List<ColumnRelation> relations;

    /**
     * 是否启用分片
     */
    private Boolean shardingEnabled;

    /**
     * 分片表达式
     */
    private String shardingExpression;

    /**
     * 分片插件全类名
     */
    private String shardingPluginClass;

    /**
     * 分片插件配置
     */
    private Map<String, Object> shardingPluginConfig;

    /**
     * 分片字段
     */
    private String shardingField;

    /**
     * 分片前缀
     */
    private String shardingPrefix;

    /**
     * 分片数量
     */
    private Integer shardCount;

    /**
     * 分片表名模板
     */
    private String shardingTableTemplate;

    /**
     * 是否开启扫描模式
     */
    private Boolean enableScan;

    /**
     * 主键最小值
     */
    private String minPrimaryKey;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // Getters and setters

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getParentNodeId() {
        return parentNodeId;
    }

    public void setParentNodeId(Long parentNodeId) {
        this.parentNodeId = parentNodeId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public List<ArchiveNode> getChildren() {
        return children;
    }

    public void setChildren(List<ArchiveNode> children) {
        this.children = children;
    }

    public Boolean getIsArchive() {
        return isArchive;
    }

    public void setIsArchive(Boolean isArchive) {
        this.isArchive = isArchive;
    }

    public List<String> getQueryColumns() {
        return queryColumns;
    }

    public void setQueryColumns(List<String> queryColumns) {
        this.queryColumns = queryColumns;
    }

    public String getPrimaryKeyColumn() {
        return primaryKeyColumn;
    }

    public void setPrimaryKeyColumn(String primaryKeyColumn) {
        this.primaryKeyColumn = primaryKeyColumn;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getRootNode() {
        return rootNode;
    }

    public void setRootNode(Boolean rootNode) {
        this.rootNode = rootNode;
    }

    public Boolean getDebugMode() {
        return debugMode;
    }

    public void setDebugMode(Boolean debugMode) {
        this.debugMode = debugMode;
    }

    public List<ArchivePlugin> getPlugins() {
        return plugins;
    }

    public void setPlugins(List<ArchivePlugin> plugins) {
        this.plugins = plugins;
    }

    public String getIndexColumn() {
        return indexColumn;
    }

    public void setIndexColumn(String indexColumn) {
        this.indexColumn = indexColumn;
    }

    public Integer getIndexType() {
        return indexType;
    }

    public void setIndexType(Integer indexType) {
        this.indexType = indexType;
    }

    public Long getIndexEnd() {
        return indexEnd;
    }

    public void setIndexEnd(Long indexEnd) {
        this.indexEnd = indexEnd;
    }

    public Long getIndexStart() {
        return indexStart;
    }

    public void setIndexStart(Long indexStart) {
        this.indexStart = indexStart;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public Integer getArchiveType() {
        return archiveType;
    }

    public void setArchiveType(Integer archiveType) {
        this.archiveType = archiveType;
    }

    public Integer getFilterDefaultValue() {
        return filterDefaultValue;
    }

    public void setFilterDefaultValue(Integer filterDefaultValue) {
        this.filterDefaultValue = filterDefaultValue;
    }

    public Integer getReserveDays() {
        return reserveDays;
    }

    public void setReserveDays(Integer reserveDays) {
        this.reserveDays = reserveDays;
    }

    public Map<String, Object> getProps() {
        return props;
    }

    public void setProps(Map<String, Object> props) {
        this.props = props;
    }

    public List<ColumnRelation> getRelations() {
        return relations;
    }

    public void setRelations(List<ColumnRelation> relations) {
        this.relations = relations;
    }

    public Boolean getShardingEnabled() {
        return shardingEnabled;
    }

    public void setShardingEnabled(Boolean shardingEnabled) {
        this.shardingEnabled = shardingEnabled;
    }

    public String getShardingExpression() {
        return shardingExpression;
    }

    public void setShardingExpression(String shardingExpression) {
        this.shardingExpression = shardingExpression;
    }

    public String getShardingPluginClass() {
        return shardingPluginClass;
    }

    public void setShardingPluginClass(String shardingPluginClass) {
        this.shardingPluginClass = shardingPluginClass;
    }

    public Map<String, Object> getShardingPluginConfig() {
        return shardingPluginConfig;
    }

    public void setShardingPluginConfig(Map<String, Object> shardingPluginConfig) {
        this.shardingPluginConfig = shardingPluginConfig;
    }

    public String getShardingField() {
        return shardingField;
    }

    public void setShardingField(String shardingField) {
        this.shardingField = shardingField;
    }

    public String getShardingPrefix() {
        return shardingPrefix;
    }

    public void setShardingPrefix(String shardingPrefix) {
        this.shardingPrefix = shardingPrefix;
    }

    public Integer getShardCount() {
        return shardCount;
    }

    public void setShardCount(Integer shardCount) {
        this.shardCount = shardCount;
    }

    public String getShardingTableTemplate() {
        return shardingTableTemplate;
    }

    public void setShardingTableTemplate(String shardingTableTemplate) {
        this.shardingTableTemplate = shardingTableTemplate;
    }

    public Boolean getEnableScan() {
        return enableScan;
    }

    public void setEnableScan(Boolean enableScan) {
        this.enableScan = enableScan;
    }

    public String getMinPrimaryKey() {
        return minPrimaryKey;
    }

    public void setMinPrimaryKey(String minPrimaryKey) {
        this.minPrimaryKey = minPrimaryKey;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}