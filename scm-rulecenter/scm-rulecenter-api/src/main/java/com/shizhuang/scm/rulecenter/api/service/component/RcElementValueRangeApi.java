package com.shizhuang.scm.rulecenter.api.service.component;

import com.poizon.fusion.common.model.Result;

import java.util.List;

/**
 * 通过接口取某个方案下的key-value值
 */
public interface RcElementValueRangeApi {

    /**
     * 获取指定方案下，某些元素作为key某些元素作为value
     * @param schemaCode
     * @param value
     * @param label
     * @return
     */
    Result<List<RcValueRangeResponse>> getValueRange(String schemaCode,String value,String label);

    Result<List<RcValueRangeResponse>> getValueRangeWithParam(String schemaCode,String value,String label,String param);


}
