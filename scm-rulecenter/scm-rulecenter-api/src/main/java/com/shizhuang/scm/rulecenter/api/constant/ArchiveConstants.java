package com.shizhuang.scm.rulecenter.api.constant;

public class ArchiveConstants {

    public static final String GLOBAL_CONFIG_KEY_PREFIX = "archive.config";
    // %s 为 taskId 值为版本号
    public static final String TASK_CONFIG_KEY_PREFIX = "archive.task.config_%s";


    public static final String ARK_GROUP = "archive-platform";

    public static final String ARK_KEY_PREFIX = "archive.platform";

    /**
     * 任务列表
     */
    public static final String TASK_LIST_KEY = ARK_KEY_PREFIX + ".task_list";

    /**
     * 全局配置
     */
    public static final String TASK_GLOBAL_CONFIG = ARK_KEY_PREFIX + ".global_config";

    /**
     * 调试模式
     */
    public static final Integer MODE_DEBUG = 1;

    /**
     * 生产模式
     */
    public static final Integer MODE_PROD = 2;

    /**
     * 运行状态
     */
    public static final Integer STATUS_RUNNING = 1;

    /**
     * 暂停状态
     */
    public static final Integer STATUS_PAUSE = 0;

    /**
     * 全天归档
     */
    public static final Integer ARCHIVE_TYPE_ALL_DAY = 1;

    /**
     * 指定时间归档
     */
    public static final Integer ARCHIVE_TYPE_SPECIFIC_TIME = 2;

    /**
     * 索引类型 区间
     */
    public static final Integer INDEX_TYPE_SPAN = 1;

    /**
     * 索引类型 保留天数
     */
    public static final Integer INDEX_TYPE_RESERVE_DAYS = 2;

    /**
     * 先归档父节点，先归档子节点值为1
     */
    public static final Integer ARCHIVE_TYPE_PARENT = 2;

    /**
     * 先归档子节点，先归档子节点值为1
     */
    public static final Integer ARCHIVE_TYPE_CHILDREN = 1;

    /**
     * 过滤0值和空字符串
     */
    public static final Integer FILTER_DEFAULT_VALUE = 1;

    // ==================== 扫描模式常量定义 ====================

    /**
     * 扫描模式：主键扫描
     * 通过主键进行遍历提升索引效果
     */
    public static final String SCAN_MODE_PRIMARY_KEY = "scan";

    /**
     * 扫描模式：条件扫描
     * 直接使用ark配置里面的condition来分页获取数据
     */
    public static final String SCAN_MODE_CONDITION = "condition";

    /**
     * 扫描模式：索引扫描
     * 通过索引字段进行扫描
     */
    public static final String SCAN_MODE_INDEX = "index";

    // ==================== Props Key 常量定义 ====================

    /**
     * 索引类型
     */
    public static final String PROPS_KEY_INDEX_TYPE = "indexType";

    /**
     * 索引开始值
     */
    public static final String PROPS_KEY_INDEX_START = "indexStart";

    /**
     * 索引结束值
     */
    public static final String PROPS_KEY_INDEX_END = "indexEnd";

    /**
     * 保留天数
     */
    public static final String PROPS_KEY_RESERVE_DAYS = "reserveDays";

    /**
     * 分片插件配置
     */
    public static final String PROPS_KEY_SHARDING_PLUGIN_CONFIG = "shardingPluginConfig";

    /**
     * 分片数量
     */
    public static final String PROPS_KEY_SHARD_COUNT = "shardCount";

    /**
     * 分片表名模板
     */
    public static final String PROPS_KEY_SHARDING_TABLE_TEMPLATE = "shardingTableTemplate";

    /**
     * 是否开启扫描模式
     */
    public static final String PROPS_KEY_ENABLE_SCAN = "enableScan";

    /**
     * 主键最小值
     */
    public static final String PROPS_KEY_MIN_PRIMARY_KEY = "minPrimaryKey";

    /**
     * 索引值
     */
    public static final String PROPS_KEY_INDEX_VALUE = "indexValue";

    /**
     * 索引列类型
     */
    public static final String PROPS_KEY_INDEX_COLUMN_TYPE = "indexColumType";

    /**
     * 超时时间（秒）
     */
    public static final String PROPS_KEY_TIMEOUT_SECONDS = "timeoutSeconds";

    /**
     * 最大扫描次数
     */
    public static final String PROPS_KEY_MAX_SCAN_COUNT = "maxScanCount";

    /**
     * 是否暂停
     */
    public static final String PROPS_KEY_PAUSE = "pause";

    /**
     * 扫描模式
     */
    public static final String PROPS_KEY_SCAN_MODE = "scanMode";

    /**
     * 归档类型
     */
    public static final String PROPS_KEY_ARCHIVE_TYPE = "archiveType";

    /**
     * 过滤默认值
     */
    public static final String PROPS_KEY_FILTER_DEFAULT_VALUE = "filterDefaultValue";

    /**
     * 关联条件
     */
    public static final String PROPS_KEY_RELATION_CONDITIONS = "relationConditions";

}
