package com.shizhuang.scm.rulecenter.api.query;

import com.shizhuang.scm.rulecenter.api.dto.ElementInstDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ParamInstExportRequest {

    /**
     * 方案code
     */
    String schemaCode;

    /**
     * 查询参数
     */
    List<ElementInstDTO> queryParams;

    /**
     * 是否需要校验用户关联仓库查询条件
     */
    Boolean checkAuthUserWarehouse = false;

}
