<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.shizhuang.scm</groupId>
        <artifactId>scm-rulecenter-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scm-rulecenter-api</artifactId>
    <properties>
    <fusion-common.version>1.3.15</fusion-common.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shizhuang.duapp</groupId>
            <artifactId>dubbo-swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-common</artifactId>
            <version>${fusion-common.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.1.10</version>
        </dependency>
        <dependency>
            <groupId>com.dewu.scm.lms</groupId>
            <artifactId>lms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>fusion-adapt-avatar</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
