/**
 * 分片表达式校验测试
 * 用于验证分片表达式的格式是否正确
 */

// 分片表达式校验函数
function validateShardingExpression(expression) {
  if (!expression) return true;
  
  // 支持两种格式：
  // 1. 简单格式：field % number
  // 2. 模板格式：prefix${field%number+offset}
  const simplePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\s*[%]\s*\d+$/;
  const templatePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\$\{[a-zA-Z_][a-zA-Z0-9_]*[%]\d+([+\-]\d+)?\}$/;
  
  return simplePattern.test(expression.trim()) || templatePattern.test(expression.trim());
}

// 测试用例
const testCases = [
  // 简单格式测试
  { expression: "item_id % 128", expected: true, description: "简单格式 - 基本求余" },
  { expression: "order_id % 64", expected: true, description: "简单格式 - 基本求余" },
  { expression: "user_id % 256", expected: true, description: "简单格式 - 基本求余" },
  { expression: "id % 10", expected: true, description: "简单格式 - 简单字段名" },
  { expression: "table_name % 100", expected: true, description: "简单格式 - 下划线字段名" },
  
  // 模板格式测试
  { expression: "pink_operate_item_${item_id%128+1}", expected: true, description: "模板格式 - 带偏移量" },
  { expression: "archive_table_${user_id%256}", expected: true, description: "模板格式 - 无偏移量" },
  { expression: "shard_${order_id%64-1}", expected: true, description: "模板格式 - 负偏移量" },
  { expression: "table_${id%10+5}", expected: true, description: "模板格式 - 正偏移量" },
  
  // 错误格式测试
  { expression: "item_id / 128", expected: false, description: "错误操作符" },
  { expression: "item_id % abc", expected: false, description: "非数字除数" },
  { expression: "123_id % 128", expected: false, description: "字段名以数字开头" },
  { expression: "item_id % 128 + 1", expected: false, description: "简单格式带偏移量" },
  { expression: "${item_id%128}", expected: false, description: "模板格式缺少前缀" },
  { expression: "pink_operate_item_${item_id%128*2}", expected: false, description: "不支持的操作符" },
  { expression: "item_id % 128 % 64", expected: false, description: "多个操作符" },
  { expression: "", expected: true, description: "空字符串" },
  { expression: null, expected: true, description: "null值" },
  { expression: undefined, expected: true, description: "undefined值" },
  
  // 边界情况测试
  { expression: "a % 1", expected: true, description: "最短字段名" },
  { expression: "very_long_field_name_123 % 999999", expected: true, description: "长字段名" },
  { expression: "field % 0", expected: true, description: "除数为0（校验不处理业务逻辑）" },
  { expression: "field % 999999999", expected: true, description: "大数字" },
  
  // 空格处理测试
  { expression: "  item_id % 128  ", expected: true, description: "前后空格" },
  { expression: "item_id  %  128", expected: true, description: "多个空格" },
  { expression: "item_id%128", expected: true, description: "无空格" },
];

// 运行测试
function runTests() {
  console.log("开始分片表达式校验测试...\n");
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((testCase, index) => {
    const result = validateShardingExpression(testCase.expression);
    const status = result === testCase.expected ? "✅ 通过" : "❌ 失败";
    
    console.log(`测试 ${index + 1}: ${testCase.description}`);
    console.log(`  表达式: "${testCase.expression}"`);
    console.log(`  期望结果: ${testCase.expected}`);
    console.log(`  实际结果: ${result}`);
    console.log(`  状态: ${status}\n`);
    
    if (result === testCase.expected) {
      passed++;
    } else {
      failed++;
    }
  });
  
  console.log(`测试完成: ${passed} 个通过, ${failed} 个失败`);
  
  if (failed === 0) {
    console.log("🎉 所有测试通过！");
  } else {
    console.log("⚠️  有测试失败，请检查校验逻辑");
  }
}

// 交互式测试
function interactiveTest() {
  console.log("交互式分片表达式测试");
  console.log("输入 'quit' 退出\n");
  
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  function askExpression() {
    rl.question('请输入分片表达式: ', (expression) => {
      if (expression.toLowerCase() === 'quit') {
        rl.close();
        return;
      }
      
      const result = validateShardingExpression(expression);
      console.log(`校验结果: ${result ? '✅ 通过' : '❌ 失败'}\n`);
      
      askExpression();
    });
  }
  
  askExpression();
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateShardingExpression,
    runTests,
    interactiveTest
  };
}

// 如果直接运行此文件
if (typeof require !== 'undefined' && require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--interactive') || args.includes('-i')) {
    interactiveTest();
  } else {
    runTests();
  }
}

// 浏览器环境下的使用示例
if (typeof window !== 'undefined') {
  window.validateShardingExpression = validateShardingExpression;
  console.log('分片表达式校验函数已加载到全局作用域');
} 