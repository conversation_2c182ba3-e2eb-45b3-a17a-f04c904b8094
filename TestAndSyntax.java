import java.util.List;
import java.util.ArrayList;
import java.util.regex.Pattern;

// 模拟ColumnRelation类
class ColumnRelation {
    private String currentColumn;
    private String relatedColumn;
    private String relatedTable;
    
    public void setCurrentColumn(String currentColumn) { this.currentColumn = currentColumn; }
    public void setRelatedColumn(String relatedColumn) { this.relatedColumn = relatedColumn; }
    public void setRelatedTable(String relatedTable) { this.relatedTable = relatedTable; }
    
    public String getCurrentColumn() { return currentColumn; }
    public String getRelatedColumn() { return relatedColumn; }
    public String getRelatedTable() { return relatedTable; }
    
    @Override
    public String toString() {
        return "ColumnRelation{currentColumn='" + currentColumn + "', relatedColumn='" + relatedColumn + "', relatedTable='" + relatedTable + "'}";
    }
}

// 测试and语法支持
public class TestAndSyntax {
    
    public static void main(String[] args) {
        TestAndSyntax test = new TestAndSyntax();
        
        // 测试单个条件
        System.out.println("=== 测试单个条件 ===");
        String singleCondition = "pink_inbound_detail.id = pink_operate_item.inbound_detail_id";
        test.testCondition(singleCondition);
        
        // 测试两个条件用and连接
        System.out.println("\n=== 测试两个条件用and连接 ===");
        String twoConditions = "pink_inbound_detail.id = pink_operate_item.inbound_detail_id and pink_inbound_detail.type = pink_operate_item.item_type";
        test.testCondition(twoConditions);
        
        // 测试多个条件用and连接
        System.out.println("\n=== 测试多个条件用and连接 ===");
        String multiConditions = "table1.col1 = table2.col2 and table1.col3 = table3.col4 and table1.col5 = table4.col6";
        test.testCondition(multiConditions);
        
        // 测试大小写不敏感的and
        System.out.println("\n=== 测试大小写不敏感的and ===");
        String caseInsensitiveAnd = "table1.col1 = table2.col2 AND table1.col3 = table3.col4";
        test.testCondition(caseInsensitiveAnd);
        
        // 测试空格处理
        System.out.println("\n=== 测试空格处理 ===");
        String spaceTest = "table1.col1=table2.col2  and   table1.col3=table3.col4";
        test.testCondition(spaceTest);
    }
    
    public void testCondition(String relationConditions) {
        System.out.println("输入: " + relationConditions);
        try {
            List<ColumnRelation> relations = parseSqlRelationConditions(relationConditions);
            System.out.println("解析成功，共 " + relations.size() + " 个关系:");
            for (int i = 0; i < relations.size(); i++) {
                System.out.println("  关系" + (i+1) + ": " + relations.get(i));
            }
        } catch (Exception e) {
            System.err.println("解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析SQL格式的关系条件
     * 格式：table1.column1 = table2.column2
     * 支持多个条件用 and 连接
     * 参考 ArchiveDomainService.parseRelationConditions 的实现
     */
    private List<ColumnRelation> parseSqlRelationConditions(String relationConditions) {
        if (relationConditions == null || relationConditions.trim().isEmpty()) {
            return null;
        }
        
        try {
            List<ColumnRelation> relations = new ArrayList<>();
            
            // 按and分割多个条件，大小写不敏感，支持空格
            String[] conditionParts = relationConditions.split("(?i)\\s+and\\s+");
            if (conditionParts.length == 0) {
                throw new RuntimeException("无效的关联条件格式");
            }
            
            // 验证每个条件的基本格式
            for (String condition : conditionParts) {
                condition = condition.trim();
                // 校验每个条件必须包含等号
                if (!condition.contains("=")) {
                    throw new RuntimeException("关联条件必须使用等值关联: " + condition);
                }
            }
            
            // 解析每个条件
            for (String condition : conditionParts) {
                condition = condition.trim();
                
                // 解析单个条件
                String[] parts = condition.split("=");
                if (parts.length != 2) {
                    throw new RuntimeException("关联条件格式错误，应为 table1.column1 = table2.column2: " + condition);
                }
                
                String left = parts[0].trim().replace("`", "");
                String right = parts[1].trim().replace("`", "");
                
                // 解析表名和字段名
                String[] leftArr = left.split("\\.");
                String[] rightArr = right.split("\\.");
                
                if (leftArr.length != 2 || rightArr.length != 2) {
                    throw new RuntimeException("关联条件格式错误，应为 table1.column1 = table2.column2: " + condition);
                }
                
                // 验证表名和字段名格式
                validateTableAndColumnFormat(leftArr[0], leftArr[1], "左侧");
                validateTableAndColumnFormat(rightArr[0], rightArr[1], "右侧");
                
                // 创建ColumnRelation对象
                ColumnRelation relation = new ColumnRelation();
                relation.setCurrentColumn(leftArr[1]);      // 当前表字段
                relation.setRelatedColumn(rightArr[1]);    // 关联表字段
                relation.setRelatedTable(rightArr[0]);     // 关联表名
                
                relations.add(relation);
            }
            
            return relations;
            
        } catch (Exception e) {
            throw new RuntimeException("SQL格式关系条件解析失败: " + relationConditions + ", 错误信息: " + e.getMessage());
        }
    }

    /**
     * 验证表名和字段名格式
     * 参考前端表单的正则表达式验证逻辑
     */
    private void validateTableAndColumnFormat(String tableName, String columnName, String side) {
        // 表名格式：字母开头，包含字母、数字、下划线
        if (!tableName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            throw new RuntimeException(side + "表名格式错误: " + tableName + "，应为字母开头，包含字母、数字、下划线");
        }
        
        // 字段名格式：字母开头，包含字母、数字、下划线
        if (!columnName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            throw new RuntimeException(side + "字段名格式错误: " + columnName + "，应为字母开头，包含字母、数字、下划线");
        }
    }
}
