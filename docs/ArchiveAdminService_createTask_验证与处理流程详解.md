# ArchiveAdminService#createTask 方法校验与处理流程详解

## 方法签名

```java
public TaskCreateResponse createTask(TaskInfoRequest request)
```

---

## 1. 入参说明

| 字段名           | 类型      | 必填 | 说明                         |
|------------------|-----------|------|------------------------------|
| appName          | String    | 是   | 应用名称                     |
| taskName         | String    | 是   | 任务名称（同应用下唯一）     |
| datasourceName   | String    | 是   | 数据源名称                   |
| databaseName     | String    | 是   | 数据库名称                   |
| limit            | Integer   | 是   | 扫描数量限制                 |
| interval         | Integer   | 是   | 扫描间隔（秒）               |
| status           | Integer   | 否   | 任务状态（0-暂停，1-运行）   |
| mode             | Integer   | 否   | 运行模式（1-debug，2-生产）  |
| executionType    | Integer   | 否   | 执行类型（1-全天，2-指定时间）|
| timeSpan         | String    | 否   | 时间范围（HH:mm-HH:mm）      |
| dataSourceUrl    | String    | 否   | 数据源URL                    |
| tableName        | String    | 是   | 根节点表名                   |
| ...              | ...       | ...  | 其它节点相关参数（见下）      |

---

## 2. 校验与处理流程

### 步骤1：索引列类型与参数校验（validateIndexColumn）

- **目标**：确保索引列类型、区间、保留天数、过滤条件等参数的合法性，防止危险配置。
- **细化逻辑**：
  1. **索引类型判断**：
     - 若 `indexColumType` 不为空，去除括号后缀（如 `int(11)` → `int`），判断类型属于日期型还是数值型。
     - **日期型**（date/datetime/timestamp）：
       - 强制 `indexType=2`（保留天数）。
       - 若无过滤条件且 `reserveDays` 为空或≤0，抛出异常：“没有过滤条件时，保留天数必填”。
     - **数值型**（int/bigint/float等）：
       - 强制 `indexType=1`（区间）。
       - 若区间起止都为空且无过滤条件，抛出异常：“区间值过滤时，过滤条件必填，避免误删”。
       - 自动拼接 `indexValue` 字段为 `indexStart-indexEnd`。
     - **其它类型**：抛出异常：“索引列类型不支持”。
  2. **无索引类型时**：若过滤条件也为空，抛出异常：“索引和过滤条件不能全部为空”。
- **异常场景**：
  - 索引类型不支持
  - 缺少保留天数或区间值
  - 缺少过滤条件

### 步骤2：任务领域对象转换（convertTaskConfigFromRequest）

- **目标**：将请求参数转换为领域对象 `ArchiveTaskDomain`，便于后续业务处理。
- **细化逻辑**：
  - 字段映射包括：`taskName`、`datasourceName`、`databaseName`、`limit`、`interval`、`status`、`mode`、`executionType`、`dataSourceUrl`、`start/end`（时间区间）、`taskDetails`（表结构）。
  - 根节点表（`taskDetails`）的各类索引、归档、过滤、插件等参数也在此组装。

### 步骤3：任务表结构校验

- **目标**：防止创建无表的任务。
- **逻辑**：
  - 如果 `taskDetails` 为空，直接返回失败，提示：“未配置任何表”。

### 步骤4：任务唯一性校验

- **目标**：确保同一应用下任务名称唯一。
- **逻辑**：
  - 查询当前 app 下所有任务配置（`getTaskConfig(request.getAppName())`），判断是否已存在同名任务。
  - 如果存在同名任务，抛出异常：“任务名称已经存在”。

### 步骤5：归档全局参数组装

- **目标**：组装全局归档参数，便于后续归档流程。
- **逻辑**：
  - 构造 `ArchiveGlobal`，填充 `appName`、`datasourceName`、`databaseName`。

### 步骤6：任务保存

- **目标**：持久化任务配置。
- **逻辑**：
  - 调用 `archiveDomainService.saveOrUpdate(createTask, archiveGlobal)` 持久化任务配置。

### 步骤7：返回结果

- **目标**：返回任务创建结果。
- **逻辑**：
  - 返回 `TaskCreateResponse`，标记 `success` 字段，若有校验警告则写入 `warnings` 字段。

---

## 3. 典型异常与提示

| 场景                         | 异常/提示内容                        |
|------------------------------|--------------------------------------|
| 索引类型不支持               | "索引列类型不支持"                   |
| 缺少保留天数或区间值         | "没有过滤条件时，保留天数必填"/"区间值过滤时，过滤条件必填，避免误删" |
| 缺少过滤条件                 | "索引和过滤条件不能全部为空"         |
| 任务名称重复                 | "任务名称已经存在"                   |
| 未配置任何表                 | "未配置任何表"（warnings 字段返回）   |

---

## 4. 返回结构说明

- **成功**：
  - `success: true`
  - 其它业务字段
- **失败**：
  - `success: false`
  - `warnings` 字段给出详细原因
  - 或抛出业务异常，前端可感知

---

## 5. 适用场景

- 归档任务的创建接口
- 任务配置的参数校验、唯一性校验、业务规则校验
- 归档任务的全局参数组装与持久化

---

如需进一步细化参数说明、异常码说明或配合流程图，请补充需求。 