# SCP-Archive 任务配置协议

## 概述

本文档定义了SCP-Archive系统的任务配置协议，基于Ark配置中心+Dubbo接口方案设计，支持任务新增、修改、删除的实时通知。协议设计遵循精简原则，与现有系统字段保持一致，确保兼容性。

## 设计原则

1. **精简性**：只包含需求中明确提到的功能字段
2. **兼容性**：与现有ArchiveTaskDomain、ArchiveNode等模型完全兼容
3. **扩展性**：支持Ark配置中心的版本控制和变更通知
4. **实用性**：满足系统核心功能需求

## 核心协议定义

### TaskConfig - 任务配置协议

```java
/**
 * SCP-Archive 任务配置协议
 * 
 * 基于Ark配置中心+Dubbo接口方案设计
 * 支持任务新增、修改、删除的实时通知
 * 与现有系统字段保持一致，确保兼容性
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class TaskConfig {
    
    // ==================== 任务基础信息 ====================
    
    /**
     * 任务ID - 全局唯一标识
     * 格式：task_${业务标识}_${时间戳}
     * 示例：task_order_archive_20241201120000
     * 用于Ark配置中心存储和版本控制
     * 对应现有ArchiveTaskDomain.taskName字段
     */
    private String taskId;
    
    /**
     * 任务名称 - 业务可读名称
     * 示例：订单数据归档任务
     * 对应现有ArchiveTaskDomain.taskName字段
     */
    private String taskName;
    
    /**
     * 任务状态 - 控制任务执行
     * 0: 暂停 1: 运行
     * 对应现有ArchiveTaskDomain.status字段
     */
    private Integer status;
    
    /**
     * 运行模式 - 调试/生产模式
     * 1: debug 2: 生产
     * 对应现有ArchiveTaskDomain.mode字段
     */
    private Integer mode;
    
    // ==================== 版本控制信息 ====================
    
    /**
     * 配置版本号 - 用于变更检测
     * 格式：递增数字，如1000023
     * Ark配置中心存储格式：task_${taskId}=${version}
     * 示例：task_order_archive_20241201120000=1000023
     */
    private String version;
    
    /**
     * 最后更新时间 - 配置变更时间戳
     */
    private Date lastUpdateTime;
    
    /**
     * 创建时间 - 任务创建时间戳
     */
    private Date createTime;
    
    // ==================== 数据源配置 ====================
    
    /**
     * 数据源名称 - 通过ApplicationContext获取
     * 通过applicationContext.getBeansOfType(DataSource.class)匹配
     * 示例：orderDataSource
     * 对应现有ArchiveTaskDomain.datasourceName字段
     */
    private String datasourceName;
    
    /**
     * 数据库名称 - 目标数据库
     * 示例：order_db
     * 对应现有ArchiveTaskDomain.databaseName字段
     */
    private String databaseName;
    
    // ==================== 执行配置 ====================
    
    /**
     * 扫描间隔 - 单位：秒
     * 默认：30秒
     * 对应现有ArchiveTaskDomain.interval字段
     */
    private Integer interval;
    
    /**
     * 扫描数量 - 每批处理记录数
     * 默认：100条
     * 对应现有ArchiveTaskDomain.limit字段
     */
    private Integer limit;
    
    /**
     * 执行类型 - 全天/指定时间
     * 1: 全天 2: 指定时间
     * 对应现有ArchiveTaskDomain.executionType字段
     */
    private Integer executionType;
    
    /**
     * 开始执行时间 - 格式：HH:mm
     * 示例：22:00
     * 对应现有ArchiveTaskDomain.start字段
     */
    private String start;
    
    /**
     * 结束执行时间 - 格式：HH:mm
     * 示例：06:00
     * 对应现有ArchiveTaskDomain.end字段
     */
    private String end;
    
    /**
     * 超时时间 - 单位：秒
     * 默认：3600秒(1小时)
     */
    private Integer timeoutSeconds;
    
    // ==================== 归档节点树 ====================
    
    /**
     * 根节点 - 归档逻辑的根节点
     * 支持复杂的树形结构，包含条件判断、数据转换等
     * 对应现有ArchiveTaskDomain.taskDetails字段
     */
    private ArchiveNode rootNode;
    
    // ==================== 内部状态字段 ====================
    
    /**
     * 最后执行时间 - 任务最后执行时间戳
     * 实时更新，不参与配置变更
     */
    private Date lastExecuteTime;
    
    /**
     * 下次执行时间 - 根据执行配置计算
     * 实时更新，不参与配置变更
     */
    private Date nextExecuteTime;
    
    // ==================== 构造函数和Getter/Setter ====================
    
    // 构造函数
    public TaskConfig() {}
    
    public TaskConfig(String taskId, String taskName) {
        this.taskId = taskId;
        this.taskName = taskName;
        this.createTime = new Date();
        this.lastUpdateTime = new Date();
        this.status = 0; // 默认暂停
        this.mode = 1; // 默认调试模式
        this.version = "1";
        this.interval = 30;
        this.limit = 100;
        this.timeoutSeconds = 3600;
        this.executionType = 1; // 默认全天执行
    }
    
    // Getter和Setter方法
    // ... (省略标准的getter/setter方法)
    
    // ==================== 业务方法 ====================
    
    /**
     * 检查任务是否可执行
     * @return true: 可执行, false: 不可执行
     */
    public boolean isExecutable() {
        return this.status == 1 && this.rootNode != null && this.datasourceName != null;
    }
    
    /**
     * 更新版本号
     * @param newVersion 新版本号
     */
    public void updateVersion(String newVersion) {
        this.version = newVersion;
        this.lastUpdateTime = new Date();
    }
    
    /**
     * 复制任务配置（用于执行前拷贝）
     * 只拷贝节点信息，保留实时配置
     * @return 任务配置副本
     */
    public TaskConfig copyForExecution() {
        TaskConfig copy = new TaskConfig();
        copy.setTaskId(this.taskId);
        copy.setTaskName(this.taskName);
        copy.setDatasourceName(this.datasourceName);
        copy.setDatabaseName(this.databaseName);
        copy.setRootNode(this.rootNode);
        copy.setInterval(this.interval);
        copy.setLimit(this.limit);
        copy.setTimeoutSeconds(this.timeoutSeconds);
        copy.setExecutionType(this.executionType);
        copy.setStart(this.start);
        copy.setEnd(this.end);
        // 不拷贝实时配置：status, mode, lastExecuteTime, nextExecuteTime等
        return copy;
    }
}
```

### ArchiveNode - 归档节点协议

```java
/**
 * 归档节点 - 基于现有ArchiveNode结构
 * 保持与现有系统的完全兼容
 */
public class ArchiveNode {
    
    /**
     * 表名
     * 对应现有ArchiveNode.tableName字段
     */
    private String tableName;
    
    /**
     * 查询条件
     * 对应现有ArchiveNode.condition字段
     */
    private String condition;
    
    /**
     * 子节点列表
     * 对应现有ArchiveNode.children字段
     */
    private List<ArchiveNode> children;
    
    /**
     * 是否归档
     * 对应现有ArchiveNode.isArchive字段
     */
    private Boolean isArchive;
    
    /**
     * 查询字段列表
     * 对应现有ArchiveNode.queryColumns字段
     */
    private List<String> queryColumns;
    
    /**
     * 主键列
     * 对应现有ArchiveNode.primaryKeyColumn字段
     */
    private String primaryKeyColumn;
    
    /**
     * 父节点
     * 对应现有ArchiveNode.parentNode字段
     */
    private ArchiveNode parentNode;
    
    /**
     * 节点状态
     * 对应现有ArchiveNode.status字段
     */
    private Integer status;
    
    /**
     * 是否为根节点
     * 对应现有ArchiveNode.rootNode字段
     */
    private boolean rootNode;
    
    /**
     * 是否为debug模式
     * 对应现有ArchiveNode.debugMode字段
     */
    private boolean debugMode;
    
    /**
     * 插件列表
     * 对应现有ArchiveNode.plugins字段
     */
    private List<ArchivePlugin> plugins;
    
    /**
     * 索引字段
     * 对应现有ArchiveNode.indexColumn字段
     */
    private String indexColumn;
    
    /**
     * 索引类型 1 区间 2 保留天数
     * 对应现有ArchiveNode.indexType字段
     */
    private Integer indexType;
    
    /**
     * 索引结束值
     * 对应现有ArchiveNode.indexEnd字段
     */
    private Long indexEnd;
    
    /**
     * 索引开始值
     * 对应现有ArchiveNode.indexStart字段
     */
    private Long indexStart;
    
    /**
     * 索引排序
     * 对应现有ArchiveNode.orderBy字段
     */
    private String orderBy;
    
    /**
     * 保留天数
     * 对应现有ArchiveNode.reserveDays字段
     */
    private Integer reserveDays;
    
    /**
     * 额外属性
     * 对应现有ArchiveNode.props字段
     */
    private Map<String, Object> props = new HashMap<>();
    
    /**
     * 关联关系
     * 对应现有ArchiveNode.relations字段
     */
    private List<ColumnRelation> relations;
    
    // 构造函数
    public ArchiveNode() {
        this.children = new ArrayList<>();
        this.props = new HashMap<>();
        this.isArchive = true;
        this.debugMode = false;
        this.rootNode = false;
    }
    
    /**
     * 获取节点键值
     * 对应现有ArchiveNode.getNodeKey()方法
     */
    public String getNodeKey() {
        return tableName;
    }
    
    // Getter和Setter方法
    // ... (省略标准的getter/setter方法)
}
```

### ColumnRelation - 列关联关系协议

```java
/**
 * 列关联关系 - 基于现有ColumnRelation结构
 */
public class ColumnRelation {
    
    /**
     * 当前表字段
     */
    private String currentColumn;
    
    /**
     * 关联表字段
     */
    private String relatedColumn;
    
    /**
     * 关联表名
     */
    private String relatedTable;
    
    // 构造函数和Getter/Setter
    public ColumnRelation() {}
    
    public ColumnRelation(String currentColumn, String relatedColumn, String relatedTable) {
        this.currentColumn = currentColumn;
        this.relatedColumn = relatedColumn;
        this.relatedTable = relatedTable;
    }
    
    // ... (省略getter/setter方法)
}
```

### ArchivePlugin - 归档插件协议

```java
/**
 * 归档插件接口 - 基于现有ArchivePlugin结构
 */
public interface ArchivePlugin {
    
    /**
     * 插件名称
     */
    String getPluginName();
    
    /**
     * 插件优先级
     */
    int getPriority();
    
    /**
     * 执行插件逻辑
     */
    PluginResult execute(ArchiveContext context);
}

/**
 * 插件执行结果
 */
public class PluginResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 是否需要中断执行
     */
    private boolean shouldInterrupt;
    
    // 构造函数和Getter/Setter
    public PluginResult() {}
    
    public PluginResult(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.shouldInterrupt = false;
    }
    
    public PluginResult(boolean success, String message, boolean shouldInterrupt) {
        this.success = success;
        this.message = message;
        this.shouldInterrupt = shouldInterrupt;
    }
    
    // ... (省略getter/setter方法)
}
```

## 字段映射关系

### TaskConfig 与现有系统字段对应关系

| TaskConfig字段 | 现有系统字段 | 说明 |
|---------------|-------------|------|
| taskId | ArchiveTaskDomain.taskName | 任务唯一标识 |
| taskName | ArchiveTaskDomain.taskName | 任务名称 |
| status | ArchiveTaskDomain.status | 任务状态 (0:暂停 1:运行) |
| mode | ArchiveTaskDomain.mode | 运行模式 (1:debug 2:生产) |
| datasourceName | ArchiveTaskDomain.datasourceName | 数据源名称 |
| databaseName | ArchiveTaskDomain.databaseName | 数据库名称 |
| interval | ArchiveTaskDomain.interval | 扫描间隔 |
| limit | ArchiveTaskDomain.limit | 扫描数量 |
| executionType | ArchiveTaskDomain.executionType | 执行类型 |
| start | ArchiveTaskDomain.start | 开始时间 |
| end | ArchiveTaskDomain.end | 结束时间 |
| rootNode | ArchiveTaskDomain.taskDetails | 归档节点树 |

### ArchiveNode 与现有系统字段对应关系

| ArchiveNode字段 | 现有系统字段 | 说明 |
|----------------|-------------|------|
| tableName | ArchiveNode.tableName | 表名 |
| condition | ArchiveNode.condition | 查询条件 |
| children | ArchiveNode.children | 子节点列表 |
| isArchive | ArchiveNode.isArchive | 是否归档 |
| queryColumns | ArchiveNode.queryColumns | 查询字段列表 |
| primaryKeyColumn | ArchiveNode.primaryKeyColumn | 主键列 |
| parentNode | ArchiveNode.parentNode | 父节点 |
| status | ArchiveNode.status | 节点状态 |
| rootNode | ArchiveNode.rootNode | 是否为根节点 |
| debugMode | ArchiveNode.debugMode | 是否为debug模式 |
| plugins | ArchiveNode.plugins | 插件列表 |
| indexColumn | ArchiveNode.indexColumn | 索引字段 |
| indexType | ArchiveNode.indexType | 索引类型 |
| indexEnd | ArchiveNode.indexEnd | 索引结束值 |
| indexStart | ArchiveNode.indexStart | 索引开始值 |
| orderBy | ArchiveNode.orderBy | 索引排序 |
| reserveDays | ArchiveNode.reserveDays | 保留天数 |
| props | ArchiveNode.props | 额外属性 |
| relations | ArchiveNode.relations | 关联关系 |

## 配置存储格式

### Ark配置中心存储格式

```properties
# 任务版本号存储格式
task_order_archive_20241201120000=1000023
task_user_archive_20241201120001=1000045
task_payment_archive_20241201120002=1000067
```

### Dubbo接口配置数据格式

```json
{
  "taskId": "task_order_archive_20241201120000",
  "taskName": "订单数据归档任务",
  "status": 1,
  "mode": 2,
  "version": "1000023",
  "datasourceName": "orderDataSource",
  "databaseName": "order_db",
  "interval": 30,
  "limit": 100,
  "executionType": 1,
  "timeoutSeconds": 3600,
  "rootNode": {
    "tableName": "order_info",
    "condition": "created_time < date_sub(now(), interval 30 day)",
    "isArchive": true,
    "indexColumn": "id",
    "indexType": 1,
    "orderBy": "asc",
    "children": [
      {
        "tableName": "order_item",
        "condition": "order_id = ?",
        "isArchive": true
      }
    ]
  }
}
```

## 版本控制机制

### 版本号生成规则

1. **格式**：递增数字，如1000023
2. **生成时机**：配置变更时自动递增
3. **唯一性**：时间戳+随机数保证唯一性
4. **存储位置**：Ark配置中心

### 变更检测流程

1. **监听配置变更**：通过Ark配置中心的addListener机制
2. **版本号对比**：比较本地版本号与Ark配置中心版本号
3. **配置更新**：版本号不一致时通过Dubbo接口获取最新配置
4. **本地缓存更新**：更新本地配置缓存

### 配置变更类型

1. **新增任务**：本地没有该任务ID，通过Dubbo接口获取完整配置
2. **修改任务**：版本号变更，通过Dubbo接口获取最新配置
3. **删除任务**：Ark配置中心移除该任务ID，本地标记删除

## 执行机制

### 配置拷贝策略

```java
/**
 * 执行前配置拷贝
 * 避免实时配置修改影响执行
 */
public TaskConfig copyForExecution() {
    TaskConfig copy = new TaskConfig();
    // 拷贝节点信息
    copy.setRootNode(this.rootNode);
    copy.setDatasourceName(this.datasourceName);
    copy.setDatabaseName(this.databaseName);
    // 拷贝执行配置
    copy.setInterval(this.interval);
    copy.setLimit(this.limit);
    copy.setTimeoutSeconds(this.timeoutSeconds);
    copy.setExecutionType(this.executionType);
    copy.setStart(this.start);
    copy.setEnd(this.end);
    // 不拷贝实时配置
    // status, mode, lastExecuteTime, nextExecuteTime等
    return copy;
}
```

### 实时配置字段

以下字段支持实时修改，不影响正在执行的任务：

- `status`：任务状态
- `mode`：运行模式
- `interval`：扫描间隔
- `limit`：扫描数量
- `executionType`：执行类型
- `start`：开始时间
- `end`：结束时间
- `timeoutSeconds`：超时时间

### 静态配置字段

以下字段修改后需要重新加载配置：

- `rootNode`：归档节点树
- `datasourceName`：数据源名称
- `databaseName`：数据库名称

## 最佳实践

### 1. 任务ID命名规范

```java
// 推荐格式：task_${业务标识}_${时间戳}
String taskId = "task_order_archive_20241201120000";
String taskId = "task_user_archive_20241201120001";
String taskId = "task_payment_archive_20241201120002";
```

### 2. 数据源配置

```java
// 通过ApplicationContext获取数据源
@Autowired
private ApplicationContext applicationContext;

public DataSource getDataSource(String datasourceName) {
    Map<String, DataSource> dataSources = applicationContext.getBeansOfType(DataSource.class);
    return dataSources.get(datasourceName);
}
```

### 3. 版本号管理

```java
// 版本号递增
public void updateVersion() {
    String currentVersion = this.version;
    long newVersion = Long.parseLong(currentVersion) + 1;
    this.version = String.valueOf(newVersion);
    this.lastUpdateTime = new Date();
}
```

### 4. 配置验证

```java
// 配置验证
public boolean isValid() {
    return this.taskId != null 
        && this.taskName != null 
        && this.datasourceName != null 
        && this.rootNode != null;
}
```

## 总结

本任务配置协议设计遵循精简原则，与现有系统高度兼容，支持Ark配置中心+Dubbo接口的配置变更方案。通过版本控制机制实现配置的实时通知，通过配置拷贝策略确保执行稳定性。

协议设计充分考虑了现有系统的字段结构和业务逻辑，确保平滑迁移和功能完整性。 