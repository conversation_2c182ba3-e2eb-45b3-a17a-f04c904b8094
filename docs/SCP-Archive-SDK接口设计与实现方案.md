# SCP-Archive SDK接口设计与实现方案

## 概述

本文档定义了SCP-Archive系统的SDK接口设计，基于Ark配置中心+Dubbo接口方案，专注于配置获取能力，不包含配置变更功能。详细描述了新增、更新、删除任务时服务端和SDK的实现思路。

## 设计原则

1. **职责分离**：SDK只负责配置获取，不包含配置变更能力
2. **实时性**：通过Ark配置中心实现配置变更的实时通知
3. **可靠性**：通过Dubbo接口保证配置数据的完整性
4. **兼容性**：与现有系统架构保持一致

## SDK接口设计

### AppConfig - 应用级别配置

```java
/**
 * SCP-Archive 应用级别配置
 * 
 * 用于区分不同的接入客户端，与任务是一对多关系
 * 基于现有ArchiveDataConfig扩展，增加app_name字段
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AppConfig {
    
    /**
     * 应用名称 - 全局唯一标识
     * 格式：${spring.application.name}
     * 示例：order-service, user-service, payment-service
     */
    private String appName;
    

    
    /**
     * 全局开关 - 是否启用归档功能
     * 默认：true
     */
    private Boolean enable;
    
    /**
     * 线程数量 - 归档执行线程数
     * 默认：5
     */
    private Integer threads;
    
    /**
     * 锁过期时间 - 分布式锁过期时间（秒）
     * 默认：300秒
     */
    private Integer lockExpireSeconds;
    
    /**
     * 执行间隔 - 任务扫描间隔（秒）
     * 默认：5秒
     */
    private Integer interval;
    
    /**
     * 最大线程数 - 避免线程过多导致数据库压力
     * 默认：3
     */
    private Integer maxThreads;
    
    /**
     * 任务配置列表 - 一对多关系
     */
    private List<TaskConfig> tasks;
    
    /**
     * 版本号 - 用于变更检测
     */
    private Long version;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    // 构造函数
    public AppConfig() {
        this.enable = true;
        this.threads = 5;
        this.lockExpireSeconds = 300;
        this.interval = 5;
        this.maxThreads = 3;
        this.tasks = new ArrayList<>();
    }
    
    public AppConfig(String appName) {
        this();
        this.appName = appName;
    }
    
    // Getter和Setter方法
    // ... (省略标准的getter/setter方法)
    
    /**
     * 添加任务配置
     */
    public void addTask(TaskConfig task) {
        if (this.tasks == null) {
            this.tasks = new ArrayList<>();
        }
        this.tasks.add(task);
    }
    
    /**
     * 移除任务配置
     */
    public void removeTask(String taskId) {
        if (this.tasks != null) {
            this.tasks.removeIf(task -> Objects.equals(task.getTaskId(), taskId));
        }
    }
    
    /**
     * 获取任务配置
     */
    public TaskConfig getTask(String taskId) {
        if (this.tasks != null) {
            return this.tasks.stream()
                .filter(task -> Objects.equals(task.getTaskId(), taskId))
                .findFirst()
                .orElse(null);
        }
        return null;
    }
}
```

### ArchiveConfigService - 配置获取接口

```java
/**
 * SCP-Archive 配置获取服务接口
 * 
 * 基于Dubbo实现，提供配置获取能力
 * 不包含配置变更功能，变更通过Ark配置中心通知
 * 任务版本号包含在TaskConfig中，不单独提供版本号接口
 * 支持应用级别配置，区分不同的接入客户端
 * 所有配置获取操作必须指定appName，不支持查询所有应用配置
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public interface ArchiveConfigService {
    
    /**
     * 获取应用配置
     * 
     * @param appName 应用名称
     * @return 应用配置，如果不存在返回null
     */
    AppConfig getAppConfig(String appName);
    
    /**
     * 获取单个任务配置
     * 
     * @param appName 应用名称
     * @param taskId 任务ID
     * @return 任务配置，如果不存在返回null
     */
    TaskConfig getTaskConfig(String appName, Long taskId);
    
    /**
     * 获取应用下的所有任务配置
     * 
     * @param appName 应用名称
     * @return 任务配置列表
     */
    List<TaskConfig> getAppTaskConfigs(String appName);
    
    /**
     * 批量获取任务配置
     * 
     * @param appName 应用名称
     * @param taskIds 任务ID列表
     * @return 任务配置列表
     */
    List<TaskConfig> getTaskConfigs(String appName, List<Long> taskIds);
    
    /**
     * 检查应用是否存在
     * 
     * @param appName 应用名称
     * @return true: 存在, false: 不存在
     */
    boolean existsApp(String appName);
    
    /**
     * 检查任务是否存在
     * 
     * @param appName 应用名称
     * @param taskId 任务ID
     * @return true: 存在, false: 不存在
     */
    boolean existsTask(String appName, Long taskId);
}
```

### 服务端实现

```java
/**
 * SCP-Archive 配置服务实现
 * 
 * 服务端实现，提供配置存储和获取能力
 * 支持应用级别配置，区分不同的接入客户端
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class ArchiveConfigServiceImpl implements ArchiveConfigService {
    
    @Autowired
    private AppConfigRepository appConfigRepository;
    
    @Autowired
    private TaskConfigRepository taskConfigRepository;
    
    @Autowired
    private ArkConfigService arkConfigService;
    
    @Override
    public AppConfig getAppConfig(String appName) {
        if (StringUtils.isEmpty(appName)) {
            return null;
        }
        
        // 从数据库获取应用配置
        AppConfigEntity entity = appConfigRepository.findByAppName(appName);
        if (entity == null) {
            return null;
        }
        
        // 转换为DTO
        return convertToAppConfig(entity);
    }
    
    @Override
    public TaskConfig getTaskConfig(String appName, Long taskId) {
        if (StringUtils.isEmpty(appName) || taskId == null) {
            return null;
        }
        
        // 先获取应用配置ID
        AppConfigEntity appEntity = appConfigRepository.findByAppName(appName);
        if (appEntity == null) {
            return null;
        }
        
        // 从数据库获取任务配置
        TaskConfigEntity entity = taskConfigRepository.findByAppConfigIdAndTaskId(appEntity.getId(), taskId);
        if (entity == null) {
            return null;
        }
        
        // 转换为DTO
        return convertToTaskConfig(entity);
    }
    
    @Override
    public List<TaskConfig> getAppTaskConfigs(String appName) {
        if (StringUtils.isEmpty(appName)) {
            return Collections.emptyList();
        }
        
        // 先获取应用配置ID
        AppConfigEntity appEntity = appConfigRepository.findByAppName(appName);
        if (appEntity == null) {
            return Collections.emptyList();
        }
        
        // 从数据库获取应用下的所有任务配置
        List<TaskConfigEntity> entities = taskConfigRepository.findByAppConfigId(appEntity.getId());
        return entities.stream()
            .map(this::convertToTaskConfig)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<TaskConfig> getTaskConfigs(String appName, List<Long> taskIds) {
        if (StringUtils.isEmpty(appName) || CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        
        // 先获取应用配置ID
        AppConfigEntity appEntity = appConfigRepository.findByAppName(appName);
        if (appEntity == null) {
            return Collections.emptyList();
        }
        
        // 批量从数据库获取任务配置
        List<TaskConfigEntity> entities = taskConfigRepository.findByAppConfigIdAndTaskIdIn(appEntity.getId(), taskIds);
        return entities.stream()
            .map(this::convertToTaskConfig)
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean existsApp(String appName) {
        if (StringUtils.isEmpty(appName)) {
            return false;
        }
        
        return appConfigRepository.existsByAppName(appName);
    }
    
    @Override
    public boolean existsTask(String appName, Long taskId) {
        if (StringUtils.isEmpty(appName) || taskId == null) {
            return false;
        }
        
        // 先获取应用配置ID
        AppConfigEntity appEntity = appConfigRepository.findByAppName(appName);
        if (appEntity == null) {
            return false;
        }
        
        return taskConfigRepository.existsByAppConfigIdAndTaskId(appEntity.getId(), taskId);
    }
    
    /**
     * 应用配置实体转换为DTO
     */
    private AppConfig convertToAppConfig(AppConfigEntity entity) {
        AppConfig config = new AppConfig();
        config.setAppName(entity.getAppName());
        config.setEnable(entity.getEnable());
        config.setThreads(entity.getThreads());
        config.setLockExpireSeconds(entity.getLockExpireSeconds());
        config.setInterval(entity.getInterval());
        config.setMaxThreads(entity.getMaxThreads());
        config.setVersion(entity.getVersion());
        config.setCreateTime(entity.getCreateTime());
        config.setUpdateTime(entity.getUpdateTime());
        
        // 解析扩展字段
        if (StringUtils.hasText(entity.getExt())) {
            try {
                // 扩展字段可以存储任何格式的数据，根据业务需要解析
                // 这里不再自动解析为任务配置列表，因为任务配置现在通过外键关联
            } catch (Exception e) {
                log.error("解析扩展字段失败: {}", entity.getAppName(), e);
            }
        }
        
        return config;
    }
    
    /**
     * 任务配置实体转换为DTO
     */
    private TaskConfig convertToTaskConfig(TaskConfigEntity entity) {
        TaskConfig config = new TaskConfig();
        config.setTaskId(entity.getTaskId());
        config.setTaskName(entity.getTaskName());
        config.setStatus(entity.getStatus());
        config.setMode(entity.getMode());
        config.setVersion(entity.getVersion());
        config.setDatasourceName(entity.getDatasourceName());
        config.setDatabaseName(entity.getDatabaseName());
        config.setInterval(entity.getInterval());
        config.setLimit(entity.getLimit());
        config.setExecutionType(entity.getExecutionType());
        config.setStart(entity.getStart());
        config.setEnd(entity.getEnd());
        config.setTimeoutSeconds(entity.getTimeoutSeconds());
        config.setCreateTime(entity.getCreateTime());
        config.setLastUpdateTime(entity.getUpdateTime());
        
        // 解析归档节点树
        if (StringUtils.hasText(entity.getConfigData())) {
            try {
                ArchiveNode rootNode = JsonUtils.parseObject(entity.getConfigData(), ArchiveNode.class);
                config.setRootNode(rootNode);
            } catch (Exception e) {
                log.error("解析归档节点树失败: {}", entity.getTaskId(), e);
            }
        }
        
        return config;
    }
}
```

## 新增任务实现

### 服务端实现思路

#### 1. 任务创建流程

```java
/**
 * 任务管理服务
 */
@Service
public class TaskManagementService {
    
    @Autowired
    private TaskConfigRepository taskConfigRepository;
    
    @Autowired
    private ArkConfigService arkConfigService;
    
    /**
     * 创建新任务
     */
    @Transactional
    public void createTask(String appName, TaskConfig taskConfig) {
        // 1. 验证应用是否存在并获取应用配置ID
        AppConfigEntity appEntity = appConfigRepository.findByAppName(appName);
        if (appEntity == null) {
            throw new RuntimeException("应用不存在: " + appName);
        }
        
        // 2. 生成任务ID
        Long taskId = generateTaskId();
        taskConfig.setTaskId(taskId);
        
        // 3. 生成版本号
        Long version = generateVersion();
        taskConfig.setVersion(version);
        
        // 4. 保存到数据库
        TaskConfigEntity entity = convertToEntity(appEntity.getId(), taskConfig);
        taskConfigRepository.save(entity);
        
        // 5. 更新Ark配置中心
        updateArkConfig(appName, taskId, version);
        
        log.info("任务创建成功: appName={}, taskId={}, version={}", appName, taskId, version);
    }
    
    /**
     * 生成任务ID
     */
    private Long generateTaskId() {
        return System.currentTimeMillis();
    }
    
    /**
     * 生成版本号
     */
    private Long generateVersion() {
        return System.currentTimeMillis();
    }
    
    /**
     * 更新Ark配置中心
     */
    private void updateArkConfig(String appName, Long taskId, Long version) {
        String arkKey = "task_" + taskId;
        
        try {
            boolean success = arkConfigService.publishValue(
                PublishValueRequest.builder()
                    .dataId(appName + ".properties")
                    .groupId("${spring.application.name}")
                    .key(arkKey)
                    .value(String.valueOf(version))
                    .build()
            );
            
            if (!success) {
                throw new RuntimeException("更新Ark配置中心失败: " + appName + ":" + taskId);
            }
        } catch (Exception e) {
            log.error("更新Ark配置中心失败: {}:{}", appName, taskId, e);
            throw new RuntimeException("更新Ark配置中心失败", e);
        }
    }
}
```

#### 2. 数据库表结构

```sql
-- 应用配置表
CREATE TABLE `archive_app_config` (
  `id` bigint AUTO_INCREMENT NOT NULL COMMENT '主键ID',
  `app_name` varchar(64) NOT NULL DEFAULT '' COMMENT '应用名称',
  `enable` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用',
  `threads` int NOT NULL DEFAULT '5' COMMENT '线程数量',
  `lock_expire_seconds` int NOT NULL DEFAULT '300' COMMENT '锁过期时间',
  `interval` int NOT NULL DEFAULT '5' COMMENT '执行间隔',
  `max_threads` int NOT NULL DEFAULT '3' COMMENT '最大线程数',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号',
  `ext` text NULL DEFAULT NULL COMMENT '扩展字段',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_enable` (`enable`),
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_app_name` (`app_name`)
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 COMMENT = '归档应用配置表';

-- 任务配置表
CREATE TABLE `archive_task_config` (
  `id` bigint AUTO_INCREMENT NOT NULL COMMENT '主键ID',
  `app_config_id` bigint NOT NULL DEFAULT '0' COMMENT '应用配置ID',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID',
  `task_name` varchar(128) NOT NULL DEFAULT '' COMMENT '任务名称',
  `datasource_name` varchar(64) NOT NULL DEFAULT '' COMMENT '数据源名称',
  `database_name` varchar(64) NOT NULL DEFAULT '' COMMENT '数据库名称',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号',
  `status` int NOT NULL DEFAULT '0' COMMENT '任务状态',
  `mode` int NOT NULL DEFAULT '1' COMMENT '运行模式',
  `interval` int NOT NULL DEFAULT '30' COMMENT '扫描间隔',
  `limit` int NOT NULL DEFAULT '100' COMMENT '扫描数量',
  `execution_type` int NOT NULL DEFAULT '1' COMMENT '执行类型',
  `start` bigint NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end` bigint NOT NULL DEFAULT '0' COMMENT '结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_app_config_id` (`app_config_id`),
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_status` (`status`),
  INDEX `idx_task_id` (`task_id`),
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_task_id` (`task_id`)
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 COMMENT = '归档任务配置表';

-- 节点配置表
CREATE TABLE `archive_node_config` (
  `id` bigint AUTO_INCREMENT NOT NULL COMMENT '主键ID',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID',
  `node_id` bigint NOT NULL DEFAULT '0' COMMENT '节点ID',
  `parent_node_id` bigint NOT NULL DEFAULT '0' COMMENT '父节点ID',
  `table_name` varchar(128) NOT NULL DEFAULT '' COMMENT '表名',
  `condition` text NULL DEFAULT NULL COMMENT '查询条件',
  `datasource_name` varchar(64) NOT NULL DEFAULT '' COMMENT '节点级数据源名称',
  `database_name` varchar(64) NOT NULL DEFAULT '' COMMENT '节点级数据库名称',
  `is_archive` tinyint NOT NULL DEFAULT '1' COMMENT '是否归档',
  `query_columns` text NULL DEFAULT NULL COMMENT '查询字段列表（JSON格式）',
  `primary_key_column` varchar(64) NOT NULL DEFAULT '' COMMENT '主键列',
  `status` int NOT NULL DEFAULT '1' COMMENT '节点状态',
  `root_node` tinyint NOT NULL DEFAULT '0' COMMENT '是否为根节点',
  `debug_mode` tinyint NOT NULL DEFAULT '0' COMMENT '是否为debug模式',
  `index_column` varchar(64) NOT NULL DEFAULT '' COMMENT '索引字段',
  `order_by` varchar(64) NOT NULL DEFAULT '' COMMENT '排序字段',
  `archive_type` int NOT NULL DEFAULT '1' COMMENT '归档类型 1:先归档子节点 2:先归档父节点',
  `filter_default_value` int NOT NULL DEFAULT '0' COMMENT '默认值过滤 0:不过滤 1:过滤',
  `sharding_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用分片',
  `sharding_expression` varchar(256) NOT NULL DEFAULT '' COMMENT '分片表达式',
  `sharding_plugin_class` varchar(256) NOT NULL DEFAULT '' COMMENT '分片插件全类名',
  `sharding_field` varchar(64) NOT NULL DEFAULT '' COMMENT '分片字段名称',
  `plugins` text NULL DEFAULT NULL COMMENT '插件列表（JSON格式）',
  `relations` text NULL DEFAULT NULL COMMENT '关联关系（JSON格式）',
  `props` text NULL DEFAULT NULL COMMENT '额外属性（JSON格式）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_parent_node_id` (`parent_node_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_table_name` (`table_name`),
  INDEX `idx_task_id` (`task_id`),
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_task_node` (`task_id`, `node_id`)
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 COMMENT = '归档节点配置表';
```

### SDK实现思路

#### 1. 配置监听器

```java
/**
 * SDK配置监听器
 * 监听Ark配置中心变更，自动更新本地配置
 */
@Component
public class ArchiveConfigListener {
    
    @Autowired
    private ArchiveConfigService configService;
    
    @Autowired
    private ConfigCache configCache;
    
    /**
     * Ark配置变更监听
     */
    @ArkConfigChangeListener(
        region = {"${spring.application.name}#${spring.application.name}.properties"},
        keyPrefixes = {"task_"}
    )
    public void onConfigChanged(ConfigChangeEvent event) {
        log.info("收到配置变更事件: {}", event);
        
        for (ConfigChangeItem item : event.getChangeItems()) {
            String key = item.getKey();
            String newValue = item.getNewValue();
            String oldValue = item.getOldValue();
            
            // 提取任务ID
            String taskId = extractTaskId(key);
            if (taskId == null) {
                continue;
            }
            
            // 获取应用名称
            String appName = getAppNameFromEvent(event);
            
            // 处理配置变更
            handleConfigChange(appName, taskId, newValue, oldValue);
        }
    }
    
    /**
     * 处理配置变更
     */
    private void handleConfigChange(String appName, String taskId, String newVersion, String oldVersion) {
        try {
            Long taskIdLong = Long.parseLong(taskId);
            if (oldVersion == null) {
                // 新增任务
                handleNewTask(appName, taskIdLong, newVersion);
            } else if (newVersion == null) {
                // 删除任务
                handleDeletedTask(appName, taskIdLong);
            } else {
                // 更新任务
                handleUpdatedTask(appName, taskIdLong, newVersion, oldVersion);
            }
        } catch (Exception e) {
            log.error("处理配置变更失败: appName={}, taskId={}, newVersion={}, oldVersion={}", 
                appName, taskId, newVersion, oldVersion, e);
        }
    }
    
    /**
     * 处理新增任务
     */
    private void handleNewTask(String appName, Long taskId, String version) {
        log.info("检测到新增任务: appName={}, taskId={}, version={}", appName, taskId, version);
        
        // 通过Dubbo接口获取完整配置
        TaskConfig taskConfig = configService.getTaskConfig(appName, taskId);
        if (taskConfig == null) {
            log.warn("新增任务配置获取失败: appName={}, taskId={}", appName, taskId);
            return;
        }
        
        // 更新本地缓存
        configCache.updateConfig(appName, taskId, taskConfig);
        
        log.info("新增任务配置更新成功: appName={}, taskId={}", appName, taskId);
    }
    
    /**
     * 从事件中获取应用名称
     */
    private String getAppNameFromEvent(ConfigChangeEvent event) {
        // 从事件的数据ID中提取应用名称
        String dataId = event.getDataId();
        if (dataId != null && dataId.endsWith(".properties")) {
            return dataId.substring(0, dataId.length() - 11); // 去掉".properties"
        }
        return "default"; // 默认应用名称
    }
    
    /**
     * 提取任务ID
     */
    private String extractTaskId(String key) {
        if (key.startsWith("task_")) {
            return key.substring(5); // 去掉"task_"前缀
        }
        return null;
    }
}
```

#### 2. 配置缓存管理

```java
/**
 * SDK配置缓存管理
 * 管理本地配置缓存，提供配置获取能力
 * 支持应用级别配置，区分不同的接入客户端
 */
@Component
public class ConfigCache {
    
    // 应用配置缓存：appName -> AppConfig
    private final Map<String, AppConfig> appConfigMap = new ConcurrentHashMap<>();
    
    // 任务配置缓存：appName:taskId -> TaskConfig
    private final Map<String, TaskConfig> taskConfigMap = new ConcurrentHashMap<>();
    
    // 版本号缓存：appName:taskId -> version
    private final Map<String, String> versionMap = new ConcurrentHashMap<>();
    
    /**
     * 更新应用配置
     */
    public void updateAppConfig(String appName, AppConfig config) {
        appConfigMap.put(appName, config);
        log.info("应用配置缓存更新: appName={}, version={}", appName, config.getVersion());
    }
    
    /**
     * 更新任务配置
     */
    public void updateTaskConfig(String appName, String taskId, TaskConfig config) {
        String key = appName + ":" + taskId;
        taskConfigMap.put(key, config);
        versionMap.put(key, config.getVersion());
        log.info("任务配置缓存更新: appName={}, taskId={}, version={}", appName, taskId, config.getVersion());
    }
    
    /**
     * 获取应用配置
     */
    public AppConfig getAppConfig(String appName) {
        return appConfigMap.get(appName);
    }
    
    /**
     * 获取任务配置
     */
    public TaskConfig getTaskConfig(String appName, String taskId) {
        String key = appName + ":" + taskId;
        return taskConfigMap.get(key);
    }
    
    /**
     * 检查版本是否变更
     */
    public boolean isVersionChanged(String appName, String taskId, String version) {
        String key = appName + ":" + taskId;
        String localVersion = versionMap.get(key);
        return !Objects.equals(localVersion, version);
    }
    
    /**
     * 移除应用配置
     */
    public void removeAppConfig(String appName) {
        appConfigMap.remove(appName);
        log.info("应用配置缓存移除: appName={}", appName);
    }
    
    /**
     * 移除任务配置
     */
    public void removeTaskConfig(String appName, String taskId) {
        String key = appName + ":" + taskId;
        taskConfigMap.remove(key);
        versionMap.remove(key);
        log.info("任务配置缓存移除: appName={}, taskId={}", appName, taskId);
    }
    

    
    /**
     * 获取应用下的所有任务配置
     */
    public Map<String, TaskConfig> getAppTaskConfigs(String appName) {
        Map<String, TaskConfig> result = new HashMap<>();
        String prefix = appName + ":";
        
        taskConfigMap.entrySet().stream()
            .filter(entry -> entry.getKey().startsWith(prefix))
            .forEach(entry -> {
                String taskId = entry.getKey().substring(prefix.length());
                result.put(taskId, entry.getValue());
            });
        
        return result;
    }
    
    /**
     * 获取所有任务配置
     */
    public Map<String, TaskConfig> getAllTaskConfigs() {
        return new HashMap<>(taskConfigMap);
    }
}
```

## 更新任务实现

### 服务端实现思路

#### 1. 任务更新流程

```java
/**
 * 任务管理服务 - 更新任务
 */
@Service
public class TaskManagementService {
    
    /**
     * 更新任务
     */
    @Transactional
    public void updateTask(Long taskId, TaskConfig taskConfig) {
        // 1. 验证任务是否存在
        TaskConfigEntity existingEntity = taskConfigRepository.findByTaskId(taskId);
        if (existingEntity == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        // 2. 生成新版本号
        Long newVersion = generateVersion();
        taskConfig.setVersion(newVersion);
        taskConfig.setTaskId(taskId);
        
        // 3. 更新数据库
        TaskConfigEntity entity = convertToEntity(taskConfig);
        entity.setId(existingEntity.getId());
        entity.setCreateTime(existingEntity.getCreateTime());
        taskConfigRepository.save(entity);
        
        // 4. 更新Ark配置中心
        updateArkConfig(taskId, newVersion);
        
        log.info("任务更新成功: taskId={}, version={}", taskId, newVersion);
    }
    
    /**
     * 更新任务状态（实时配置）
     */
    @Transactional
    public void updateTaskStatus(Long taskId, Integer status) {
        TaskConfigEntity entity = taskConfigRepository.findByTaskId(taskId);
        if (entity == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        entity.setStatus(status);
        taskConfigRepository.save(entity);
        
        log.info("任务状态更新: taskId={}, status={}", taskId, status);
    }
}
```

### SDK实现思路

#### 1. 更新任务处理

```java
/**
 * SDK配置监听器 - 更新任务处理
 */
@Component
public class ArchiveConfigListener {
    
    /**
     * 处理更新任务
     */
    private void handleUpdatedTask(String appName, Long taskId, String newVersion, String oldVersion) {
        log.info("检测到任务更新: appName={}, taskId={}, newVersion={}, oldVersion={}", 
            appName, taskId, newVersion, oldVersion);
        
        // 检查版本是否真的变更了
        if (Objects.equals(newVersion, oldVersion)) {
            log.info("版本号未变更，跳过更新: appName={}, taskId={}", appName, taskId);
            return;
        }
        
        // 通过Dubbo接口获取最新配置
        TaskConfig taskConfig = configService.getTaskConfig(appName, taskId);
        if (taskConfig == null) {
            log.warn("更新任务配置获取失败: appName={}, taskId={}", appName, taskId);
            return;
        }
        
        // 更新本地缓存
        configCache.updateConfig(appName, taskId, taskConfig);
        
        log.info("任务配置更新成功: appName={}, taskId={}, version={}", appName, taskId, newVersion);
    }
}
```

#### 2. 配置热更新

```java
/**
 * SDK配置热更新服务
 * 支持实时配置的热更新，不影响正在执行的任务
 */
@Component
public class ConfigHotUpdateService {
    
    @Autowired
    private ConfigCache configCache;
    
    /**
     * 热更新实时配置
     */
    public void hotUpdateRealTimeConfig(String taskId, Map<String, Object> realTimeConfig) {
        TaskConfig existingConfig = configCache.getConfig(taskId);
        if (existingConfig == null) {
            log.warn("任务配置不存在，无法热更新: taskId={}", taskId);
            return;
        }
        
        // 只更新实时配置字段
        if (realTimeConfig.containsKey("status")) {
            existingConfig.setStatus((Integer) realTimeConfig.get("status"));
        }
        if (realTimeConfig.containsKey("mode")) {
            existingConfig.setMode((Integer) realTimeConfig.get("mode"));
        }
        if (realTimeConfig.containsKey("interval")) {
            existingConfig.setInterval((Integer) realTimeConfig.get("interval"));
        }
        if (realTimeConfig.containsKey("limit")) {
            existingConfig.setLimit((Integer) realTimeConfig.get("limit"));
        }
        if (realTimeConfig.containsKey("executionType")) {
            existingConfig.setExecutionType((Integer) realTimeConfig.get("executionType"));
        }
        if (realTimeConfig.containsKey("start")) {
            existingConfig.setStart((String) realTimeConfig.get("start"));
        }
        if (realTimeConfig.containsKey("end")) {
            existingConfig.setEnd((String) realTimeConfig.get("end"));
        }
        if (realTimeConfig.containsKey("timeoutSeconds")) {
            existingConfig.setTimeoutSeconds((Integer) realTimeConfig.get("timeoutSeconds"));
        }
        
        // 更新缓存
        configCache.updateConfig(taskId, existingConfig);
        
        log.info("实时配置热更新成功: taskId={}", taskId);
    }
}
```

## 删除任务实现

### 服务端实现思路

#### 1. 任务删除流程

```java
/**
 * 任务管理服务 - 删除任务
 */
@Service
public class TaskManagementService {
    
    /**
     * 删除任务
     */
    @Transactional
    public void deleteTask(Long taskId) {
        // 1. 验证任务是否存在
        TaskConfigEntity entity = taskConfigRepository.findByTaskId(taskId);
        if (entity == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        // 2. 从数据库删除
        taskConfigRepository.deleteByTaskId(taskId);
        
        // 3. 从Ark配置中心删除
        deleteArkConfig(taskId);
        
        log.info("任务删除成功: taskId={}", taskId);
    }
    
    /**
     * 从Ark配置中心删除
     */
    private void deleteArkConfig(Long taskId) {
        String arkKey = "task_" + taskId;
        
        try {
            // 通过设置空值来删除配置
            boolean success = arkConfigService.publishValue(
                PublishValueRequest.builder()
                    .dataId("application.properties")
                    .groupId("${spring.application.name}")
                    .key(arkKey)
                    .value("") // 空值表示删除
                    .build()
            );
            
            if (!success) {
                throw new RuntimeException("删除Ark配置失败: " + taskId);
            }
        } catch (Exception e) {
            log.error("删除Ark配置失败: {}", taskId, e);
            throw new RuntimeException("删除Ark配置失败", e);
        }
    }
    
    /**
     * 批量删除任务
     */
    @Transactional
    public void deleteTasks(List<Long> taskIds) {
        for (Long taskId : taskIds) {
            try {
                deleteTask(taskId);
            } catch (Exception e) {
                log.error("删除任务失败: taskId={}", taskId, e);
                // 继续删除其他任务
            }
        }
    }
}
```

### SDK实现思路

#### 1. 删除任务处理

```java
/**
 * SDK配置监听器 - 删除任务处理
 */
@Component
public class ArchiveConfigListener {
    
    /**
     * 处理删除任务
     */
    private void handleDeletedTask(String appName, Long taskId) {
        log.info("检测到任务删除: appName={}, taskId={}", appName, taskId);
        
        // 从本地缓存移除配置
        configCache.removeTaskConfig(appName, taskId);
        
        // 通知任务执行器停止任务
        notifyTaskExecutor(appName, taskId, "DELETE");
        
        log.info("任务删除处理完成: appName={}, taskId={}", appName, taskId);
    }
    
    /**
     * 通知任务执行器
     */
    private void notifyTaskExecutor(String appName, Long taskId, String action) {
        try {
            // 发布任务变更事件
            TaskChangeEvent event = new TaskChangeEvent();
            event.setAppName(appName);
            event.setTaskId(taskId);
            event.setAction(action);
            event.setTimestamp(System.currentTimeMillis());
            
            applicationContext.publishEvent(event);
        } catch (Exception e) {
            log.error("通知任务执行器失败: appName={}, taskId={}, action={}", appName, taskId, action, e);
        }
    }
}
```

#### 2. 任务执行器响应

```java
/**
 * 任务执行器
 * 响应配置变更事件，管理任务执行
 */
@Component
public class TaskExecutor {
    
    private final Map<String, ScheduledFuture<?>> runningTasks = new ConcurrentHashMap<>();
    
    /**
     * 监听任务变更事件
     */
    @EventListener
    public void onTaskChange(TaskChangeEvent event) {
        String appName = event.getAppName();
        String taskId = event.getTaskId();
        String action = event.getAction();
        
        switch (action) {
            case "DELETE":
                stopTask(appName, taskId);
                break;
            case "UPDATE":
                restartTask(appName, taskId);
                break;
            case "CREATE":
                startTask(appName, taskId);
                break;
            default:
                log.warn("未知的任务变更动作: appName={}, taskId={}, action={}", appName, taskId, action);
        }
    }
    
    /**
     * 停止任务
     */
    private void stopTask(String appName, String taskId) {
        String key = appName + ":" + taskId;
        ScheduledFuture<?> future = runningTasks.remove(key);
        if (future != null) {
            future.cancel(false);
            log.info("任务已停止: appName={}, taskId={}", appName, taskId);
        }
    }
    
    /**
     * 启动任务
     */
    private void startTask(String appName, String taskId) {
        // 获取任务配置
        TaskConfig config = configCache.getTaskConfig(appName, taskId);
        if (config == null) {
            log.warn("任务配置不存在，无法启动: appName={}, taskId={}", appName, taskId);
            return;
        }
        
        // 检查任务是否可执行
        if (!config.isExecutable()) {
            log.info("任务不可执行，跳过启动: appName={}, taskId={}", appName, taskId);
            return;
        }
        
        // 启动任务
        String key = appName + ":" + taskId;
        ScheduledFuture<?> future = taskScheduler.scheduleAtFixedRate(
            () -> executeTask(config),
            Instant.now(),
            Duration.ofSeconds(config.getInterval())
        );
        
        runningTasks.put(key, future);
        log.info("任务已启动: appName={}, taskId={}", appName, taskId);
    }
    
    /**
     * 重启任务
     */
    private void restartTask(String appName, String taskId) {
        stopTask(appName, taskId);
        startTask(appName, taskId);
    }
}
```

## 配置初始化

### SDK启动时配置加载

```java
/**
 * SDK配置初始化服务
 * 启动时加载所有配置
 */
@Component
public class ConfigInitializationService {
    
    @Autowired
    private ArchiveConfigService configService;
    
    @Autowired
    private ConfigCache configCache;
    
    /**
     * 初始化配置
     */
    @PostConstruct
    public void initializeConfig() {
        log.info("开始初始化配置...");
        
        try {
            // 获取当前应用名称
            String appName = getCurrentAppName();
            
            // 获取应用配置
            AppConfig appConfig = configService.getAppConfig(appName);
            if (appConfig != null) {
                configCache.updateAppConfig(appName, appConfig);
                log.info("应用配置加载成功: appName={}", appName);
            }
            
            // 获取应用下的所有任务配置
            List<TaskConfig> taskConfigs = configService.getAppTaskConfigs(appName);
            log.info("获取到{}个任务配置", taskConfigs.size());
            
            // 更新本地缓存
            for (TaskConfig config : taskConfigs) {
                configCache.updateTaskConfig(appName, config.getTaskId(), config);
            }
            
            log.info("配置初始化完成，应用: {}, 任务数: {}", appName, taskConfigs.size());
            
        } catch (Exception e) {
            log.error("配置初始化失败", e);
            throw new RuntimeException("配置初始化失败", e);
        }
    }
    
    /**
     * 获取当前应用名称
     */
    private String getCurrentAppName() {
        return System.getProperty("spring.application.name", "default");
    }
}
```

## 配置变更重试与回滚机制

### 1. 重试机制

- **自动重试**：当SDK端监听到配置变更后，若通过Dubbo接口获取配置失败，系统会自动进行重试。默认重试3次（可配置），每次重试间隔递增（如1s、3s、10s）。
- **指数退避**：采用指数退避算法，避免短时间内对服务端造成压力。
- **本地缓存兜底**：重试期间，SDK继续使用本地缓存的旧配置，保证归档任务不中断。

### 2. 回滚与暂停归档

- **重试失败处理**：若重试次数超过最大阈值（如3次），SDK会自动暂停对应任务的归档执行，防止因配置不一致导致数据风险。
- **暂停机制**：暂停后，任务执行器不再调度该任务，并记录暂停原因和时间，便于后续排查。
- **恢复机制**：当配置获取恢复正常（如下次监听到变更并成功获取），SDK自动恢复归档任务的调度。

### 3. 监控与告警

- **失败告警**：配置获取失败、重试次数超限、任务被暂停等关键事件会通过日志和监控系统上报，支持邮件/短信/钉钉等多渠道告警。
- **可观测性**：可通过管理端或监控平台查看当前归档任务的配置状态、失败次数、暂停状态等信息。

### 4. 典型流程

1. Ark配置中心推送变更事件
2. SDK监听到变更，尝试Dubbo获取新配置
3. Dubbo接口异常/超时，自动重试
4. 超过最大重试次数，暂停归档任务，记录日志并告警
5. 后续配置恢复，自动恢复归档任务

### 5. 设计原则

- **安全优先**：配置异常时宁可暂停归档，避免数据风险
- **自动恢复**：配置恢复后自动恢复归档，无需人工介入
- **可观测性**：全链路日志、监控、告警，便于运维排查

---

> 该机制确保了归档系统在配置变更异常场景下的安全性和可用性，避免因配置不一致导致的数据丢失或业务中断。

## 节点配置管理

### 节点配置数据访问层

```java
/**
 * 节点配置数据访问层
 */
@Repository
public class ArchiveNodeConfigRepository {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 保存节点配置
     */
    public ArchiveNodeConfig save(ArchiveNodeConfig nodeConfig) {
        if (nodeConfig.getId() == null) {
            // 插入新节点
            String sql = "INSERT INTO archive_node_config (task_id, node_id, parent_node_id, table_name, " +
                        "condition, datasource_name, database_name, is_archive, query_columns, " +
                        "primary_key_column, status, root_node, debug_mode, index_column, order_by, " +
                        "archive_type, filter_default_value, sharding_enabled, sharding_expression, " +
                        "sharding_plugin_class, sharding_field, plugins, relations, props, create_time, update_time) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            jdbcTemplate.update(sql, 
                nodeConfig.getTaskId(), nodeConfig.getNodeId(), nodeConfig.getParentNodeId(),
                nodeConfig.getTableName(), nodeConfig.getCondition(),
                nodeConfig.getDatasourceName(), nodeConfig.getDatabaseName(), nodeConfig.getIsArchive(),
                JsonUtils.toJson(nodeConfig.getQueryColumns()), nodeConfig.getPrimaryKeyColumn(),
                nodeConfig.getStatus(), nodeConfig.getRootNode(), nodeConfig.getDebugMode(),
                nodeConfig.getIndexColumn(), nodeConfig.getOrderBy(), nodeConfig.getArchiveType(),
                nodeConfig.getFilterDefaultValue(), nodeConfig.getShardingEnabled(), nodeConfig.getShardingExpression(),
                nodeConfig.getShardingPluginClass(), nodeConfig.getShardingField(), JsonUtils.toJson(nodeConfig.getPlugins()),
                JsonUtils.toJson(nodeConfig.getRelations()), JsonUtils.toJson(nodeConfig.getProps()),
                nodeConfig.getCreateTime(), nodeConfig.getUpdateTime());
        } else {
            // 更新节点
            String sql = "UPDATE archive_node_config SET parent_node_id = ?, table_name = ?, " +
                        "condition = ?, datasource_name = ?, database_name = ?, is_archive = ?, query_columns = ?, " +
                        "primary_key_column = ?, status = ?, root_node = ?, debug_mode = ?, index_column = ?, " +
                        "order_by = ?, archive_type = ?, filter_default_value = ?, sharding_enabled = ?, " +
                        "sharding_expression = ?, sharding_plugin_class = ?, sharding_field = ?, plugins = ?, " +
                        "relations = ?, props = ?, update_time = ? WHERE id = ?";
            
            jdbcTemplate.update(sql,
                nodeConfig.getParentNodeId(), nodeConfig.getTableName(),
                nodeConfig.getCondition(), nodeConfig.getDatasourceName(), nodeConfig.getDatabaseName(),
                nodeConfig.getIsArchive(), JsonUtils.toJson(nodeConfig.getQueryColumns()),
                nodeConfig.getPrimaryKeyColumn(), nodeConfig.getStatus(), nodeConfig.getRootNode(),
                nodeConfig.getDebugMode(), nodeConfig.getIndexColumn(), nodeConfig.getOrderBy(),
                nodeConfig.getArchiveType(), nodeConfig.getFilterDefaultValue(), nodeConfig.getShardingEnabled(),
                nodeConfig.getShardingExpression(), nodeConfig.getShardingPluginClass(), nodeConfig.getShardingField(),
                JsonUtils.toJson(nodeConfig.getPlugins()), JsonUtils.toJson(nodeConfig.getRelations()),
                JsonUtils.toJson(nodeConfig.getProps()), nodeConfig.getUpdateTime(), nodeConfig.getId());
        }
        
        return nodeConfig;
    }
    
    /**
     * 根据任务ID获取所有节点配置
     */
    public List<ArchiveNodeConfig> findByTaskId(Long taskId) {
        String sql = "SELECT * FROM archive_node_config WHERE task_id = ? ORDER BY create_time ASC";
        return jdbcTemplate.query(sql, new Object[]{taskId}, new ArchiveNodeConfigRowMapper());
    }
    
    /**
     * 根据任务ID和节点ID获取节点配置
     */
    public ArchiveNodeConfig findByTaskIdAndNodeId(Long taskId, Long nodeId) {
        String sql = "SELECT * FROM archive_node_config WHERE task_id = ? AND node_id = ?";
        List<ArchiveNodeConfig> results = jdbcTemplate.query(sql, new Object[]{taskId, nodeId}, new ArchiveNodeConfigRowMapper());
        return results.isEmpty() ? null : results.get(0);
    }
    
    /**
     * 根据父节点ID获取子节点配置
     */
    public List<ArchiveNodeConfig> findByParentNodeId(Long taskId, Long parentNodeId) {
        String sql = "SELECT * FROM archive_node_config WHERE task_id = ? AND parent_node_id = ? ORDER BY create_time ASC";
        return jdbcTemplate.query(sql, new Object[]{taskId, parentNodeId}, new ArchiveNodeConfigRowMapper());
    }
    
    /**
     * 删除节点配置
     */
    public boolean deleteByTaskIdAndNodeId(Long taskId, Long nodeId) {
        String sql = "DELETE FROM archive_node_config WHERE task_id = ? AND node_id = ?";
        int rows = jdbcTemplate.update(sql, taskId, nodeId);
        return rows > 0;
    }
    
    /**
     * 删除任务的所有节点配置
     */
    public boolean deleteByTaskId(Long taskId) {
        String sql = "DELETE FROM archive_node_config WHERE task_id = ?";
        int rows = jdbcTemplate.update(sql, taskId);
        return rows > 0;
    }
}
```

### 节点配置模型

```java
/**
 * 节点配置模型
 */
@Data
public class ArchiveNodeConfig {
    private Long id;                    // 主键ID
    private Long taskId;                // 任务ID
    private Long nodeId;                // 节点ID
    private Long parentNodeId;          // 父节点ID
    private String tableName;           // 表名
    private String condition;           // 查询条件
    private String datasourceName;      // 节点级数据源名称
    private String databaseName;        // 节点级数据库名称
    private Boolean isArchive;          // 是否归档
    private List<String> queryColumns;  // 查询字段列表
    private String primaryKeyColumn;    // 主键列
    private Integer status;             // 节点状态
    private Boolean rootNode;           // 是否为根节点
    private Boolean debugMode;          // 是否为debug模式
    private String indexColumn;         // 索引字段
    private String orderBy;             // 排序字段
    private Integer archiveType;        // 归档类型 1:先归档子节点 2:先归档父节点
    private Integer filterDefaultValue; // 默认值过滤 0:不过滤 1:过滤
    private Boolean shardingEnabled;    // 是否启用分片
    private String shardingExpression;  // 分片表达式
    private String shardingPluginClass; // 分片插件全类名
    private String shardingField;       // 分片字段名称
    private List<String> plugins;       // 插件列表
    private List<ColumnRelation> relations; // 关联关系
    private Map<String, Object> props;  // 额外属性
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间
}
```

### 节点配置服务

```java
/**
 * 节点配置服务
 */
@Service
public class ArchiveNodeConfigService {
    
    @Autowired
    private ArchiveNodeConfigRepository nodeConfigRepository;
    
    /**
     * 保存节点配置
     */
    public ArchiveNodeConfig saveNodeConfig(ArchiveNodeConfig nodeConfig) {
        // 验证节点配置
        validateNodeConfig(nodeConfig);
        
        // 设置更新时间
        nodeConfig.setUpdateTime(new Date());
        
        // 保存节点配置
        return nodeConfigRepository.save(nodeConfig);
    }
    
    /**
     * 根据任务ID获取节点树
     */
    public List<ArchiveNodeConfig> getNodeTreeByTaskId(Long taskId) {
        // 获取所有节点配置
        List<ArchiveNodeConfig> allNodes = nodeConfigRepository.findByTaskId(taskId);
        
        // 构建节点树
        return buildNodeTree(allNodes);
    }
    
    /**
     * 构建节点树
     */
    private List<ArchiveNodeConfig> buildNodeTree(List<ArchiveNodeConfig> allNodes) {
        // 创建节点映射
        Map<Long, ArchiveNodeConfig> nodeMap = new HashMap<>();
        List<ArchiveNodeConfig> rootNodes = new ArrayList<>();
        
        // 初始化节点映射
        for (ArchiveNodeConfig node : allNodes) {
            nodeMap.put(node.getNodeId(), node);
        }
        
        // 构建树形结构
        for (ArchiveNodeConfig node : allNodes) {
            if (node.getParentNodeId() == null || node.getRootNode()) {
                rootNodes.add(node);
            } else {
                ArchiveNodeConfig parent = nodeMap.get(node.getParentNodeId());
                if (parent != null) {
                    // 这里需要扩展ArchiveNodeConfig模型，添加children字段
                    // 或者使用DTO来构建树形结构
                }
            }
        }
        
        return rootNodes;
    }
    
    /**
     * 验证节点配置
     */
    private void validateNodeConfig(ArchiveNodeConfig nodeConfig) {
        if (nodeConfig.getTaskId() == null) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        if (nodeConfig.getNodeId() == null) {
            throw new IllegalArgumentException("节点ID不能为空");
        }
        
        if (StringUtils.isEmpty(nodeConfig.getTableName())) {
            throw new IllegalArgumentException("表名不能为空");
        }
        
        if (StringUtils.isEmpty(nodeConfig.getCondition())) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        // 验证分片配置
        if (Boolean.TRUE.equals(nodeConfig.getShardingEnabled())) {
            if (StringUtils.isEmpty(nodeConfig.getShardingField())) {
                throw new IllegalArgumentException("启用分片时必须指定分片字段名称");
            }
            if (StringUtils.isEmpty(nodeConfig.getShardingExpression())) {
                throw new IllegalArgumentException("启用分片时必须指定分片表达式");
            }
            if (StringUtils.isEmpty(nodeConfig.getShardingPluginClass())) {
                throw new IllegalArgumentException("启用分片时必须指定分片插件全类名");
            }
        }
    }
}
```

## 总结

### 设计优势

1. **应用隔离**：通过app_name字段区分不同接入客户端，实现配置隔离
2. **安全限制**：所有配置获取操作必须指定appName，不支持查询所有应用配置
3. **一对多关系**：应用与任务是一对多关系，便于管理和维护
4. **接口精简**：去除配置变更能力，专注于配置获取
5. **版本控制**：版本号包含在TaskConfig中，简化接口设计
6. **实时性**：Ark配置中心秒级通知，Dubbo接口保证数据完整性
7. **可靠性**：多重保障机制，配置变更安全可靠
8. **扩展性**：支持复杂配置数据结构，满足业务需求
9. **节点独立**：节点配置独立存储，支持复杂的树形结构
10. **字段优化**：独立字段设计，支持高性能查询和索引

### 技术特点

- **应用级别配置**：基于ArchiveDataConfig扩展，增加app_name字段
- **解耦设计**：配置存储和通知机制分离
- **缓存优化**：本地缓存减少网络请求，支持应用级别缓存
- **异常处理**：完善的错误处理和重试机制
- **监控支持**：配置变更监控和告警

### 应用场景

1. **多应用部署**：不同应用使用不同的归档配置
2. **配置隔离**：每个应用的配置相互独立，互不影响
3. **统一管理**：通过管理端统一管理所有应用的配置
4. **灵活扩展**：支持新应用快速接入，无需修改现有配置

### 实现思路

1. **SDK接口**：专注于配置获取，不包含变更能力，版本号包含在TaskConfig中
2. **新增任务**：服务端创建任务并更新Ark配置，SDK监听并获取配置
3. **更新任务**：服务端更新任务并更新版本号，SDK检测版本变更并更新配置
4. **删除任务**：服务端删除任务并清理Ark配置，SDK移除本地配置并停止任务

### 最佳实践

1. **应用命名**：使用有意义的应用名称，便于识别和管理
2. **安全访问**：所有配置获取操作必须指定appName，确保配置隔离
3. **配置管理**：通过管理端统一管理配置
4. **版本控制**：每次变更生成新版本号
5. **监控告警**：配置变更及时监控和告警
6. **故障处理**：完善的降级和重试机制

通过Ark配置中心的实时通知机制和Dubbo接口的数据完整性保证，实现了高效可靠的配置管理。应用级别配置设计使得系统更加灵活和可扩展，支持多应用场景下的配置隔离和统一管理。 