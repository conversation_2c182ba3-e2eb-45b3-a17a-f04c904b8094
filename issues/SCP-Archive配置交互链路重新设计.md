# SCP-Archive配置交互链路重新设计任务计划

## 任务概述
根据SCP-Archive功能增强需求细化方案，重新设计配置交互链路，将admin（scm-rulecenter）考虑进去，实现SDK和admin的完全解耦。

## 当前架构分析
- **Admin端**：scm-rulecenter负责配置管理，提供Web界面
- **SDK端**：scp-archive-sdk负责配置获取和执行
- **问题**：当前配置交互链路不够清晰，admin和SDK耦合度高

## 目标架构
- **Admin层**：scm-rulecenter负责配置管理，通过dubbo接口提供配置服务
- **SDK层**：scp-archive-sdk负责配置获取和执行，支持配置监听
- **配置中心**：作为配置存储和同步的中间层

## 详细实施计划

### 阶段1：Admin端改造（scm-rulecenter）

#### 1.1 新增Dubbo服务接口
**文件**：`scm-rulecenter-api/src/main/java/com/shizhuang/scm/rulecenter/api/service/ArchiveConfigService.java`
- 定义配置CRUD接口
- 定义配置版本控制接口
- 定义配置验证接口

#### 1.2 实现配置管理逻辑
**文件**：`scm-rulecenter-application/src/main/java/com/shizhuang/scm/rulecenter/application/service/impl/ArchiveConfigServiceImpl.java`
- 实现配置CRUD操作
- 实现配置版本控制
- 实现配置验证逻辑

#### 1.3 配置同步机制
**文件**：`scm-rulecenter-domain/src/main/java/com/shizhuang/scm/rulecenter/domains/archive/service/ArchiveConfigDomainService.java`
- 将配置同步到配置中心
- 处理配置冲突
- 配置变更通知

### 阶段2：SDK端改造（scp-archive-sdk）

#### 2.1 配置获取服务
**文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ArchiveConfigClient.java`
- 从配置中心获取配置
- 配置缓存管理
- 配置更新策略

#### 2.2 配置监听机制
**文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ConfigurationListener.java`
- 监听配置中心变更
- 实时更新本地配置
- 配置变更事件处理

#### 2.3 执行引擎改造
**文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/service/impl/ArchiveConfigManagerImpl.java`
- 适配新的配置获取方式
- 支持多数据源配置
- 支持分片配置

### 阶段3：配置中心集成

#### 3.1 配置存储结构
- 设计配置项的存储格式
- 支持配置版本管理
- 支持配置备份和恢复

#### 3.2 配置同步策略
- admin到配置中心的同步机制
- 配置变更的原子性保证
- 配置回滚机制

#### 3.3 配置监听机制
- SDK监听配置变更的实现
- 配置变更的实时通知
- 配置变更的批量处理

### 阶段4：接口设计

#### 4.1 Dubbo服务接口
**文件**：`ArchiveConfigService.java`
```java
public interface ArchiveConfigService {
    // 配置CRUD
    TaskConfig getTaskConfig(String taskName);
    void saveTaskConfig(TaskConfig taskConfig);
    void updateTaskConfig(TaskConfig taskConfig);
    void deleteTaskConfig(String taskName);
    
    // 配置版本控制
    List<String> getTaskVersions(String taskName);
    TaskConfig getTaskConfig(String taskName, String version);
    void rollbackToVersion(String taskName, String version);
    
    // 配置验证
    ValidationResult validateTaskConfig(TaskConfig taskConfig);
}
```

#### 4.2 配置模型扩展
- 支持多数据源配置
- 支持分片配置
- 支持preview功能配置

#### 4.3 配置验证接口
- 配置语法验证
- 配置逻辑验证
- 配置依赖验证

## 文件结构规划

```
scm-rulecenter/
├── scm-rulecenter-api/
│   └── src/main/java/com/shizhuang/scm/rulecenter/api/
│       └── service/
│           └── ArchiveConfigService.java          # Dubbo服务接口
├── scm-rulecenter-application/
│   └── src/main/java/com/shizhuang/scm/rulecenter/application/service/impl/
│       └── ArchiveConfigServiceImpl.java          # Dubbo服务实现
└── scm-rulecenter-domain/
    └── src/main/java/com/shizhuang/scm/rulecenter/domains/archive/
        └── service/
            └── ArchiveConfigDomainService.java    # 领域服务

scp-archive-sdk/
├── src/main/java/com/shizhuang/duapp/scp/framework/archive/
│   ├── core/
│   │   ├── config/
│   │   │   ├── ArchiveConfigClient.java           # 配置客户端
│   │   │   ├── ConfigurationListener.java         # 配置监听器
│   │   │   └── impl/
│   │   │       └── ArchiveConfigClientImpl.java   # 配置客户端实现
│   │   └── service/
│   │       └── impl/
│   │           └── ArchiveConfigManagerImpl.java  # 配置管理器改造
│   └── integration/
│       └── ArchiveConfigServiceIntegration.java   # Dubbo集成
```

## 预期结果
- Admin和SDK完全解耦，支持独立部署
- 配置实时同步，支持多实例
- 保持向后兼容，平滑迁移
- 支持新功能（多数据源、分片、preview等）

## 风险评估
- **技术风险**：Dubbo服务稳定性、配置中心依赖
- **业务风险**：配置迁移过程中的数据一致性
- **性能风险**：网络调用开销、配置同步延迟

## 迁移策略
- 采用灰度发布，逐步切换
- 保留原有配置作为备份
- 提供回滚机制
- 全面测试验证

## 时间计划
- **阶段1**：Admin端改造（1周）
- **阶段2**：SDK端改造（1周）
- **阶段3**：配置中心集成（3天）
- **阶段4**：接口设计和集成测试（4天）
- **总计**：约3周 