# admin端Dubbo接口——任务配置查询设计文档

## 1. 接口概述
admin通过Dubbo接口向SDK提供任务配置信息查询能力，支持按应用（appName）、任务ID（taskId）等维度获取详细归档任务配置。接口遵循现有Dubbo接口规范，返回结构化数据，便于多端对接。

## 2. Dubbo接口定义
```java
public interface ArchiveConfigService {
    /**
     * 获取单个任务配置
     * @param appName 应用名称
     * @param taskId 任务ID
     * @return 任务配置（TaskConfig），不存在返回null
     */
    TaskConfig getTaskConfig(String appName, String taskId);

    /**
     * 获取应用下所有任务配置
     * @param appName 应用名称
     * @return 任务配置列表
     */
    List<TaskConfig> getAppTaskConfigs(String appName);
}
```

## 3. 请求参数说明
- `appName`（string）：应用名称，必填。
- `taskId`（string）：任务ID，必填（仅getTaskConfig）。

# 字段类型调整说明

> 说明：根据数据库与实现，taskId、nodeId、parentNodeId等主键/外键字段统一为Long类型，接口、协议、JSON示例全部同步。

---

## 4. 返回结构字段定义（JSON格式）

### 4.1 TaskConfig
```json
{
  "taskId": 1234567890123456,      // Long 任务唯一ID
  "taskName": "订单数据归档任务",
  "status": 1,
  "mode": 2,
  "version": "1000023",
  "lastUpdateTime": "2024-06-01T12:00:00Z",
  "createTime": "2024-05-01T10:00:00Z",
  "datasourceName": "orderDataSource",
  "databaseName": "order_db",
  "interval": 30,
  "limit": 100,
  "executionType": 1,
  "start": "22:00",
  "end": "06:00",
  "timeoutSeconds": 3600,
  "maxScanCount": 1000,           // int 最大扫描次数
  "pause": false,                 // boolean 是否暂停
  "scanMode": "primaryKey",     // string 扫描模式 condition/primaryKey
  "minPrimaryKey": "1000",      // string 主键最小值
  "baseTable": "order_info",    // string 主表名
  "rootNode": { /* ArchiveNode对象，见下 */ },
  "lastExecuteTime": "2024-06-01T12:00:00Z",
  "nextExecuteTime": "2024-06-01T13:00:00Z"
}
```

### 4.2 ArchiveNode
```json
{
  "nodeId": 9876543210987654,      // Long 节点唯一ID
  "taskId": 1234567890123456,      // Long 归属任务ID
  "parentNodeId": 0,               // Long 父节点ID，根节点为0或null
  "tableName": "order_info",
  "condition": "created_time < date_sub(now(), interval 30 day)",
  "children": [ /* ArchiveNode数组，递归结构 */ ],
  "isArchive": true,
  "queryColumns": ["id", "order_no"], // 不返回，客户端自己计算
  "primaryKeyColumn": "id",
  "status": 1,
  "rootNode": true,
  "debugMode": false,
  "plugins": [ /* ArchivePlugin数组 */ ],
  "indexColumn": "id",
  "indexType": 1,
  "indexEnd": 100000,
  "indexStart": 1,
  "orderBy": "asc",
  "reserveDays": 30,
  "props": { "key": "value" },
  "relations": [ /* ColumnRelation数组 */ ],
  "shardingEnabled": true,                // boolean 是否启用分片
  "shardingExpression": "order_id % 128",// string 分片表达式
  "shardingPluginClass": "com.xxx.Plugin",// string 分片插件全类名
  "shardingPluginConfig": { "k": "v" }, // map 分片插件配置
  "shardingField": "order_id",           // string 分片字段
  "shardCount": 128,                      // int 分片数量
  "shardingTableTemplate": "order_{shard}", // string 分片表名模板
  "enableScan": true,                     // boolean 是否开启扫描模式
  "minPrimaryKey": "1000"                // string 主键最小值
}
```

### 4.3 ArchivePlugin
```json
{
  "pluginName": "string 插件名称",
  "priority": "int 插件优先级"
}
```

### 4.4 ColumnRelation
```json
{
  "currentColumn": "string 当前表字段",
  "relatedColumn": "string 关联表字段",
  "relatedTable": "string 关联表名"
}
```

## 5. 典型请求/响应示例

### 5.1 getTaskConfig 请求
```json
{
  "appName": "order-service",
  "taskId": "task_order_archive_20241201120000"
}
```

### 5.2 getTaskConfig 响应（示例）
```json
{
  "taskId": 1234567890123456,
  "taskName": "订单数据归档任务",
  "status": 1,
  "mode": 2,
  "version": "1000023",
  "lastUpdateTime": "2024-06-01T12:00:00Z",
  "createTime": "2024-05-01T10:00:00Z",
  "datasourceName": "orderDataSource",
  "databaseName": "order_db",
  "interval": 30,
  "limit": 100,
  "executionType": 1,
  "start": "22:00",
  "end": "06:00",
  "timeoutSeconds": 3600,
  "rootNode": {
    "nodeId": 9876543210987654,
    "taskId": 1234567890123456,
    "parentNodeId": 0,
    "tableName": "order_info",
    "condition": "created_time < date_sub(now(), interval 30 day)",
    "isArchive": true,
    "indexColumn": "id",
    "indexType": 1,
    "orderBy": "asc",
    "children": [
      {
        "nodeId": 9876543210987655,
        "taskId": 1234567890123456,
        "parentNodeId": 9876543210987654,
        "tableName": "order_item",
        "condition": "order_id = ?",
        "isArchive": true
      }
    ]
  }
}
```

## 6. 异常与错误码
- 查询不到配置时返回null或空列表。
- Dubbo异常按统一规范抛出（如RpcException），建议SDK端处理网络/序列化等异常。

## 7. 获取全局配置接口（不返回任务明细）

### 7.1 Dubbo接口定义
```java
public interface ArchiveConfigService {
    /**
     * 获取全局配置（应用级别配置，不包含任务明细）
     * @param appName 应用名称
     * @return 全局配置（AppConfig），不存在返回null
     */
    AppConfig getAppConfig(String appName);
}
```

### 7.2 请求参数说明
- `appName`（string）：应用名称，必填。

### 7.3 返回结构字段定义（JSON格式）
#### AppConfig（不含任务明细）
```json
{
  "appName": "string 应用名称，如 order-service",
  "enable": "boolean 是否启用归档功能，默认true",
  "threads": "int 归档执行线程数，默认5",
  "lockExpireSeconds": "int 分布式锁过期时间（秒），默认300",
  "interval": "int 任务扫描间隔（秒），默认5",
  "maxThreads": "int 最大线程数，默认3",
  "version": "long 配置版本号",
  "createTime": "string 创建时间，ISO8601格式",
  "updateTime": "string 更新时间，ISO8601格式"
}
```

### 7.4 典型请求/响应示例
#### getAppConfig 请求
```json
{
  "appName": "order-service"
}
```

#### getAppConfig 响应
```json
{
  "appName": "order-service",
  "enable": true,
  "threads": 5,
  "lockExpireSeconds": 300,
  "interval": 5,
  "maxThreads": 3,
  "version": 1000023,
  "createTime": "2024-05-01T10:00:00Z",
  "updateTime": "2024-06-01T12:00:00Z"
}
```

> 说明：所有ID相关字段均为Long类型，接口、协议、数据库、Java对象保持一致。

---
如需补充接口、字段或示例，请告知。 

# 协议字段补全说明

> 说明：根据SDK端实际使用，补充协议中缺失的字段，确保admin与SDK协议完全一致。

---

## 4.1 TaskConfig（补全字段）
```json
{
  "taskId": 1234567890123456,
  "taskName": "订单数据归档任务",
  "status": 1,
  "mode": 2,
  "version": "1000023",
  "lastUpdateTime": "2024-06-01T12:00:00Z",
  "createTime": "2024-05-01T10:00:00Z",
  "datasourceName": "orderDataSource",
  "databaseName": "order_db",
  "interval": 30,
  "limit": 100,
  "executionType": 1,
  "start": "22:00",
  "end": "06:00",
  "timeoutSeconds": 3600,
  "maxScanCount": 1000,           // int 最大扫描次数
  "pause": false,                 // boolean 是否暂停
  "scanMode": "primaryKey",     // string 扫描模式 condition/primaryKey
  "minPrimaryKey": "1000",      // string 主键最小值
  "baseTable": "order_info",    // string 主表名
  "rootNode": { /* ArchiveNode对象，见下 */ },
  "lastExecuteTime": "2024-06-01T12:00:00Z",
  "nextExecuteTime": "2024-06-01T13:00:00Z"
}
```

- **maxScanCount**：int，最大扫描次数，防止全表扫描过多
- **pause**：boolean，是否暂停任务
- **scanMode**：string，扫描模式，condition/primaryKey
- **minPrimaryKey**：string，主键最小值，配合scanMode使用
- **baseTable**：string，主表名

---

## 4.2 ArchiveNode（补全字段）
```json
{
  "nodeId": 9876543210987654,
  "taskId": 1234567890123456,
  "parentNodeId": 0,
  "tableName": "order_info",
  "condition": "created_time < date_sub(now(), interval 30 day)",
  "children": [ /* ArchiveNode数组 */ ],
  "isArchive": true,
  "queryColumns": ["id", "order_no"],
  "primaryKeyColumn": "id",
  "status": 1,
  "rootNode": true,
  "debugMode": false,
  "plugins": [ /* ArchivePlugin数组 */ ],
  "indexColumn": "id",
  "indexType": 1,
  "indexEnd": 100000,
  "indexStart": 1,
  "orderBy": "asc",
  "reserveDays": 30,
  "props": { "key": "value" },
  "relations": [ /* ColumnRelation数组 */ ],
  "shardingEnabled": true,                // boolean 是否启用分片
  "shardingExpression": "order_id % 128",// string 分片表达式
  "shardingPluginClass": "com.xxx.Plugin",// string 分片插件全类名
  "shardingPluginConfig": { "k": "v" }, // map 分片插件配置
  "shardingField": "order_id",           // string 分片字段
  "shardCount": 128,                      // int 分片数量
  "shardingTableTemplate": "order_{shard}", // string 分片表名模板
  "enableScan": true,                     // boolean 是否开启扫描模式
  "minPrimaryKey": "1000"                // string 主键最小值
}
```

- **shardingEnabled**：boolean，是否启用分片
- **shardingExpression**：string，分片表达式
- **shardingPluginClass**：string，分片插件全类名
- **shardingPluginConfig**：map，分片插件配置
- **shardingField**：string，分片字段
- **shardCount**：int，分片数量
- **shardingTableTemplate**：string，分片表名模板
- **enableScan**：boolean，是否开启扫描模式
- **minPrimaryKey**：string，主键最小值

---

如需进一步补充字段或详细说明，请告知。 