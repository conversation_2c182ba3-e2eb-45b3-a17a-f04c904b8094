# ArchiveManagementController 实现任务

## 任务概述
基于 SCP-Archive-SDK 接口设计与实现方案，在 scm-rulecenter 项目中实现 ArchiveManagementController，提供配置查询和任务管理功能。

## 实现内容

### 1. 核心控制器
**文件**：`scm-rulecenter/scm-rulecenter-interfaces/src/main/java/com/shizhuang/scm/rulecenter/interfaces/facade/ArchiveManagementController.java`

**功能特性**：
- 基于数据库层实现，与现有 ArchiveController 形成互补
- 不使用路径参数，全部使用查询参数和请求体
- 统一的异常处理和日志记录
- 完整的参数验证

### 2. 接口设计

#### 2.1 应用配置管理
- `GET /archive-management/app-config?appName={appName}` - 获取应用配置

#### 2.2 任务配置管理
- `GET /archive-management/task-config?appName={appName}&taskId={taskId}` - 获取单个任务配置
- `POST /archive-management/task-configs?appName={appName}` - 获取任务配置（支持批量查询和全量查询）

#### 2.3 任务管理
- `POST /archive-management/task` - 创建新任务
- `POST /archive-management/update-task` - 更新任务
- `POST /archive-management/delete-task?taskId={taskId}` - 删除任务
- `POST /archive-management/update-task-status?taskId={taskId}&status={status}` - 更新任务状态
- `POST /archive-management/task-nodes?taskId={taskId}` - 保存节点配置（全量更新模式）

### 3. 请求DTO类

#### 3.1 ArchiveTaskCreateRequest
**文件**：`scm-rulecenter/scm-rulecenter-interfaces/src/main/java/com/shizhuang/scm/rulecenter/interfaces/request/ArchiveTaskCreateRequest.java`

**功能**：
- 任务创建请求参数封装
- 完整的参数验证注解
- 默认值设置

#### 3.2 ArchiveTaskUpdateRequest
**文件**：`scm-rulecenter/scm-rulecenter-interfaces/src/main/java/com/shizhuang/scm/rulecenter/interfaces/request/ArchiveTaskUpdateRequest.java`

**功能**：
- 任务更新请求参数封装
- 与创建请求结构一致，便于维护

### 4. 数据转换类

#### 4.1 ArchiveTaskConverter
**文件**：`scm-rulecenter/scm-rulecenter-interfaces/src/main/java/com/shizhuang/scm/rulecenter/interfaces/converter/ArchiveTaskConverter.java`

**功能**：
- 在请求DTO和DO对象之间进行转换
- 处理时间格式转换（HH:mm ↔ 时间戳）
- 处理字段映射和类型转换
- 自动设置创建时间和更新时间

### 5. 技术实现

#### 5.1 依赖服务
- `ArchiveConfigService` - 配置查询服务
- `TaskManagementService` - 任务管理服务
- `ArchiveTaskConverter` - 数据转换服务

#### 5.2 数据转换
- 使用自定义的 `ArchiveTaskConverter` 进行对象转换
- 处理时间格式转换（HH:mm ↔ 时间戳）
- 自动设置创建时间和更新时间
- 保持数据一致性和类型安全

#### 5.3 异常处理
- 统一的 `ArchiveConfigException` 处理
- 友好的错误信息返回
- 完整的日志记录

#### 5.4 参数验证
- 使用 `@Valid` 和 `@Validated` 注解
- `@NotEmpty` 和 `@NotNull` 参数校验
- 自定义验证消息

### 6. 与现有系统集成

#### 6.1 与 ArchiveController 的关系
- ArchiveController：专注于数据源管理、表结构查询、任务预览等
- ArchiveManagementController：专注于配置管理和任务CRUD操作
- 两者功能互补，不重复

#### 6.2 数据库层集成
- 基于之前实现的 Mapper、Repository、Service 层
- 完整的数据库操作支持
- 事务管理和异常处理

### 7. 接口使用示例

#### 7.1 创建任务
```bash
curl -X POST "http://localhost:8080/archive-management/task" \
  -H "Content-Type: application/json" \
  -d '{
    "appName": "order-service",
    "taskName": "订单数据归档",
    "datasourceName": "orderDataSource",
    "databaseName": "order_db",
    "limit": 100,
    "interval": 30,
    "status": 0,
    "mode": 1,
    "executionType": 1
  }'
```

#### 7.2 获取应用配置
```bash
curl -X GET "http://localhost:8080/archive-management/app-config?appName=order-service"
```

#### 7.3 获取任务配置
```bash
# 获取应用下所有任务配置
curl -X POST "http://localhost:8080/archive-management/task-configs?appName=order-service" \
  -H "Content-Type: application/json" \
  -d '[]'

# 批量获取指定任务配置
curl -X POST "http://localhost:8080/archive-management/task-configs?appName=order-service" \
  -H "Content-Type: application/json" \
  -d '[123, 456, 789]'
```

#### 7.4 更新任务状态
```bash
curl -X POST "http://localhost:8080/archive-management/update-task-status?taskId=123&status=1"
```

#### 7.5 保存节点配置（全量更新）
```bash
# 保存节点配置
curl -X POST "http://localhost:8080/archive-management/task-nodes?taskId=123" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "nodeName": "节点1",
      "nodeType": 1,
      "config": "节点配置信息"
    }
  ]'

# 删除所有节点配置（传入空列表）
curl -X POST "http://localhost:8080/archive-management/task-nodes?taskId=123" \
  -H "Content-Type: application/json" \
  -d '[]'
```

### 8. 测试验证

#### 8.1 功能测试
- 配置查询功能验证
- 任务CRUD操作验证
- 参数验证功能验证
- 异常处理验证
- 数据转换功能验证

#### 8.2 集成测试
- 与数据库层集成验证
- 与现有系统兼容性验证
- 性能测试验证

### 9. 后续优化

#### 9.1 功能增强
- 添加配置版本控制接口
- 支持配置变更通知
- 添加配置缓存机制

#### 9.2 性能优化
- 添加接口缓存
- 批量操作优化
- 数据库查询优化

#### 9.3 监控告警
- 接口调用监控
- 异常告警机制
- 性能指标监控

## 优化内容

### 1. 代码优化
- **移除工具类依赖**：去掉 `ArchiveResponseUtil` 工具类，直接使用 try-catch 方式处理异常
- **增强参数验证**：在请求DTO中添加更严格的参数验证，包括数值范围、时间格式等
- **添加接口文档**：使用Swagger注解添加完整的接口文档，与 ArchiveController 风格保持一致
- **简化接口设计**：删除不必要的检查接口和批量删除接口，只保留核心业务接口
- **统一HTTP方法**：所有接口只使用 GET 和 POST 方法，提高兼容性
- **自定义数据转换**：使用 `ArchiveTaskConverter` 替代 BeanUtils，提供更好的类型安全和转换逻辑

### 2. 性能优化
- **简化代码结构**：直接使用 try-catch 方式，减少不必要的抽象层
- **参数验证优化**：在DTO层进行严格验证，减少无效请求的处理
- **减少接口数量**：删除冗余的检查接口和批量删除接口，减少系统复杂度
- **数据转换优化**：自定义转换逻辑，避免反射调用，提高性能

### 3. 可维护性优化
- **代码风格统一**：与现有的 ArchiveController 保持一致的异常处理方式
- **文档完善**：添加详细的接口文档和参数说明
- **错误信息友好**：提供更详细的错误信息和参数验证提示
- **接口简化**：统一的HTTP方法使用，便于前端调用和维护
- **数据转换清晰**：明确的转换逻辑，便于理解和维护

## 总结

本次实现完成了 ArchiveManagementController 的核心功能，提供了完整的配置管理和任务管理接口。实现遵循了以下原则：

1. **职责分离**：与现有 ArchiveController 功能互补
2. **接口设计**：不使用路径参数，便于扩展
3. **异常处理**：统一的异常处理和错误信息，与 ArchiveController 风格保持一致
4. **参数验证**：完整的参数校验机制
5. **日志记录**：详细的操作日志记录
6. **代码规范**：遵循项目编码规范
7. **代码优化**：简化代码结构，提高可维护性
8. **文档完善**：提供完整的接口文档
9. **接口简化**：只保留核心业务接口，删除冗余检查接口和批量删除接口
10. **HTTP方法统一**：只使用 GET 和 POST 方法，提高兼容性
11. **数据转换优化**：使用自定义转换类，提供更好的类型安全和转换逻辑
12. **全量更新模式**：参考 ArchiveController 设计，采用全量数据更新模式，删除 delete-task-nodes 接口

实现与现有的数据库层服务完美集成，为 SCP-Archive 系统提供了强大的配置管理能力。通过优化，代码风格与现有系统保持一致，接口更加友好和简洁，数据转换更加安全和高效。 

# ArchiveManagementController 新增全局配置更新接口

## 上下文
- 需求：ArchiveManagementController 增加 POST 接口用于更新全局配置，null 字段不更新，异常需日志记录并抛出。
- 相关对象：ArchiveGlobalConfigRequest、ArchiveAdminService、ArchiveAppConfigMapper（updateByPrimaryKeySelective）。

## 执行计划
1. 在 ArchiveManagementController 新增 POST 接口 `/api/global-config/update`，入参 ArchiveGlobalConfigRequest。
2. Controller 调用 ArchiveAdminService 新增 updateGlobalConfig 方法。
3. Service 层遍历 DTO，仅将非 null 字段赋值到 DO，调用 mapper 的 updateByPrimaryKeySelective。
4. Mapper 层已支持按字段更新，无需改动。
5. Service 层 catch Exception，记录日志并抛出业务异常。
6. Controller 返回统一响应体。 