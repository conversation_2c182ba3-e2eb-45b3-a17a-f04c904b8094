# 归档配置流转全流程说明

## 1. 配置创建与管理（admin 端：scm-rulecenter）
- **角色/系统**：admin（scm-rulecenter，规则中心/归档管理后台）
- **操作**：管理员/运维在管理后台页面创建或编辑归档任务配置。
- **接口**：
  - 前端通过API（如`/api/archive/config/save`）将配置数据提交到后端。
  - 后端由`ArchiveManagementController`接收请求，调用`ArchiveConfigManager.registerTaskConfig(TaskConfig)`进行校验、注册和版本管理。
  - 通过`ArchiveConfigService`等服务层，最终调用MyBatis Mapper（如`ArchiveTaskConfigMapper`、`ArchiveNodeConfigMapper`）将配置写入**scm-rulecenter的数据库**（如表`archive_task_config`、`archive_node_config`等）。
- **数据位置**：此时配置数据在：scm-rulecenter（admin服务）数据库

## 2. 配置存储与分发（scm-rulecenter数据库）
- **角色/系统**：scm-rulecenter数据库（如MySQL）
- **操作**：存储归档任务配置，支持多版本、备份、回滚。
- **表结构**：
  - `archive_task_config`：存储任务级配置
  - `archive_node_config`：存储节点级配置
  - 其他相关表
- **接口**：通过Mapper和Service层进行读写，供admin端和SDK端调用。
- **数据位置**：此时配置数据在：scm-rulecenter数据库

## 3. 配置拉取与应用（客户端SDK端：scp-framework1）
- **角色/系统**：客户端SDK（scp-framework1/scp-archive-sdk，归档执行端）
- **操作**：SDK服务端通过RPC（如Dubbo接口），向scm-rulecenter请求归档任务配置。
- **接口/模块**：
  - Dubbo接口（如`ArchiveConfigQueryService`，详见`admin任务配置查询Dubbo接口设计文档`）或HTTP接口，由SDK端调用。
  - scm-rulecenter端由`ArchiveManagementController`或相关Service实现接口，查询数据库并返回配置。
  - SDK端本地通过`ArchiveConfigManager`缓存配置，支持热更新。
- **数据位置**：此时配置数据在：scp-framework1（客户端SDK服务）本地内存

## 4. 配置变更与版本号通知机制
- **场景说明**：为保证客户端SDK始终获取到最新的归档配置，系统采用“配置中心版本号通知机制”进行配置变更的高效同步。
- **机制流程**：
  1. **配置变更触发**：管理员在admin（scm-rulecenter）端通过`ArchiveManagementController`修改归档配置，配置被写入数据库，并生成新的配置版本号（如自增ID或时间戳）。
  2. **版本号通知与拉取**：
     - scm-rulecenter端在配置变更后，将最新的配置版本号和内容同步到**配置中心**（如Nacos、Apollo等）。
     - 客户端SDK通过配置中心的订阅/监听机制（ARK配置中心）自动感知配置版本号变化。
  3. **SDK拉取新配置**：
     - 当SDK监听到配置中心的版本号或内容发生变化时，立即拉取最新配置。
     - 配置中心返回最新配置内容及其版本号。
  4. **本地缓存与热更新**：
     - SDK收到新配置后，更新本地缓存，并将新版本号记录到本地。
     - 后续归档任务执行均基于最新配置。
- **接口/模块**：
  - `ArchiveManagementController`：配置变更入口，负责版本号生成与同步到配置中心。
  - 配置中心（ARK配置中心）：负责配置的存储、版本号管理和变更通知。
  - SDK配置监听模块：负责监听配置中心变更并拉取新配置。
- **优势**：
  - 避免全量推送，减少网络和系统压力。
  - 保证多实例SDK配置一致性和实时性。
  - 支持灰度、回滚等高级场景。

## 5. 配置中心推送设计细节
- **Key字段定义**：
  - 配置中心（ARK配置中心）采用分层Key设计，便于区分全局配置和任务级别配置。
  - **全局配置Key**：如 `archive.global.config`，用于存储归档相关的全局参数、默认策略等。
  - **任务级配置Key**：如 `archive.task.config.{taskId}`，每个归档任务独立一个Key，`{taskId}`为任务ID。
- **推送与监听机制**：
  - 配置变更后，scm-rulecenter将最新配置内容推送到对应Key。
  - SDK通过ARK配置中心监听这些Key的变更事件。
- **SDK更新配置的Dubbo接口**：
  - **全局配置更新**：SDK监听到`archive.global.config`变更时，调用 `ArchiveConfigDubboService#getGlobalConfig()` 获取最新全局配置。
  - **任务级配置更新**：SDK监听到`archive.task.config.{taskId}`变更时，调用 `ArchiveConfigDubboService#getTaskConfig(String appName, String taskId)` 获取指定任务的最新配置。
- **接口定义示例**：
  - `ArchiveConfigDubboService#getGlobalConfig()`
    - 用于获取全局归档配置。
    - 返回类型：`ArchiveGlobalConfigDTO`
  - `ArchiveConfigDubboService#getTaskConfig(String taskId)`
    - 用于获取指定任务的归档配置。
    - 参数：`taskId`（任务ID）
    - 返回类型：`ArchiveTaskConfigDTO`
- **优势**：
  - 支持全局与任务级配置的独立变更与推送，提升灵活性。
  - SDK只需监听和拉取实际变更的配置，减少不必要的全量同步。
  - 便于灰度、分组、动态扩展等高级场景。

- **客户端拉取与异常处理**：
  - SDK监听到配置中心Key（如`archive.global.config`或`archive.task.config.{taskId}`）变更后，需主动拉取最新配置。
  - 如果拉取配置失败，SDK应采用指数退避等策略进行重试，避免频繁请求。
  - 在重试期间，相关归档任务需暂停执行，防止因配置不一致导致的数据风险。
  - 待配置拉取成功并本地缓存更新后，自动恢复归档任务的正常执行。

## 6. 配置应用于归档执行
- **角色/系统**：客户端SDK（scp-framework1）
- **操作**：归档任务执行时，SDK通过本地缓存的配置驱动归档链路（如查询、删除、预览等）。
- **接口/模块**：
  - `ArchiveConfigManager.getTaskConfig(taskId)`：获取任务配置
  - `QueryExecutor`、`PreviewManager`等：根据配置执行归档任务

## 7. 关键流转路径与接口

| 步骤 | 数据所在系统         | 关键接口/模块                                      | 数据存储/表                   | 说明                                 |
|------|---------------------|----------------------------------------------------|-------------------------------|--------------------------------------|
| 1    | scm-rulecenter      | `ArchiveManagementController`<br>ArchiveConfigManager<br>ArchiveConfigService<br>ArchiveTaskConfigMapper | `archive_task_config`<br>`archive_node_config` | 配置创建、校验、注册、存储到数据库 |
| 2    | scm-rulecenter数据库 | Mapper接口                                         | `archive_task_config`<br>`archive_node_config` | 配置存储、分发、推送/拉取            |
| 3    | scp-framework1 SDK  | Dubbo/HTTP接口<br>ArchiveConfigManager            | 本地缓存                       | SDK拉取配置、本地缓存、热更新        |
| 4    | scp-framework1 SDK  | QueryExecutor/PreviewManager等                     | 本地缓存                       | 归档任务执行时应用配置               |

## 8. 数据流转时序图

```mermaid
sequenceDiagram
    participant Admin as admin(scm-rulecenter)
    participant DB as scm-rulecenter数据库
    participant SDK as 客户端SDK(scp-framework1)

    Admin->>Admin: 创建/编辑归档配置
    Admin->>Admin: ArchiveManagementController/ArchiveConfigManager 校验&注册
    Admin->>DB: ArchiveConfigService/Mapper 写入配置
    Note right of DB: 配置存储/多版本管理
    SDK->>Admin: Dubbo/HTTP接口请求配置
    Admin->>DB: 查询配置
    DB-->>Admin: 返回配置
    Admin-->>SDK: 返回配置
    SDK->>SDK: ArchiveConfigManager 缓存/热更新
    SDK->>SDK: 归档任务执行时读取配置
```

## 9. 总结
- **admin（scm-rulecenter）**：负责配置的创建、校验、注册和存储到scm-rulecenter数据库，核心接口为`ArchiveManagementController`，数据最终落库。
- **scm-rulecenter数据库**：作为配置流转的中枢，负责存储、分发和多版本管理。
- **客户端SDK（scp-framework1）**：通过Dubbo/HTTP接口从scm-rulecenter获取配置，缓存和热更新，并在归档任务执行时应用配置。

每一步都明确数据在哪个系统、通过什么接口和数据库表流转，保证配置的安全、可控和高可用。 