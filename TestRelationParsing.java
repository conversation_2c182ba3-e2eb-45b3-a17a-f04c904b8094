import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

// 模拟ColumnRelation类
class ColumnRelation {
    private String currentColumn;
    private String relatedColumn;
    private String relatedTable;
    
    public void setCurrentColumn(String currentColumn) { this.currentColumn = currentColumn; }
    public void setRelatedColumn(String relatedColumn) { this.relatedColumn = relatedColumn; }
    public void setRelatedTable(String relatedTable) { this.relatedTable = relatedTable; }
    
    public String getCurrentColumn() { return currentColumn; }
    public String getRelatedColumn() { return relatedColumn; }
    public String getRelatedTable() { return relatedTable; }
    
    @Override
    public String toString() {
        return "ColumnRelation{currentColumn='" + currentColumn + "', relatedColumn='" + relatedColumn + "', relatedTable='" + relatedTable + "'}";
    }
}

// 测试关系条件解析
public class TestRelationParsing {
    
    public static void main(String[] args) {
        TestRelationParsing test = new TestRelationParsing();
        
        // 测试SQL格式的关系条件
        String sqlCondition = "pink_inbound_detail.id = pink_operate_item.inbound_detail_id";
        System.out.println("测试SQL格式: " + sqlCondition);
        
        List<ColumnRelation> relations = test.parseSqlRelationConditions(sqlCondition);
        for (ColumnRelation relation : relations) {
            System.out.println("解析结果: " + relation);
        }
        
        // 测试多个条件
        String multiCondition = "table1.col1 = table2.col2 and table1.col3 = table3.col4";
        System.out.println("\n测试多个条件: " + multiCondition);
        
        relations = test.parseSqlRelationConditions(multiCondition);
        for (ColumnRelation relation : relations) {
            System.out.println("解析结果: " + relation);
        }
    }
    
    /**
     * 解析SQL格式的关系条件
     * 格式：table1.column1 = table2.column2
     */
    private List<ColumnRelation> parseSqlRelationConditions(String relationConditions) {
        if (relationConditions == null || relationConditions.trim().isEmpty()) {
            return null;
        }
        
        try {
            List<ColumnRelation> relations = new java.util.ArrayList<>();
            
            // 按and分割多个条件
            String[] conditionParts = relationConditions.split("(?i)\\s+and\\s+");
            
            for (String condition : conditionParts) {
                condition = condition.trim();
                
                // 校验每个条件必须包含等号
                if (!condition.contains("=")) {
                    throw new RuntimeException("关联条件必须使用等值关联: " + condition);
                }
                
                // 解析单个条件
                String[] parts = condition.split("=");
                if (parts.length != 2) {
                    throw new RuntimeException("关联条件格式错误，应为 table1.column1 = table2.column2: " + condition);
                }
                
                String left = parts[0].trim().replace("`", "");
                String right = parts[1].trim().replace("`", "");
                
                // 解析表名和字段名
                String[] leftArr = left.split("\\.");
                String[] rightArr = right.split("\\.");
                
                if (leftArr.length != 2 || rightArr.length != 2) {
                    throw new RuntimeException("关联条件格式错误，应为 table1.column1 = table2.column2: " + condition);
                }
                
                // 创建ColumnRelation对象
                ColumnRelation relation = new ColumnRelation();
                relation.setCurrentColumn(leftArr[1]);      // 当前表字段
                relation.setRelatedColumn(rightArr[1]);    // 关联表字段
                relation.setRelatedTable(rightArr[0]);     // 关联表名
                
                relations.add(relation);
            }
            
            System.out.println("SQL格式关系条件解析成功: relationConditions=" + relationConditions + ", relations=" + relations.size());
            
            return relations;
            
        } catch (Exception e) {
            System.err.println("SQL格式关系条件解析失败: relationConditions=" + relationConditions + ", error=" + e.getMessage());
            throw new RuntimeException("SQL格式关系条件解析失败: " + relationConditions + ", 错误信息: " + e.getMessage());
        }
    }
}
