package com.shizhuang.duapp.scp.framework.admin.sdk.constants;

public class ArchiveSDKConstants {


    public static final String ARK_GROUP = "archive-platform";

    public static final String ARK_KEY_PREFIX = "archive.platform";

    /**
     * 任务列表
     */
    public static final String TASK_LIST_KEY = ARK_KEY_PREFIX + ".task_list";

    /**
     * 全局配置
     */
    public static final String TASK_GLOBAL_CONFIG = ARK_KEY_PREFIX + ".global_config";

    /**
     * 调试模式
     */
    public static final Integer MODE_DEBUG = 1;

    /**
     * 生产模式
     */
    public static final Integer MODE_PROD = 2;

    /**
     * 运行状态
     */
    public static final Integer STATUS_RUNNING = 1;

    /**
     * 全天归档
     */
    public static final Integer ARCHIVE_TYPE_ALL_DAY = 1;

    /**
     * 指定时间归档
     */
    public static final Integer ARCHIVE_TYPE_SPECIFIC_TIME = 2;

    /**
     * 索引类型 区间
     */
    public static final Integer INDEX_TYPE_SPAN = 1;

    /**
     * 索引类型 保留天数
     */
    public static final Integer INDEX_TYPE_RESERVE_DAYS = 2;

    /**
     * 先归档父节点，先归档子节点值为1
     */
    public static final Integer ARCHIVE_TYPE_PARENT = 2;

    /**
     * 过滤0值和空字符串
     */
    public static final Integer FILTER_DEFAULT_VALUE = 1;

}
