package com.shizhuang.duapp.scp.framework.archive.core.event.message;

/**
 * AppConfig（全局配置）更新事件
 */
public class AppConfigUpdatedEvent {
    private final long timestamp;
    private boolean deleted;
    private Long version;

    public AppConfigUpdatedEvent(Long version) {
        this.version = version;
        this.timestamp = System.currentTimeMillis();
    }

    public AppConfigUpdatedEvent(boolean deleted) {
        this.version = null;
        this.deleted = deleted;
        this.timestamp = System.currentTimeMillis();
    }

    public boolean isDeleted() {
        return deleted;
    }

    public Long getVersion() {
        return version;
    }

    public long getTimestamp() {
        return timestamp;
    }
} 