package com.shizhuang.duapp.scp.framework.archive.core.event.message;

/**
 * 任务状态获取重试失败事件
 */
public class TaskStatusRetryFailedEvent extends ArchiveTaskEvent {
    private final int retryCount;
    private final String errorMessage;

    public TaskStatusRetryFailedEvent(Long taskId, int retryCount, String errorMessage) {
        super(taskId);
        this.retryCount = retryCount;
        this.errorMessage = errorMessage;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
} 