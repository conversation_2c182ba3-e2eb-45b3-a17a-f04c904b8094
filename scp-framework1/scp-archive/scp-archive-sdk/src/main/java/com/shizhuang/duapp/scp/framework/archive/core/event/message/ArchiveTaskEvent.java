package com.shizhuang.duapp.scp.framework.archive.core.event.message;

/**
 * 归档任务相关事件基类
 */
public abstract class ArchiveTaskEvent {
    private final Long taskId;
    private final long timestamp;

    public ArchiveTaskEvent(Long taskId) {
        this.taskId = taskId;
        this.timestamp = System.currentTimeMillis();
    }

    public Long getTaskId() {
        return taskId;
    }

    public long getTimestamp() {
        return timestamp;
    }
} 