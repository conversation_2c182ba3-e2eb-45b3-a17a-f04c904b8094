package com.shizhuang.duapp.scp.framework.archive.core.config;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Supplier;

public class RetryManager {
    private static final ConcurrentHashMap<String, Object> lockMap = new ConcurrentHashMap<>();

    public static <T> T retryWithKey(String key, Supplier<T> supplier, Consumer<Integer> listener) {
        Object lock = lockMap.computeIfAbsent(key, k -> new Object());
        synchronized (lock) {
            try {
                return RetryUtils.retry(supplier, listener);
            } finally {
                lockMap.remove(key, lock);
            }
        }
    }
} 