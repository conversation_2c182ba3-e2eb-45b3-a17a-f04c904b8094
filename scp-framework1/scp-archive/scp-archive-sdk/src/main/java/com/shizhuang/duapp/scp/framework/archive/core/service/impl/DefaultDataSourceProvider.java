package com.shizhuang.duapp.scp.framework.archive.core.service.impl;

import com.shizhuang.duapp.scp.framework.archive.core.service.DatasourceProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 多数据源提供者实现
 * <p>
 * 简化设计，专注于数据源管理和JdbcTemplate提供
 * <p>
 * 主要职责：
 * 1. 数据源注册与管理
 * 2. JdbcTemplate创建与缓存
 * 3. 数据源状态检查
 */
@Slf4j
@Service
public class DefaultDataSourceProvider implements DatasourceProvider {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 数据源映射表
     */
    private final Map<String, DataSource> dataSourceMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 获取所有数据源配置
        Map<String, DataSource> dataSources = applicationContext.getBeansOfType(DataSource.class);
        // 添加数据源到缓存
        dataSources.forEach((name, dataSource) -> registerDataSource(name, dataSource));
    }

    /**
     * JdbcTemplate缓存
     */
    private final Map<String, NamedParameterJdbcTemplate> jdbcTemplateCache = new ConcurrentHashMap<>();

    /**
     * 注册数据源
     *
     * @param name       数据源名称
     * @param dataSource 数据源
     */
    public void registerDataSource(String name, DataSource dataSource) {
        if (!StringUtils.hasText(name) || dataSource == null) {
            log.warn("数据源注册失败：名称为空或数据源为空");
            return;
        }

        dataSourceMap.put(name, dataSource);
        // 清除对应的JdbcTemplate缓存
        jdbcTemplateCache.put(name, new NamedParameterJdbcTemplate(dataSource));

        log.info("数据源注册成功: {}", name);
    }

    /**
     * 检查数据源是否存在
     *
     * @param name 数据源名称
     * @return 是否存在
     */
    public boolean hasDataSource(String name) {
        return dataSourceMap.containsKey(name);
    }

    @Override
    public List<String> getAvailableDataSources() {
        return Collections.emptyList();
    }

    /**
     * 获取所有数据源名称
     *
     * @return 数据源名称列表
     */
    public java.util.Set<String> getDataSourceNames() {
        return dataSourceMap.keySet();
    }

    @Override
    public NamedParameterJdbcTemplate getJdbcTemplate(String datasourceName) {
        if (!StringUtils.hasText(datasourceName)) {
            throw new IllegalArgumentException("数据源名称不能为空");
        }
        return jdbcTemplateCache.get(datasourceName);
    }

    @Override
    public NamedParameterJdbcTemplate getJdbcTemplate(String datasourceName, String databaseName) {
        return getJdbcTemplate(datasourceName);
    }

    /**
     * 获取数据源
     *
     * @param name 数据源名称
     * @return 数据源
     */
    public DataSource getDataSource(String name) {
        return dataSourceMap.get(name);
    }

} 