package com.shizhuang.duapp.scp.framework.archive.core.parser;

import com.shizhuang.duapp.scp.framework.archive.core.config.LocalArchiveNode;
import com.shizhuang.duapp.scp.framework.archive.core.executor.ArchiveTask;
import com.shizhuang.duapp.scp.framework.archive.core.plugin.ArchivePlugin;
import com.shizhuang.scm.rulecenter.api.dto.ArchiveNode;
import com.shizhuang.scm.rulecenter.api.dto.TaskConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
public class ArchiveConfigParser {

    private List<ArchivePlugin> plugins;

    public ArchiveConfigParser(List<ArchivePlugin> plugins) {
        this.plugins = plugins;
    }

    /**
     * 递归构建本地节点树
     */
    private LocalArchiveNode buildLocalNodeTree(LocalArchiveNode node, LocalArchiveNode parent) {
        LocalArchiveNode local = new LocalArchiveNode();
        // 拷贝属性
        local.setNodeId(node.getNodeId());
        local.setTaskId(node.getTaskId());
        local.setParentNodeId(node.getParentNodeId());
        local.setTableName(node.getTableName());
        local.setCondition(node.getCondition());
        local.setIsArchive(node.getIsArchive());
        local.setQueryColumns(node.getQueryColumns());
        local.setPrimaryKeyColumn(node.getPrimaryKeyColumn());
        local.setStatus(node.getStatus());
        local.setRootNode(node.getRootNode());
        local.setDebugMode(node.getDebugMode());
        local.setPlugins(node.getPlugins());
        local.setIndexColumn(node.getIndexColumn());
        local.setIndexType(node.getIndexType());
        local.setIndexEnd(node.getIndexEnd());
        local.setIndexStart(node.getIndexStart());
        local.setOrderBy(node.getOrderBy());
        local.setReserveDays(node.getReserveDays());
        local.setProps(node.getProps());
        local.setRelations(node.getRelations());
        local.setShardingEnabled(node.getShardingEnabled());
        local.setShardingExpression(node.getShardingExpression());
        local.setShardingPluginClass(node.getShardingPluginClass());
        local.setShardingPluginConfig(node.getShardingPluginConfig());
        local.setShardingField(node.getShardingField());
        local.setShardingPrefix(node.getShardingPrefix());
        local.setShardCount(node.getShardCount());
        local.setShardingTableTemplate(node.getShardingTableTemplate());
        local.setEnableScan(node.getEnableScan());
        local.setMinPrimaryKey(node.getMinPrimaryKey());
        local.setParent(parent);
        // 递归 children
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            List<LocalArchiveNode> localChildren = new ArrayList<>();
            for (ArchiveNode child : node.getChildren()) {
                localChildren.add(buildLocalNodeTree((LocalArchiveNode) child, local));
            }
            local.setChildren((List) localChildren);
        }
        return local;
    }

    /**
     * 将配置解析成归档任务列表
     *
     * @param taskConfig
     * @return
     */
    public ArchiveTask parse(TaskConfig taskConfig) {
        LocalArchiveNode root = buildLocalNodeTree((LocalArchiveNode) taskConfig.getRootNode(), null);
        return new ArchiveTask(Collections.singletonList(root), taskConfig);
    }

    /**
     * 预览配置
     *
     * @param taskConfig
     * @return
     */
    public ArchiveTask parsePreview(TaskConfig taskConfig) {
        LocalArchiveNode root = buildLocalNodeTree((LocalArchiveNode) taskConfig.getRootNode(), null);
        return new ArchiveTask(Collections.singletonList(root), taskConfig);
    }

}
