package com.shizhuang.duapp.scp.framework.archive.core.config;

import com.shizhuang.scm.rulecenter.api.dto.ArchiveNode;

public class LocalArchiveNode extends ArchiveNode {
    private LocalArchiveNode parent;

    public LocalArchiveNode getParent() {
        return parent;
    }

    public void setParent(LocalArchiveNode parent) {
        this.parent = parent;
    }

    public String getKey() {
        return getNodeId().toString();
    }

} 