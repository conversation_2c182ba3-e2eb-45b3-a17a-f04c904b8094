package com.shizhuang.duapp.scp.framework.archive.core.event;

import com.shizhuang.duapp.scp.framework.archive.core.config.ArchiveConfigManager;
import com.shizhuang.duapp.scp.framework.archive.core.event.message.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 归档事件监听器，统一处理任务和配置相关事件
 */
@Slf4j
@Service
public class ArchiveEventListener implements EventListener {

    @Resource
    private ArchiveConfigManager archiveConfigManager;

    @Override
    public void onEvent(Object event) {
        if (event instanceof TaskCreatedEvent) {
            handleTaskCreated((TaskCreatedEvent) event);
        } else if (event instanceof TaskDeletedEvent) {
            handleTaskDeleted((TaskDeletedEvent) event);
        } else if (event instanceof TaskPausedEvent) {
            handleTaskPaused((TaskPausedEvent) event);
        } else if (event instanceof TaskStatusRetryFailedEvent) {
            handleTaskStatusRetryFailed((TaskStatusRetryFailedEvent) event);
        } else if (event instanceof GlobalConfigFetchFailedEvent) {
            handleGlobalConfigFetchFailed((GlobalConfigFetchFailedEvent) event);
        } else if (event instanceof AppConfigUpdatedEvent) {
            handleAppConfigUpdated((AppConfigUpdatedEvent) event);
        } else {
            log.debug("Unhandled event: {}", event.getClass().getSimpleName());
        }
    }

    private void handleTaskCreated(TaskCreatedEvent event) {
        log.info("任务创建: {}", event.getTaskId());
        archiveConfigManager.refreshTaskConfig(event.getTaskId());
    }

    private void handleTaskDeleted(TaskDeletedEvent event) {
        log.info("任务删除: {}", event.getTaskId());
        archiveConfigManager.refreshTaskConfig(event.getTaskId());
    }

    private void handleTaskPaused(TaskPausedEvent event) {
        log.warn("任务暂停: {}, 原因: {}", event.getTaskId(), event.getReason());
        archiveConfigManager.pauseTask(event.getTaskId());
    }

    private void handleTaskStatusRetryFailed(TaskStatusRetryFailedEvent event) {
        log.error("任务状态获取重试失败: {}, 重试次数: {}, 错误: {}", event.getTaskId(), event.getRetryCount(), event.getErrorMessage());
        archiveConfigManager.pauseTask(event.getTaskId());
    }

    private void handleGlobalConfigFetchFailed(GlobalConfigFetchFailedEvent event) {
        log.error("全局配置获取失败: {}, 重试次数: {}, 错误: {}", event.getConfigKey(), event.getRetryCount(), event.getErrorMessage());
        archiveConfigManager.parseGlobalConfig();
    }

    private void handleAppConfigUpdated(AppConfigUpdatedEvent event) {
        log.info("全局配置已更新: {}, 版本: {}", event.isDeleted(), event.getVersion());
        if(event.isDeleted()){
            archiveConfigManager.refreshGlobalConfig();
            return;
        }
        archiveConfigManager.refreshGlobalConfig();
    }
} 