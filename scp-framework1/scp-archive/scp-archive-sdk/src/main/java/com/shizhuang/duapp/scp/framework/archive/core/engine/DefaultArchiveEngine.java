package com.shizhuang.duapp.scp.framework.archive.core.engine;

import com.shizhuang.duapp.scp.framework.admin.sdk.model.ArchiveDataConfig;
import com.shizhuang.duapp.scp.framework.archive.core.ArchiveEngine;
import com.shizhuang.duapp.scp.framework.archive.core.config.ArchiveConfigManager;
import com.shizhuang.duapp.scp.framework.archive.core.executor.ArchiveTaskExecutor;
import com.shizhuang.duapp.scp.framework.archive.core.lock.DistributedLock;
import com.shizhuang.duapp.scp.framework.archive.core.plugin.PluginManager;
import com.shizhuang.duapp.scp.framework.archive.core.scheduler.TaskScheduler;
import com.shizhuang.duapp.scp.framework.archive.core.sharding.ShardingManager;
import com.shizhuang.duapp.shutdown.ShutdownAbstract;
import com.shizhuang.scm.rulecenter.api.dto.TaskConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.LockSupport;

@Slf4j
@Component
public class DefaultArchiveEngine extends ShutdownAbstract implements ArchiveEngine {

    /**
     * 分布式锁
     */
    @Resource
    private DistributedLock distributedLock;

    /**
     * 任务调度器 - 策略模式
     */
    @Resource
    private TaskScheduler taskScheduler;

    /**
     * 任务执行器 - 模板方法模式
     */
    @Resource
    private ArchiveTaskExecutor taskExecutor;

    /**
     * 插件管理器 - 责任链模式
     */
    @Resource
    private PluginManager pluginManager;

    @Resource
    private ShardingManager shardingManager;

    @Resource
    private ArchiveConfigManager archiveConfigManager;

    // ==================== 运行时状态 ====================

    /**
     * 主线程
     */
    private Thread mainThread;

    /**
     * 运行标志
     */
    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 停机标志
     */
    private volatile boolean shutdown = false;


    @Override
    public int order() {
        return Integer.MIN_VALUE;
    }

    @Override
    public void shutdownGracefully() {
        log.info("开始优雅停机...");
        // 设置停机标志
        shutdown = true;
        // 停止主线程
        running.set(false);
        if (mainThread != null) {
            mainThread.interrupt();
        }
        log.info("优雅停机完成");
    }

    @Override
    public void start() {
        if (!running.compareAndSet(false, true)) {
            log.warn("归档循环已在运行中");
            return;
        }
        mainThread = new Thread(() -> {
            long interval = 60;
            log.info("归档主循环启动");
            while (running.get() && !Thread.currentThread().isInterrupted() && !shutdown) {
                try {
                    ArchiveDataConfig archiveDataConfig = archiveConfigManager.getGlobalConfig();
                    interval = archiveDataConfig.getInterval();
                    // 执行任务调度
                    executeTaskScheduling(archiveDataConfig);
                } catch (Exception e) {
                    log.error("归档任务执行异常", e);
                } finally {
                    // 等待下次调度
                    LockSupport.parkNanos(TimeUnit.SECONDS.toNanos(interval));
                }
            }
            log.info("归档主循环停止");
        }, "archive-main-thread");
        mainThread.setDaemon(true);
        mainThread.start();
    }


    /**
     * 执行任务调度
     */
    private void executeTaskScheduling(ArchiveDataConfig taskConfig) {
        // 1. 过滤可执行任务
        List<TaskConfig> executableTasks = taskScheduler.filterExecutableTasks();
        if (CollectionUtils.isEmpty(executableTasks)) {
            return;
        }
        // 3. 串行执行任务
        executableTasks.forEach(task -> {
            String lockKey = getLockKey(task.getTaskName());
            if (distributedLock.acquire(lockKey, taskConfig.getLockExpireSeconds(), TimeUnit.SECONDS)) {
                try {
                    // 执行任务
                    taskExecutor.execute(task, taskConfig);
                } catch (Exception e) {
                    log.error("任务执行异常: {}", task.getTaskName(), e);
                } finally {
                    taskScheduler.updateScheduleTime(task);
                    distributedLock.release(lockKey);
                }
            }
        });
    }

    /**
     * 获取锁键
     */
    private String getLockKey(String taskName) {
        return "archive_lock_" + taskName;
    }

}
