package com.shizhuang.duapp.scp.framework.archive.core.event.message;

/**
 * 全局配置获取失败事件
 */
public class GlobalConfigFetchFailedEvent {
    private final String configKey;
    private final int retryCount;
    private final String errorMessage;
    private final long timestamp;

    public GlobalConfigFetchFailedEvent(String configKey, int retryCount, String errorMessage) {
        this.configKey = configKey;
        this.retryCount = retryCount;
        this.errorMessage = errorMessage;
        this.timestamp = System.currentTimeMillis();
    }

    public String getConfigKey() {
        return configKey;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public long getTimestamp() {
        return timestamp;
    }
} 