package com.shizhuang.duapp.scp.framework.archive.core.config;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 通用重试工具，支持指数退避，最大重试时间30分钟
 */
@Slf4j
public class RetryUtils {
    private static final long DEFAULT_INITIAL_DELAY = 5000; // 初始延迟5s
    private static final long DEFAULT_MAX_DELAY = 30 * 60 * 1000L; // 30分钟
    private static final double DEFAULT_BACKOFF = 1.8; // 指数退避因子


    public static <T> T retry(Supplier<T> supplier, Consumer<Integer> listener) {
        return retry(supplier, DEFAULT_INITIAL_DELAY, DEFAULT_MAX_DELAY, DEFAULT_BACKOFF, listener);
    }

    public static <T> T retry(Supplier<T> supplier, long initialDelay, long maxDelay, double backoff, Consumer<Integer> listener) {
        long delay = initialDelay;
        int attempt = 0;
        while (true) {
            try {
                attempt++;
                return supplier.get();
            } catch (Exception e) {
                listener.accept(attempt);
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ignored) {
                }
                delay = Math.min((long) (delay * backoff), maxDelay);
            }
        }
    }
}
