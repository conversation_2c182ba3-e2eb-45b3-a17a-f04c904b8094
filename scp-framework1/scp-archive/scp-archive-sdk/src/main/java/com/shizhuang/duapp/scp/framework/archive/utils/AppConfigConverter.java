package com.shizhuang.duapp.scp.framework.archive.utils;

import com.shizhuang.scm.rulecenter.api.dto.AppConfig;
import com.shizhuang.scm.rulecenter.api.dto.TaskConfig;
import com.shizhuang.scm.rulecenter.api.dto.ArchiveNode;
import com.shizhuang.duapp.scp.framework.admin.sdk.model.ArchiveDataConfig;
import com.shizhuang.duapp.scp.framework.archive.core.config.LocalArchiveNode;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 工具类：将 AppConfig 数据更新到 ArchiveDataConfig
 */
public class AppConfigConverter {
    /**
     * 将 AppConfig 的数据更新到 ArchiveDataConfig
     *
     * @param appConfig         源配置
     * @param archiveDataConfig 目标配置（被更新）
     */
    public static void updateArchiveDataConfig(AppConfig appConfig, ArchiveDataConfig archiveDataConfig) {
        if (appConfig == null || archiveDataConfig == null) {
            return;
        }
        if (appConfig.getThreads() != null) {
            archiveDataConfig.setThreads(appConfig.getThreads());
        }
        if (appConfig.getLockExpireSeconds() != null) {
            archiveDataConfig.setLockExpireSeconds(appConfig.getLockExpireSeconds());
        }
        if (appConfig.getEnable() != null) {
            archiveDataConfig.setEnable(appConfig.getEnable());
        }
        if (appConfig.getInterval() != null) {
            archiveDataConfig.setInterval(appConfig.getInterval());
        }
        if (appConfig.getMaxThreads() != null) {
            archiveDataConfig.setMaxThreads(appConfig.getMaxThreads());
        }
    }

    /**
     * 递归将 ArchiveNode 转为 LocalArchiveNode
     */
    public static LocalArchiveNode convertToLocalNode(ArchiveNode node, LocalArchiveNode parent) {
        if (node == null) return null;
        LocalArchiveNode local = new LocalArchiveNode();
        BeanUtils.copyProperties(node, local);
        local.setParent(parent);
        // 递归 children
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            List<LocalArchiveNode> localChildren = new ArrayList<>();
            for (ArchiveNode child : node.getChildren()) {
                localChildren.add(convertToLocalNode(child, local));
            }
            local.setChildren((List) localChildren);
        }
        return local;
    }

    /**
     * 替换 TaskConfig 的 rootNode 为 LocalArchiveNode，返回原 TaskConfig
     */
    public static TaskConfig replaceRootNodeWithLocal(TaskConfig taskConfig) {
        if (taskConfig == null || taskConfig.getRootNode() == null) return taskConfig;
        LocalArchiveNode localRoot = convertToLocalNode(taskConfig.getRootNode(), null);
        taskConfig.setRootNode(localRoot);
        return taskConfig;
    }
} 