package com.shizhuang.duapp.scp.framework.archive.core.event;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 事件发布器，负责分发事件到所有注册的监听器
 */
@Component
public class EventPublisher {
    @Resource
    private List<EventListener> listeners;

    /**
     * 发送（同步）事件
     */
    public void publish(Object event) {
        for (EventListener listener : listeners) {
            listener.onEvent(event);
        }
    }

    /**
     * 发送（异步）事件
     */
    @Async
    public void publishAsync(Object event) {
        for (EventListener listener : listeners) {
            listener.onEvent(event);
        }
    }
} 