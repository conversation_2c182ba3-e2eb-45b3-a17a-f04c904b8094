# SCP-Archive-SDK 详细架构设计文档

---

## 1. 总体架构概览

### 1.1 架构理念
- **分层架构**：基于DDD领域驱动设计，清晰分离业务逻辑、应用服务、基础设施
- **插件化**：支持可插拔扩展，通过SPI机制实现业务定制
- **事件驱动**：通过事件总线解耦模块间依赖，提升系统可扩展性
- **配置驱动**：支持动态配置、热刷新，实现系统行为的动态调整

### 1.2 核心设计模式
- **策略模式**：任务调度策略、扫描模式策略
- **模板方法模式**：任务执行流程
- **建造者模式**：复杂对象构建
- **责任链模式**：插件执行链
- **观察者模式**：配置变更监听
- **单例模式**：SDK客户端、配置管理器

---

## 2. 包结构与模块职责

```
com.shizhuang.duapp.scp.framework.archive
│
├── api                    # 对外API接口层
│   ├── ArchiveSdkClient  # SDK主入口
│   ├── SdkConfig         # SDK配置
│   └── facade/           # 外观模式接口
│
├── config                # 配置管理层
│   ├── ArchiveConfigManager      # 配置管理器
│   ├── ConfigSourceAggregator    # 配置聚合器
│   ├── source/                   # 配置来源
│   │   ├── ConfigSource         # 配置来源接口
│   │   ├── NacosConfigSource    # Nacos配置源
│   │   ├── DubboConfigSource    # Dubbo配置源
│   │   └── FileConfigSource     # 文件配置源
│   ├── listener/                # 配置监听器
│   │   ├── ConfigChangeListener # 配置变更监听器
│   │   └── ConfigChangeNotifier # 配置变更通知器
│   └── storage/                 # 配置存储
│       └── ConfigurationStorage # 配置存储接口
│
├── core                  # 核心领域层
│   ├── ArchiveEngine            # 归档引擎
│   ├── model/                   # 领域模型
│   │   ├── ArchiveNode         # 归档节点
│   │   ├── TaskConfig          # 任务配置
│   │   └── ArchiveDataConfig   # 归档数据配置
│   ├── context/                 # 执行上下文
│   │   └── ArchiveContext      # 归档上下文
│   ├── executor/                # 执行器
│   │   ├── ArchiveTask         # 归档任务
│   │   ├── ArchiveTaskExecutor # 任务执行器
│   │   └── DefaultArchiveTaskExecutor # 默认执行器
│   ├── scheduler/               # 调度器
│   │   └── TaskScheduler       # 任务调度器
│   ├── service/                 # 领域服务
│   │   ├── QueryExecutor       # 查询执行器
│   │   ├── DeleteExecutor      # 删除执行器
│   │   ├── DatasourceProvider  # 数据源提供者
│   │   └── impl/               # 实现类
│   ├── checkpoint/              # 断点续传
│   │   ├── CheckpointManager   # 断点管理器
│   │   └── ArchiveCheckpoint   # 归档断点
│   ├── lock/                    # 分布式锁
│   │   └── DistributedLock     # 分布式锁
│   ├── sharding/                # 分片管理
│   │   └── ShardingManager     # 分片管理器
│   ├── scan/                    # 扫描模式
│   │   └── ScanModeHandler     # 扫描模式处理器
│   └── exception/               # 异常处理
│       └── ArchiveException    # 归档异常
│
├── extension             # 扩展与插件层
│   ├── plugin/                  # 插件机制
│   │   ├── ArchivePlugin       # 插件接口
│   │   ├── PluginManager       # 插件管理器
│   │   ├── PluginResult        # 插件结果
│   │   ├── ArchiveEvent        # 事件类型
│   │   └── impl/               # 插件实现
│   │       ├── LoggingPlugin          # 日志插件
│   │       ├── MonitoringPlugin       # 监控插件
│   │       ├── SafeDeletePlugin       # 安全删除插件
│   │       ├── IntervalControlPlugin  # 间隔控制插件
│   │       └── ParentFirstArchivePlugin # 父节点优先插件
│   └── spi/                     # SPI扩展点
│       └── ArchiveExtension    # 扩展点接口
│
├── event                 # 事件驱动层
│   ├── EventBus                # 事件总线
│   ├── EventPublisher          # 事件发布器
│   ├── EventListener           # 事件监听器
│   ├── events/                 # 事件定义
│   │   ├── ConfigChangedEvent  # 配置变更事件
│   │   ├── TaskStatusChangedEvent # 任务状态变更事件
│   │   ├── TaskStartedEvent    # 任务开始事件
│   │   ├── TaskCompletedEvent  # 任务完成事件
│   │   └── ErrorEvent          # 错误事件
│   └── handler/                # 事件处理器
│       └── EventHandler        # 事件处理器接口
│
├── monitor               # 监控统计层
│   ├── ArchiveMetrics          # 归档指标
│   ├── MetricsCollector        # 指标采集器
│   ├── HealthChecker          # 健康检查器
│   ├── ArchiveMonitor         # 归档监控器
│   ├── reporter/              # 指标上报
│   │   ├── MetricsReporter    # 指标上报器
│   │   ├── PrometheusReporter # Prometheus上报器
│   │   └── LogReporter        # 日志上报器
│   └── alert/                 # 告警机制
│       └── AlertManager       # 告警管理器
│
└── utils                 # 工具类层
    ├── DateUtils              # 日期工具
    ├── SqlUtils               # SQL工具
    ├── JsonUtils              # JSON工具
    ├── ThreadUtils            # 线程工具
    └── ValidateUtils          # 验证工具
```

---

## 3. 核心类详细设计

### 3.1 API层设计

#### 3.1.1 ArchiveSdkClient（SDK主入口）

```java
/**
 * Archive SDK 客户端 - 门面模式
 * 
 * 职责：
 * 1. SDK生命周期管理
 * 2. 统一入口，简化API
 * 3. 依赖注入和资源管理
 * 4. 异常统一处理
 */
@Component
@Slf4j
public class ArchiveSdkClient {
    
    // 核心依赖组件
    private final ArchiveConfigManager configManager;
    private final TaskRegistry taskRegistry;
    private final TaskScheduler taskScheduler;
    private final EventBus eventBus;
    private final MetricsCollector metricsCollector;
    private final PluginManager pluginManager;
    private final HealthChecker healthChecker;
    
    // SDK状态管理
    private volatile boolean initialized = false;
    private volatile boolean running = false;
    private final Object stateLock = new Object();
    
    /**
     * 初始化SDK
     */
    public void init(SdkConfig config) {
        synchronized (stateLock) {
            if (initialized) {
                throw new ArchiveException("SDK already initialized");
            }
            
            try {
                // 1. 初始化配置管理器
                configManager.init(config);
                
                // 2. 初始化插件管理器
                pluginManager.init();
                
                // 3. 初始化事件总线
                eventBus.init();
                
                // 4. 初始化监控组件
                metricsCollector.init();
                
                // 5. 初始化健康检查
                healthChecker.init();
                
                // 6. 启动后台任务
                startBackgroundTasks();
                
                initialized = true;
                running = true;
                
                log.info("Archive SDK initialized successfully");
                
            } catch (Exception e) {
                log.error("Failed to initialize Archive SDK", e);
                throw new ArchiveException("SDK initialization failed", e);
            }
        }
    }
    
    /**
     * 注册任务
     */
    public String registerTask(TaskConfig config) {
        validateSdkState();
        validateTaskConfig(config);
        
        try {
            // 1. 生成任务ID
            String taskId = generateTaskId(config);
            
            // 2. 注册到任务注册表
            taskRegistry.register(taskId, config);
            
            // 3. 发布任务注册事件
            eventBus.publish(new TaskRegisteredEvent(taskId, config));
            
            // 4. 更新监控指标
            metricsCollector.recordTaskRegistration(taskId);
            
            log.info("Task registered: {}", taskId);
            return taskId;
            
        } catch (Exception e) {
            log.error("Failed to register task: {}", config.getTaskName(), e);
            throw new ArchiveException("Task registration failed", e);
        }
    }
    
    /**
     * 获取任务状态
     */
    public TaskStatus getTaskStatus(String taskId) {
        validateSdkState();
        validateTaskId(taskId);
        
        return taskRegistry.getTaskStatus(taskId);
    }
    
    /**
     * 订阅事件
     */
    public void subscribeEvent(EventListener listener) {
        validateSdkState();
        if (listener == null) {
            throw new IllegalArgumentException("Event listener cannot be null");
        }
        
        eventBus.subscribe(listener);
        log.info("Event listener subscribed: {}", listener.getClass().getSimpleName());
    }
    
    /**
     * 获取监控指标
     */
    public ArchiveMetrics getMetrics() {
        validateSdkState();
        return metricsCollector.getMetrics();
    }
    
    /**
     * 获取健康状态
     */
    public HealthStatus getHealthStatus() {
        if (!initialized) {
            return HealthStatus.NOT_INITIALIZED;
        }
        return healthChecker.checkHealth();
    }
    
    /**
     * 优雅关闭SDK
     */
    public void shutdown() {
        synchronized (stateLock) {
            if (!initialized || !running) {
                return;
            }
            
            log.info("Shutting down Archive SDK...");
            
            try {
                // 1. 停止接收新任务
                running = false;
                
                // 2. 等待正在执行的任务完成
                taskScheduler.waitForTasksCompletion(30, TimeUnit.SECONDS);
                
                // 3. 关闭事件总线
                eventBus.shutdown();
                
                // 4. 关闭监控组件
                metricsCollector.shutdown();
                
                // 5. 关闭插件管理器
                pluginManager.shutdown();
                
                // 6. 关闭配置管理器
                configManager.shutdown();
                
                log.info("Archive SDK shutdown completed");
                
            } catch (Exception e) {
                log.error("Error during SDK shutdown", e);
            }
        }
    }
    
    // 私有方法
    private void validateSdkState() {
        if (!initialized || !running) {
            throw new ArchiveException("SDK not initialized or not running");
        }
    }
    
    private void validateTaskConfig(TaskConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("Task config cannot be null");
        }
        // 更多验证逻辑...
    }
    
    private void validateTaskId(String taskId) {
        if (StringUtils.isEmpty(taskId)) {
            throw new IllegalArgumentException("Task ID cannot be empty");
        }
    }
    
    private String generateTaskId(TaskConfig config) {
        return config.getTaskName() + "_" + System.currentTimeMillis();
    }
    
    private void startBackgroundTasks() {
        // 启动配置刷新任务
        // 启动健康检查任务
        // 启动监控指标收集任务
    }
}
```

#### 3.1.2 SdkConfig（SDK配置）

```java
/**
 * SDK配置类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SdkConfig {
    
    /**
     * 应用名称
     */
    private String applicationName;
    
    /**
     * 环境标识
     */
    private String environment;
    
    /**
     * 配置来源设置
     */
    private ConfigSourceSettings configSource;
    
    /**
     * 线程池设置
     */
    private ThreadPoolSettings threadPool;
    
    /**
     * 监控设置
     */
    private MonitoringSettings monitoring;
    
    /**
     * 插件设置
     */
    private PluginSettings plugin;
    
    /**
     * 是否启用调试模式
     */
    private boolean debugMode = false;
    
    /**
     * 配置来源设置
     */
    @Data
    public static class ConfigSourceSettings {
        private List<String> nacosServers;
        private String dubboRegistry;
        private String configGroup = "archive-platform";
        private int refreshInterval = 60; // 秒
    }
    
    /**
     * 线程池设置
     */
    @Data
    public static class ThreadPoolSettings {
        private int corePoolSize = 2;
        private int maximumPoolSize = 10;
        private long keepAliveTime = 60L;
        private int queueCapacity = 1000;
    }
    
    /**
     * 监控设置
     */
    @Data
    public static class MonitoringSettings {
        private boolean enabled = true;
        private int metricsCollectionInterval = 30; // 秒
        private List<String> reporters = Arrays.asList("log", "prometheus");
    }
    
    /**
     * 插件设置
     */
    @Data
    public static class PluginSettings {
        private boolean autoRegistration = true;
        private List<String> enabledPlugins = new ArrayList<>();
        private List<String> disabledPlugins = new ArrayList<>();
    }
}
```

### 3.2 配置管理层设计

#### 3.2.1 ArchiveConfigManager（配置管理器）

```java
/**
 * 归档配置管理器 - 单例模式
 * 
 * 职责：
 * 1. 统一配置管理入口
 * 2. 配置缓存与版本管理
 * 3. 配置变更监听与通知
 * 4. 配置验证与默认值处理
 * 5. 多环境配置支持
 */
@Component
@Slf4j
public class ArchiveConfigManager implements ConfigChangeListener {
    
    // 配置存储
    private final ConfigurationStorage configStorage;
    private final ConfigSourceAggregator configAggregator;
    
    // 配置缓存 - 线程安全
    private volatile ArchiveDataConfig globalConfig;
    private final ConcurrentHashMap<String, TaskConfig> taskConfigCache = new ConcurrentHashMap<>();
    
    // 配置版本管理
    private final AtomicLong configVersion = new AtomicLong(0);
    private volatile long lastUpdateTime = 0;
    
    // 并发控制
    private final ReadWriteLock configLock = new ReentrantReadWriteLock();
    
    // 配置变更监听器
    private final List<ConfigChangeListener> listeners = new CopyOnWriteArrayList<>();
    
    // 事件发布器
    private final EventBus eventBus;
    
    /**
     * 初始化配置管理器
     */
    public void init(SdkConfig sdkConfig) {
        try {
            // 1. 初始化配置聚合器
            configAggregator.init(sdkConfig.getConfigSource());
            
            // 2. 加载初始配置
            loadInitialConfig();
            
            // 3. 启动配置监听
            startConfigListener();
            
            log.info("Archive config manager initialized");
            
        } catch (Exception e) {
            log.error("Failed to initialize config manager", e);
            throw new ArchiveException("Config manager initialization failed", e);
        }
    }
    
    /**
     * 获取全局配置 - 读锁保护
     */
    public ArchiveDataConfig getGlobalConfig() {
        configLock.readLock().lock();
        try {
            if (globalConfig == null) {
                throw new ArchiveException("Global config not loaded");
            }
            return globalConfig;
        } finally {
            configLock.readLock().unlock();
        }
    }
    
    /**
     * 获取任务配置
     */
    public TaskConfig getTaskConfig(String taskId) {
        configLock.readLock().lock();
        try {
            TaskConfig config = taskConfigCache.get(taskId);
            if (config == null) {
                log.warn("Task config not found: {}", taskId);
            }
            return config;
        } finally {
            configLock.readLock().unlock();
        }
    }
    
    /**
     * 获取所有任务配置
     */
    public List<TaskConfig> getAllTaskConfigs() {
        configLock.readLock().lock();
        try {
            return new ArrayList<>(taskConfigCache.values());
        } finally {
            configLock.readLock().unlock();
        }
    }
    
    /**
     * 刷新配置 - 写锁保护
     */
    public void refreshConfig() {
        configLock.writeLock().lock();
        try {
            log.info("Refreshing archive config...");
            
            // 1. 从配置源拉取最新配置
            ArchiveDataConfig newConfig = configAggregator.getConfig();
            
            // 2. 验证配置
            validateConfig(newConfig);
            
            // 3. 比较配置变更
            ConfigChangeEvent changeEvent = compareAndDetectChanges(globalConfig, newConfig);
            
            // 4. 更新配置缓存
            updateConfigCache(newConfig);
            
            // 5. 发布配置变更事件
            if (changeEvent.hasChanges()) {
                publishConfigChangeEvent(changeEvent);
            }
            
            // 6. 更新版本和时间戳
            configVersion.incrementAndGet();
            lastUpdateTime = System.currentTimeMillis();
            
            log.info("Config refreshed successfully, version: {}", configVersion.get());
            
        } catch (Exception e) {
            log.error("Failed to refresh config", e);
            throw new ArchiveException("Config refresh failed", e);
        } finally {
            configLock.writeLock().unlock();
        }
    }
    
    /**
     * 注册任务配置
     */
    public void registerTaskConfig(String taskId, TaskConfig config) {
        configLock.writeLock().lock();
        try {
            // 1. 验证任务配置
            validateTaskConfig(config);
            
            // 2. 添加到缓存
            TaskConfig oldConfig = taskConfigCache.put(taskId, config);
            
            // 3. 发布事件
            if (oldConfig == null) {
                eventBus.publish(new TaskConfigRegisteredEvent(taskId, config));
            } else {
                eventBus.publish(new TaskConfigUpdatedEvent(taskId, oldConfig, config));
            }
            
            log.info("Task config registered: {}", taskId);
            
        } catch (Exception e) {
            log.error("Failed to register task config: {}", taskId, e);
            throw new ArchiveException("Task config registration failed", e);
        } finally {
            configLock.writeLock().unlock();
        }
    }
    
    /**
     * 移除任务配置
     */
    public void removeTaskConfig(String taskId) {
        configLock.writeLock().lock();
        try {
            TaskConfig removedConfig = taskConfigCache.remove(taskId);
            
            if (removedConfig != null) {
                eventBus.publish(new TaskConfigRemovedEvent(taskId, removedConfig));
                log.info("Task config removed: {}", taskId);
            }
            
        } finally {
            configLock.writeLock().unlock();
        }
    }
    
    /**
     * 添加配置变更监听器
     */
    public void addConfigChangeListener(ConfigChangeListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            log.info("Config change listener added: {}", listener.getClass().getSimpleName());
        }
    }
    
    /**
     * 移除配置变更监听器
     */
    public void removeConfigChangeListener(ConfigChangeListener listener) {
        if (listener != null) {
            listeners.remove(listener);
            log.info("Config change listener removed: {}", listener.getClass().getSimpleName());
        }
    }
    
    /**
     * 获取配置版本
     */
    public long getConfigVersion() {
        return configVersion.get();
    }
    
    /**
     * 获取最后更新时间
     */
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    /**
     * 导出配置（用于调试和备份）
     */
    public ConfigExport exportConfig() {
        configLock.readLock().lock();
        try {
            return ConfigExport.builder()
                    .globalConfig(globalConfig)
                    .taskConfigs(new HashMap<>(taskConfigCache))
                    .version(configVersion.get())
                    .exportTime(System.currentTimeMillis())
                    .build();
        } finally {
            configLock.readLock().unlock();
        }
    }
    
    /**
     * 配置变更监听实现
     */
    @Override
    public void onConfigChanged(ConfigChangeEvent event) {
        try {
            // 刷新配置
            refreshConfig();
            
            // 通知所有监听器
            notifyListeners(event);
            
        } catch (Exception e) {
            log.error("Error handling config change", e);
        }
    }
    
    // 私有方法
    private void loadInitialConfig() {
        ArchiveDataConfig config = configAggregator.getConfig();
        validateConfig(config);
        updateConfigCache(config);
        configVersion.set(1);
        lastUpdateTime = System.currentTimeMillis();
    }
    
    private void startConfigListener() {
        configAggregator.addListener(this);
    }
    
    private void validateConfig(ArchiveDataConfig config) {
        if (config == null) {
            throw new ArchiveException("Config cannot be null");
        }
        
        // 验证线程数配置
        if (config.getThreads() <= 0 || config.getThreads() > config.getMaxThreads()) {
            throw new ArchiveException("Invalid thread configuration");
        }
        
        // 验证任务配置
        if (config.getTasks() != null) {
            for (TaskConfig task : config.getTasks()) {
                validateTaskConfig(task);
            }
        }
    }
    
    private void validateTaskConfig(TaskConfig config) {
        if (config == null) {
            throw new ArchiveException("Task config cannot be null");
        }
        
        if (StringUtils.isEmpty(config.getTaskName())) {
            throw new ArchiveException("Task name cannot be empty");
        }
        
        if (config.getInterval() == null || config.getInterval() <= 0) {
            throw new ArchiveException("Task interval must be positive");
        }
        
        // 更多验证逻辑...
    }
    
    private ConfigChangeEvent compareAndDetectChanges(ArchiveDataConfig oldConfig, ArchiveDataConfig newConfig) {
        ConfigChangeEvent.Builder builder = ConfigChangeEvent.builder();
        
        if (oldConfig == null) {
            return builder.changeType(ConfigChangeType.INITIAL_LOAD).build();
        }
        
        // 比较全局配置变更
        if (!Objects.equals(oldConfig.getThreads(), newConfig.getThreads())) {
            builder.addChange("threads", oldConfig.getThreads(), newConfig.getThreads());
        }
        
        if (!Objects.equals(oldConfig.getMaxThreads(), newConfig.getMaxThreads())) {
            builder.addChange("maxThreads", oldConfig.getMaxThreads(), newConfig.getMaxThreads());
        }
        
        // 比较任务配置变更
        Map<String, TaskConfig> oldTasks = oldConfig.getTasks().stream()
                .collect(Collectors.toMap(TaskConfig::getTaskName, Function.identity()));
        
        Map<String, TaskConfig> newTasks = newConfig.getTasks().stream()
                .collect(Collectors.toMap(TaskConfig::getTaskName, Function.identity()));
        
        // 检测新增任务
        newTasks.entrySet().stream()
                .filter(entry -> !oldTasks.containsKey(entry.getKey()))
                .forEach(entry -> builder.addTaskAdded(entry.getKey(), entry.getValue()));
        
        // 检测删除任务
        oldTasks.entrySet().stream()
                .filter(entry -> !newTasks.containsKey(entry.getKey()))
                .forEach(entry -> builder.addTaskRemoved(entry.getKey(), entry.getValue()));
        
        // 检测修改任务
        newTasks.entrySet().stream()
                .filter(entry -> oldTasks.containsKey(entry.getKey()))
                .filter(entry -> !Objects.equals(oldTasks.get(entry.getKey()), entry.getValue()))
                .forEach(entry -> builder.addTaskUpdated(entry.getKey(), 
                        oldTasks.get(entry.getKey()), entry.getValue()));
        
        return builder.build();
    }
    
    private void updateConfigCache(ArchiveDataConfig newConfig) {
        // 更新全局配置
        this.globalConfig = newConfig;
        
        // 更新任务配置缓存
        taskConfigCache.clear();
        if (newConfig.getTasks() != null) {
            newConfig.getTasks().forEach(task -> 
                    taskConfigCache.put(task.getTaskName(), task));
        }
    }
    
    private void publishConfigChangeEvent(ConfigChangeEvent event) {
        try {
            eventBus.publish(event);
        } catch (Exception e) {
            log.error("Failed to publish config change event", e);
        }
    }
    
    private void notifyListeners(ConfigChangeEvent event) {
        for (ConfigChangeListener listener : listeners) {
            try {
                listener.onConfigChanged(event);
            } catch (Exception e) {
                log.error("Error notifying config change listener: {}", 
                        listener.getClass().getSimpleName(), e);
            }
        }
    }
}
```

#### 3.2.2 ConfigSourceAggregator（配置聚合器）

```java
/**
 * 配置源聚合器 - 策略模式
 * 
 * 职责：
 * 1. 聚合多种配置来源
 * 2. 配置优先级管理
 * 3. 配置合并策略
 * 4. 降级和容错处理
 */
@Component
@Slf4j
public class ConfigSourceAggregator implements ConfigSource {
    
    // 配置源列表（按优先级排序）
    private final List<ConfigSource> configSources = new ArrayList<>();
    
    // 配置合并策略
    private ConfigMergeStrategy mergeStrategy = new DefaultConfigMergeStrategy();
    
    // 缓存配置
    private volatile ArchiveDataConfig cachedConfig;
    private volatile long lastRefreshTime = 0;
    private final long cacheExpireTime = 30000; // 30秒
    
    // 降级配置
    private ArchiveDataConfig fallbackConfig;
    
    /**
     * 初始化聚合器
     */
    public void init(SdkConfig.ConfigSourceSettings settings) {
        // 1. 添加 Nacos 配置源（优先级最高）
        if (settings.getNacosServers() != null && !settings.getNacosServers().isEmpty()) {
            NacosConfigSource nacosSource = new NacosConfigSource();
            nacosSource.init(settings);
            addConfigSource(nacosSource, 1);
        }
        
        // 2. 添加 Dubbo 配置源
        if (StringUtils.isNotEmpty(settings.getDubboRegistry())) {
            DubboConfigSource dubboSource = new DubboConfigSource();
            dubboSource.init(settings);
            addConfigSource(dubboSource, 2);
        }
        
        // 3. 添加文件配置源（优先级最低，作为降级）
        FileConfigSource fileSource = new FileConfigSource();
        fileSource.init(settings);
        addConfigSource(fileSource, 3);
        
        // 4. 加载降级配置
        loadFallbackConfig();
        
        log.info("Config source aggregator initialized with {} sources", configSources.size());
    }
    
    /**
     * 添加配置源
     */
    public void addConfigSource(ConfigSource source, int priority) {
        if (source != null) {
            // 按优先级插入（数字越小优先级越高）
            int index = 0;
            for (int i = 0; i < configSources.size(); i++) {
                if (getSourcePriority(configSources.get(i)) > priority) {
                    index = i;
                    break;
                }
                index = i + 1;
            }
            
            configSources.add(index, source);
            setSourcePriority(source, priority);
            
            log.info("Config source added: {} (priority: {})", 
                    source.getClass().getSimpleName(), priority);
        }
    }
    
    /**
     * 获取聚合配置
     */
    @Override
    public ArchiveDataConfig getConfig() {
        // 检查缓存是否有效
        if (cachedConfig != null && !isCacheExpired()) {
            return cachedConfig;
        }
        
        try {
            // 从各配置源获取配置
            List<ArchiveDataConfig> configs = new ArrayList<>();
            
            for (ConfigSource source : configSources) {
                try {
                    ArchiveDataConfig config = source.getConfig();
                    if (config != null) {
                        configs.add(config);
                        log.debug("Config loaded from: {}", source.getClass().getSimpleName());
                    }
                } catch (Exception e) {
                    log.warn("Failed to load config from: {}", source.getClass().getSimpleName(), e);
                }
            }
            
            // 合并配置
            ArchiveDataConfig mergedConfig = mergeConfigs(configs);
            
            // 验证配置
            if (mergedConfig != null && isValidConfig(mergedConfig)) {
                cachedConfig = mergedConfig;
                lastRefreshTime = System.currentTimeMillis();
                return mergedConfig;
            } else {
                log.warn("Invalid merged config, using fallback");
                return getFallbackConfig();
            }
            
        } catch (Exception e) {
            log.error("Error getting aggregated config", e);
            return getFallbackConfig();
        }
    }
    
    /**
     * 添加配置变更监听器
     */
    @Override
    public void addListener(ConfigChangeListener listener) {
        if (listener != null) {
            // 为每个配置源添加监听器
            for (ConfigSource source : configSources) {
                source.addListener(event -> {
                    // 配置变更时清除缓存并通知
                    invalidateCache();
                    listener.onConfigChanged(event);
                });
            }
        }
    }
    
    /**
     * 刷新配置
     */
    @Override
    public void refresh() {
        invalidateCache();
        
        // 刷新所有配置源
        for (ConfigSource source : configSources) {
            try {
                source.refresh();
            } catch (Exception e) {
                log.warn("Failed to refresh config source: {}", 
                        source.getClass().getSimpleName(), e);
            }
        }
        
        log.info("Config sources refreshed");
    }
    
    /**
     * 设置配置合并策略
     */
    public void setMergeStrategy(ConfigMergeStrategy strategy) {
        if (strategy != null) {
            this.mergeStrategy = strategy;
        }
    }
    
    /**
     * 获取配置源状态
     */
    public List<ConfigSourceStatus> getConfigSourceStatus() {
        return configSources.stream()
                .map(this::getSourceStatus)
                .collect(Collectors.toList());
    }
    
    // 私有方法
    private boolean isCacheExpired() {
        return System.currentTimeMillis() - lastRefreshTime > cacheExpireTime;
    }
    
    private void invalidateCache() {
        cachedConfig = null;
        lastRefreshTime = 0;
    }
    
    private ArchiveDataConfig mergeConfigs(List<ArchiveDataConfig> configs) {
        if (configs.isEmpty()) {
            return null;
        }
        
        if (configs.size() == 1) {
            return configs.get(0);
        }
        
        return mergeStrategy.merge(configs);
    }
    
    private boolean isValidConfig(ArchiveDataConfig config) {
        return config != null 
                && config.getThreads() > 0 
                && config.getMaxThreads() > 0
                && config.getInterval() > 0;
    }
    
    private ArchiveDataConfig getFallbackConfig() {
        if (fallbackConfig != null) {
            log.info("Using fallback config");
            return fallbackConfig;
        }
        
        // 返回最小可用配置
        ArchiveDataConfig minConfig = new ArchiveDataConfig();
        minConfig.setEnable(false);
        minConfig.setThreads(1);
        minConfig.setMaxThreads(2);
        minConfig.setInterval(300);
        minConfig.setLockExpireSeconds(3600);
        minConfig.setTasks(new ArrayList<>());
        
        log.warn("Using minimal fallback config");
        return minConfig;
    }
    
    private void loadFallbackConfig() {
        try {
            // 从文件加载降级配置
            fallbackConfig = loadConfigFromFile("fallback-config.json");
        } catch (Exception e) {
            log.warn("Failed to load fallback config", e);
        }
    }
    
    private ArchiveDataConfig loadConfigFromFile(String filename) {
        // 实现从文件加载配置的逻辑
        return null;
    }
    
    private int getSourcePriority(ConfigSource source) {
        // 从源实例中获取优先级（需要在源中存储）
        return 1000; // 默认优先级
    }
    
    private void setSourcePriority(ConfigSource source, int priority) {
        // 在源实例中设置优先级
    }
    
    private ConfigSourceStatus getSourceStatus(ConfigSource source) {
        try {
            ArchiveDataConfig config = source.getConfig();
            return new ConfigSourceStatus(
                    source.getClass().getSimpleName(),
                    config != null ? ConfigSourceStatus.Status.HEALTHY : ConfigSourceStatus.Status.UNAVAILABLE,
                    System.currentTimeMillis()
            );
        } catch (Exception e) {
            return new ConfigSourceStatus(
                    source.getClass().getSimpleName(),
                    ConfigSourceStatus.Status.ERROR,
                    System.currentTimeMillis(),
                    e.getMessage()
            );
        }
    }
}
```

### 3.3 核心领域层设计

#### 3.3.1 ArchiveEngine（归档引擎）

```java
/**
 * 归档引擎 - 外观模式 + 模板方法模式
 * 
 * 职责：
 * 1. 归档流程编排
 * 2. 任务生命周期管理
 * 3. 资源协调与管理
 * 4. 异常处理与恢复
 * 5. 性能监控与优化
 */
@Component
@Slf4j
public class ArchiveEngine extends ShutdownAbstract {
    
    // 核心组件依赖
    private final ArchiveConfigManager configManager;
    private final TaskScheduler taskScheduler;
    private final ArchiveTaskExecutor taskExecutor;
    private final PluginManager pluginManager;
    private final EventBus eventBus;
    private final MetricsCollector metricsCollector;
    private final HealthChecker healthChecker;
    
    // 执行控制组件
    private final CheckpointManager checkpointManager;
    private final DistributedLock distributedLock;
    private final ShardingManager shardingManager;
    
    // 线程池
    private ThreadPoolExecutor executorService;
    private ScheduledExecutorService scheduledExecutor;
    
    // 引擎状态
    private volatile boolean running = false;
    private final AtomicBoolean shutdown = new AtomicBoolean(false);
    
    // 任务执行统计
    private final AtomicLong totalTasksExecuted = new AtomicLong(0);
    private final AtomicLong totalTasksSucceeded = new AtomicLong(0);
    private final AtomicLong totalTasksFailed = new AtomicLong(0);
    
    /**
     * 启动归档引擎
     */
    public void start() {
        synchronized (this) {
            if (running) {
                log.warn("Archive engine is already running");
                return;
            }
            
            try {
                log.info("Starting archive engine...");
                
                // 1. 初始化线程池
                initializeThreadPools();
                
                // 2. 启动配置监听
                startConfigListener();
                
                // 3. 启动任务调度
                startTaskScheduling();
                
                // 4. 启动健康检查
                startHealthCheck();
                
                // 5. 启动监控收集
                startMetricsCollection();
                
                running = true;
                
                log.info("Archive engine started successfully");
                
                // 发布引擎启动事件
                eventBus.publish(new EngineStartedEvent());
                
            } catch (Exception e) {
                log.error("Failed to start archive engine", e);
                throw new ArchiveException("Engine startup failed", e);
            }
        }
    }
    
    /**
     * 执行单个任务 - 模板方法
     */
    public CompletableFuture<TaskExecutionResult> executeTask(TaskConfig taskConfig) {
        return CompletableFuture.supplyAsync(() -> {
            String taskId = taskConfig.getTaskName();
            MDC.put("taskId", taskId);
            
            try {
                log.info("Starting task execution: {}", taskId);
                
                // 1. 前置检查
                preExecutionCheck(taskConfig);
                
                // 2. 获取分布式锁
                String lockKey = "archive_task_" + taskId;
                boolean lockAcquired = distributedLock.tryLock(lockKey, 
                        Duration.ofSeconds(configManager.getGlobalConfig().getLockExpireSeconds()));
                
                if (!lockAcquired) {
                    log.warn("Failed to acquire lock for task: {}", taskId);
                    return TaskExecutionResult.failed("Lock acquisition failed");
                }
                
                try {
                    // 3. 执行任务
                    TaskExecutionResult result = doExecuteTask(taskConfig);
                    
                    // 4. 更新统计
                    updateExecutionStats(result);
                    
                    // 5. 发布执行结果事件
                    publishExecutionEvent(taskConfig, result);
                    
                    return result;
                    
                } finally {
                    // 释放锁
                    distributedLock.unlock(lockKey);
                }
                
            } catch (Exception e) {
                log.error("Task execution failed: {}", taskId, e);
                
                TaskExecutionResult errorResult = TaskExecutionResult.error(e);
                updateExecutionStats(errorResult);
                publishExecutionEvent(taskConfig, errorResult);
                
                return errorResult;
                
            } finally {
                MDC.remove("taskId");
            }
            
        }, executorService);
    }
    
    /**
     * 批量执行任务
     */
    public List<CompletableFuture<TaskExecutionResult>> executeTasks(List<TaskConfig> taskConfigs) {
        return taskConfigs.stream()
                .map(this::executeTask)
                .collect(Collectors.toList());
    }
    
    /**
     * 暂停任务
     */
    public void pauseTask(String taskId) {
        // 实现任务暂停逻辑
        log.info("Task paused: {}", taskId);
        eventBus.publish(new TaskPausedEvent(taskId));
    }
    
    /**
     * 恢复任务
     */
    public void resumeTask(String taskId) {
        // 实现任务恢复逻辑
        log.info("Task resumed: {}", taskId);
        eventBus.publish(new TaskResumedEvent(taskId));
    }
    
    /**
     * 取消任务
     */
    public void cancelTask(String taskId) {
        // 实现任务取消逻辑
        log.info("Task cancelled: {}", taskId);
        eventBus.publish(new TaskCancelledEvent(taskId));
    }
    
    /**
     * 获取引擎状态
     */
    public EngineStatus getEngineStatus() {
        return EngineStatus.builder()
                .running(running)
                .totalTasksExecuted(totalTasksExecuted.get())
                .totalTasksSucceeded(totalTasksSucceeded.get())
                .totalTasksFailed(totalTasksFailed.get())
                .activeThreads(executorService.getActiveCount())
                .queueSize(executorService.getQueue().size())
                .uptime(System.currentTimeMillis() - startTime)
                .build();
    }
    
    /**
     * 优雅关闭
     */
    @Override
    public void shutdownGracefully() {
        if (shutdown.compareAndSet(false, true)) {
            log.info("Shutting down archive engine gracefully...");
            
            try {
                // 1. 停止接收新任务
                running = false;
                
                // 2. 等待正在执行的任务完成
                executorService.shutdown();
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Force shutting down executor service");
                    executorService.shutdownNow();
                }
                
                // 3. 关闭调度器
                scheduledExecutor.shutdown();
                if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
                
                // 4. 释放资源
                releaseResources();
                
                log.info("Archive engine shutdown completed");
                
                // 发布引擎关闭事件
                eventBus.publish(new EngineShutdownEvent());
                
            } catch (Exception e) {
                log.error("Error during engine shutdown", e);
            }
        }
    }
    
    // 私有方法 - 模板方法模式的具体步骤
    
    private void preExecutionCheck(TaskConfig taskConfig) {
        // 1. 检查引擎状态
        if (!running) {
            throw new ArchiveException("Engine is not running");
        }
        
        // 2. 验证任务配置
        if (!taskScheduler.validateTaskConfig(taskConfig)) {
            throw new ArchiveException("Invalid task config: " + taskConfig.getTaskName());
        }
        
        // 3. 检查资源可用性
        if (executorService.getActiveCount() >= configManager.getGlobalConfig().getMaxThreads()) {
            throw new ArchiveException("Thread pool exhausted");
        }
        
        // 4. 执行插件前置检查
        ArchiveContext context = new ArchiveContext(null, taskConfig.getTaskName(), 
                taskConfig.getDatasourceName(), taskConfig, 
                configManager.getGlobalConfig(), generateTraceId());
        
        PluginResult pluginResult = pluginManager.executePlugins(
                ArchiveEvent.BEFORE_TASK_EXECUTE, context);
        
        if (!pluginResult.shouldContinue()) {
            throw new ArchiveException("Task execution blocked by plugin: " + 
                    pluginResult.getMessage());
        }
    }
    
    private TaskExecutionResult doExecuteTask(TaskConfig taskConfig) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        
        try {
            // 1. 创建执行上下文
            ArchiveContext context = createExecutionContext(taskConfig);
            
            // 2. 加载检查点（断点续传）
            ArchiveCheckpoint checkpoint = checkpointManager.loadCheckpoint(taskConfig.getTaskName());
            if (checkpoint != null) {
                context.setArchiveCheckpoint(checkpoint);
                log.info("Resuming task from checkpoint: {}", taskConfig.getTaskName());
            }
            
            // 3. 执行任务
            taskExecutor.execute(taskConfig, configManager.getGlobalConfig());
            
            // 4. 清理检查点
            if (checkpoint != null) {
                checkpointManager.removeCheckpoint(taskConfig.getTaskName());
            }
            
            stopwatch.stop();
            
            return TaskExecutionResult.success(stopwatch.elapsed(TimeUnit.MILLISECONDS));
            
        } catch (Exception e) {
            stopwatch.stop();
            
            // 保存检查点用于断点续传
            try {
                ArchiveCheckpoint checkpoint = createCheckpoint(taskConfig, e);
                checkpointManager.saveCheckpoint(checkpoint);
            } catch (Exception ex) {
                log.error("Failed to save checkpoint", ex);
            }
            
            throw e;
        }
    }
    
    private ArchiveContext createExecutionContext(TaskConfig taskConfig) {
        return new ArchiveContext(
                null, // ArchiveNode will be set by executor
                taskConfig.getTaskName(),
                taskConfig.getDatasourceName(),
                taskConfig,
                configManager.getGlobalConfig(),
                generateTraceId()
        );
    }
    
    private ArchiveCheckpoint createCheckpoint(TaskConfig taskConfig, Exception error) {
        return new ArchiveCheckpoint()
                .setTaskName(taskConfig.getTaskName())
                .setCheckpointTime(new Date())
                .setCurrentNode("error_node")
                .setScanMode(false);
    }
    
    private void updateExecutionStats(TaskExecutionResult result) {
        totalTasksExecuted.incrementAndGet();
        
        if (result.isSuccess()) {
            totalTasksSucceeded.incrementAndGet();
        } else {
            totalTasksFailed.incrementAndGet();
        }
        
        // 更新监控指标
        metricsCollector.recordTaskExecution(result);
    }
    
    private void publishExecutionEvent(TaskConfig taskConfig, TaskExecutionResult result) {
        try {
            if (result.isSuccess()) {
                eventBus.publish(new TaskCompletedEvent(taskConfig.getTaskName(), result));
            } else {
                eventBus.publish(new TaskFailedEvent(taskConfig.getTaskName(), result));
            }
        } catch (Exception e) {
            log.error("Failed to publish execution event", e);
        }
    }
    
    private void initializeThreadPools() {
        ArchiveDataConfig globalConfig = configManager.getGlobalConfig();
        
        // 主执行线程池
        executorService = new ThreadPoolExecutor(
                globalConfig.getThreads(),
                globalConfig.getMaxThreads(),
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new ThreadFactoryBuilder()
                        .setNameFormat("archive-executor-%d")
                        .setDaemon(true)
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // 调度线程池
        scheduledExecutor = Executors.newScheduledThreadPool(2,
                new ThreadFactoryBuilder()
                        .setNameFormat("archive-scheduler-%d")
                        .setDaemon(true)
                        .build());
    }
    
    private void startConfigListener() {
        configManager.addConfigChangeListener(event -> {
            if (event.hasThreadConfigChanges()) {
                // 动态调整线程池
                adjustThreadPool(event);
            }
        });
    }
    
    private void startTaskScheduling() {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            if (!running) return;
            
            try {
                // 获取可执行任务
                List<TaskConfig> executableTasks = taskScheduler
                        .filterExecutableTasks(configManager.getGlobalConfig());
                
                // 提交任务执行
                for (TaskConfig task : executableTasks) {
                    executeTask(task);
                }
                
            } catch (Exception e) {
                log.error("Error in task scheduling", e);
            }
            
        }, 0, configManager.getGlobalConfig().getInterval(), TimeUnit.SECONDS);
    }
    
    private void startHealthCheck() {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            try {
                HealthStatus status = healthChecker.checkHealth();
                if (status != HealthStatus.HEALTHY) {
                    log.warn("Engine health check failed: {}", status);
                    eventBus.publish(new HealthCheckFailedEvent(status));
                }
            } catch (Exception e) {
                log.error("Error in health check", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    private void startMetricsCollection() {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            try {
                metricsCollector.collectEngineMetrics(getEngineStatus());
            } catch (Exception e) {
                log.error("Error collecting metrics", e);
            }
        }, 10, 30, TimeUnit.SECONDS);
    }
    
    private void adjustThreadPool(ConfigChangeEvent event) {
        // 动态调整线程池大小
        Integer newCoreSize = event.getNewValue("threads", Integer.class);
        Integer newMaxSize = event.getNewValue("maxThreads", Integer.class);
        
        if (newCoreSize != null) {
            executorService.setCorePoolSize(newCoreSize);
        }
        
        if (newMaxSize != null) {
            executorService.setMaximumPoolSize(newMaxSize);
        }
        
        log.info("Thread pool adjusted - core: {}, max: {}", 
                executorService.getCorePoolSize(), 
                executorService.getMaximumPoolSize());
    }
    
    private void releaseResources() {
        // 释放分布式锁
        // 清理临时文件
        // 关闭数据库连接
        // 等等...
    }
    
    private String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    private long startTime = System.currentTimeMillis();
    
    @Override
    public int order() {
        return Integer.MIN_VALUE;
    }
}
```

---

## 4. 完整调用链路图

```mermaid
sequenceDiagram
    participant Client as 业务方
    participant SDK as ArchiveSdkClient
    participant ConfigMgr as ArchiveConfigManager
    participant Scheduler as TaskScheduler
    participant Engine as ArchiveEngine
    participant Executor as ArchiveTaskExecutor
    participant Plugin as PluginManager
    participant Event as EventBus
    participant Monitor as MetricsCollector
    
    Note over Client, Monitor: SDK初始化流程
    
    Client->>SDK: init(config)
    SDK->>ConfigMgr: init(config)
    ConfigMgr->>ConfigMgr: loadInitialConfig()
    ConfigMgr->>Event: publish(ConfigLoadedEvent)
    SDK->>Plugin: init()
    SDK->>Monitor: init()
    SDK->>Engine: start()
    Engine->>Scheduler: startTaskScheduling()
    SDK-->>Client: 初始化完成
    
    Note over Client, Monitor: 任务注册流程
    
    Client->>SDK: registerTask(taskConfig)
    SDK->>SDK: validateTaskConfig()
    SDK->>ConfigMgr: registerTaskConfig(taskId, config)
    ConfigMgr->>Event: publish(TaskRegisteredEvent)
    SDK->>Monitor: recordTaskRegistration()
    SDK-->>Client: 返回taskId
    
    Note over Client, Monitor: 任务执行流程
    
    Scheduler->>Scheduler: filterExecutableTasks()
    Scheduler->>Engine: executeTask(taskConfig)
    Engine->>Engine: preExecutionCheck()
    Engine->>Engine: acquireDistributedLock()
    
    Engine->>Plugin: executePlugins(BEFORE_TASK_EXECUTE)
    Plugin-->>Engine: PluginResult
    
    Engine->>Executor: execute(taskConfig)
    Executor->>Executor: parseTask()
    Executor->>Executor: validateTask()
    
    loop 对每个归档节点
        Executor->>Plugin: executePlugins(BEFORE_QUERY)
        Executor->>Executor: queryData()
        Executor->>Plugin: executePlugins(AFTER_QUERY)
        
        Executor->>Plugin: executePlugins(BEFORE_DELETE)
        Executor->>Executor: deleteData()
        Executor->>Plugin: executePlugins(AFTER_DELETE)
    end
    
    Executor->>Plugin: executePlugins(AFTER_TASK_EXECUTE)
    Executor-->>Engine: TaskExecutionResult
    
    Engine->>Engine: updateExecutionStats()
    Engine->>Event: publish(TaskCompletedEvent)
    Engine->>Monitor: recordTaskExecution()
    Engine->>Engine: releaseDistributedLock()
    
    Note over Client, Monitor: 配置变更流程
    
    ConfigMgr->>ConfigMgr: onConfigChanged()
    ConfigMgr->>Event: publish(ConfigChangedEvent)
    Engine->>Engine: adjustThreadPool()
    Scheduler->>Scheduler: updateSchedulePolicy()
    
    Note over Client, Monitor: 监控告警流程
    
    Monitor->>Monitor: collectMetrics()
    Monitor->>Monitor: checkThresholds()
    Monitor->>Event: publish(AlertEvent)
    Monitor->>Monitor: reportMetrics()
```

---

## 5. 核心类关系图

```mermaid
classDiagram
    %% API层
    class ArchiveSdkClient {
        -configManager: ArchiveConfigManager
        -taskRegistry: TaskRegistry
        -eventBus: EventBus
        -metricsCollector: MetricsCollector
        +init(SdkConfig)
        +registerTask(TaskConfig) String
        +getTaskStatus(String) TaskStatus
        +subscribeEvent(EventListener)
        +getMetrics() ArchiveMetrics
        +shutdown()
    }
    
    class SdkConfig {
        -applicationName: String
        -environment: String
        -configSource: ConfigSourceSettings
        -threadPool: ThreadPoolSettings
        -monitoring: MonitoringSettings
    }
    
    %% 配置层
    class ArchiveConfigManager {
        -configStorage: ConfigurationStorage
        -configAggregator: ConfigSourceAggregator
        -globalConfig: ArchiveDataConfig
        -taskConfigCache: ConcurrentHashMap
        +getGlobalConfig() ArchiveDataConfig
        +getTaskConfig(String) TaskConfig
        +refreshConfig()
        +registerTaskConfig(String, TaskConfig)
        +addConfigChangeListener(ConfigChangeListener)
    }
    
    class ConfigSourceAggregator {
        -configSources: List~ConfigSource~
        -mergeStrategy: ConfigMergeStrategy
        +getConfig() ArchiveDataConfig
        +addConfigSource(ConfigSource, int)
        +refresh()
    }
    
    class ConfigSource {
        <<interface>>
        +getConfig() ArchiveDataConfig
        +addListener(ConfigChangeListener)
        +refresh()
    }
    
    %% 核心层
    class ArchiveEngine {
        -configManager: ArchiveConfigManager
        -taskScheduler: TaskScheduler
        -taskExecutor: ArchiveTaskExecutor
        -pluginManager: PluginManager
        -distributedLock: DistributedLock
        +start()
        +executeTask(TaskConfig) CompletableFuture
        +pauseTask(String)
        +resumeTask(String)
        +getEngineStatus() EngineStatus
        +shutdownGracefully()
    }
    
    class TaskScheduler {
        +filterExecutableTasks(ArchiveDataConfig) List~TaskConfig~
        +calculateNextExecuteTime(TaskConfig) Long
        +validateTaskConfig(TaskConfig) boolean
    }
    
    class ArchiveTaskExecutor {
        <<abstract>>
        +execute(TaskConfig, ArchiveDataConfig)
        #beforeExecute(TaskConfig, ArchiveDataConfig)
        #parseTask(TaskConfig) ArchiveTask
        #validateTask(ArchiveTask)
        #executeTask(ArchiveTask, ArchiveDataConfig)
        #afterExecute(TaskConfig, ArchiveDataConfig, boolean, Exception)
    }
    
    class ArchiveTask {
        -archiveNodes: List~ArchiveNode~
        -taskName: String
        -datasourceName: String
        -config: TaskConfig
        +getArchiveNodes() List~ArchiveNode~
        +isDebugMode() boolean
    }
    
    class ArchiveContext {
        -archiveNode: ArchiveNode
        -taskName: String
        -taskConfig: TaskConfig
        -archiveConfig: ArchiveDataConfig
        -traceId: String
        +addVariable(String, Object) ArchiveContext
        +getVariable(String) Object
        +addCondition(ConditionContext)
    }
    
    %% 插件层
    class PluginManager {
        -plugins: List~ArchivePlugin~
        -eventPluginCache: Map~ArchiveEvent, List~ArchivePlugin~~
        +registerPlugin(ArchivePlugin)
        +executePlugins(ArchiveEvent, ArchiveContext) PluginResult
        +getPluginsByEvent(ArchiveEvent) List~ArchivePlugin~
    }
    
    class ArchivePlugin {
        <<interface>>
        +onEvent(ArchiveEvent, ArchiveContext) PluginResult
        +getPriority() int
        +isEnabled() boolean
        +getSupportedEvents() Set~ArchiveEvent~
        +isPreviewSupported() boolean
    }
    
    class ArchiveEvent {
        <<enumeration>>
        BEFORE_TASK_EXECUTE
        AFTER_TASK_EXECUTE
        BEFORE_QUERY
        AFTER_QUERY
        BEFORE_DELETE
        AFTER_DELETE
        ON_ERROR
        ON_COMPLETE
    }
    
    %% 事件层
    class EventBus {
        -subscribers: Map~Class, List~EventListener~~
        +publish(Object)
        +subscribe(EventListener)
        +unsubscribe(EventListener)
    }
    
    class EventListener {
        <<interface>>
        +onEvent(Object)
    }
    
    %% 监控层
    class MetricsCollector {
        -metrics: ArchiveMetrics
        -reporters: List~MetricsReporter~
        +recordTaskExecution(TaskExecutionResult)
        +recordTaskRegistration(String)
        +collectEngineMetrics(EngineStatus)
        +getMetrics() ArchiveMetrics
    }
    
    class ArchiveMetrics {
        -taskCount: AtomicLong
        -successCount: AtomicLong
        -failureCount: AtomicLong
        -avgLatency: AtomicLong
        +getTaskCount() long
        +getSuccessRate() double
        +getAvgLatency() long
    }
    
    %% 关系定义
    ArchiveSdkClient --> ArchiveConfigManager
    ArchiveSdkClient --> EventBus
    ArchiveSdkClient --> MetricsCollector
    ArchiveSdkClient --> ArchiveEngine
    
    ArchiveConfigManager --> ConfigSourceAggregator
    ConfigSourceAggregator --> ConfigSource
    
    ArchiveEngine --> TaskScheduler
    ArchiveEngine --> ArchiveTaskExecutor
    ArchiveEngine --> PluginManager
    ArchiveEngine --> EventBus
    
    ArchiveTaskExecutor --> ArchiveTask
    ArchiveTaskExecutor --> ArchiveContext
    
    PluginManager --> ArchivePlugin
    PluginManager --> ArchiveEvent
    
    EventBus --> EventListener
    
    MetricsCollector --> ArchiveMetrics
```

---

## 6. 重要设计决策说明

### 6.1 为什么选择这种架构？

1. **分层架构**：清晰分离关注点，便于维护和测试
2. **插件化设计**：支持业务定制，提升扩展性
3. **事件驱动**：解耦模块间依赖，提升可维护性
4. **配置驱动**：支持动态调整，提升运维友好性

### 6.2 关键技术选择

1. **线程安全**：使用ConcurrentHashMap、ReadWriteLock等保证并发安全
2. **异步执行**：使用CompletableFuture提升执行效率
3. **资源管理**：统一的生命周期管理，支持优雅关闭
4. **监控集成**：内置监控体系，支持多种上报方式

### 6.3 扩展点设计

1. **配置源扩展**：可插拔的配置来源支持
2. **插件机制**：责任链模式，支持自定义业务逻辑
3. **事件机制**：观察者模式，支持自定义事件处理
4. **监控扩展**：可插拔的监控上报器

---

## 7. 使用示例

### 7.1 基本使用

```java
// 1. 创建SDK配置
SdkConfig config = SdkConfig.builder()
    .applicationName("my-app")
    .environment("prod")
    .configSource(SdkConfig.ConfigSourceSettings.builder()
        .nacosServers(Arrays.asList("127.0.0.1:8848"))
        .dubboRegistry("zookeeper://127.0.0.1:2181")
        .build())
    .build();

// 2. 初始化SDK
ArchiveSdkClient client = new ArchiveSdkClient();
client.init(config);

// 3. 注册任务
TaskConfig taskConfig = new TaskConfig();
taskConfig.setTaskName("order_archive");
taskConfig.setDatasourceName("order_db");
// ... 设置其他配置

String taskId = client.registerTask(taskConfig);

// 4. 订阅事件
client.subscribeEvent(event -> {
    if (event instanceof TaskCompletedEvent) {
        System.out.println("Task completed: " + event.getTaskId());
    }
});

// 5. 获取监控指标
ArchiveMetrics metrics = client.getMetrics();
System.out.println("Success rate: " + metrics.getSuccessRate());
```

### 7.2 自定义插件

```java
@Component
public class CustomArchivePlugin implements ArchivePlugin {
    
    @Override
    public PluginResult onEvent(ArchiveEvent event, ArchiveContext context) {
        if (event == ArchiveEvent.BEFORE_DELETE) {
            // 自定义删除前逻辑
            log.info("Custom logic before delete for task: {}", context.getTaskName());
        }
        
        return PluginResult.success();
    }
    
    @Override
    public Set<ArchiveEvent> getSupportedEvents() {
        return Set.of(ArchiveEvent.BEFORE_DELETE, ArchiveEvent.AFTER_DELETE);
    }
    
    @Override
    public int getPriority() {
        return 100; // 较高优先级
    }
}
```

---

这份详细的架构设计文档涵盖了SCP-Archive-SDK的完整设计思路、核心类实现和调用关系。每个类都有明确的职责定义，采用了合适的设计模式，确保系统的高内聚、低耦合、易扩展和易维护。 