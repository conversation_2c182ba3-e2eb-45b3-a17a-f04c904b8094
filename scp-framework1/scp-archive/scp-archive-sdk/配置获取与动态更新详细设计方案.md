# scp-archive-sdk 配置获取与动态更新详细设计方案

---

## 1. 现有实现分析

### 1.1 启动阶段配置获取
- 启动时通过 `NacosConfigManager` 拉取配置（如 `ArchiveArkConfig`、`ConfigChangeNotifier` 的 `@PostConstruct` 初始化）。
- 配置内容通过 Nacos 配置中心下发，支持全局、应用、任务级别的配置。
- 配置内容解析为 `ArchiveDataConfig`、`TaskConfig` 等对象，缓存于本地（如 `ArchiveConfigManager`）。

### 1.2 Dubbo 配置拉取
- 变更事件发生时（如 Nacos 配置变更），会调用 Dubbo 服务 `com.shizhuang.scm.rulecenter.api.service.ArchiveConfigDubboService` 获取最新配置（见 `DubboConfigClient`）。
- 支持重试机制，保证配置拉取的可靠性。

### 1.3 配置变更监听与刷新
- 通过 Nacos 的 `AbstractConfigChangeListener` 监听配置变更。
- 变更后自动刷新本地缓存，并通过配置管理器（`ArchiveConfigManager`）对外暴露最新配置。
- 支持手动刷新、前后值比对、线程安全等机制。

---

## 2. 详细设计方案

### 2.1 启动阶段配置拉取
- **优先拉取 Nacos 配置**，如未获取到或配置不全，则通过 Dubbo 服务 `ArchiveConfigDubboService` 拉取补全。
- 拉取到的配置通过 `ArchiveConfigManager` 进行缓存和管理。
- 支持配置初始化失败时的重试与降级处理。

### 2.2 配置变更处理
- **Nacos 配置变更事件监听**：监听全局、应用、任务级配置的变更。
- **变更后处理流程**：
  1. 对比新旧配置，若有实际变更则继续。
  2. 通过 Dubbo 服务拉取最新配置，保证数据一致性。
  3. 更新本地缓存（`ArchiveConfigManager`），并通知相关业务组件。
  4. 支持任务级配置的注册、更新、删除等操作。
- **线程安全**：配置缓存、任务配置映射等均采用并发容器和读写锁保证并发安全。
- **异常处理**：拉取失败时记录日志，必要时暂停相关任务。

### 2.3 配置管理与解耦
- 统一通过 `ArchiveConfigManager` 管理所有配置，业务代码只依赖该管理器获取配置。
- 支持多级配置（全局、应用、任务），并可扩展其他配置来源。
- 保证线程安全、异常处理和日志记录。

### 2.4 主要类与职责
- `ArchiveArkConfig`：负责 Nacos 配置监听与初始化。
- `ConfigChangeNotifier`：负责多级配置变更监听、Dubbo 拉取、缓存刷新。
- `DubboConfigClient`：封装 Dubbo 服务调用，支持重试。
- `ArchiveConfigManager`：统一管理配置缓存、注册、更新、删除、导出等。

---

## 3. 方案流程图

```mermaid
flowchart TD
    A[SDK启动] --> B[Nacos拉取配置]
    B -- 配置缺失/不全 --> C[Dubbo拉取配置]
    B -- 配置完整 --> D[本地缓存]
    C --> D
    D --> E[业务使用]
    F[Nacos变更事件] --> G[Dubbo拉取最新配置]
    G --> H[刷新本地缓存]
    H --> E
```

---

## 4. 关键点总结
- 启动时优先 Nacos，必要时 Dubbo 补全。
- 变更事件触发后，Dubbo 拉取最新配置，保证一致性。
- 配置管理器统一对外暴露，业务无感知底层实现。
- 支持多级配置、线程安全、异常处理和日志。

---

## 5. 扩展建议
- 支持多环境、多租户配置隔离。
- 预留本地文件、其他配置中心等扩展点。
- 配置变更可支持事件总线通知业务方。
- 配置变更历史与版本管理。

---

如需进一步细化类图、接口说明或伪代码，可随时补充。 