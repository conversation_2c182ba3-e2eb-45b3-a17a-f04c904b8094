# SCP-Archive-SDK 顶层架构设计方案

---

## 1. 设计目标

- **高内聚、低耦合**：各模块职责清晰，便于维护和扩展。
- **可插拔、可扩展**：支持多种配置来源、任务类型、执行策略、监控与事件机制。
- **线程安全、健壮性**：核心组件并发安全，异常处理标准化。
- **易用性与可测试性**：对外API友好，便于Mock和自动化测试。

---

## 2. 顶层包结构建议

```
com.shizhuang.duapp.scp.framework.archive
│
├── config         # 配置相关（拉取、监听、适配、管理）
├── core           # 核心领域（任务、执行、调度、上下文）
├── extension      # 插件与扩展点（SPI、第三方集成）
├── event          # 事件与通知机制
├── monitor        # 监控与统计
├── utils          # 工具类
└── api            # SDK对外API
```

---

## 3. 核心顶层类设计

### 3.1 配置体系

- **ConfigSource（接口）**
  - 统一配置来源抽象（Nacos、Dubbo、文件等）
  - 方法：`getConfig()`、`addListener()`、`refresh()`

- **ConfigSourceAggregator**
  - 聚合多种ConfigSource，支持优先级、降级、合并等策略

- **ArchiveConfigManager**
  - 统一管理归档全局配置、任务配置，负责缓存、校验、暴露配置
  - 线程安全，支持热刷新

- **ConfigAdapter/Converter**
  - 外部DTO与内部DO的转换工具

### 3.2 任务与执行体系

- **ArchiveTask（接口）**
  - 归档任务抽象，定义任务元数据、执行入口、状态管理等
  - 方法：`execute()`、`getStatus()`、`pause()`、`resume()`、`cancel()`

- **TaskConfig（领域模型）**
  - 任务配置，描述任务的所有元数据

- **TaskRegistry**
  - 任务注册表，支持任务的注册、查找、动态加载

- **TaskScheduler**
  - 任务调度器，负责任务的调度、分发、重试、并发控制
  - 支持多种调度策略（定时、事件驱动、手动触发等）

- **TaskExecutor**
  - 任务执行器，负责具体的任务执行逻辑，支持线程池、异步、分布式等

- **TaskContext**
  - 任务执行上下文，封装执行所需的所有环境与依赖

### 3.3 插件与扩展体系

- **ArchivePlugin（接口）**
  - 插件扩展点，支持自定义任务类型、数据源、执行策略等
  - SPI机制自动发现与加载

- **PluginManager**
  - 插件管理器，负责插件的注册、生命周期管理、扩展点暴露

### 3.4 事件与通知体系

- **ArchiveEvent（基类）**
  - 归档相关事件（配置变更、任务状态变更、异常、告警等）

- **EventBus/EventPublisher**
  - 事件总线，支持事件的发布、订阅、异步处理

- **EventListener（接口）**
  - 事件监听器，业务方可实现监听归档相关事件

### 3.5 监控与统计体系

- **ArchiveMetrics**
  - 归档核心指标（任务数、成功/失败数、延迟、资源消耗等）

- **MetricsCollector**
  - 指标采集器，支持多种上报方式（日志、Prometheus、第三方APM等）

- **HealthChecker**
  - 健康检查，定期检测SDK各核心组件状态

### 3.6 对外API

- **ArchiveSdkClient**
  - SDK主入口，业务方通过该类进行SDK初始化、任务注册、配置获取、事件订阅等
  - 方法示例：
    - `init(SdkConfig config)`
    - `registerTask(TaskConfig config)`
    - `getTaskStatus(String taskId)`
    - `subscribeEvent(EventListener listener)`
    - `getMetrics()`

---

## 4. 典型类关系与调用流程

```mermaid
classDiagram
    class ArchiveSdkClient {
        +init(SdkConfig)
        +registerTask(TaskConfig)
        +getTaskStatus(String)
        +subscribeEvent(EventListener)
        +getMetrics()
    }
    class ArchiveConfigManager {
        +getConfig()
        +refresh()
        +addListener()
    }
    class TaskRegistry {
        +register(TaskConfig)
        +find(String)
    }
    class TaskScheduler {
        +schedule(ArchiveTask)
        +pause(String)
        +resume(String)
    }
    class TaskExecutor {
        +execute(ArchiveTask, TaskContext)
    }
    class PluginManager {
        +register(ArchivePlugin)
        +getPlugins()
    }
    class EventBus {
        +publish(ArchiveEvent)
        +subscribe(EventListener)
    }
    class MetricsCollector {
        +collect(ArchiveMetrics)
        +report()
    }
    ArchiveSdkClient --> ArchiveConfigManager
    ArchiveSdkClient --> TaskRegistry
    ArchiveSdkClient --> EventBus
    ArchiveSdkClient --> MetricsCollector
    TaskRegistry --> TaskScheduler
    TaskScheduler --> TaskExecutor
    ArchiveSdkClient --> PluginManager
    ArchiveConfigManager --> ConfigSourceAggregator
    ConfigSourceAggregator --> ConfigSource
    EventBus --> EventListener
    MetricsCollector --> ArchiveMetrics
```

---

## 5. 关键设计要点说明

### 5.1 统一入口与生命周期管理
- 所有SDK操作通过 ArchiveSdkClient 进行，便于生命周期管理和依赖注入。
- 支持多实例、隔离多租户。

### 5.2 配置与任务解耦
- 配置变更通过事件驱动影响任务调度与执行，避免直接耦合。
- 支持配置热刷新、回滚、灰度等高级特性。

### 5.3 插件化与扩展
- 任务类型、数据源、执行策略、监控等均可通过插件扩展。
- SPI机制自动发现，业务方可自定义实现。

### 5.4 事件驱动与监控
- 归档全流程关键节点均有事件抛出，便于业务方感知和自定义处理。
- 监控体系可灵活对接第三方APM、日志、报警等。

### 5.5 并发与健壮性
- 任务调度、执行、配置刷新等均为线程安全实现。
- 统一异常处理、日志、告警，提升可维护性。

### 5.6 可测试性
- 关键依赖均为接口，便于Mock和自动化测试。
- 支持集成测试、回放、模拟等。

---

## 6. 典型调用链举例

1. **SDK初始化**
   - 业务方调用 `ArchiveSdkClient.init()`，加载配置、注册插件、初始化任务调度与事件机制。

2. **任务注册与调度**
   - 业务方通过 `registerTask` 注册任务，任务注册表校验并存储，调度器自动调度。

3. **配置变更与热刷新**
   - 配置中心推送变更，ConfigSource 监听到后通过事件通知 ConfigManager，ConfigManager 刷新缓存并通过事件总线通知相关任务与业务方。

4. **任务执行与监控**
   - 调度器分发任务到执行器，执行器执行归档逻辑，执行结果通过事件与监控体系上报。

5. **事件订阅与自定义处理**
   - 业务方可通过 `subscribeEvent` 订阅归档相关事件，实现自定义告警、日志、二次处理等。

---

## 7. 扩展与演进建议

- 支持多租户、分布式部署、任务分片等高级特性。
- 任务流式处理、异步编排、失败重试、幂等保障。
- 归档数据回溯、审计、合规等能力。
- 监控与运维平台对接，支持可视化管理。

---

如需进一步细化某一模块的类图、接口定义、伪代码或落地实施建议，可随时补充。 

---

## 8. 主要核心类方法设计

### 8.1 ArchiveSdkClient

- `void init(SdkConfig config)`
  - 初始化SDK，加载配置、插件、调度器等
- `String registerTask(TaskConfig config)`
  - 注册任务，返回任务ID
- `TaskStatus getTaskStatus(String taskId)`
  - 获取任务当前状态
- `void subscribeEvent(EventListener listener)`
  - 订阅SDK事件
- `ArchiveMetrics getMetrics()`
  - 获取当前监控指标
- `void shutdown()`
  - 优雅关闭SDK

### 8.2 ArchiveConfigManager

- `ArchiveDataConfig getConfig()`
  - 获取全局归档配置
- `TaskConfig getTaskConfig(String taskId)`
  - 获取指定任务配置
- `void refresh()`
  - 主动刷新配置
- `void addListener(ConfigChangeListener listener)`
  - 注册配置变更监听器

### 8.3 ConfigSource（接口）

- `ArchiveDataConfig getConfig()`
  - 拉取全局配置
- `void addListener(ConfigChangeListener listener)`
  - 注册配置变更监听
- `void refresh()`
  - 主动刷新配置

### 8.4 ConfigSourceAggregator

- `ArchiveDataConfig getConfig()`
  - 聚合多来源配置，按优先级返回
- `void addSource(ConfigSource source)`
  - 添加配置来源
- `void refreshAll()`
  - 刷新所有来源配置

### 8.5 TaskRegistry

- `void register(TaskConfig config)`
  - 注册任务
- `ArchiveTask find(String taskId)`
  - 查找任务
- `List<ArchiveTask> listAll()`
  - 获取所有任务

### 8.6 TaskScheduler

- `void schedule(ArchiveTask task)`
  - 调度任务
- `void pause(String taskId)`
  - 暂停任务
- `void resume(String taskId)`
  - 恢复任务
- `void cancel(String taskId)`
  - 取消任务
- `void reschedule(String taskId, SchedulePolicy policy)`
  - 重新调度任务

### 8.7 TaskExecutor

- `TaskResult execute(ArchiveTask task, TaskContext context)`
  - 执行任务，返回结果
- `void submit(ArchiveTask task)`
  - 异步提交任务

### 8.8 TaskContext

- `TaskConfig getTaskConfig()`
  - 获取任务配置
- `Map<String, Object> getEnv()`
  - 获取执行环境变量
- `void setEnv(String key, Object value)`
  - 设置环境变量

### 8.9 ArchivePlugin（接口）

- `void onRegister(PluginManager manager)`
  - 插件注册时回调
- `List<String> getSupportedTypes()`
  - 支持的扩展类型

### 8.10 PluginManager

- `void register(ArchivePlugin plugin)`
  - 注册插件
- `List<ArchivePlugin> getPlugins()`
  - 获取所有插件
- `ArchivePlugin getPlugin(String type)`
  - 获取指定类型插件

### 8.11 ArchiveEvent（基类）

- `String getType()`
  - 事件类型
- `Object getPayload()`
  - 事件负载
- `long getTimestamp()`
  - 事件时间戳

### 8.12 EventBus/EventPublisher

- `void publish(ArchiveEvent event)`
  - 发布事件
- `void subscribe(EventListener listener)`
  - 订阅事件

### 8.13 EventListener（接口）

- `void onEvent(ArchiveEvent event)`
  - 事件回调

### 8.14 ArchiveMetrics

- `int getTaskCount()`
  - 当前任务数
- `int getSuccessCount()`
  - 成功任务数
- `int getFailureCount()`
  - 失败任务数
- `long getAvgLatency()`
  - 平均延迟
- `Map<String, Object> getCustomMetrics()`
  - 自定义指标

### 8.15 MetricsCollector

- `void collect(ArchiveMetrics metrics)`
  - 采集指标
- `void report()`
  - 上报指标

### 8.16 HealthChecker

- `HealthStatus check()`
  - 检查SDK健康状态

---

## 9. 关键调用流程示例

### 9.1 SDK初始化与任务注册

1. 业务方调用 `ArchiveSdkClient.init(config)`
2. SdkClient 加载配置（ConfigSourceAggregator）、注册插件（PluginManager）、初始化调度器（TaskScheduler）、事件总线（EventBus）等
3. 业务方通过 `registerTask(TaskConfig)` 注册任务，TaskRegistry 校验并存储，TaskScheduler 自动调度

### 9.2 配置变更与热刷新

1. 配置中心推送变更，ConfigSource 监听到后通过事件通知 ConfigManager
2. ConfigManager 刷新缓存，发布 ConfigChangedEvent 到 EventBus
3. 相关任务/调度器/业务方通过订阅事件感知变更，自动刷新或重调度

### 9.3 任务执行与监控

1. TaskScheduler 分发任务到 TaskExecutor
2. TaskExecutor 执行任务，生成 TaskResult
3. 执行结果通过 EventBus 发布 TaskStatusChangedEvent
4. MetricsCollector 采集并上报归档指标

### 9.4 事件订阅与自定义处理

1. 业务方实现 EventListener 并通过 `subscribeEvent` 注册
2. 事件总线收到事件后回调业务方监听器，实现自定义告警、日志、二次处理等

---

如需进一步细化某一类的伪代码、接口定义或实现建议，可随时补充。 