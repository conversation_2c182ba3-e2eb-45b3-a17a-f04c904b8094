# SCP-Archive 需求变更细化方案

## 概述

本文档基于当前SCP-Archive系统架构和需求变更要求，详细描述数据源管理优化、配置存储重构、配置变更通知机制等核心需求的细化方案。该方案结合SDK(scp-framework1)和管理端(scm-rulecenter)的现有架构，确保系统的高可用性和可维护性。

## 1. 数据源管理优化需求

### 1.1 现状分析

当前SCP-Archive系统通过`applicationContext.getBeansOfType(DataSource.class)`方式获取数据源，数据源管理相对简单但缺乏灵活性。

### 1.2 优化需求

#### 1.2.1 数据源自动发现
- **需求描述**：系统启动时自动发现Spring容器中的所有DataSource Bean
- **实现要求**：
  - 移除手动数据源维护功能，数据源完全由系统管理
  - 数据源名称自动匹配，确保系统内不会出现重复名称
  - 支持数据源的动态注册和注销
  - 数据源连接状态自动检测
  - **重要说明**：多数据源并非由SCP-Archive自己管理，而是从系统Spring容器中获取，避免重复管理

#### 1.2.2 数据源配置简化
- **需求描述**：简化数据源配置，减少维护成本
- **实现要求**：
  - 数据源配置统一管理
  - 支持数据源别名配置
  - 数据源连接池参数优化
  - 数据源健康检查机制

#### 1.2.3 多数据源支持增强
- **需求描述**：增强多数据源支持能力
- **实现要求**：
  - 支持节点级数据源覆盖
  - 数据源优先级机制
  - 跨数据源归档任务支持
  - 数据源切换容错机制

## 2. 配置存储架构重构需求

### 2.1 现状分析

当前配置存储在配置中心，需要迁移到远程服务，通过Dubbo接口提供服务。

### 2.2 重构需求

#### 2.2.1 配置存储迁移
- **需求描述**：将配置存储从配置中心迁移到scm-rulecenter服务
- **实现要求**：
  - 配置数据存储在scm-rulecenter数据库
  - 通过Dubbo接口提供配置CRUD服务
  - 支持配置版本管理
  - 配置数据备份和恢复机制

#### 2.2.2 任务ID全局唯一
- **需求描述**：为每个归档任务分配全局唯一的ID
- **实现要求**：
  - 任务ID生成规则设计
  - 任务ID唯一性校验
  - 任务ID与配置的关联关系
  - 任务ID的命名规范

#### 2.2.3 配置服务接口设计
- **需求描述**：设计完整的配置服务Dubbo接口
- **实现要求**：
  - 支持单个任务配置获取
  - 支持一次性加载所有配置
  - 配置查询和过滤功能
  - 配置变更历史记录

## 3. 配置变更通知机制需求

### 3.1 现状分析

Dubbo接口无法感知配置变更，需要设计配置变更通知机制。

### 3.2 通知机制需求

#### 3.2.1 Ark配置中心集成
- **需求描述**：利用Ark配置中心实现配置变更通知
- **实现要求**：
  - Ark配置中心存储任务ID和版本号映射，格式为`task_XXX=1000023`
  - 使用`configService.addListener()`机制实现通知
  - 支持配置变更事件的实时推送
  - 配置变更的可靠性保证
  - **重要说明**：版本号仅用于实现通知机制，因为Ark变更事件只有修改或新增才会触发，通过版本号变化来感知配置变更

#### 3.2.2 变更类型支持
- **需求描述**：支持配置的新增、修改、删除三种变更类型
- **实现要求**：
  - 新增任务：本地没有该任务ID时认为是新增
  - 修改任务：版本号变更时更新配置
  - 删除任务：任务ID从配置中心移除时删除本地配置
  - 变更内容的协议设计
  - **重要说明**：由于Ark配置中心只支持修改和新增事件，删除操作需要通过版本号机制实现，即删除时将版本号设为0或特殊值

#### 3.2.3 版本号管理
- **需求描述**：设计配置版本号管理机制
- **实现要求**：
  - 配置版本号生成规则（递增数字，如1000023）
  - 版本号变更检测机制
  - 版本号冲突处理
  - 版本回滚支持
  - **重要说明**：版本号主要用于触发Ark配置变更事件，当版本号发生变化时，SDK需要重新拉取最新配置
  - **格式规范**：Ark配置中心存储格式为`task_任务ID=版本号`，例如`task_order_archive=1000023`

## 4. 配置同步策略需求

### 4.1 执行前配置拷贝

#### 4.1.1 配置拷贝机制
- **需求描述**：任务执行前拷贝配置信息，避免实时修改影响执行
- **实现要求**：
  - 只拷贝节点信息，任务本身配置实时生效
  - 配置拷贝的原子性保证
  - 拷贝配置的版本控制
  - 拷贝失败的处理机制

#### 4.1.2 实时生效配置
- **需求描述**：部分配置需要实时生效
- **实现要求**：
  - 执行频率配置实时生效
  - 任务状态配置实时生效
  - 全局开关配置实时生效
  - 配置生效的优先级管理

### 4.2 配置更新策略

#### 4.2.1 增量更新
- **需求描述**：支持配置的增量更新
- **实现要求**：
  - 只更新变更的配置项
  - 配置更新的原子性
  - 更新失败的回滚机制
  - 更新性能优化

#### 4.2.2 全量更新
- **需求描述**：支持配置的全量更新
- **实现要求**：
  - 全量配置的完整性校验
  - 全量更新的性能优化
  - 更新过程中的服务可用性保证
  - 更新结果的验证机制

## 5. 系统集成需求

### 5.1 SDK端集成

#### 5.1.1 配置获取服务
- **需求描述**：SDK端集成配置获取服务
- **实现要求**：
  - 通过Dubbo接口获取配置
  - 配置缓存机制
  - 配置获取的重试机制
  - 配置获取的超时处理

#### 5.1.2 配置监听服务
- **需求描述**：SDK端集成配置变更监听
- **实现要求**：
  - Ark配置中心监听机制
  - 配置变更的实时处理
  - 监听服务的容错机制
  - 监听状态的监控
  - **重要说明**：监听Ark配置中心中`task_XXX=版本号`格式的配置项，当版本号发生变化时触发配置拉取
  - **容错处理**：监听失败时需要有重试机制，避免配置更新不及时

### 5.2 管理端集成

#### 5.2.1 配置管理服务
- **需求描述**：管理端集成配置管理服务
- **实现要求**：
  - 配置的CRUD操作
  - 配置验证机制
  - 配置发布服务
  - 配置管理权限控制

#### 5.2.2 配置同步服务
- **需求描述**：管理端集成配置同步服务
- **实现要求**：
  - 配置到Ark配置中心的同步
  - 配置版本号管理
  - 同步状态的监控
  - 同步失败的处理
  - **重要说明**：管理端在配置变更时需要同时更新Ark配置中心中的版本号，格式为`task_任务ID=新版本号`
  - **版本号递增**：每次配置变更时版本号必须递增，确保能触发Ark配置变更事件

## 6. 性能优化需求

### 6.1 配置获取性能

#### 6.1.1 缓存优化
- **需求描述**：优化配置获取的性能
- **实现要求**：
  - 多级缓存机制
  - 缓存更新策略
  - 缓存命中率监控
  - 缓存容量管理

#### 6.1.2 批量操作
- **需求描述**：支持配置的批量操作
- **实现要求**：
  - 批量配置获取
  - 批量配置更新
  - 批量操作的事务管理
  - 批量操作的性能优化


## 12. 关键场景说明

### 12.1 数据源管理场景
- **场景描述**：多数据源并非由SCP-Archive自己管理，而是从系统Spring容器中获取
- **实现要点**：
  - 使用`applicationContext.getBeansOfType(DataSource.class)`获取所有数据源
  - 数据源名称由系统统一管理，避免重复
  - SCP-Archive只负责使用数据源，不负责创建和维护

### 12.2 版本号通知机制场景
- **场景描述**：版本号仅用于实现通知机制，因为Ark变更事件只有修改或新增才会触发
- **实现要点**：
  - Ark配置中心存储格式：`task_任务ID=版本号`（如`task_order_archive=1000023`）
  - 版本号变化触发Ark配置变更事件
  - SDK监听到事件后重新拉取最新配置
  - 删除操作通过特殊版本号（如0）实现

### 12.3 配置变更处理场景
- **场景描述**：配置变更需要同时更新数据库和Ark配置中心
- **实现要点**：
  - 管理端配置变更时，先更新数据库
  - 然后更新Ark配置中心中的版本号
  - 版本号必须递增，确保触发变更事件
  - SDK监听到变更后从数据库拉取最新配置
