# SCP-Archive 系统重构设计文档

## 1. 重构概述

### 1.1 重构目标
基于现有scp-archive系统的分析，本重构方案旨在解决以下核心问题：
- **配置管理优化**：增强配置获取失败的容错处理和恢复机制
- **任务状态管理**：统一任务生命周期管理，支持暂停、恢复、失败处理
- **事件驱动架构**：引入完整的事件总线，提升系统解耦度和可扩展性
- **容错与恢复**：设计全面的故障处理和自动恢复机制
- **可观测性增强**：提供完整的监控、日志和告警能力

### 1.2 设计原则
- **安全优先**：配置异常时宁可暂停归档，避免数据风险
- **自动恢复**：系统具备自动故障检测和恢复能力
- **事件驱动**：通过事件解耦模块间依赖，提升可维护性
- **可观测性**：全链路监控，便于问题定位和运维管理
- **向后兼容**：保持现有API的兼容性，平滑迁移

## 2. 整体架构设计

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────┐
│                    API Layer                        │
│  ArchiveSdkClient, EventListener, MetricsAPI       │
├─────────────────────────────────────────────────────┤
│                Application Layer                    │
│  ConfigManager, TaskManager, EventBus, Monitor     │
├─────────────────────────────────────────────────────┤
│                  Domain Layer                       │
│  TaskScheduler, TaskExecutor, FailureHandler       │
├─────────────────────────────────────────────────────┤
│              Infrastructure Layer                   │
│  ConfigSource, DistributedLock, MetricsCollector   │
└─────────────────────────────────────────────────────┘
```

### 2.2 核心组件关系

```mermaid
graph TB
    A[ArchiveSdkClient] --> B[ConfigManager]
    A --> C[TaskManager]
    A --> D[EventBus]
    
    B --> E[ConfigSourceAggregator]
    E --> F[NacosConfigSource]
    E --> G[DubboConfigSource]
    E --> H[FileConfigSource]
    
    C --> I[TaskScheduler]
    C --> J[TaskExecutor]
    C --> K[TaskStateManager]
    
    D --> L[EventPublisher]
    D --> M[EventSubscriber]
    
    N[FailureHandler] --> O[RetryManager]
    N --> P[CircuitBreaker]
    N --> Q[FallbackManager]
```

## 3. 配置管理重构设计

### 3.1 配置管理架构

#### 3.1.1 核心组件

**ConfigManager（配置管理器）**
- 统一配置管理入口
- 配置缓存和版本管理
- 配置变更事件发布
- 配置一致性保证

**ConfigSourceAggregator（配置源聚合器）**
- 多配置源聚合和优先级管理
- 配置源故障切换
- 配置合并和冲突解决

**ConfigSource（配置源接口）**
- 统一配置获取接口
- 支持Nacos、Dubbo、File等多种实现
- 配置变更监听能力

#### 3.1.2 配置获取流程

```mermaid
sequenceDiagram
    participant CM as ConfigManager
    participant CSA as ConfigSourceAggregator
    participant NS as NacosConfigSource
    participant DS as DubboConfigSource
    participant EB as EventBus
    participant TM as TaskManager

    CM->>CSA: getConfig()
    CSA->>NS: getConfig()
    alt Nacos成功
        NS-->>CSA: config
        CSA-->>CM: config
    else Nacos失败
        CSA->>DS: getConfig()
        alt Dubbo成功
            DS-->>CSA: config
            CSA-->>CM: config
        else Dubbo失败
            CSA-->>CM: fallback config
        end
    end
    
    CM->>EB: publish ConfigChangedEvent
    EB->>TM: notify config change
```

### 3.2 配置失败处理机制

#### 3.2.1 重试策略

**RetryManager（重试管理器）**
```java
public class RetryManager {
    private final RetryPolicy retryPolicy;
    private final CircuitBreaker circuitBreaker;
    
    public <T> T executeWithRetry(String operation, Supplier<T> supplier) {
        return retryPolicy.execute(() -> {
            if (circuitBreaker.allowRequest()) {
                try {
                    T result = supplier.get();
                    circuitBreaker.recordSuccess();
                    return result;
                } catch (Exception e) {
                    circuitBreaker.recordFailure();
                    throw e;
                }
            } else {
                throw new CircuitBreakerOpenException();
            }
        });
    }
}
```

**重试配置**
- 最大重试次数：3次（可配置）
- 重试间隔：指数退避（1s, 2s, 4s）
- 重试条件：网络异常、超时异常、服务不可用
- 熔断机制：连续失败5次后熔断30秒

#### 3.2.2 降级策略

**FallbackManager（降级管理器）**
```java
public class FallbackManager {
    private final ConfigCache localCache;
    private final DefaultConfigProvider defaultProvider;
    
    public ArchiveConfig getFallbackConfig(String configKey) {
        // 1. 尝试本地缓存
        ArchiveConfig cached = localCache.get(configKey);
        if (cached != null && !isExpired(cached)) {
            return cached;
        }
        
        // 2. 使用默认配置
        return defaultProvider.getDefaultConfig(configKey);
    }
}
```

### 3.3 任务暂停与恢复机制

#### 3.3.1 任务状态管理

**TaskStateManager（任务状态管理器）**
```java
public class TaskStateManager {
    private final Map<String, TaskState> taskStates = new ConcurrentHashMap<>();
    private final EventBus eventBus;
    
    public void pauseTask(String taskId, PauseReason reason) {
        TaskState state = taskStates.get(taskId);
        if (state != null && state.canPause()) {
            state.pause(reason);
            eventBus.publish(new TaskPausedEvent(taskId, reason));
        }
    }
    
    public void resumeTask(String taskId) {
        TaskState state = taskStates.get(taskId);
        if (state != null && state.canResume()) {
            state.resume();
            eventBus.publish(new TaskResumedEvent(taskId));
        }
    }
}
```

#### 3.3.2 配置失败处理流程

```mermaid
stateDiagram-v2
    [*] --> Running
    Running --> ConfigFailed: 配置获取失败
    ConfigFailed --> Retrying: 开始重试
    Retrying --> Running: 重试成功
    Retrying --> Paused: 重试失败
    Paused --> Retrying: 配置恢复
    Paused --> [*]: 手动停止
```

## 4. 事件驱动架构设计

### 4.1 事件总线设计

#### 4.1.1 核心组件

**EventBus（事件总线）**
```java
public class EventBus {
    private final EventPublisher publisher;
    private final EventSubscriberRegistry registry;
    private final ExecutorService asyncExecutor;
    
    public void publish(ArchiveEvent event) {
        publisher.publish(event);
    }
    
    public void subscribe(EventListener listener) {
        registry.register(listener);
    }
}
```

#### 4.1.2 事件类型定义

**配置相关事件**
- `ConfigChangedEvent`：配置变更事件
- `ConfigLoadFailedEvent`：配置加载失败事件
- `ConfigRecoveredEvent`：配置恢复事件

**任务相关事件**
- `TaskStartedEvent`：任务开始事件
- `TaskCompletedEvent`：任务完成事件
- `TaskFailedEvent`：任务失败事件
- `TaskPausedEvent`：任务暂停事件
- `TaskResumedEvent`：任务恢复事件

**系统相关事件**
- `SystemStartedEvent`：系统启动事件
- `SystemShutdownEvent`：系统关闭事件
- `HealthCheckEvent`：健康检查事件

### 4.2 事件处理机制

#### 4.2.1 事件监听器

```java
@Component
public class ConfigEventListener implements EventListener {
    
    @EventHandler
    public void handleConfigChanged(ConfigChangedEvent event) {
        // 处理配置变更
        taskManager.refreshTaskConfig(event.getConfigKey());
    }
    
    @EventHandler
    public void handleConfigFailed(ConfigLoadFailedEvent event) {
        // 处理配置加载失败
        taskManager.pauseRelatedTasks(event.getConfigKey());
        alertManager.sendAlert(event);
    }
}
```

## 5. 容错与恢复机制设计

### 5.1 故障检测

#### 5.1.1 健康检查

**HealthChecker（健康检查器）**
```java
public class HealthChecker {
    private final List<HealthIndicator> indicators;
    private final ScheduledExecutorService scheduler;
    
    @PostConstruct
    public void startHealthCheck() {
        scheduler.scheduleAtFixedRate(this::performHealthCheck, 0, 30, TimeUnit.SECONDS);
    }
    
    private void performHealthCheck() {
        HealthStatus status = indicators.stream()
            .map(HealthIndicator::check)
            .reduce(HealthStatus.UP, HealthStatus::combine);
            
        eventBus.publish(new HealthCheckEvent(status));
    }
}
```

### 5.2 自动恢复

#### 5.2.1 恢复策略

**RecoveryManager（恢复管理器）**
```java
public class RecoveryManager {
    
    @EventHandler
    public void handleConfigRecovered(ConfigRecoveredEvent event) {
        // 配置恢复后，自动恢复相关任务
        List<String> pausedTasks = taskStateManager.getPausedTasksByReason(
            PauseReason.CONFIG_FAILURE);
        
        for (String taskId : pausedTasks) {
            taskStateManager.resumeTask(taskId);
        }
    }
}
```
