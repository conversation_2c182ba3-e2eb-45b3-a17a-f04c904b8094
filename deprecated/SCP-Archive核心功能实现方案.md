# SCP-Archive 核心功能实现方案

## 概述

本文档描述SCP-Archive系统的核心功能实现方案，包括管理端配置维护、SDK配置获取和执行链路三个核心模块。所有实现均使用伪代码表述，专注于核心业务逻辑。

## 1. 数据模型定义

### 1.1 归档配置模型

**文件位置**：`scp-framework1/scp-archive/scp-archive-common/src/main/java/com/shizhuang/duapp/scp/framework/archive/model/ArchiveConfig.java`

```java
// 归档配置模型
public class ArchiveConfig {
    private String configId;           // 配置ID
    private String configName;         // 配置名称
    private String tenantId;           // 租户ID
    private String datasourceName;     // 数据源名称
    private String databaseName;       // 数据库名称
    private List<ArchiveNode> nodes;   // 归档节点列表
    private ArchiveSchedule schedule;  // 执行计划
    private ArchiveSettings settings;  // 归档设置
    private String status;             // 配置状态：DRAFT/PUBLISHED/DISABLED
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
    private String createBy;           // 创建人
    private String updateBy;           // 更新人
}

// 归档节点模型
public class ArchiveNode {
    private String nodeId;             // 节点ID
    private String tableName;          // 表名
    private String condition;          // 归档条件
    private String datasourceName;     // 节点级数据源（可选）
    private Boolean shardingEnabled;   // 是否启用分片
    private String shardingExpression; // 分片表达式
    private List<ArchiveNode> children; // 子节点
}

// 归档计划模型
public class ArchiveSchedule {
    private String cronExpression;     // Cron表达式
    private String timeZone;           // 时区
    private Boolean enabled;           // 是否启用
}

// 归档设置模型
public class ArchiveSettings {
    private Integer batchSize;         // 批处理大小
    private Integer maxRetries;        // 最大重试次数
    private Long timeoutSeconds;       // 超时时间
    private Boolean previewMode;       // 预览模式
}
```

### 1.2 归档任务模型

**文件位置**：`scp-framework1/scp-archive/scp-archive-common/src/main/java/com/shizhuang/duapp/scp/framework/archive/model/ArchiveTask.java`

```java
// 归档任务模型
public class ArchiveTask {
    private String taskId;             // 任务ID
    private String configId;           // 配置ID
    private String status;             // 任务状态：PENDING/RUNNING/SUCCESS/FAILED
    private Date startTime;            // 开始时间
    private Date endTime;              // 结束时间
    private Integer processedCount;    // 处理记录数
    private Integer deletedCount;      // 删除记录数
    private String errorMessage;       // 错误信息
    private Map<String, Object> context; // 执行上下文
}

// 归档结果模型
public class ArchiveResult {
    private String taskId;             // 任务ID
    private Boolean success;           // 是否成功
    private String message;            // 结果消息
    private Integer processedCount;    // 处理记录数
    private Integer deletedCount;      // 删除记录数
    private Long duration;             // 执行时长
    private List<String> errors;       // 错误列表
}
```

## 2. Admin端配置维护功能

### 2.1 配置管理接口

**文件位置**：`scm-rulecenter/scm-rulecenter-interfaces/src/main/java/com/shizhuang/scm/rulecenter/interfaces/controller/ArchiveConfigController.java`

```java
// 配置管理控制器
@RestController
@RequestMapping("/api/archive/config")
public class ArchiveConfigController {
    
    @Autowired
    private ArchiveConfigService archiveConfigService;
    
    // 创建配置
    @PostMapping
    public Result<ArchiveConfig> createConfig(@RequestBody ArchiveConfig config) {
        // 验证配置参数
        ValidationResult validation = archiveConfigService.validateConfig(config);
        if (!validation.isValid()) {
            return Result.error(validation.getErrors());
        }
        
        // 保存配置
        ArchiveConfig savedConfig = archiveConfigService.createConfig(config);
        
        // 发布到配置中心
        archiveConfigService.publishConfig(savedConfig.getConfigId());
        
        return Result.success(savedConfig);
    }
    
    // 更新配置
    @PutMapping("/{configId}")
    public Result<ArchiveConfig> updateConfig(@PathVariable String configId, 
                                            @RequestBody ArchiveConfig config) {
        // 验证配置参数
        ValidationResult validation = archiveConfigService.validateConfig(config);
        if (!validation.isValid()) {
            return Result.error(validation.getErrors());
        }
        
        // 更新配置
        ArchiveConfig updatedConfig = archiveConfigService.updateConfig(configId, config);
        
        // 发布到配置中心
        archiveConfigService.publishConfig(configId);
        
        return Result.success(updatedConfig);
    }
    
    // 删除配置
    @DeleteMapping("/{configId}")
    public Result<Void> deleteConfig(@PathVariable String configId) {
        // 删除配置
        archiveConfigService.deleteConfig(configId);
        
        // 从配置中心移除
        archiveConfigService.unpublishConfig(configId);
        
        return Result.success();
    }
    
    // 查询配置列表
    @GetMapping
    public Result<PageResult<ArchiveConfig>> listConfigs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String configName,
            @RequestParam(required = false) String status) {
        
        PageResult<ArchiveConfig> result = archiveConfigService.listConfigs(page, size, configName, status);
        return Result.success(result);
    }
    
    // 获取配置详情
    @GetMapping("/{configId}")
    public Result<ArchiveConfig> getConfig(@PathVariable String configId) {
        ArchiveConfig config = archiveConfigService.getConfig(configId);
        return Result.success(config);
    }
    
    // 发布配置
    @PostMapping("/{configId}/publish")
    public Result<Void> publishConfig(@PathVariable String configId) {
        archiveConfigService.publishConfig(configId);
        return Result.success();
    }
    
    // 禁用配置
    @PostMapping("/{configId}/disable")
    public Result<Void> disableConfig(@PathVariable String configId) {
        archiveConfigService.disableConfig(configId);
        return Result.success();
    }
}
```

### 2.2 配置业务逻辑

**文件位置**：`scm-rulecenter/scm-rulecenter-application/src/main/java/com/shizhuang/scm/rulecenter/application/service/ArchiveConfigService.java`

```java
// 配置业务逻辑服务
@Service
public class ArchiveConfigService {
    
    @Autowired
    private ArchiveConfigRepository archiveConfigRepository;
    
    @Autowired
    private ConfigCenterService configCenterService;
    
    @Autowired
    private DatasourceValidator datasourceValidator;
    
    // 创建配置
    public ArchiveConfig createConfig(ArchiveConfig config) {
        // 设置基础信息
        config.setConfigId(generateConfigId());
        config.setStatus("DRAFT");
        config.setCreateTime(new Date());
        config.setUpdateTime(new Date());
        
        // 保存配置
        ArchiveConfig savedConfig = archiveConfigRepository.save(config);
        
        return savedConfig;
    }
    
    // 更新配置
    public ArchiveConfig updateConfig(String configId, ArchiveConfig config) {
        // 检查配置是否存在
        ArchiveConfig existingConfig = archiveConfigRepository.findById(configId);
        if (existingConfig == null) {
            throw new BusinessException("配置不存在: " + configId);
        }
        
        // 更新配置信息
        config.setConfigId(configId);
        config.setUpdateTime(new Date());
        
        // 保存配置
        ArchiveConfig updatedConfig = archiveConfigRepository.save(config);
        
        return updatedConfig;
    }
    
    // 删除配置
    public void deleteConfig(String configId) {
        // 检查配置是否存在
        ArchiveConfig config = archiveConfigRepository.findById(configId);
        if (config == null) {
            throw new BusinessException("配置不存在: " + configId);
        }
        
        // 删除配置
        archiveConfigRepository.deleteById(configId);
    }
    
    // 查询配置列表
    public PageResult<ArchiveConfig> listConfigs(Integer page, Integer size, String configName, String status) {
        // 构建查询条件
        ArchiveConfigQuery query = new ArchiveConfigQuery();
        query.setPage(page);
        query.setSize(size);
        query.setConfigName(configName);
        query.setStatus(status);
        
        // 执行查询
        PageResult<ArchiveConfig> result = archiveConfigRepository.queryByPage(query);
        
        return result;
    }
    
    // 获取配置详情
    public ArchiveConfig getConfig(String configId) {
        ArchiveConfig config = archiveConfigRepository.findById(configId);
        if (config == null) {
            throw new BusinessException("配置不存在: " + configId);
        }
        
        return config;
    }
    
    // 验证配置
    public ValidationResult validateConfig(ArchiveConfig config) {
        ValidationResult result = new ValidationResult();
        
        // 验证基础信息
        if (StringUtils.isEmpty(config.getConfigName())) {
            result.addError("配置名称不能为空");
        }
        
        if (StringUtils.isEmpty(config.getDatasourceName())) {
            result.addError("数据源名称不能为空");
        }
        
        if (StringUtils.isEmpty(config.getDatabaseName())) {
            result.addError("数据库名称不能为空");
        }
        
        // 验证数据源
        if (StringUtils.isNotEmpty(config.getDatasourceName())) {
            try {
                datasourceValidator.validateDatasource(config.getDatasourceName(), config.getDatabaseName());
            } catch (Exception e) {
                result.addError("数据源验证失败: " + e.getMessage());
            }
        }
        
        // 验证归档节点
        if (config.getNodes() != null) {
            validateArchiveNodes(config.getNodes(), result);
        }
        
        return result;
    }
    
    // 验证归档节点
    private void validateArchiveNodes(List<ArchiveNode> nodes, ValidationResult result) {
        for (ArchiveNode node : nodes) {
            if (StringUtils.isEmpty(node.getTableName())) {
                result.addError("表名不能为空");
            }
            
            if (StringUtils.isEmpty(node.getCondition())) {
                result.addError("归档条件不能为空");
            }
            
            // 递归验证子节点
            if (node.getChildren() != null) {
                validateArchiveNodes(node.getChildren(), result);
            }
        }
    }
    
    // 发布配置到配置中心
    public void publishConfig(String configId) {
        // 获取配置
        ArchiveConfig config = getConfig(configId);
        
        // 更新状态为已发布
        config.setStatus("PUBLISHED");
        archiveConfigRepository.save(config);
        
        // 发布到配置中心
        configCenterService.publishConfig(configId, config);
    }
    
    // 从配置中心移除配置
    public void unpublishConfig(String configId) {
        // 更新状态为已禁用
        ArchiveConfig config = getConfig(configId);
        config.setStatus("DISABLED");
        archiveConfigRepository.save(config);
        
        // 从配置中心移除
        configCenterService.unpublishConfig(configId);
    }
    
    // 禁用配置
    public void disableConfig(String configId) {
        // 更新状态为已禁用
        ArchiveConfig config = getConfig(configId);
        config.setStatus("DISABLED");
        archiveConfigRepository.save(config);
        
        // 从配置中心移除
        configCenterService.unpublishConfig(configId);
    }
    
    // 生成配置ID
    private String generateConfigId() {
        return "ARCHIVE_" + System.currentTimeMillis() + "_" + RandomStringUtils.randomAlphanumeric(6);
    }
}
```

### 2.3 配置数据访问

**文件位置**：`scm-rulecenter/scm-rulecenter-infrastructure/src/main/java/com/shizhuang/scm/rulecenter/infrastructure/repository/ArchiveConfigRepository.java`

```java
// 配置数据访问层
@Repository
public class ArchiveConfigRepository {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    // 保存配置
    public ArchiveConfig save(ArchiveConfig config) {
        if (StringUtils.isEmpty(config.getConfigId())) {
            // 插入新配置
            String sql = "INSERT INTO archive_config (config_id, config_name, tenant_id, datasource_name, " +
                        "database_name, nodes_json, schedule_json, settings_json, status, create_time, " +
                        "update_time, create_by, update_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            jdbcTemplate.update(sql, 
                config.getConfigId(), config.getConfigName(), config.getTenantId(),
                config.getDatasourceName(), config.getDatabaseName(), 
                JsonUtils.toJson(config.getNodes()), JsonUtils.toJson(config.getSchedule()),
                JsonUtils.toJson(config.getSettings()), config.getStatus(),
                config.getCreateTime(), config.getUpdateTime(),
                config.getCreateBy(), config.getUpdateBy());
        } else {
            // 更新配置
            String sql = "UPDATE archive_config SET config_name = ?, datasource_name = ?, " +
                        "database_name = ?, nodes_json = ?, schedule_json = ?, settings_json = ?, " +
                        "status = ?, update_time = ?, update_by = ? WHERE config_id = ?";
            
            jdbcTemplate.update(sql,
                config.getConfigName(), config.getDatasourceName(), config.getDatabaseName(),
                JsonUtils.toJson(config.getNodes()), JsonUtils.toJson(config.getSchedule()),
                JsonUtils.toJson(config.getSettings()), config.getStatus(),
                config.getUpdateTime(), config.getUpdateBy(), config.getConfigId());
        }
        
        return config;
    }
    
    // 根据ID查询配置
    public ArchiveConfig findById(String configId) {
        String sql = "SELECT * FROM archive_config WHERE config_id = ?";
        
        try {
            return jdbcTemplate.queryForObject(sql, new Object[]{configId}, new ArchiveConfigRowMapper());
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }
    
    // 分页查询配置
    public PageResult<ArchiveConfig> queryByPage(ArchiveConfigQuery query) {
        // 构建查询SQL
        StringBuilder sql = new StringBuilder("SELECT * FROM archive_config WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        if (StringUtils.isNotEmpty(query.getConfigName())) {
            sql.append(" AND config_name LIKE ?");
            params.add("%" + query.getConfigName() + "%");
        }
        
        if (StringUtils.isNotEmpty(query.getStatus())) {
            sql.append(" AND status = ?");
            params.add(query.getStatus());
        }
        
        sql.append(" ORDER BY create_time DESC");
        
        // 执行分页查询
        int offset = (query.getPage() - 1) * query.getSize();
        sql.append(" LIMIT ? OFFSET ?");
        params.add(query.getSize());
        params.add(offset);
        
        List<ArchiveConfig> configs = jdbcTemplate.query(sql.toString(), params.toArray(), new ArchiveConfigRowMapper());
        
        // 查询总数
        String countSql = "SELECT COUNT(*) FROM archive_config WHERE 1=1";
        if (StringUtils.isNotEmpty(query.getConfigName())) {
            countSql += " AND config_name LIKE '%" + query.getConfigName() + "%'";
        }
        if (StringUtils.isNotEmpty(query.getStatus())) {
            countSql += " AND status = '" + query.getStatus() + "'";
        }
        
        int total = jdbcTemplate.queryForObject(countSql, Integer.class);
        
        return new PageResult<>(configs, total, query.getPage(), query.getSize());
    }
    
    // 删除配置
    public void deleteById(String configId) {
        String sql = "DELETE FROM archive_config WHERE config_id = ?";
        jdbcTemplate.update(sql, configId);
    }
}

// 配置查询条件
public class ArchiveConfigQuery {
    private Integer page = 1;
    private Integer size = 10;
    private String configName;
    private String status;
    // getter/setter方法
}

// 配置行映射器
public class ArchiveConfigRowMapper implements RowMapper<ArchiveConfig> {
    @Override
    public ArchiveConfig mapRow(ResultSet rs, int rowNum) throws SQLException {
        ArchiveConfig config = new ArchiveConfig();
        config.setConfigId(rs.getString("config_id"));
        config.setConfigName(rs.getString("config_name"));
        config.setTenantId(rs.getString("tenant_id"));
        config.setDatasourceName(rs.getString("datasource_name"));
        config.setDatabaseName(rs.getString("database_name"));
        config.setNodes(JsonUtils.fromJson(rs.getString("nodes_json"), List.class));
        config.setSchedule(JsonUtils.fromJson(rs.getString("schedule_json"), ArchiveSchedule.class));
        config.setSettings(JsonUtils.fromJson(rs.getString("settings_json"), ArchiveSettings.class));
        config.setStatus(rs.getString("status"));
        config.setCreateTime(rs.getTimestamp("create_time"));
        config.setUpdateTime(rs.getTimestamp("update_time"));
        config.setCreateBy(rs.getString("create_by"));
        config.setUpdateBy(rs.getString("update_by"));
        return config;
    }
}
```

## 3. SDK端配置获取功能

### 3.1 配置获取客户端

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ArchiveConfigClient.java`

```java
// 配置获取客户端
@Component
public class ArchiveConfigClient {
    
    @Autowired
    private ConfigCenterService configCenterService;
    
    @Autowired
    private ArchiveConfigCache configCache;
    
    @Autowired
    private ArchiveConfigListener configListener;
    
    // 获取配置
    public ArchiveConfig getConfig(String configId) {
        // 先从缓存获取
        ArchiveConfig config = configCache.getConfig(configId);
        if (config != null) {
            return config;
        }
        
        // 从配置中心获取
        config = configCenterService.getConfig(configId);
        if (config != null) {
            // 缓存配置
            configCache.putConfig(configId, config);
            
            // 注册配置监听
            configListener.registerListener(configId);
        }
        
        return config;
    }
    
    // 获取所有已发布配置
    public List<ArchiveConfig> getAllPublishedConfigs() {
        // 从配置中心获取所有已发布配置
        List<ArchiveConfig> configs = configCenterService.getAllPublishedConfigs();
        
        // 缓存配置
        for (ArchiveConfig config : configs) {
            configCache.putConfig(config.getConfigId(), config);
            configListener.registerListener(config.getConfigId());
        }
        
        return configs;
    }
    
    // 刷新配置
    public void refreshConfig(String configId) {
        // 从配置中心重新获取配置
        ArchiveConfig config = configCenterService.getConfig(configId);
        if (config != null) {
            // 更新缓存
            configCache.putConfig(configId, config);
        } else {
            // 移除缓存
            configCache.removeConfig(configId);
        }
    }
    
    // 初始化客户端
    @PostConstruct
    public void init() {
        // 获取所有已发布配置并缓存
        getAllPublishedConfigs();
    }
}
```

### 3.2 配置缓存管理

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ArchiveConfigCache.java`

```java
// 配置缓存管理
@Component
public class ArchiveConfigCache {
    
    private final Map<String, ArchiveConfig> configCache = new ConcurrentHashMap<>();
    private final Map<String, Long> configTimestamps = new ConcurrentHashMap<>();
    private final long cacheExpireTime = 30 * 60 * 1000; // 30分钟
    
    // 获取配置
    public ArchiveConfig getConfig(String configId) {
        ArchiveConfig config = configCache.get(configId);
        if (config != null) {
            // 检查缓存是否过期
            Long timestamp = configTimestamps.get(configId);
            if (timestamp != null && System.currentTimeMillis() - timestamp < cacheExpireTime) {
                return config;
            } else {
                // 缓存过期，移除
                removeConfig(configId);
            }
        }
        
        return null;
    }
    
    // 缓存配置
    public void putConfig(String configId, ArchiveConfig config) {
        configCache.put(configId, config);
        configTimestamps.put(configId, System.currentTimeMillis());
    }
    
    // 移除配置
    public void removeConfig(String configId) {
        configCache.remove(configId);
        configTimestamps.remove(configId);
    }
    
    // 清空缓存
    public void clearCache() {
        configCache.clear();
        configTimestamps.clear();
    }
    
    // 获取所有缓存的配置
    public List<ArchiveConfig> getAllCachedConfigs() {
        return new ArrayList<>(configCache.values());
    }
    
    // 检查配置是否存在
    public boolean containsConfig(String configId) {
        return configCache.containsKey(configId);
    }
}
```

### 3.3 配置变更监听器

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ArchiveConfigListener.java`

```java
// 配置变更监听器
@Component
public class ArchiveConfigListener {
    
    @Autowired
    private ArchiveConfigCache configCache;
    
    @Autowired
    private ConfigCenterService configCenterService;
    
    private final Set<String> registeredConfigs = new ConcurrentHashSet<>();
    
    // 注册配置监听
    public void registerListener(String configId) {
        if (registeredConfigs.add(configId)) {
            // 注册到配置中心监听
            configCenterService.registerListener(configId, this::onConfigChanged);
        }
    }
    
    // 取消配置监听
    public void unregisterListener(String configId) {
        if (registeredConfigs.remove(configId)) {
            // 从配置中心取消监听
            configCenterService.unregisterListener(configId);
        }
    }
    
    // 配置变更回调
    public void onConfigChanged(String configId, ConfigChangeEvent event) {
        switch (event.getType()) {
            case "UPDATE":
                // 配置更新，刷新缓存
                refreshConfig(configId);
                break;
            case "DELETE":
                // 配置删除，移除缓存
                configCache.removeConfig(configId);
                unregisterListener(configId);
                break;
            case "DISABLE":
                // 配置禁用，移除缓存
                configCache.removeConfig(configId);
                unregisterListener(configId);
                break;
        }
    }
    
    // 刷新配置
    private void refreshConfig(String configId) {
        try {
            // 从配置中心重新获取配置
            ArchiveConfig config = configCenterService.getConfig(configId);
            if (config != null) {
                // 更新缓存
                configCache.putConfig(configId, config);
            } else {
                // 移除缓存
                configCache.removeConfig(configId);
            }
        } catch (Exception e) {
            // 记录错误日志
            log.error("刷新配置失败: " + configId, e);
        }
    }
}

// 配置变更事件
public class ConfigChangeEvent {
    private String configId;
    private String type; // UPDATE/DELETE/DISABLE
    private Date timestamp;
    private String operator;
    
    // getter/setter方法
}
```

## 4. 执行链路实现

### 4.1 归档执行器

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/executor/ArchiveExecutor.java`

```java
// 归档执行器
@Component
public class ArchiveExecutor {
    
    @Autowired
    private ArchiveConfigClient configClient;
    
    @Autowired
    private ArchiveValidator validator;
    
    @Autowired
    private QueryExecutor queryExecutor;
    
    @Autowired
    private DeleteExecutor deleteExecutor;
    
    @Autowired
    private ArchiveTaskRepository taskRepository;
    
    // 执行归档任务
    public ArchiveResult executeArchive(String configId) {
        ArchiveResult result = new ArchiveResult();
        result.setTaskId(generateTaskId());
        result.setStartTime(new Date());
        
        try {
            // 1. 获取配置
            ArchiveConfig config = configClient.getConfig(configId);
            if (config == null) {
                throw new ArchiveException("配置不存在: " + configId);
            }
            
            // 2. 验证配置
            ValidationResult validation = validator.validateConfig(config);
            if (!validation.isValid()) {
                throw new ArchiveException("配置验证失败: " + validation.getErrors());
            }
            
            // 3. 创建任务记录
            ArchiveTask task = createTask(configId, result.getTaskId());
            
            // 4. 执行归档
            executeArchiveNodes(config.getNodes(), config, task, result);
            
            // 5. 更新任务状态
            task.setStatus("SUCCESS");
            task.setEndTime(new Date());
            task.setProcessedCount(result.getProcessedCount());
            task.setDeletedCount(result.getDeletedCount());
            taskRepository.save(task);
            
            // 6. 设置结果
            result.setSuccess(true);
            result.setMessage("归档执行成功");
            result.setEndTime(new Date());
            result.setDuration(System.currentTimeMillis() - result.getStartTime().getTime());
            
        } catch (Exception e) {
            // 处理异常
            handleExecutionError(result, e);
        }
        
        return result;
    }
    
    // 执行归档节点
    private void executeArchiveNodes(List<ArchiveNode> nodes, ArchiveConfig config, 
                                   ArchiveTask task, ArchiveResult result) {
        for (ArchiveNode node : nodes) {
            try {
                // 1. 查询数据
                List<Map<String, Object>> dataList = queryExecutor.query(node, config);
                result.setProcessedCount(result.getProcessedCount() + dataList.size());
                
                // 2. 删除数据
                if (!dataList.isEmpty()) {
                    int deletedCount = deleteExecutor.delete(node, config, dataList);
                    result.setDeletedCount(result.getDeletedCount() + deletedCount);
                }
                
                // 3. 递归处理子节点
                if (node.getChildren() != null) {
                    executeArchiveNodes(node.getChildren(), config, task, result);
                }
                
            } catch (Exception e) {
                // 记录节点执行错误
                result.getErrors().add("节点 " + node.getTableName() + " 执行失败: " + e.getMessage());
                log.error("节点执行失败: " + node.getTableName(), e);
            }
        }
    }
    
    // 创建任务记录
    private ArchiveTask createTask(String configId, String taskId) {
        ArchiveTask task = new ArchiveTask();
        task.setTaskId(taskId);
        task.setConfigId(configId);
        task.setStatus("RUNNING");
        task.setStartTime(new Date());
        task.setProcessedCount(0);
        task.setDeletedCount(0);
        
        return taskRepository.save(task);
    }
    
    // 处理执行错误
    private void handleExecutionError(ArchiveResult result, Exception e) {
        result.setSuccess(false);
        result.setMessage("归档执行失败: " + e.getMessage());
        result.setEndTime(new Date());
        result.setDuration(System.currentTimeMillis() - result.getStartTime().getTime());
        result.getErrors().add(e.getMessage());
        
        // 更新任务状态
        ArchiveTask task = taskRepository.findByTaskId(result.getTaskId());
        if (task != null) {
            task.setStatus("FAILED");
            task.setEndTime(new Date());
            task.setErrorMessage(e.getMessage());
            taskRepository.save(task);
        }
        
        log.error("归档执行失败: " + result.getTaskId(), e);
    }
    
    // 生成任务ID
    private String generateTaskId() {
        return "TASK_" + System.currentTimeMillis() + "_" + RandomStringUtils.randomAlphanumeric(6);
    }
}
```

### 4.2 配置验证器

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/validator/ArchiveValidator.java`

```java
// 配置验证器
@Component
public class ArchiveValidator {
    
    @Autowired
    private DatasourceProvider datasourceProvider;
    
    // 验证配置
    public ValidationResult validateConfig(ArchiveConfig config) {
        ValidationResult result = new ValidationResult();
        
        // 验证基础信息
        if (StringUtils.isEmpty(config.getConfigName())) {
            result.addError("配置名称不能为空");
        }
        
        if (StringUtils.isEmpty(config.getDatasourceName())) {
            result.addError("数据源名称不能为空");
        }
        
        if (StringUtils.isEmpty(config.getDatabaseName())) {
            result.addError("数据库名称不能为空");
        }
        
        // 验证数据源连接
        if (StringUtils.isNotEmpty(config.getDatasourceName())) {
            try {
                datasourceProvider.getJdbcTemplate(config.getDatasourceName(), config.getDatabaseName());
            } catch (Exception e) {
                result.addError("数据源连接失败: " + e.getMessage());
            }
        }
        
        // 验证归档节点
        if (config.getNodes() != null) {
            validateArchiveNodes(config.getNodes(), config, result);
        }
        
        return result;
    }
    
    // 验证归档节点
    private void validateArchiveNodes(List<ArchiveNode> nodes, ArchiveConfig config, ValidationResult result) {
        for (ArchiveNode node : nodes) {
            // 验证表名
            if (StringUtils.isEmpty(node.getTableName())) {
                result.addError("表名不能为空");
                continue;
            }
            
            // 验证归档条件
            if (StringUtils.isEmpty(node.getCondition())) {
                result.addError("归档条件不能为空");
                continue;
            }
            
            // 验证表是否存在
            try {
                String datasourceName = StringUtils.isNotEmpty(node.getDatasourceName()) ? 
                    node.getDatasourceName() : config.getDatasourceName();
                String databaseName = config.getDatabaseName();
                
                JdbcTemplate jdbcTemplate = datasourceProvider.getJdbcTemplate(datasourceName, databaseName);
                validateTableExists(jdbcTemplate, node.getTableName(), result);
                
            } catch (Exception e) {
                result.addError("验证表 " + node.getTableName() + " 失败: " + e.getMessage());
            }
            
            // 递归验证子节点
            if (node.getChildren() != null) {
                validateArchiveNodes(node.getChildren(), config, result);
            }
        }
    }
    
    // 验证表是否存在
    private void validateTableExists(JdbcTemplate jdbcTemplate, String tableName, ValidationResult result) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ?";
            int count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            
            if (count == 0) {
                result.addError("表不存在: " + tableName);
            }
        } catch (Exception e) {
            result.addError("验证表存在性失败: " + tableName + ", " + e.getMessage());
        }
    }
}

// 验证结果
public class ValidationResult {
    private boolean valid = true;
    private List<String> errors = new ArrayList<>();
    
    public void addError(String error) {
        this.valid = false;
        this.errors.add(error);
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public List<String> getErrors() {
        return errors;
    }
}
```

### 4.3 查询执行器

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/executor/QueryExecutor.java`

```java
// 查询执行器
@Component
public class QueryExecutor {
    
    @Autowired
    private DatasourceProvider datasourceProvider;
    
    // 查询数据
    public List<Map<String, Object>> query(ArchiveNode node, ArchiveConfig config) {
        // 获取数据源
        String datasourceName = StringUtils.isNotEmpty(node.getDatasourceName()) ? 
            node.getDatasourceName() : config.getDatasourceName();
        String databaseName = config.getDatabaseName();
        
        JdbcTemplate jdbcTemplate = datasourceProvider.getJdbcTemplate(datasourceName, databaseName);
        
        // 构建查询SQL
        String sql = buildQuerySql(node, config);
        
        // 执行查询
        log.info("执行查询 - 表: {}, SQL: {}", node.getTableName(), sql);
        
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        
        log.info("查询完成 - 表: {}, 记录数: {}", node.getTableName(), result.size());
        
        return result;
    }
    
    // 构建查询SQL
    private String buildQuerySql(ArchiveNode node, ArchiveConfig config) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(node.getTableName());
        
        if (StringUtils.isNotEmpty(node.getCondition())) {
            sql.append(" WHERE ").append(node.getCondition());
        }
        
        // 添加限制条件
        ArchiveSettings settings = config.getSettings();
        if (settings != null && settings.getBatchSize() != null) {
            sql.append(" LIMIT ").append(settings.getBatchSize());
        }
        
        return sql.toString();
    }
}
```

### 4.4 删除执行器

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/executor/DeleteExecutor.java`

```java
// 删除执行器
@Component
public class DeleteExecutor {
    
    @Autowired
    private DatasourceProvider datasourceProvider;
    
    // 删除数据
    public int delete(ArchiveNode node, ArchiveConfig config, List<Map<String, Object>> dataList) {
        if (dataList.isEmpty()) {
            return 0;
        }
        
        // 获取数据源
        String datasourceName = StringUtils.isNotEmpty(node.getDatasourceName()) ? 
            node.getDatasourceName() : config.getDatasourceName();
        String databaseName = config.getDatabaseName();
        
        JdbcTemplate jdbcTemplate = datasourceProvider.getJdbcTemplate(datasourceName, databaseName);
        
        // 构建删除SQL
        String sql = buildDeleteSql(node, dataList);
        Object[] params = buildDeleteParams(node, dataList);
        
        // 执行删除
        log.info("执行删除 - 表: {}, SQL: {}, 参数数量: {}", node.getTableName(), sql, params.length);
        
        int deletedCount = jdbcTemplate.update(sql, params);
        
        log.info("删除完成 - 表: {}, 删除记录数: {}", node.getTableName(), deletedCount);
        
        return deletedCount;
    }
    
    // 构建删除SQL
    private String buildDeleteSql(ArchiveNode node, List<Map<String, Object>> dataList) {
        // 根据数据构建IN条件
        Set<String> conditions = new HashSet<>();
        
        for (Map<String, Object> row : dataList) {
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                conditions.add(entry.getKey() + " = ?");
            }
            break; // 只取第一行数据构建条件
        }
        
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM ").append(node.getTableName());
        sql.append(" WHERE ").append(String.join(" AND ", conditions));
        
        return sql.toString();
    }
    
    // 构建删除参数
    private Object[] buildDeleteParams(ArchiveNode node, List<Map<String, Object>> dataList) {
        List<Object> params = new ArrayList<>();
        
        for (Map<String, Object> row : dataList) {
            for (Object value : row.values()) {
                params.add(value);
            }
        }
        
        return params.toArray();
    }
}
```

## 5. 配置中心集成

### 5.1 配置中心服务

**文件位置**：`scp-framework1/scp-archive/scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ConfigCenterService.java`

```java
// 配置中心服务
@Component
public class ConfigCenterService {
    
    @Autowired
    private NacosConfigService nacosConfigService;
    
    // 发布配置
    public void publishConfig(String configId, ArchiveConfig config) {
        String dataId = "archive-config-" + configId;
        String group = "ARCHIVE_GROUP";
        String content = JsonUtils.toJson(config);
        
        try {
            nacosConfigService.publishConfig(dataId, group, content);
            log.info("配置发布成功: {}", configId);
        } catch (Exception e) {
            log.error("配置发布失败: {}", configId, e);
            throw new ArchiveException("配置发布失败", e);
        }
    }
    
    // 取消发布配置
    public void unpublishConfig(String configId) {
        String dataId = "archive-config-" + configId;
        String group = "ARCHIVE_GROUP";
        
        try {
            nacosConfigService.removeConfig(dataId, group);
            log.info("配置取消发布成功: {}", configId);
        } catch (Exception e) {
            log.error("配置取消发布失败: {}", configId, e);
            throw new ArchiveException("配置取消发布失败", e);
        }
    }
    
    // 获取配置
    public ArchiveConfig getConfig(String configId) {
        String dataId = "archive-config-" + configId;
        String group = "ARCHIVE_GROUP";
        
        try {
            String content = nacosConfigService.getConfig(dataId, group, 5000);
            if (StringUtils.isNotEmpty(content)) {
                return JsonUtils.fromJson(content, ArchiveConfig.class);
            }
        } catch (Exception e) {
            log.error("获取配置失败: {}", configId, e);
        }
        
        return null;
    }
    
    // 获取所有已发布配置
    public List<ArchiveConfig> getAllPublishedConfigs() {
        List<ArchiveConfig> configs = new ArrayList<>();
        
        try {
            // 获取配置列表
            List<String> configIds = nacosConfigService.listConfigs("ARCHIVE_GROUP");
            
            for (String configId : configIds) {
                if (configId.startsWith("archive-config-")) {
                    String actualConfigId = configId.substring("archive-config-".length());
                    ArchiveConfig config = getConfig(actualConfigId);
                    if (config != null && "PUBLISHED".equals(config.getStatus())) {
                        configs.add(config);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取所有配置失败", e);
        }
        
        return configs;
    }
    
    // 注册配置监听
    public void registerListener(String configId, ConfigChangeListener listener) {
        String dataId = "archive-config-" + configId;
        String group = "ARCHIVE_GROUP";
        
        try {
            nacosConfigService.addListener(dataId, group, listener);
            log.info("注册配置监听成功: {}", configId);
        } catch (Exception e) {
            log.error("注册配置监听失败: {}", configId, e);
        }
    }
    
    // 取消配置监听
    public void unregisterListener(String configId) {
        String dataId = "archive-config-" + configId;
        String group = "ARCHIVE_GROUP";
        
        try {
            nacosConfigService.removeListener(dataId, group);
            log.info("取消配置监听成功: {}", configId);
        } catch (Exception e) {
            log.error("取消配置监听失败: {}", configId, e);
        }
    }
}

// 配置变更监听器接口
public interface ConfigChangeListener {
    void onConfigChanged(String configId, ConfigChangeEvent event);
}
```

## 6. 核心执行流程

### 6.1 配置管理流程

```
1. Admin端创建/更新配置
   ├── 验证配置参数
   ├── 保存到数据库
   └── 发布到配置中心

2. Admin端删除/禁用配置
   ├── 更新数据库状态
   └── 从配置中心移除

3. SDK端获取配置
   ├── 从缓存获取（优先）
   ├── 从配置中心获取
   ├── 缓存配置
   └── 注册配置监听
```

### 6.2 归档执行流程

```
1. 触发归档执行
   ├── 获取配置
   ├── 验证配置
   └── 创建任务记录

2. 执行归档节点
   ├── 查询数据
   ├── 删除数据
   └── 递归处理子节点

3. 完成归档任务
   ├── 更新任务状态
   ├── 记录执行结果
   └── 返回执行结果
```

### 6.3 配置变更流程

```
1. Admin端变更配置
   ├── 更新数据库
   └── 发布到配置中心

2. 配置中心通知变更
   └── 触发监听器回调

3. SDK端处理变更
   ├── 刷新配置缓存
   ├── 更新本地配置
   └── 记录变更日志
```

## 7. 关键特性

### 7.1 配置隔离
- 每个租户的配置相互隔离
- 支持多数据源配置
- 节点级数据源覆盖

### 7.2 配置缓存
- 本地缓存减少网络请求
- 缓存过期自动刷新
- 配置变更实时更新

### 7.3 执行监控
- 任务执行状态跟踪
- 执行结果详细记录
- 错误信息完整保存

### 7.4 容错机制
- 配置验证失败处理
- 数据源连接异常处理
- 节点执行失败隔离

## 8. 总结

本方案实现了SCP-Archive系统的核心功能链路：

1. **管理端配置维护**：通过REST API提供配置的CRUD操作，支持配置验证和发布管理
2. **SDK配置获取**：通过配置中心获取配置，支持缓存和实时监听
3. **执行链路**：完整的归档执行流程，包括配置获取、验证、执行和结果记录

所有功能均使用伪代码表述，专注于核心业务逻辑，为后续的具体实现提供了清晰的技术方案。 