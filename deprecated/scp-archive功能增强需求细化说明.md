# SCP-Archive 功能增强需求细化说明

## 概述

本文档详细描述了SCP-Archive系统的四个核心功能增强需求，包括多数据源支持、sharding能力、元数据存储迁移和完善的preview功能。每个需求都经过细化，明确了具体的实现要求、配置方式、处理逻辑和预期效果。

## 1. 多数据源支持

### 1.1 功能描述
支持每个归档节点独立配置数据源，实现节点级别的数据源隔离和管理。

### 1.2 细化需求

#### 1.2.1 节点级数据源配置
- **配置字段**：每个 `ArchiveNode` 节点可以独立配置 `datasourceName` 和 `databaseName` 字段
- **配置优先级**：节点配置 > 任务级默认配置 > 全局默认配置
- **配置验证**：执行前验证节点配置的数据源是否存在且可用

#### 1.2.2 数据源获取机制
- **获取方法**：节点执行时通过 `DatasourceProvider.getJdbcTemplate(datasourceName, databaseName)` 方法获取对应的数据源
- **数据源管理**：支持动态注册和移除数据源
- **连接池管理**：每个数据源独立管理连接池，避免相互影响

#### 1.2.3 配置示例
```yaml
# 任务配置示例，此处仅用作示例描述，具体配置需要参考项目的配置协议
tasks:
  - taskName: "order_archive"
    defaultDatasourceName: "default_db"      # 任务级默认数据源
    defaultDatabaseName: "default_system"    # 任务级默认数据库
    nodes:
      - tableName: "order_info"
        datasourceName: "order_db"           # 节点级数据源
        databaseName: "order_system"         # 节点级数据库
        condition: "created_time < ?"
        children:
          - tableName: "order_item"
            datasourceName: "item_db"        # 子节点独立数据源
            databaseName: "item_system"      # 子节点独立数据库
            condition: "order_id = ?"
```

#### 1.2.4 实现要点
- 扩展 `ArchiveNode` 模型，添加数据源相关字段
- 修改 `DatasourceProvider` 接口，支持多参数获取
- 在节点执行时根据配置获取对应的数据源
- 提供数据源配置验证和错误处理机制

## 2. Sharding能力

### 2.1 功能描述
支持分片表的归档处理，通过分片表达式或自定义插件实现数据路由。

### 2.2 细化需求

#### 2.2.1 分片配置字段
- **shardingEnabled**：是否启用分片功能
- **shardingExpression**：分片表达式，支持简单表达式如 `item_id % 128`
- **shardingPluginClass**：分片插件全类名，如 `com.example.CustomShardingPlugin`,如果使用插件全类名，则需要配置分表字段

#### 2.2.2 分片表达式支持
- **表达式格式**：支持 `字段名 % 分片数` 的简单表达式
- **支持字段类型**：支持整数类型字段的分片计算
- **表达式验证**：执行前验证表达式的语法正确性
- **插件能力**：归档系统会把查询的字段匹配出的表名返回，且返回的结构与传递的参数需要对应

#### 2.2.5 配置示例
```yaml
nodes:
  - tableName: "order_info"
    shardingEnabled: true
    shardingExpression: "order_id % 128"           # 简单表达式
    # 或者使用自定义插件
    shardingPluginClass: "com.example.OrderShardingPlugin"
    shardingPluginConfig:
      shardCount: 128
      shardKey: "order_id"
```

#### 2.2.6 实现要点
- 扩展 `ArchiveNode` 模型，添加分片相关字段
- 实现分片表达式解析器
- 定义分片插件接口和加载机制

## 3. 元数据存储迁移

### 3.1 功能描述
将现有的单一JSON配置拆分为每个任务一个独立的配置项，解决配置膨胀问题。

### 3.2 细化需求

#### 3.2.1 存储结构拆分
- **配置键设计**：使用 `archive.task.{taskName}` 作为每个任务的配置键
- **配置独立性**：每个任务配置独立存储，互不影响
- **配置版本控制**：支持配置版本管理，便于回滚

#### 3.2.2 任务变更处理
- **新增任务**：创建新的配置项，不影响现有任务
- **更新任务**：原子性更新对应任务的配置项
- **删除任务**：删除对应配置项，清理相关断点数据
- **任务重命名**：先创建新配置项，再删除旧配置项

#### 3.2.3 数据迁移策略
- **迁移工具**：提供迁移工具，将现有大JSON拆分为多个任务配置
- **增量迁移**：支持增量迁移，避免一次性迁移大量数据
- **系统可用性**：迁移过程中保持系统可用性
- **回滚机制**：提供迁移失败的回滚机制

#### 3.2.4 配置存储优化
- **配置备份**：提供配置备份和恢复机制
- **配置监控**：监控配置变更和存储使用情况
- **配置清理**：定期清理过期的配置和断点数据

#### 3.2.5 存储结构示例
```yaml
# 迁移前：单一JSON配置
archive.config: |
  {
    "tasks": [
      {"taskName": "task1", "config": {...}},
      {"taskName": "task2", "config": {...}}
    ]
  }

# 迁移后：独立配置项
archive.task.task1: |
  {"taskName": "task1", "config": {...}}
archive.task.task2: |
  {"taskName": "task2", "config": {...}}
```

#### 3.2.6 实现要点
- 设计配置迁移工具和脚本
- 修改配置管理器，支持独立配置项管理
- 实现配置版本控制和回滚机制
- 提供配置监控和清理功能

## 4. 完善Preview功能

### 4.1 功能描述
实现指定节点的预览功能，构建从根节点到指定节点的完整查询链路。

### 4.2 细化需求

#### 4.2.1 节点路径分析
- **路径查找**：根据请求的 `targetKey` 分析从根节点到目标节点的完整路径
- **路径验证**：验证路径的有效性和可达性
- **循环检测**：检测并处理节点间的循环依赖

#### 4.2.2 查询链路构建
- **链路生成**：构建从根节点到指定节点的完整查询链路
- **依赖关系**：明确节点间的依赖关系和执行顺序
- **参数传递**：将上一个节点的查询结果作为下一个节点的查询参数

#### 4.2.3 SQL生成策略
- **根节点SQL**：生成基础查询SQL，包含条件过滤
- **中间节点SQL**：生成基于父节点结果的关联查询SQL
- **目标节点SQL**：生成最终的查询SQL，包含所有必要的关联条件

#### 4.2.4 预览结果展示
- **查询链路**：显示完整的查询链路和节点关系
- **SQL语句**：展示每个节点的SQL语句和参数
- **数据预估**：提供预估的数据量信息
- **执行计划**：支持预览执行计划分析

#### 4.2.5 功能特性
- **节点指定**：支持指定任意节点进行预览
- **插件支持**：支持预览模式下的插件执行
- **结果缓存**：提供预览结果的缓存机制
- **结果导出**：支持预览结果的导出功能

#### 4.2.6 请求响应示例
```yaml
# 请求示例
preview:
  taskName: "order_archive"
  targetKey: "order_item"  # 预览到order_item节点
  parameters:
    timeRange: "30 days"
    maxRows: 1000

# 返回结果示例
result:
  nodePath: ["order_info", "order_item"]
  queryChain:
    - node: "order_info"
      tableName: "order_info"
      sql: "SELECT id, order_no FROM order_info WHERE created_time < DATE_SUB(NOW(), INTERVAL 30 DAY)"
      estimatedRows: 10000
      parameters: ["30 days"]
    - node: "order_item" 
      tableName: "order_item"
      sql: "SELECT * FROM order_item WHERE order_id IN (SELECT id FROM order_info WHERE created_time < DATE_SUB(NOW(), INTERVAL 30 DAY))"
      estimatedRows: 50000
      parameters: ["30 days"]
  totalEstimatedRows: 50000
  executionPlan: "EXPLAIN SELECT ..."
  warnings: []
  executionTime: "2.5s"
```

#### 4.2.7 实现要点
- 实现节点路径分析算法
- 构建查询链路生成器
- 开发SQL生成和优化器
- 提供预览结果缓存和导出功能