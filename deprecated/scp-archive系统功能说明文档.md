# SCP-Archive 系统功能说明文档

## 1. 系统概述

### 1.1 项目背景
SCP-Archive 是一个高性能、高可用的数据归档框架，专门用于处理大规模数据库的历史数据归档和清理工作。系统采用分布式架构设计，支持断点续传、插件扩展和多种归档策略，能够有效解决数据库历史数据积累过多导致的性能问题。

### 1.2 核心特性
- **分布式执行**：支持多节点并行归档，通过分布式锁保证数据一致性
- **断点续传**：任务中断后可从断点继续执行，确保数据完整性
- **插件机制**：支持自定义插件，灵活扩展归档前后处理逻辑
- **多种扫描模式**：支持条件扫描和主键扫描，适应不同数据量场景
- **实时监控**：提供完善的监控和统计功能，支持任务状态追踪
- **优雅停机**：支持系统优雅关闭，确保正在执行的任务完成
- **多数据源支持**：统一的数据源管理，支持多种数据库类型

### 1.3 适用场景
- 大型电商平台的订单数据归档
- 日志系统的历史数据清理
- 用户行为数据的定期归档
- 业务数据的冷热分离
- 数据合规性要求的数据删除

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────┐
│                 Admin API Layer                     │
│  ArchiveApiService, 任务管理API, 监控查询API        │
└─────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────┐
│                 Engine Layer                        │
│  ArchiveEngine, TaskScheduler, PluginManager       │
└─────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────┐
│                 Core Layer                          │
│  QueryExecutor, DeleteExecutor, ScanModeHandler    │
└─────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────┐
│              Infrastructure Layer                   │
│  DatasourceProvider, DistributedLock, Checkpoint   │
└─────────────────────────────────────────────────────┘
```

### 2.2 模块划分
- **scp-archive-common**：公共模块，包含常量、模型和接口定义
- **scp-archive-sdk**：核心SDK模块，包含归档引擎和执行逻辑
- **scp-archive-sdk-test**：测试模块，包含单元测试和集成测试

## 3. 功能模块详细说明

### 3.1 任务调度模块（TaskScheduler）

#### 3.1.1 功能概述
负责归档任务的调度和分发，支持多种调度策略和时间控制。

#### 3.1.2 主要功能
- **任务过滤**：根据执行时间、状态等条件过滤可执行任务
- **时间控制**：支持全天归档和指定时间归档两种模式
- **下次执行时间计算**：根据任务配置计算下次执行时间
- **任务分发**：将任务分发给执行引擎进行处理

#### 3.1.3 输入参数
- `ArchiveDataConfig`：归档数据配置，包含全局配置信息
- `TaskConfig`：任务配置，包含单个任务的详细配置

#### 3.1.4 输出结果
- `List<TaskConfig>`：过滤后的可执行任务列表
- `Long`：下次执行时间戳

#### 3.1.5 关键流程
1. 检查任务是否启用且处于运行状态
2. 根据执行时间策略过滤任务
3. 检查任务是否到达执行时间
4. 返回可执行任务列表

### 3.2 归档引擎模块（ArchiveEngine）

#### 3.2.1 功能概述
系统的核心执行引擎，负责任务的整体调度、分布式锁管理、断点续传和归档流程控制。

#### 3.2.2 主要功能
- **任务调度与分发**：管理归档任务的生命周期
- **分布式锁管理**：确保同一任务在多节点环境下的互斥执行
- **断点续传**：支持任务中断后从断点继续执行
- **归档流程控制**：协调各个组件完成归档操作
- **优雅停机**：支持系统优雅关闭

#### 3.2.3 输入参数
- `ArchiveDataConfig`：归档数据配置
- `TaskConfig`：任务配置

#### 3.2.4 输出结果
- 无直接返回值，通过日志和监控反馈执行结果

#### 3.2.5 关键流程
1. 初始化核心组件（查询执行器、删除执行器等）
2. 启动归档任务主循环
3. 按配置的时间间隔执行任务调度
4. 获取分布式锁后执行具体任务
5. 更新任务的下次执行时间
6. 释放分布式锁

#### 3.2.6 注意事项
- 必须在分布式环境中使用分布式锁
- 支持优雅停机，避免数据不一致
- 主循环采用守护线程，系统关闭时自动退出

### 3.3 任务执行模块（ArchiveTaskExecutor）

#### 3.3.1 功能概述
采用模板方法模式定义归档任务的执行流程，提供标准化的任务执行框架。

#### 3.3.2 主要功能
- **任务解析**：将任务配置解析为可执行的归档任务
- **任务验证**：验证任务配置的合法性
- **任务执行**：执行具体的归档逻辑
- **异常处理**：处理执行过程中的异常情况
- **执行监控**：记录任务执行时间和状态

#### 3.3.3 输入参数
- `TaskConfig`：任务配置
- `ArchiveDataConfig`：归档数据配置

#### 3.3.4 输出结果
- 无直接返回值，通过日志记录执行结果

#### 3.3.5 关键流程
1. 前置处理：准备执行环境
2. 解析任务：将配置转换为归档任务对象
3. 验证任务：检查任务配置的合法性
4. 执行任务：调用具体的归档逻辑
5. 后置处理：清理资源和记录结果

#### 3.3.6 注意事项
- 采用模板方法模式，子类只需实现特定步骤
- 支持APM链路追踪，便于问题定位
- 异常情况下会记录详细的错误信息

### 3.4 查询执行模块（QueryExecutor）

#### 3.4.1 功能概述
负责执行数据库查询操作，支持多种查询模式和条件构建。

#### 3.4.2 主要功能
- **SQL构建**：根据归档上下文构建查询SQL
- **查询执行**：执行数据库查询操作
- **结果处理**：处理查询结果并返回
- **异常处理**：处理查询过程中的异常

#### 3.4.3 输入参数
- `ArchiveContext`：归档上下文，包含查询条件、排序等信息

#### 3.4.4 输出结果
- `List<Map<String, Object>>`：查询结果集

#### 3.4.5 关键流程
1. 从归档上下文获取查询条件
2. 构建查询SQL语句
3. 执行数据库查询
4. 处理查询结果
5. 返回结果集

#### 3.4.6 注意事项
- 支持分页查询，避免大结果集导致内存溢出
- 查询超时控制，防止长时间阻塞
- 支持多种数据源类型

### 3.5 删除执行模块（DeleteExecutor）

#### 3.5.1 功能概述
负责执行数据库删除操作，支持批量删除和事务控制。

#### 3.5.2 主要功能
- **删除SQL构建**：根据查询结果构建删除SQL
- **批量删除**：支持批量删除操作，提高效率
- **事务控制**：确保删除操作的一致性
- **删除统计**：记录删除的数据量

#### 3.5.3 输入参数
- `ArchiveContext`：归档上下文
- `List<Map<String, Object>>`：要删除的数据

#### 3.5.4 输出结果
- `int`：删除的数据条数

#### 3.5.5 关键流程
1. 根据查询结果构建删除条件
2. 构建删除SQL语句
3. 执行批量删除操作
4. 提交事务
5. 返回删除条数

#### 3.5.6 注意事项
- 支持预览模式，不执行实际删除
- 删除操作需要事务保护
- 支持外键约束检查

### 3.6 扫描模式处理模块（ScanModeHandler）

#### 3.6.1 功能概述
专门处理大数据量场景下的分批扫描归档逻辑，优化大表的归档性能。

#### 3.6.2 主要功能
- **分批扫描**：按索引字段分批扫描数据
- **主键条件构建**：根据扫描结果构建主键条件
- **断点保存**：保存扫描进度断点
- **循环控制**：控制扫描循环的继续或退出

#### 3.6.3 输入参数
- `ArchiveNode`：归档节点配置
- `ArchiveContext`：归档上下文
- `ArchiveCheckpoint`：断点信息

#### 3.6.4 输出结果
- `boolean`：是否应该继续执行

#### 3.6.5 关键流程
1. 创建扫描上下文
2. 执行索引字段扫描查询
3. 检查扫描结果
4. 构建主键条件
5. 保存断点信息
6. 返回是否继续标识

#### 3.6.6 注意事项
- 需要配置索引字段才能启用
- 适用于大数据量表的归档
- 扫描结果为空时自动退出

### 3.7 插件管理模块（PluginManager）

#### 3.7.1 功能概述
负责管理和执行各种归档插件，支持责任链模式的插件执行。

#### 3.7.2 主要功能
- **插件注册**：支持自动和手动插件注册
- **插件缓存**：按事件类型缓存插件，提高执行效率
- **插件执行**：支持中断和非中断两种执行模式
- **异常处理**：处理插件执行过程中的异常

#### 3.7.3 输入参数
- `ArchiveEvent`：归档事件类型
- `ArchiveContext`：归档上下文

#### 3.7.4 输出结果
- `boolean`：插件执行结果，true表示继续，false表示中断

#### 3.7.5 关键流程
1. 根据事件类型获取相关插件
2. 按优先级排序插件
3. 顺序执行插件
4. 处理插件返回结果
5. 返回最终执行结果

#### 3.7.6 注意事项
- 插件按优先级顺序执行
- 支持插件执行中断机制
- 插件异常不会影响其他插件执行

### 3.8 断点管理模块（CheckpointManager）

#### 3.8.1 功能概述
统一管理断点续传逻辑，确保任务中断后能够从断点继续执行。

#### 3.8.2 主要功能
- **断点加载**：从存储介质加载断点信息
- **断点保存**：保存任务执行进度断点
- **断点验证**：验证断点信息的有效性
- **断点清理**：清理过期或无效的断点

#### 3.8.3 输入参数
- `String`：任务名称
- `ArchiveNode`：归档节点
- `ArchiveContext`：归档上下文

#### 3.8.4 输出结果
- `ArchiveCheckpoint`：断点信息对象

#### 3.8.5 关键流程
1. 根据任务名称查找断点
2. 验证断点信息有效性
3. 将断点信息设置到上下文
4. 返回断点对象

#### 3.8.6 注意事项
- 断点信息需要持久化存储
- 支持多种存储介质
- 断点过期后自动清理

### 3.9 配置管理模块（ArchiveConfigManager）

#### 3.9.1 功能概述
负责管理归档任务的配置信息，提供配置验证、缓存和更新功能。

#### 3.9.2 主要功能
- **配置注册**：注册归档任务配置
- **配置验证**：验证配置信息的合法性
- **配置缓存**：缓存配置信息，提高访问效率
- **配置更新**：支持配置的动态更新

#### 3.9.3 输入参数
- `ArchiveDataConfig`：归档数据配置

#### 3.9.4 输出结果
- `boolean`：操作是否成功

#### 3.9.5 关键流程
1. 验证配置信息
2. 生成配置键值
3. 缓存配置信息
4. 更新任务配置映射
5. 记录操作日志

#### 3.9.6 注意事项
- 配置信息需要通过验证
- 支持配置信息的动态更新
- 配置缓存采用线程安全设计

### 3.10 监控统计模块（ArchiveMonitor）

#### 3.10.1 功能概述
负责收集和统计归档任务的执行指标，提供监控数据导出功能。

#### 3.10.2 主要功能
- **任务执行统计**：统计任务执行次数、成功率等
- **性能指标收集**：收集执行时间、数据量等性能指标
- **错误统计**：统计和记录错误信息
- **监控数据导出**：提供监控数据的查询和导出

#### 3.10.3 输入参数
- `String`：任务名称
- `long`：执行时间
- `int`：数据量

#### 3.10.4 输出结果
- `TaskStats`：任务统计信息
- `GlobalStats`：全局统计信息

#### 3.10.5 关键流程
1. 收集任务执行数据
2. 更新统计信息
3. 计算性能指标
4. 记录错误信息
5. 提供数据查询接口

#### 3.10.6 注意事项
- 统计数据采用原子操作
- 支持多线程并发访问
- 提供丰富的监控指标

### 3.11 API服务模块（ArchiveApiService）

#### 3.11.1 功能概述
提供归档任务的对外API接口，支持任务管理、配置管理和监控查询。

#### 3.11.2 主要功能
- **任务管理API**：提供任务注册、更新、删除等接口
- **配置管理API**：提供配置查询、更新等接口
- **监控查询API**：提供监控数据查询接口
- **状态查询API**：提供任务状态查询接口

#### 3.11.3 输入参数
- `ArchiveDataConfig`：归档数据配置
- `String`：任务名称或配置键

#### 3.11.4 输出结果
- `boolean`：操作是否成功
- `Map<String, Object>`：查询结果

#### 3.11.5 关键流程
1. 参数验证
2. 调用对应的管理器方法
3. 处理返回结果
4. 记录操作日志
5. 返回结果

#### 3.11.6 注意事项
- 所有API都有异常处理
- 支持操作权限控制
- 提供详细的错误信息

## 4. 关键业务流程

### 4.1 归档任务执行流程

```mermaid
graph TD
    A[启动归档循环] --> B[检查任务配置]
    B --> C{任务是否启用}
    C -->|否| D[等待下次调度]
    C -->|是| E[过滤可执行任务]
    E --> F{是否有可执行任务}
    F -->|否| D
    F -->|是| G[获取分布式锁]
    G --> H{锁获取成功}
    H -->|否| D
    H -->|是| I[解析任务配置]
    I --> J[验证任务配置]
    J --> K[加载断点信息]
    K --> L[执行归档逻辑]
    L --> M[更新下次执行时间]
    M --> N[释放分布式锁]
    N --> D
    L --> O[异常处理]
    O --> N
```

### 4.2 数据归档处理流程

```mermaid
graph TD
    A[开始归档] --> B[加载断点信息]
    B --> C{是否启用扫描模式}
    C -->|是| D[执行扫描模式处理]
    C -->|否| E[构建查询条件]
    D --> E
    E --> F[执行前置插件]
    F --> G{插件是否要求中断}
    G -->|是| H[结束归档]
    G -->|否| I[执行数据查询]
    I --> J{查询结果是否为空}
    J -->|是| K[保存断点并结束]
    J -->|否| L[执行后置插件]
    L --> M[处理子节点]
    M --> N{是否为调试模式}
    N -->|是| O[记录日志不删除]
    N -->|否| P[执行数据删除]
    P --> Q[保存断点]
    Q --> R{是否继续循环}
    R -->|是| C
    R -->|否| H
    O --> Q
```

### 4.3 扫描模式处理流程

```mermaid
graph TD
    A[开始扫描模式] --> B[创建扫描上下文]
    B --> C[执行索引字段扫描]
    C --> D{扫描结果是否为空}
    D -->|是| E[结束扫描]
    D -->|否| F[提取主键列表]
    F --> G[构建主键条件]
    G --> H[保存扫描断点]
    H --> I[返回继续标识]
    I --> J[执行正常查询]
    J --> K[处理查询结果]
    K --> L[删除数据]
    L --> M[更新断点]
    M --> N{是否达到最大扫描次数}
    N -->|是| E
    N -->|否| A
```

### 4.4 插件执行流程

```mermaid
graph TD
    A[触发插件事件] --> B[获取事件相关插件]
    B --> C{是否有插件}
    C -->|否| D[返回继续执行]
    C -->|是| E[按优先级排序]
    E --> F[逐个执行插件]
    F --> G[处理插件返回结果]
    G --> H{插件是否要求中断}
    H -->|是| I[返回中断标识]
    H -->|否| J{是否还有插件}
    J -->|是| F
    J -->|否| D
    F --> K[异常处理]
    K --> L[记录错误日志]
    L --> J
```

## 5. 数据模型说明

### 5.1 核心数据结构

#### ArchiveDataConfig（归档数据配置）
- `List<TaskConfig> tasks`：任务配置列表
- `Integer interval`：执行间隔（秒）
- `Integer lockExpireSeconds`：分布式锁过期时间
- `Boolean enable`：是否启用

#### TaskConfig（任务配置）
- `String taskName`：任务名称
- `String datasourceName`：数据源名称
- `Integer mode`：运行模式（调试/生产）
- `Integer status`：任务状态
- `Long nextExecuteTime`：下次执行时间
- `Integer maxScanCount`：最大扫描次数

#### ArchiveNode（归档节点）
- `String tableName`：表名
- `String condition`：查询条件
- `List<ArchiveNode> children`：子节点列表
- `Boolean isArchive`：是否归档
- `String indexColumn`：索引字段
- `Integer indexType`：索引类型
- `String orderBy`：排序字段

#### ArchiveContext（归档上下文）
- `ArchiveNode archiveNode`：归档节点
- `String taskName`：任务名称
- `String datasourceName`：数据源名称
- `List<ConditionContext> conditions`：查询条件列表
- `String traceId`：链路追踪ID
- `ArchiveCheckpoint archiveCheckpoint`：断点信息

### 5.2 常量定义

#### 运行模式
- `MODE_DEBUG = 1`：调试模式
- `MODE_PROD = 2`：生产模式

#### 归档类型
- `ARCHIVE_TYPE_ALL_DAY = 1`：全天归档
- `ARCHIVE_TYPE_SPECIFIC_TIME = 2`：指定时间归档

#### 索引类型
- `INDEX_TYPE_SPAN = 1`：区间索引
- `INDEX_TYPE_RESERVE_DAYS = 2`：保留天数索引

## 6. 配置说明

### 6.1 基础配置

```yaml
scp:
  archive:
    # 全局配置
    enable: true                    # 是否启用归档
    interval: 30                    # 执行间隔（秒）
    lockExpireSeconds: 300         # 分布式锁过期时间（秒）
    
    # 插件配置
    plugin:
      auto-registration:
        enabled: true               # 自动注册插件
      advanced-registration:
        enabled: true               # 高级注册功能
      default:
        enabled: true               # 默认插件启用
      validation:
        enabled: true               # 插件验证
    
    # 任务配置
    task:
      performance:
        slow:
          threshold: 300000         # 慢任务阈值（毫秒）
      sql:
        timeout:
          threshold: 30000          # SQL超时阈值（毫秒）
```

### 6.2 任务配置示例

```yaml
# 任务配置示例
tasks:
  - taskName: "order_archive"
    datasourceName: "order_db"
    mode: 2                        # 生产模式
    status: 1                      # 运行状态
    archiveType: 1                 # 全天归档
    maxScanCount: 10               # 最大扫描次数
    nodes:
      - tableName: "order_info"
        condition: "created_time < date_sub(now(), interval 30 day)"
        isArchive: true
        indexColumn: "id"
        indexType: 1
        orderBy: "asc"
        children:
          - tableName: "order_item"
            condition: "order_id = ?"
            isArchive: true
```

## 7. 性能优化建议

### 7.1 数据库层面
- **索引优化**：确保查询条件字段有适当的索引
- **分区表**：对大表使用分区策略，提高查询效率
- **读写分离**：查询操作使用从库，减少主库压力
- **批量操作**：使用批量删除，减少数据库连接开销

### 7.2 应用层面
- **扫描模式**：大表使用扫描模式，避免深度分页问题
- **并发控制**：合理设置任务并发数，避免数据库过载
- **内存管理**：及时释放大结果集，避免内存溢出
- **链接池**：配置合适的数据库连接池大小

### 7.3 监控调优
- **慢查询监控**：监控SQL执行时间，及时发现性能问题
- **资源监控**：监控CPU、内存、磁盘使用情况
- **错误监控**：监控错误率和异常情况
- **业务监控**：监控归档数据量和执行频率

## 8. 故障处理指南

### 8.1 常见问题及解决方案

#### 问题1：任务执行失败
**现象**：日志显示任务执行异常
**原因**：
- 数据库连接超时
- SQL语法错误
- 数据约束违反
- 分布式锁获取失败

**解决方案**：
1. 检查数据库连接配置
2. 验证SQL语句正确性
3. 检查数据约束条件
4. 调整分布式锁超时时间

#### 问题2：归档性能慢
**现象**：归档任务执行时间过长
**原因**：
- 缺少索引
- 数据量过大
- 并发冲突
- 硬件资源不足

**解决方案**：
1. 优化数据库索引
2. 启用扫描模式
3. 调整并发参数
4. 升级硬件配置

#### 问题3：断点续传失败
**现象**：任务重启后无法从断点继续
**原因**：
- 断点信息丢失
- 断点数据格式错误
- 存储介质故障

**解决方案**：
1. 检查断点存储配置
2. 验证断点数据完整性
3. 修复存储介质问题

### 8.2 紧急处理流程

1. **立即停止任务**：通过API或配置禁用问题任务
2. **数据备份**：备份相关数据，防止数据丢失
3. **问题定位**：查看日志、监控数据定位问题
4. **修复问题**：根据问题类型采取相应措施
5. **测试验证**：在测试环境验证修复效果
6. **恢复服务**：重新启用任务并监控执行情况

## 9. 运维建议

### 9.1 日常维护

#### 定期检查
- **日志检查**：每日检查错误日志和异常信息
- **性能监控**：监控任务执行时间和资源使用情况
- **数据验证**：定期验证归档数据的完整性
- **配置检查**：检查配置文件和参数设置

#### 清理工作
- **日志清理**：定期清理过期的日志文件
- **断点清理**：清理过期的断点信息
- **监控数据清理**：清理过期的监控统计数据

### 9.2 容量规划

#### 存储规划
- **数据增长预估**：根据业务发展预估数据增长量
- **存储扩容**：制定存储扩容计划
- **归档策略**：制定数据归档和清理策略

#### 性能规划
- **并发能力**：根据业务需求规划并发处理能力
- **硬件升级**：制定硬件升级计划
- **网络带宽**：确保网络带宽满足需求

### 9.3 安全建议

#### 数据安全
- **权限控制**：严格控制数据访问权限
- **数据加密**：对敏感数据进行加密处理
- **审计日志**：记录所有数据操作审计日志

#### 系统安全
- **访问控制**：限制系统访问权限
- **网络安全**：配置防火墙和网络安全策略
- **定期更新**：及时更新系统补丁和安全修复

## 10. 其他建议与注意事项

### 10.1 开发建议

#### 插件开发
- **接口实现**：严格按照ArchivePlugin接口实现
- **异常处理**：插件内部要有完善的异常处理
- **性能考虑**：插件执行要考虑性能影响
- **测试完善**：充分测试插件功能

#### 扩展开发
- **遵循设计模式**：按照现有设计模式进行扩展
- **接口兼容**：保持接口的向后兼容性
- **文档更新**：及时更新相关文档

### 10.2 部署建议

#### 环境要求
- **Java版本**：JDK 1.8及以上
- **数据库版本**：MySQL 5.7及以上
- **内存配置**：建议4GB以上
- **磁盘空间**：根据数据量配置足够的磁盘空间

#### 部署配置
- **集群部署**：生产环境建议集群部署
- **负载均衡**：配置合适的负载均衡策略
- **监控告警**：配置完善的监控告警机制

### 10.3 使用注意事项

#### 数据安全
- **备份重要数据**：执行归档前务必备份重要数据
- **测试环境验证**：新配置先在测试环境验证
- **分批执行**：大数据量归档建议分批进行

#### 性能影响
- **业务低峰期执行**：选择业务低峰期执行归档任务
- **资源监控**：实时监控系统资源使用情况
- **影响评估**：评估归档对业务的影响

#### 配置管理
- **版本控制**：配置文件纳入版本控制
- **变更记录**：记录所有配置变更
- **回滚准备**：准备配置回滚方案

