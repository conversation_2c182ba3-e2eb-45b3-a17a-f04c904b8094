# SCP-Archive 功能增强需求细化方案

## 概述

本文档基于SCP-Archive系统现有架构，详细规划四个核心功能的增强方案：多数据源支持、sharding能力、元数据存储迁移和完善的preview功能。每个功能增强都包含详细的改造方式、改造位置、技术实现方案和实施计划。

## 1. 多数据源支持功能增强

### 1.1 改造概述

#### 1.1.1 改造目标
- 支持每个归档节点独立配置数据源，数据库名称继承自任务配置
- 实现节点级别的数据源隔离和管理
- 提供数据源配置的优先级机制（节点级 > 任务级）
- 支持跨数据源的归档任务执行
- 确保每个任务必须手动确认数据库配置

#### 1.1.2 改造范围
- 数据模型层：ArchiveNode、TaskConfig、ArchiveContext
- 基础设施层：DatasourceProvider
- 核心执行层：QueryExecutor、DeleteExecutor
- 配置管理层：ArchiveConfigManager

### 1.2 详细改造方案

#### 1.2.1 数据模型改造

**改造位置**：`scp-archive-common/src/main/java/com/shizhuang/duapp/scp/framework/admin/sdk/model/`

**ArchiveNode模型扩展**
```java
// 文件：ArchiveNode.java
public class ArchiveNode {
    // 现有字段...
    
    /**
     * 节点级数据源名称
     */
    private String datasourceName;
    
    /**
     * 节点级数据源名称（可选，不配置则使用任务级数据源）
     */
    private String datasourceName;
    
    /**
     * 获取有效数据源名称（节点级 > 任务级）
     */
    public String getEffectiveDatasourceName(TaskConfig taskConfig) {
        if (StringUtils.hasText(this.datasourceName)) {
            return this.datasourceName;
        }
        if (taskConfig != null && StringUtils.hasText(taskConfig.getDatasourceName())) {
            return taskConfig.getDatasourceName();
        }
        throw new ArchiveException("数据源未配置", ArchiveException.ErrorType.CONFIG_ERROR);
    }
    
    /**
     * 获取数据库名称（必须来自任务配置）
     */
    public String getDatabaseName(TaskConfig taskConfig) {
        if (taskConfig != null && StringUtils.hasText(taskConfig.getDatabaseName())) {
            return taskConfig.getDatabaseName();
        }
        throw new ArchiveException("数据库名称未配置", ArchiveException.ErrorType.CONFIG_ERROR);
    }
}
```

**TaskConfig模型扩展**
```java
// 文件：TaskConfig.java
public class TaskConfig {
    // 现有字段...
    
    /**
     * 任务级数据源名称（必填）
     */
    private String datasourceName;
    
    /**
     * 任务级数据库名称（必填）
     */
    private String databaseName;
}
```

**ArchiveDataConfig模型扩展**
```java
// 文件：ArchiveDataConfig.java
public class ArchiveDataConfig {
    // 现有字段...
    
    // 移除全局默认配置，每个任务必须明确配置数据源和数据库
}
```

#### 1.2.2 数据源提供者改造

**改造位置**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/service/`

**DatasourceProvider接口扩展**
```java
// 文件：DatasourceProvider.java
public interface DatasourceProvider {
    
    /**
     * 根据数据源名称和数据库名称获取JdbcTemplate
     * @param datasourceName 数据源名称
     * @param databaseName 数据库名称
     * @return JdbcTemplate实例
     */    
    NamedParameterJdbcTemplate getJdbcTemplate(String datasourceName, String databaseName);
}
```

**DatasourceProvider实现类**
```java
// 文件：impl/DatasourceProviderImpl.java
@Component
public class DatasourceProviderImpl implements DatasourceProvider {
    
    @Resource
    private ApplicationContext applicationContext;

    private Map<String, NamedParameterJdbcTemplate> jdbcTemplateMap = new HashMap<>();

    @PostConstruct
    public void init() {
        Map<String, DataSource> dataSourceMap = applicationContext.getBeansOfType(DataSource.class);
        dataSourceMap.forEach((k, v) -> jdbcTemplateMap.put(k, new NamedParameterJdbcTemplate(v)));
    }
    
    @Override
    public JdbcTemplate getJdbcTemplate(String datasourceName, String databaseName) {        
        return jdbcTemplateCache.computeIfAbsent(datasourceName, k -> {
            DataSource dataSource = jdbcTemplateMap.get(k);
            if (dataSource == null) {
                throw new ArchiveException("数据源不存在: " + k, 
                    ArchiveException.ErrorType.DATASOURCE_ERROR);
            }
            return new JdbcTemplate(dataSource);
        });
    }    

}
```

#### 1.2.3 执行器改造

**改造位置**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/service/impl/`

**QueryExecutorImpl改造**
```java
// 文件：QueryExecutorImpl.java
@Component
public class QueryExecutorImpl implements QueryExecutor {
    
    private final DatasourceProvider datasourceProvider;
    
    @Override
    public List<Map<String, Object>> query(ArchiveContext archiveContext) {
        // 获取节点级数据源
        ArchiveNode archiveNode = archiveContext.getArchiveNode();
        TaskConfig taskConfig = archiveContext.getTaskConfig();
        
        String datasourceName = archiveNode.getEffectiveDatasourceName(taskConfig);
        String databaseName = archiveNode.getDatabaseName(taskConfig);
        
        // 获取对应的JdbcTemplate
        NamedParameterJdbcTemplate jdbcTemplate = datasourceProvider.getJdbcTemplate(datasourceName, databaseName);
        
        // 构建SQL和执行查询
        String sql = buildQuerySql(archiveContext);
        Object[] params = buildQueryParams(archiveContext);
        
        log.info("执行查询 - 数据源: {}:{}, SQL: {}, 参数: {}", 
            datasourceName, databaseName, sql, Arrays.toString(params));
        
        return jdbcTemplate.queryForList(sql, params);
    }
    
    // 其他方法保持不变...
}
```

**DeleteExecutorImpl改造**
```java
// 文件：DeleteExecutorImpl.java
@Component
public class DeleteExecutorImpl implements DeleteExecutor {
    
    private final DatasourceProvider datasourceProvider;
    
    @Override
    public int delete(ArchiveContext archiveContext, List<Map<String, Object>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return 0;
        }
        
        // 获取节点级数据源
        ArchiveNode archiveNode = archiveContext.getArchiveNode();
        TaskConfig taskConfig = archiveContext.getTaskConfig();
        
        String datasourceName = archiveNode.getEffectiveDatasourceName(taskConfig);
        String databaseName = archiveNode.getDatabaseName(taskConfig);
        
        // 获取对应的JdbcTemplate
        NamedParameterJdbcTemplate jdbcTemplate = datasourceProvider.getJdbcTemplate(datasourceName, databaseName);
        
        // 构建删除SQL和执行删除
        String deleteSql = buildDeleteSql(archiveContext, dataList);
        Object[] params = buildDeleteParams(archiveContext, dataList);
        
        log.info("执行删除 - 数据源: {}:{}, SQL: {}, 参数数量: {}", 
            datasourceName, databaseName, deleteSql, params.length);
        
        return jdbcTemplate.update(deleteSql, params);
    }
    
    // 其他方法保持不变...
}
```

#### 1.2.4 配置验证器

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/validator/DatasourceValidator.java`

```java
@Component
public class DatasourceValidator {
    
    private final DatasourceProvider datasourceProvider;
    
    /**
     * 验证任务配置中的数据源
     */
    public ValidationResult validateTaskDatasources(TaskConfig taskConfig) {
        ValidationResult result = new ValidationResult();
        
        // 验证任务级数据源（必填）
        if (!StringUtils.hasText(taskConfig.getDatasourceName())) {
            result.addError("任务级数据源名称不能为空");
        }
        
        if (!StringUtils.hasText(taskConfig.getDatabaseName())) {
            result.addError("任务级数据库名称不能为空");
        }
        
        // 验证节点级数据源
        if (taskConfig.getNodes() != null) {
            validateNodeDatasources(taskConfig.getNodes(), taskConfig, result);
        }
        
        return result;
    }
    
    private void validateNodeDatasources(List<ArchiveNode> nodes, TaskConfig taskConfig, ValidationResult result) {
        for (ArchiveNode node : nodes) {
            try {
                String datasourceName = node.getEffectiveDatasourceName(taskConfig);
                String databaseName = node.getDatabaseName(taskConfig);
                
                // 验证数据源是否存在
                try {
                    datasourceProvider.getJdbcTemplate(datasourceName, databaseName);
                } catch (Exception e) {
                    result.addError(String.format("节点 %s 的数据源不可用: %s:%s", 
                        node.getTableName(), datasourceName, databaseName));
                }
                
                // 递归验证子节点
                if (node.getChildren() != null) {
                    validateNodeDatasources(node.getChildren(), taskConfig, result);
                }
            } catch (ArchiveException e) {
                result.addError(String.format("节点 %s 配置错误: %s", node.getTableName(), e.getMessage()));
            }
        }
    }
}
```

### 1.3 配置示例

```yaml
# 任务配置
tasks:
  - taskName: "order_archive"
    datasourceName: "order_db"           # 任务级数据源（必填）
    databaseName: "order_system"         # 任务级数据库（必填）
    nodes:
      - tableName: "order_info"
        condition: "created_time < DATE_SUB(NOW(), INTERVAL 30 DAY)"
        # 不配置datasourceName则使用任务级数据源
        children:
          - tableName: "order_item"
            datasourceName: "item_db"    # 节点级数据源（可选）
            # 不配置databaseName则使用任务级数据库
            condition: "order_id = ?"
```

## 2. Sharding能力功能增强

### 2.1 改造概述

#### 2.1.1 改造目标
- 支持分片表的归档处理
- 实现基于表达式和插件的分片路由
- 提供分片表名解析和查询优化
- 支持跨分片的数据归档

#### 2.1.2 改造范围
- 数据模型层：ArchiveNode
- 核心执行层：QueryExecutor、DeleteExecutor
- 分片处理层：新增ShardingManager、ShardingPlugin接口
- 配置管理层：分片配置验证

### 2.2 详细改造方案

#### 2.2.1 数据模型改造

**改造位置**：`scp-archive-common/src/main/java/com/shizhuang/duapp/scp/framework/admin/sdk/model/ArchiveNode.java`

```java
public class ArchiveNode {
    // 现有字段...
    
    /**
     * 是否启用分片功能
     */
    private Boolean shardingEnabled = false;
    
    /**
     * 分片表达式，如 "order_id % 128"
     */
    private String shardingExpression;
    
    /**
     * 分片插件全类名
     */
    private String shardingPluginClass;
    
    /**
     * 分片插件配置
     */
    private Map<String, Object> shardingPluginConfig = new HashMap<>();
    
    /**
     * 分片字段名称（用于插件模式）
     */
    private String shardingField;
    
    /**
     * 分片数量
     */
    private Integer shardCount;
    
    /**
     * 分片表名模板，如 "order_info_{shard}"
     */
    private String shardingTableTemplate;
    
    /**
     * 判断是否启用分片
     */
    public boolean isShardingEnabled() {
        return Boolean.TRUE.equals(shardingEnabled) && 
               (StringUtils.hasText(shardingExpression) || StringUtils.hasText(shardingPluginClass));
    }
}
```

#### 2.2.2 分片管理器

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/sharding/ShardingManager.java`

```java
@Component
public class ShardingManager {
    
    private final Map<String, ShardingPlugin> pluginCache = new ConcurrentHashMap<>();
    
    /**
     * 解析分片表名列表
     * @param archiveNode 归档节点
     * @param queryResult 查询结果
     * @return 分片表名列表
     */
    public List<String> resolveShardTableNames(ArchiveNode archiveNode, List<Map<String, Object>> queryResult) {
        if (!archiveNode.isShardingEnabled()) {
            return Collections.singletonList(archiveNode.getTableName());
        }
        
        Set<String> tableNames = new HashSet<>();
        
        if (StringUtils.hasText(archiveNode.getShardingExpression())) {
            // 使用表达式解析
            tableNames.addAll(resolveByExpression(archiveNode, queryResult));
        } else if (StringUtils.hasText(archiveNode.getShardingPluginClass())) {
            // 使用插件解析
            tableNames.addAll(resolveByPlugin(archiveNode, queryResult));
        }
        
        return new ArrayList<>(tableNames);
    }
    
    /**
     * 基于表达式解析分片表名
     */
    private List<String> resolveByExpression(ArchiveNode archiveNode, List<Map<String, Object>> queryResult) {
        String expression = archiveNode.getShardingExpression();
        ShardingExpressionParser parser = new ShardingExpressionParser(expression);
        
        Set<Integer> shardIndexes = new HashSet<>();
        for (Map<String, Object> row : queryResult) {
            Integer shardIndex = parser.calculateShard(row);
            if (shardIndex != null) {
                shardIndexes.add(shardIndex);
            }
        }
        
        return shardIndexes.stream()
                .map(index -> buildShardTableName(archiveNode.getTableName(), index))
                .collect(Collectors.toList());
    }
    
    /**
     * 基于插件解析分片表名
     */
    private List<String> resolveByPlugin(ArchiveNode archiveNode, List<Map<String, Object>> queryResult) {
        ShardingPlugin plugin = getShardingPlugin(archiveNode.getShardingPluginClass());
        
        ShardingContext context = new ShardingContext();
        context.setArchiveNode(archiveNode);
        context.setQueryResult(queryResult);
        context.setShardingConfig(archiveNode.getShardingPluginConfig());
        
        return plugin.resolveShardTableNames(context);
    }
    
    /**
     * 获取分片插件实例
     */
    private ShardingPlugin getShardingPlugin(String pluginClass) {
        return pluginCache.computeIfAbsent(pluginClass, className -> {
            try {
                Class<?> clazz = Class.forName(className);
                return (ShardingPlugin) clazz.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new ArchiveException("分片插件加载失败: " + className, e,
                    ArchiveException.ErrorType.PLUGIN_ERROR);
            }
        });
    }
    
    /**
     * 构建分片表名
     */
    private String buildShardTableName(String baseTableName, Integer shardIndex) {
        return baseTableName + "_" + shardIndex;
    }
}
```

#### 2.2.3 分片表达式解析器

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/sharding/ShardingExpressionParser.java`

```java
public class ShardingExpressionParser {
    
    private final String fieldName;
    private final Integer shardCount;
    private final String operator;
    
    public ShardingExpressionParser(String expression) {
        // 解析表达式，如 "order_id % 128"
        String[] parts = expression.trim().split("\\s+");
        if (parts.length != 3) {
            throw new IllegalArgumentException("分片表达式格式错误: " + expression);
        }
        
        this.fieldName = parts[0];
        this.operator = parts[1];
        this.shardCount = Integer.parseInt(parts[2]);
        
        if (!"%".equals(operator)) {
            throw new IllegalArgumentException("暂不支持的分片操作符: " + operator);
        }
    }
    
    /**
     * 计算分片索引
     */
    public Integer calculateShard(Map<String, Object> row) {
        Object fieldValue = row.get(fieldName);
        if (fieldValue == null) {
            log.warn("分片字段值为空: {}", fieldName);
            return null;
        }
        
        try {
            if (fieldValue instanceof Number) {
                return ((Number) fieldValue).intValue() % shardCount;
            } else {
                return Integer.parseInt(fieldValue.toString()) % shardCount;
            }
        } catch (NumberFormatException e) {
            log.error("分片字段值无法转换为数字: {} = {}", fieldName, fieldValue);
            return null;
        }
    }
    
    /**
     * 验证表达式语法
     */
    public static boolean validateExpression(String expression) {
        try {
            new ShardingExpressionParser(expression);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
```

#### 2.2.4 分片插件接口

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/sharding/ShardingPlugin.java`

```java
public interface ShardingPlugin {
    
    /**
     * 解析分片表名列表
     * @param context 分片上下文
     * @return 分片表名列表
     */
    List<String> resolveShardTableNames(ShardingContext context);
    
    /**
     * 验证分片配置
     * @param config 分片配置
     * @return 验证结果
     */
    default boolean validateConfig(Map<String, Object> config) {
        return true;
    }
    
    /**
     * 获取插件名称
     */
    default String getPluginName() {
        return this.getClass().getSimpleName();
    }
}
```

**分片上下文**
```java
public class ShardingContext {
    private ArchiveNode archiveNode;
    private List<Map<String, Object>> queryResult;
    private Map<String, Object> shardingConfig;
    private String traceId;
    
    // getter/setter方法...
}
```

#### 2.2.5 执行器改造

**改造位置**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/service/impl/QueryExecutorImpl.java`

```java
@Component
public class QueryExecutorImpl implements QueryExecutor {
    
    private final DatasourceProvider datasourceProvider;
    private final ShardingManager shardingManager;
    
    @Override
    public List<Map<String, Object>> query(ArchiveContext archiveContext) {
        ArchiveNode archiveNode = archiveContext.getArchiveNode();
        
        if (archiveNode.isShardingEnabled()) {
            return queryWithSharding(archiveContext);
        } else {
            return queryWithoutSharding(archiveContext);
        }
    }
    
    /**
     * 分片查询
     */
    private List<Map<String, Object>> queryWithSharding(ArchiveContext archiveContext) {
        ArchiveNode archiveNode = archiveContext.getArchiveNode();
        
        // 第一步：查询分片字段，确定涉及的分片表
        List<Map<String, Object>> shardingResult = queryShardingFields(archiveContext);
        
        // 第二步：解析分片表名
        List<String> shardTableNames = shardingManager.resolveShardTableNames(archiveNode, shardingResult);
        
        // 第三步：对每个分片表执行查询
        List<Map<String, Object>> allResults = new ArrayList<>();
        for (String shardTableName : shardTableNames) {
            ArchiveContext shardContext = createShardContext(archiveContext, shardTableName);
            List<Map<String, Object>> shardResult = queryWithoutSharding(shardContext);
            allResults.addAll(shardResult);
        }
        
        log.info("分片查询完成 - 表: {}, 分片数: {}, 总记录数: {}", 
            archiveNode.getTableName(), shardTableNames.size(), allResults.size());
        
        return allResults;
    }
    
    /**
     * 查询分片字段
     */
    private List<Map<String, Object>> queryShardingFields(ArchiveContext archiveContext) {
        ArchiveNode archiveNode = archiveContext.getArchiveNode();
        
        // 构建只查询分片字段的SQL
        String shardingField = getShardingField(archiveNode);
        String sql = String.format("SELECT DISTINCT %s FROM %s WHERE %s",
            shardingField, archiveNode.getTableName(), buildWhereClause(archiveContext));
        
        JdbcTemplate jdbcTemplate = getJdbcTemplate(archiveContext);
        Object[] params = buildQueryParams(archiveContext);
        
        return jdbcTemplate.queryForList(sql, params);
    }
    
    /**
     * 创建分片上下文
     */
    private ArchiveContext createShardContext(ArchiveContext originalContext, String shardTableName) {
        ArchiveNode shardNode = cloneArchiveNode(originalContext.getArchiveNode());
        shardNode.setTableName(shardTableName);
        
        ArchiveContext shardContext = new ArchiveContext(
            shardNode,
            originalContext.getTaskName(),
            originalContext.getDatasourceName(),
            originalContext.getTaskConfig(),
            originalContext.getArchiveConfig(),
            originalContext.getTraceId()
        );
        
        // 复制查询条件
        originalContext.getConditions().forEach(shardContext::addCondition);
        
        return shardContext;
    }
    
    // 其他方法...
}
```

#### 2.2.6 分片验证器

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/validator/ShardingValidator.java`

```java
@Component
public class ShardingValidator {
    
    /**
     * 验证分片配置
     */
    public ValidationResult validateShardingConfig(ArchiveNode archiveNode) {
        ValidationResult result = new ValidationResult();
        
        if (!archiveNode.isShardingEnabled()) {
            return result;
        }
        
        // 验证分片表达式
        if (StringUtils.hasText(archiveNode.getShardingExpression())) {
            if (!ShardingExpressionParser.validateExpression(archiveNode.getShardingExpression())) {
                result.addError("分片表达式语法错误: " + archiveNode.getShardingExpression());
            }
        }
        
        // 验证分片插件
        if (StringUtils.hasText(archiveNode.getShardingPluginClass())) {
            try {
                Class.forName(archiveNode.getShardingPluginClass());
            } catch (ClassNotFoundException e) {
                result.addError("分片插件类不存在: " + archiveNode.getShardingPluginClass());
            }
        }
        
        // 验证分片字段
        if (StringUtils.hasText(archiveNode.getShardingPluginClass()) && 
            !StringUtils.hasText(archiveNode.getShardingField())) {
            result.addError("使用分片插件时必须配置分片字段");
        }
        
        return result;
    }
}
```

### 2.3 配置示例

```yaml
# 基于表达式的分片配置
nodes:
  - tableName: "order_info"
    shardingEnabled: true
    shardingExpression: "order_id % 128"
    shardingTableTemplate: "order_info_{shard}"
    condition: "created_time < DATE_SUB(NOW(), INTERVAL 30 DAY)"

# 基于插件的分片配置
nodes:
  - tableName: "user_behavior"
    shardingEnabled: true
    shardingPluginClass: "com.example.UserBehaviorShardingPlugin"
    shardingField: "user_id"
    shardingPluginConfig:
      shardCount: 256
      shardRule: "hash"
      tablePrefix: "user_behavior"
    condition: "event_time < DATE_SUB(NOW(), INTERVAL 7 DAY)"
```

## 3. 元数据存储迁移功能增强

### 3.1 改造概述

#### 3.1.1 改造目标
- 将单一JSON配置拆分为独立的任务配置项
- 实现配置的版本控制和回滚机制
- 提供配置迁移工具和数据迁移策略
- 优化配置存储结构和访问性能

#### 3.1.2 改造范围
- 配置管理层：ArchiveConfigManager
- 存储层：ConfigurationStorage接口
- 迁移工具：ConfigMigrationTool
- 监控层：配置变更监控

### 3.2 详细改造方案

#### 3.2.1 配置存储接口设计

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ConfigurationStorage.java`

```java
public interface ConfigurationStorage {
    
    /**
     * 保存任务配置
     * @param taskName 任务名称
     * @param config 任务配置
     * @param version 配置版本
     */
    void saveTaskConfig(String taskName, TaskConfig config, String version);
    
    /**
     * 获取任务配置
     * @param taskName 任务名称
     * @return 任务配置
     */
    TaskConfig getTaskConfig(String taskName);
    
    /**
     * 获取指定版本的任务配置
     * @param taskName 任务名称
     * @param version 配置版本
     * @return 任务配置
     */
    TaskConfig getTaskConfig(String taskName, String version);
    
    /**
     * 删除任务配置
     * @param taskName 任务名称
     */
    void deleteTaskConfig(String taskName);
    
    /**
     * 获取所有任务名称
     * @return 任务名称列表
     */
    List<String> getAllTaskNames();
    
    /**
     * 获取任务配置的所有版本
     * @param taskName 任务名称
     * @return 版本列表
     */
    List<String> getTaskVersions(String taskName);
    
    /**
     * 回滚到指定版本
     * @param taskName 任务名称
     * @param version 目标版本
     */
    void rollbackToVersion(String taskName, String version);
    
    /**
     * 备份任务配置
     * @param taskName 任务名称
     * @return 备份ID
     */
    String backupTaskConfig(String taskName);
    
    /**
     * 从备份恢复
     * @param taskName 任务名称
     * @param backupId 备份ID
     */
    void restoreFromBackup(String taskName, String backupId);
}
```

#### 3.2.2 配置存储实现

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/impl/ConfigurationStorageImpl.java`

```java
@Component
public class ConfigurationStorageImpl implements ConfigurationStorage {
    
    private static final String TASK_CONFIG_PREFIX = "archive.task.";
    private static final String TASK_VERSION_PREFIX = "archive.version.";
    private static final String TASK_BACKUP_PREFIX = "archive.backup.";
    
    // 假设使用某种配置中心客户端
    private final ConfigCenterClient configCenterClient;
    private final ObjectMapper objectMapper;
    
    @Override
    public void saveTaskConfig(String taskName, TaskConfig config, String version) {
        try {
            String configKey = TASK_CONFIG_PREFIX + taskName;
            String versionKey = TASK_VERSION_PREFIX + taskName + "." + version;
            String configJson = objectMapper.writeValueAsString(config);
            
            // 保存当前配置
            configCenterClient.setConfig(configKey, configJson);
            
            // 保存版本配置
            configCenterClient.setConfig(versionKey, configJson);
            
            // 更新版本索引
            updateVersionIndex(taskName, version);
            
            log.info("任务配置保存成功: {}, 版本: {}", taskName, version);
        } catch (Exception e) {
            throw new ArchiveException("保存任务配置失败: " + taskName, e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    @Override
    public TaskConfig getTaskConfig(String taskName) {
        try {
            String configKey = TASK_CONFIG_PREFIX + taskName;
            String configJson = configCenterClient.getConfig(configKey);
            
            if (StringUtils.isEmpty(configJson)) {
                return null;
            }
            
            return objectMapper.readValue(configJson, TaskConfig.class);
        } catch (Exception e) {
            throw new ArchiveException("获取任务配置失败: " + taskName, e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    @Override
    public TaskConfig getTaskConfig(String taskName, String version) {
        try {
            String versionKey = TASK_VERSION_PREFIX + taskName + "." + version;
            String configJson = configCenterClient.getConfig(versionKey);
            
            if (StringUtils.isEmpty(configJson)) {
                return null;
            }
            
            return objectMapper.readValue(configJson, TaskConfig.class);
        } catch (Exception e) {
            throw new ArchiveException("获取任务配置失败: " + taskName + ":" + version, e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    @Override
    public void deleteTaskConfig(String taskName) {
        try {
            // 删除当前配置
            String configKey = TASK_CONFIG_PREFIX + taskName;
            configCenterClient.deleteConfig(configKey);
            
            // 删除所有版本配置
            List<String> versions = getTaskVersions(taskName);
            for (String version : versions) {
                String versionKey = TASK_VERSION_PREFIX + taskName + "." + version;
                configCenterClient.deleteConfig(versionKey);
            }
            
            // 删除版本索引
            deleteVersionIndex(taskName);
            
            log.info("任务配置删除成功: {}", taskName);
        } catch (Exception e) {
            throw new ArchiveException("删除任务配置失败: " + taskName, e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    @Override
    public List<String> getAllTaskNames() {
        try {
            List<String> configKeys = configCenterClient.getConfigKeys(TASK_CONFIG_PREFIX);
            return configKeys.stream()
                    .map(key -> key.substring(TASK_CONFIG_PREFIX.length()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new ArchiveException("获取任务列表失败", e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    @Override
    public void rollbackToVersion(String taskName, String version) {
        TaskConfig versionConfig = getTaskConfig(taskName, version);
        if (versionConfig == null) {
            throw new ArchiveException("版本配置不存在: " + taskName + ":" + version,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        // 生成新版本号
        String newVersion = generateVersion();
        saveTaskConfig(taskName, versionConfig, newVersion);
        
        log.info("任务配置回滚成功: {} 从版本 {} 回滚到 {}", taskName, version, newVersion);
    }
    
    @Override
    public String backupTaskConfig(String taskName) {
        TaskConfig config = getTaskConfig(taskName);
        if (config == null) {
            throw new ArchiveException("任务配置不存在: " + taskName,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        String backupId = generateBackupId();
        String backupKey = TASK_BACKUP_PREFIX + taskName + "." + backupId;
        
        try {
            String configJson = objectMapper.writeValueAsString(config);
            configCenterClient.setConfig(backupKey, configJson);
            
            log.info("任务配置备份成功: {}, 备份ID: {}", taskName, backupId);
            return backupId;
        } catch (Exception e) {
            throw new ArchiveException("备份任务配置失败: " + taskName, e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    @Override
    public void restoreFromBackup(String taskName, String backupId) {
        String backupKey = TASK_BACKUP_PREFIX + taskName + "." + backupId;
        
        try {
            String configJson = configCenterClient.getConfig(backupKey);
            if (StringUtils.isEmpty(configJson)) {
                throw new ArchiveException("备份配置不存在: " + taskName + ":" + backupId,
                    ArchiveException.ErrorType.CONFIG_ERROR);
            }
            
            TaskConfig config = objectMapper.readValue(configJson, TaskConfig.class);
            String newVersion = generateVersion();
            saveTaskConfig(taskName, config, newVersion);
            
            log.info("任务配置恢复成功: {} 从备份 {} 恢复到版本 {}", taskName, backupId, newVersion);
        } catch (Exception e) {
            throw new ArchiveException("恢复任务配置失败: " + taskName, e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    private void updateVersionIndex(String taskName, String version) {
        // 实现版本索引更新逻辑
    }
    
    private void deleteVersionIndex(String taskName) {
        // 实现版本索引删除逻辑
    }
    
    private String generateVersion() {
        return "v" + System.currentTimeMillis();
    }
    
    private String generateBackupId() {
        return "backup_" + System.currentTimeMillis();
    }
}
```

#### 3.2.3 配置管理器改造

**改造位置**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/config/ArchiveConfigManager.java`

```java
@Component
public class ArchiveConfigManager {
    
    private final ConfigurationStorage configurationStorage;
    private final Map<String, TaskConfig> taskConfigCache = new ConcurrentHashMap<>();
    private final ReadWriteLock cacheLock = new ReentrantReadWriteLock();
    
    /**
     * 注册任务配置
     */
    public void registerTaskConfig(TaskConfig taskConfig) {
        validateTaskConfig(taskConfig);
        
        String version = generateVersion();
        configurationStorage.saveTaskConfig(taskConfig.getTaskName(), taskConfig, version);
        
        // 更新缓存
        cacheLock.writeLock().lock();
        try {
            taskConfigCache.put(taskConfig.getTaskName(), taskConfig);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        log.info("任务配置注册成功: {}, 版本: {}", taskConfig.getTaskName(), version);
    }
    
    /**
     * 更新任务配置
     */
    public void updateTaskConfig(TaskConfig taskConfig) {
        String taskName = taskConfig.getTaskName();
        
        // 检查任务是否存在
        if (getTaskConfig(taskName) == null) {
            throw new ArchiveException("任务不存在: " + taskName,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        validateTaskConfig(taskConfig);
        
        String version = generateVersion();
        configurationStorage.saveTaskConfig(taskName, taskConfig, version);
        
        // 更新缓存
        cacheLock.writeLock().lock();
        try {
            taskConfigCache.put(taskName, taskConfig);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        log.info("任务配置更新成功: {}, 版本: {}", taskName, version);
    }
    
    /**
     * 获取任务配置
     */
    public TaskConfig getTaskConfig(String taskName) {
        // 先从缓存获取
        cacheLock.readLock().lock();
        try {
            TaskConfig cached = taskConfigCache.get(taskName);
            if (cached != null) {
                return cached;
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        
        // 从存储获取
        TaskConfig config = configurationStorage.getTaskConfig(taskName);
        if (config != null) {
            // 更新缓存
            cacheLock.writeLock().lock();
            try {
                taskConfigCache.put(taskName, config);
            } finally {
                cacheLock.writeLock().unlock();
            }
        }
        
        return config;
    }
    
    /**
     * 删除任务配置
     */
    public void deleteTaskConfig(String taskName) {
        configurationStorage.deleteTaskConfig(taskName);
        
        // 清除缓存
        cacheLock.writeLock().lock();
        try {
            taskConfigCache.remove(taskName);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        log.info("任务配置删除成功: {}", taskName);
    }
    
    /**
     * 获取所有任务配置
     */
    public List<TaskConfig> getAllTaskConfigs() {
        List<String> taskNames = configurationStorage.getAllTaskNames();
        return taskNames.stream()
                .map(this::getTaskConfig)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 回滚任务配置
     */
    public void rollbackTaskConfig(String taskName, String version) {
        configurationStorage.rollbackToVersion(taskName, version);
        
        // 清除缓存，强制重新加载
        cacheLock.writeLock().lock();
        try {
            taskConfigCache.remove(taskName);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        log.info("任务配置回滚成功: {}, 版本: {}", taskName, version);
    }
    
    /**
     * 备份任务配置
     */
    public String backupTaskConfig(String taskName) {
        return configurationStorage.backupTaskConfig(taskName);
    }
    
    /**
     * 从备份恢复任务配置
     */
    public void restoreTaskConfig(String taskName, String backupId) {
        configurationStorage.restoreFromBackup(taskName, backupId);
        
        // 清除缓存，强制重新加载
        cacheLock.writeLock().lock();
        try {
            taskConfigCache.remove(taskName);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        log.info("任务配置恢复成功: {}, 备份ID: {}", taskName, backupId);
    }
    
    private void validateTaskConfig(TaskConfig taskConfig) {
        if (taskConfig == null) {
            throw new ArchiveException("任务配置不能为空",
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        if (!StringUtils.hasText(taskConfig.getTaskName())) {
            throw new ArchiveException("任务名称不能为空",
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        // 其他验证逻辑...
    }
    
    private String generateVersion() {
        return "v" + System.currentTimeMillis();
    }
}
```

#### 3.2.4 配置迁移工具

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/migration/ConfigMigrationTool.java`

```java
@Component
public class ConfigMigrationTool {
    
    private final ConfigurationStorage configurationStorage;
    private final ObjectMapper objectMapper;
    
    /**
     * 从大JSON配置迁移到独立配置项
     */
    public MigrationResult migrateFromLegacyConfig(String legacyConfigJson) {
        MigrationResult result = new MigrationResult();
        
        try {
            // 解析原有配置
            LegacyArchiveConfig legacyConfig = objectMapper.readValue(legacyConfigJson, LegacyArchiveConfig.class);
            
            // 迁移每个任务配置
            for (TaskConfig taskConfig : legacyConfig.getTasks()) {
                try {
                    String version = generateMigrationVersion();
                    configurationStorage.saveTaskConfig(taskConfig.getTaskName(), taskConfig, version);
                    result.addSuccess(taskConfig.getTaskName(), version);
                    
                    log.info("任务配置迁移成功: {}, 版本: {}", taskConfig.getTaskName(), version);
                } catch (Exception e) {
                    result.addFailure(taskConfig.getTaskName(), e.getMessage());
                    log.error("任务配置迁移失败: {}", taskConfig.getTaskName(), e);
                }
            }
            
            // 备份原有配置
            if (result.hasSuccess()) {
                backupLegacyConfig(legacyConfigJson);
            }
            
        } catch (Exception e) {
            result.setOverallError("配置解析失败: " + e.getMessage());
            log.error("配置迁移失败", e);
        }
        
        return result;
    }
    
    /**
     * 增量迁移
     */
    public MigrationResult incrementalMigration(List<TaskConfig> taskConfigs) {
        MigrationResult result = new MigrationResult();
        
        for (TaskConfig taskConfig : taskConfigs) {
            try {
                // 检查是否已存在
                TaskConfig existing = configurationStorage.getTaskConfig(taskConfig.getTaskName());
                
                if (existing == null) {
                    // 新增任务
                    String version = generateMigrationVersion();
                    configurationStorage.saveTaskConfig(taskConfig.getTaskName(), taskConfig, version);
                    result.addSuccess(taskConfig.getTaskName(), version);
                } else {
                    // 更新任务（如果有变化）
                    if (!isConfigEqual(existing, taskConfig)) {
                        String version = generateMigrationVersion();
                        configurationStorage.saveTaskConfig(taskConfig.getTaskName(), taskConfig, version);
                        result.addUpdate(taskConfig.getTaskName(), version);
                    } else {
                        result.addSkip(taskConfig.getTaskName(), "配置无变化");
                    }
                }
            } catch (Exception e) {
                result.addFailure(taskConfig.getTaskName(), e.getMessage());
                log.error("增量迁移失败: {}", taskConfig.getTaskName(), e);
            }
        }
        
        return result;
    }
    
    /**
     * 回滚迁移
     */
    public void rollbackMigration(String backupId) {
        try {
            // 恢复原有配置
            restoreLegacyConfig(backupId);
            
            // 清理迁移后的配置
            List<String> taskNames = configurationStorage.getAllTaskNames();
            for (String taskName : taskNames) {
                configurationStorage.deleteTaskConfig(taskName);
            }
            
            log.info("迁移回滚成功, 备份ID: {}", backupId);
        } catch (Exception e) {
            throw new ArchiveException("迁移回滚失败: " + e.getMessage(), e,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
    }
    
    /**
     * 验证迁移结果
     */
    public ValidationResult validateMigration(String legacyConfigJson) {
        ValidationResult result = new ValidationResult();
        
        try {
            LegacyArchiveConfig legacyConfig = objectMapper.readValue(legacyConfigJson, LegacyArchiveConfig.class);
            
            for (TaskConfig legacyTask : legacyConfig.getTasks()) {
                TaskConfig migratedTask = configurationStorage.getTaskConfig(legacyTask.getTaskName());
                
                if (migratedTask == null) {
                    result.addError("任务配置缺失: " + legacyTask.getTaskName());
                } else if (!isConfigEqual(legacyTask, migratedTask)) {
                    result.addError("任务配置不一致: " + legacyTask.getTaskName());
                } else {
                    result.addSuccess("任务配置验证通过: " + legacyTask.getTaskName());
                }
            }
        } catch (Exception e) {
            result.addError("验证过程异常: " + e.getMessage());
        }
        
        return result;
    }
    
    private void backupLegacyConfig(String legacyConfigJson) {
        // 实现原有配置备份逻辑
    }
    
    private void restoreLegacyConfig(String backupId) {
        // 实现原有配置恢复逻辑
    }
    
    private boolean isConfigEqual(TaskConfig config1, TaskConfig config2) {
        // 实现配置比较逻辑
        try {
            String json1 = objectMapper.writeValueAsString(config1);
            String json2 = objectMapper.writeValueAsString(config2);
            return json1.equals(json2);
        } catch (Exception e) {
            return false;
        }
    }
    
    private String generateMigrationVersion() {
        return "migration_v" + System.currentTimeMillis();
    }
}
```

#### 3.2.5 迁移结果模型

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/migration/MigrationResult.java`

```java
public class MigrationResult {
    private final List<MigrationItem> successItems = new ArrayList<>();
    private final List<MigrationItem> failureItems = new ArrayList<>();
    private final List<MigrationItem> updateItems = new ArrayList<>();
    private final List<MigrationItem> skipItems = new ArrayList<>();
    private String overallError;
    
    public void addSuccess(String taskName, String version) {
        successItems.add(new MigrationItem(taskName, version, "SUCCESS"));
    }
    
    public void addFailure(String taskName, String error) {
        failureItems.add(new MigrationItem(taskName, null, error));
    }
    
    public void addUpdate(String taskName, String version) {
        updateItems.add(new MigrationItem(taskName, version, "UPDATED"));
    }
    
    public void addSkip(String taskName, String reason) {
        skipItems.add(new MigrationItem(taskName, null, reason));
    }
    
    public boolean hasSuccess() {
        return !successItems.isEmpty() || !updateItems.isEmpty();
    }
    
    public boolean hasFailure() {
        return !failureItems.isEmpty() || overallError != null;
    }
    
    public String getSummary() {
        return String.format("迁移结果 - 成功: %d, 更新: %d, 跳过: %d, 失败: %d",
            successItems.size(), updateItems.size(), skipItems.size(), failureItems.size());
    }
    
    // getter/setter方法...
    
    public static class MigrationItem {
        private String taskName;
        private String version;
        private String message;
        
        // 构造函数和getter/setter方法...
    }
}
```

### 3.3 迁移策略

#### 3.3.1 迁移步骤
1. **准备阶段**：备份现有配置，验证新存储系统
2. **迁移阶段**：执行配置拆分，验证迁移结果
3. **切换阶段**：更新系统配置，切换到新存储
4. **验证阶段**：全面测试，确保功能正常
5. **清理阶段**：清理旧配置，完成迁移

#### 3.3.2 回滚策略
- 保留原有配置备份
- 提供一键回滚功能
- 支持部分回滚（指定任务）
- 监控迁移后系统状态

## 4. Preview功能完善增强

### 4.1 改造概述

#### 4.1.1 改造目标
- 实现指定节点的预览功能
- 构建完整的查询链路分析
- 提供SQL生成和执行计划预览
- 支持预览结果的缓存和导出

#### 4.1.2 改造范围
- API层：ArchiveApiService
- 核心层：新增PreviewManager
- 分析层：新增PathAnalyzer、QueryChainBuilder
- 结果层：PreviewResult模型

### 4.2 详细改造方案

#### 4.2.1 预览管理器

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/preview/PreviewManager.java`

```java
@Component
public class PreviewManager {
    
    private final PathAnalyzer pathAnalyzer;
    private final QueryChainBuilder queryChainBuilder;
    private final QueryExecutor queryExecutor;
    private final PluginManager pluginManager;
    private final PreviewResultCache previewCache;
    
    /**
     * 执行节点预览
     */
    public PreviewResult previewNode(PreviewRequest request) {
        try {
            // 1. 验证请求参数
            validatePreviewRequest(request);
            
            // 2. 检查缓存
            String cacheKey = generateCacheKey(request);
            PreviewResult cached = previewCache.get(cacheKey);
            if (cached != null && !cached.isExpired()) {
                log.info("返回缓存的预览结果: {}", request.getTaskName());
                return cached;
            }
            
            // 3. 分析节点路径
            NodePath nodePath = pathAnalyzer.analyzePath(request.getTaskName(), request.getTargetKey());
            
            // 4. 构建查询链路
            QueryChain queryChain = queryChainBuilder.buildQueryChain(nodePath, request);
            
            // 5. 执行预览查询
            PreviewResult result = executePreview(queryChain, request);
            
            // 6. 缓存结果
            previewCache.put(cacheKey, result);
            
            log.info("节点预览完成: {} -> {}, 耗时: {}ms", 
                request.getTaskName(), request.getTargetKey(), result.getExecutionTime());
            
            return result;
        } catch (Exception e) {
            log.error("节点预览失败: {} -> {}", request.getTaskName(), request.getTargetKey(), e);
            throw new ArchiveException("节点预览失败: " + e.getMessage(), e,
                ArchiveException.ErrorType.EXECUTION_ERROR);
        }
    }
    
    /**
     * 执行预览查询
     */
    private PreviewResult executePreview(QueryChain queryChain, PreviewRequest request) {
        long startTime = System.currentTimeMillis();
        PreviewResult result = new PreviewResult();
        result.setTaskName(request.getTaskName());
        result.setTargetKey(request.getTargetKey());
        result.setNodePath(queryChain.getNodePath());
        
        List<QueryStepResult> stepResults = new ArrayList<>();
        Map<String, Object> contextData = new HashMap<>();
        
        // 逐步执行查询链路
        for (QueryStep step : queryChain.getSteps()) {
            QueryStepResult stepResult = executeQueryStep(step, contextData, request);
            stepResults.add(stepResult);
            
            // 将当前步骤结果作为下一步骤的输入
            contextData.put(step.getNodeKey(), stepResult.getQueryResult());
            
            // 检查是否达到最大预览行数
            if (stepResult.getActualRows() >= request.getMaxRows()) {
                stepResult.addWarning("达到最大预览行数限制: " + request.getMaxRows());
                break;
            }
        }
        
        result.setQuerySteps(stepResults);
        result.setExecutionTime(System.currentTimeMillis() - startTime);
        result.setTotalEstimatedRows(calculateTotalRows(stepResults));
        
        return result;
    }
    
    /**
     * 执行单个查询步骤
     */
    private QueryStepResult executeQueryStep(QueryStep step, Map<String, Object> contextData, PreviewRequest request) {
        QueryStepResult stepResult = new QueryStepResult();
        stepResult.setNodeKey(step.getNodeKey());
        stepResult.setTableName(step.getTableName());
        stepResult.setSql(step.getSql());
        stepResult.setParameters(step.getParameters());
        
        try {
            // 执行插件前置处理
            ArchiveContext previewContext = createPreviewContext(step, request);
            previewContext.setPreviewMode(true);
            
            boolean shouldContinue = pluginManager.executePluginsWithInterrupt(
                ArchiveEvent.BEFORE_QUERY, previewContext);
            
            if (!shouldContinue) {
                stepResult.addWarning("插件要求跳过查询");
                return stepResult;
            }
            
            // 执行查询
            long queryStart = System.currentTimeMillis();
            List<Map<String, Object>> queryResult = queryExecutor.query(previewContext);
            long queryTime = System.currentTimeMillis() - queryStart;
            
            stepResult.setQueryResult(queryResult);
            stepResult.setActualRows(queryResult.size());
            stepResult.setQueryTime(queryTime);
            
            // 生成执行计划
            if (request.isIncludeExecutionPlan()) {
                String executionPlan = generateExecutionPlan(step.getSql(), step.getParameters());
                stepResult.setExecutionPlan(executionPlan);
            }
            
            // 执行插件后置处理
            pluginManager.executePlugins(ArchiveEvent.AFTER_QUERY, previewContext);
            
        } catch (Exception e) {
            stepResult.addError("查询执行失败: " + e.getMessage());
            log.error("预览查询步骤失败: {}", step.getNodeKey(), e);
        }
        
        return stepResult;
    }
    
    /**
     * 生成执行计划
     */
    private String generateExecutionPlan(String sql, Object[] parameters) {
        try {
            String explainSql = "EXPLAIN " + sql;
            // 这里需要根据实际数据库类型调整
            // 返回执行计划的文本表示
            return "EXPLAIN结果..."; // 简化实现
        } catch (Exception e) {
            return "执行计划生成失败: " + e.getMessage();
        }
    }
    
    private void validatePreviewRequest(PreviewRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("预览请求不能为空");
        }
        if (!StringUtils.hasText(request.getTaskName())) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (!StringUtils.hasText(request.getTargetKey())) {
            throw new IllegalArgumentException("目标节点不能为空");
        }
        if (request.getMaxRows() <= 0) {
            request.setMaxRows(1000); // 默认值
        }
    }
    
    private String generateCacheKey(PreviewRequest request) {
        return String.format("preview:%s:%s:%d:%s", 
            request.getTaskName(), 
            request.getTargetKey(), 
            request.getMaxRows(),
            request.getParametersHash());
    }
    
    private ArchiveContext createPreviewContext(QueryStep step, PreviewRequest request) {
        // 创建预览专用的归档上下文
        ArchiveContext context = new ArchiveContext(
            step.getArchiveNode(),
            request.getTaskName(),
            step.getDatasourceName()
        );
        context.setPreviewMode(true);
        return context;
    }
    
    private long calculateTotalRows(List<QueryStepResult> stepResults) {
        return stepResults.stream()
                .mapToLong(QueryStepResult::getActualRows)
                .sum();
    }
}
```

#### 4.2.2 路径分析器

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/preview/PathAnalyzer.java`

```java
@Component
public class PathAnalyzer {
    
    private final ArchiveConfigManager configManager;
    
    /**
     * 分析从根节点到目标节点的路径
     */
    public NodePath analyzePath(String taskName, String targetKey) {
        TaskConfig taskConfig = configManager.getTaskConfig(taskName);
        if (taskConfig == null) {
            throw new ArchiveException("任务不存在: " + taskName,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        // 查找根节点
        ArchiveNode rootNode = findRootNode(taskConfig);
        if (rootNode == null) {
            throw new ArchiveException("任务没有根节点: " + taskName,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        // 查找目标节点路径
        List<ArchiveNode> path = findPathToTarget(rootNode, targetKey, new ArrayList<>());
        if (path.isEmpty()) {
            throw new ArchiveException("目标节点不存在或不可达: " + targetKey,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        // 验证路径有效性
        validatePath(path);
        
        NodePath nodePath = new NodePath();
        nodePath.setTaskName(taskName);
        nodePath.setTargetKey(targetKey);
        nodePath.setPath(path);
        nodePath.setPathKeys(path.stream().map(ArchiveNode::getNodeKey).collect(Collectors.toList()));
        
        log.info("路径分析完成: {} -> {}, 路径长度: {}", taskName, targetKey, path.size());
        
        return nodePath;
    }
    
    /**
     * 查找根节点
     */
    private ArchiveNode findRootNode(TaskConfig taskConfig) {
        List<ArchiveNode> nodes = taskConfig.getNodes();
        if (CollectionUtils.isEmpty(nodes)) {
            return null;
        }
        
        // 假设第一个节点是根节点，或者通过某种标识确定根节点
        return nodes.get(0);
    }
    
    /**
     * 递归查找到目标节点的路径
     */
    private List<ArchiveNode> findPathToTarget(ArchiveNode currentNode, String targetKey, List<ArchiveNode> currentPath) {
        // 检查循环依赖
        if (currentPath.contains(currentNode)) {
            log.warn("检测到循环依赖: {}", currentNode.getNodeKey());
            return new ArrayList<>();
        }
        
        // 添加当前节点到路径
        List<ArchiveNode> newPath = new ArrayList<>(currentPath);
        newPath.add(currentNode);
        
        // 检查是否找到目标节点
        if (targetKey.equals(currentNode.getNodeKey())) {
            return newPath;
        }
        
        // 递归搜索子节点
        if (currentNode.getChildren() != null) {
            for (ArchiveNode child : currentNode.getChildren()) {
                List<ArchiveNode> foundPath = findPathToTarget(child, targetKey, newPath);
                if (!foundPath.isEmpty()) {
                    return foundPath;
                }
            }
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 验证路径有效性
     */
    private void validatePath(List<ArchiveNode> path) {
        for (int i = 0; i < path.size() - 1; i++) {
            ArchiveNode parent = path.get(i);
            ArchiveNode child = path.get(i + 1);
            
            // 验证父子关系
            if (parent.getChildren() == null || !parent.getChildren().contains(child)) {
                throw new ArchiveException("路径中断: " + parent.getNodeKey() + " -> " + child.getNodeKey(),
                    ArchiveException.ErrorType.CONFIG_ERROR);
            }
            
            // 验证关联关系
            if (CollectionUtils.isEmpty(child.getRelations())) {
                log.warn("子节点缺少关联关系配置: {}", child.getNodeKey());
            }
        }
    }
}
```

#### 4.2.3 查询链路构建器

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/preview/QueryChainBuilder.java`

```java
@Component
public class QueryChainBuilder {
    
    /**
     * 构建查询链路
     */
    public QueryChain buildQueryChain(NodePath nodePath, PreviewRequest request) {
        QueryChain queryChain = new QueryChain();
        queryChain.setNodePath(nodePath.getPathKeys());
        
        List<QueryStep> steps = new ArrayList<>();
        List<ArchiveNode> path = nodePath.getPath();
        
        for (int i = 0; i < path.size(); i++) {
            ArchiveNode node = path.get(i);
            QueryStep step = buildQueryStep(node, i == 0 ? null : path.get(i - 1), request);
            steps.add(step);
        }
        
        queryChain.setSteps(steps);
        
        log.info("查询链路构建完成: {}, 步骤数: {}", nodePath.getTaskName(), steps.size());
        
        return queryChain;
    }
    
    /**
     * 构建单个查询步骤
     */
    private QueryStep buildQueryStep(ArchiveNode node, ArchiveNode parentNode, PreviewRequest request) {
        QueryStep step = new QueryStep();
        step.setNodeKey(node.getNodeKey());
        step.setTableName(node.getTableName());
        step.setArchiveNode(node);
        step.setDatasourceName(node.getDatasourceName());
        
        // 构建SQL语句
        String sql = buildStepSql(node, parentNode, request);
        step.setSql(sql);
        
        // 构建参数
        Object[] parameters = buildStepParameters(node, parentNode, request);
        step.setParameters(parameters);
        
        // 估算数据量
        long estimatedRows = estimateRows(node, request);
        step.setEstimatedRows(estimatedRows);
        
        return step;
    }
    
    /**
     * 构建步骤SQL
     */
    private String buildStepSql(ArchiveNode node, ArchiveNode parentNode, PreviewRequest request) {
        StringBuilder sql = new StringBuilder();
        
        // SELECT子句
        sql.append("SELECT ");
        if (node.getQueryColumns() != null && !node.getQueryColumns().isEmpty()) {
            sql.append(String.join(", ", node.getQueryColumns()));
        } else {
            sql.append("*");
        }
        
        // FROM子句
        sql.append(" FROM ").append(node.getTableName());
        
        // WHERE子句
        List<String> whereConditions = new ArrayList<>();
        
        // 添加节点自身条件
        if (StringUtils.hasText(node.getCondition())) {
            whereConditions.add(node.getCondition());
        }
        
        // 添加父节点关联条件
        if (parentNode != null && node.getRelations() != null) {
            for (ColumnRelation relation : node.getRelations()) {
                String relationCondition = String.format("%s IN (SELECT %s FROM %s WHERE %s)",
                    relation.getCurrentColumn(),
                    relation.getParentColumn(),
                    parentNode.getTableName(),
                    parentNode.getCondition());
                whereConditions.add(relationCondition);
            }
        }
        
        if (!whereConditions.isEmpty()) {
            sql.append(" WHERE ").append(String.join(" AND ", whereConditions));
        }
        
        // ORDER BY子句
        if (StringUtils.hasText(node.getOrderBy())) {
            sql.append(" ORDER BY ").append(node.getOrderBy());
        }
        
        // LIMIT子句
        sql.append(" LIMIT ").append(request.getMaxRows());
        
        return sql.toString();
    }
    
    /**
     * 构建步骤参数
     */
    private Object[] buildStepParameters(ArchiveNode node, ArchiveNode parentNode, PreviewRequest request) {
        List<Object> parameters = new ArrayList<>();
        
        // 添加节点条件参数
        if (request.getParameters() != null) {
            request.getParameters().forEach((key, value) -> {
                if (node.getCondition() != null && node.getCondition().contains(":" + key)) {
                    parameters.add(value);
                }
            });
        }
        
        return parameters.toArray();
    }
    
    /**
     * 估算数据量
     */
    private long estimateRows(ArchiveNode node, PreviewRequest request) {
        // 这里可以实现更复杂的估算逻辑
        // 比如基于表统计信息、历史数据等
        return 10000L; // 简化实现
    }
}
```

#### 4.2.4 预览结果模型

**新增文件**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/preview/PreviewResult.java`

```java
public class PreviewResult {
    private String taskName;
    private String targetKey;
    private List<String> nodePath;
    private List<QueryStepResult> querySteps;
    private long totalEstimatedRows;
    private long executionTime;
    private List<String> warnings;
    private List<String> errors;
    private long timestamp;
    private long ttl = 300000; // 5分钟缓存
    
    public PreviewResult() {
        this.warnings = new ArrayList<>();
        this.errors = new ArrayList<>();
        this.timestamp = System.currentTimeMillis();
    }
    
    public boolean isExpired() {
        return System.currentTimeMillis() - timestamp > ttl;
    }
    
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }
    
    public void addError(String error) {
        this.errors.add(error);
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    // getter/setter方法...
}
```

**查询步骤结果模型**
```java
public class QueryStepResult {
    private String nodeKey;
    private String tableName;
    private String sql;
    private Object[] parameters;
    private List<Map<String, Object>> queryResult;
    private long estimatedRows;
    private int actualRows;
    private long queryTime;
    private String executionPlan;
    private List<String> warnings;
    private List<String> errors;
    
    public QueryStepResult() {
        this.warnings = new ArrayList<>();
        this.errors = new ArrayList<>();
    }
    
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }
    
    public void addError(String error) {
        this.errors.add(error);
    }
    
    // getter/setter方法...
}
```

#### 4.2.5 预览API扩展

**改造位置**：`scp-archive-sdk/src/main/java/com/shizhuang/duapp/scp/framework/archive/core/api/ArchiveApiService.java`

```java
@Slf4j
public class ArchiveApiService {
    
    private final ArchiveConfigManager configManager;
    private final ArchiveMonitor monitor;
    private final PreviewManager previewManager;
    
    // 现有方法...
    
    /**
     * 预览指定节点
     */
    public PreviewResult previewNode(PreviewRequest request) {
        try {
            log.info("开始节点预览: {} -> {}", request.getTaskName(), request.getTargetKey());
            
            PreviewResult result = previewManager.previewNode(request);
            
            log.info("节点预览完成: {} -> {}, 耗时: {}ms", 
                request.getTaskName(), request.getTargetKey(), result.getExecutionTime());
            
            return result;
        } catch (Exception e) {
            log.error("节点预览失败: {} -> {}", request.getTaskName(), request.getTargetKey(), e);
            throw e;
        }
    }
    
    /**
     * 获取任务的节点树结构
     */
    public TaskNodeTree getTaskNodeTree(String taskName) {
        TaskConfig taskConfig = configManager.getTaskConfig(taskName);
        if (taskConfig == null) {
            throw new ArchiveException("任务不存在: " + taskName,
                ArchiveException.ErrorType.CONFIG_ERROR);
        }
        
        TaskNodeTree tree = new TaskNodeTree();
        tree.setTaskName(taskName);
        tree.setRootNodes(taskConfig.getNodes());
        
        return tree;
    }
    
    /**
     * 验证预览请求
     */
    public ValidationResult validatePreviewRequest(PreviewRequest request) {
        ValidationResult result = new ValidationResult();
        
        try {
            // 验证任务存在
            TaskConfig taskConfig = configManager.getTaskConfig(request.getTaskName());
            if (taskConfig == null) {
                result.addError("任务不存在: " + request.getTaskName());
                return result;
            }
            
            // 验证目标节点存在
            PathAnalyzer pathAnalyzer = new PathAnalyzer();
            NodePath nodePath = pathAnalyzer.analyzePath(request.getTaskName(), request.getTargetKey());
            
            result.addSuccess("预览请求验证通过");
            
        } catch (Exception e) {
            result.addError("预览请求验证失败: " + e.getMessage());
        }
        
        return result;
    }
}
```

### 4.3 配置示例

```yaml
# 预览请求示例
preview:
  taskName: "order_archive"
  targetKey: "order_item"
  maxRows: 1000
  includeExecutionPlan: true
  parameters:
    timeRange: "30 days"
    minAmount: 100

# 预览配置
scp:
  archive:
    preview:
      enabled: true
      cacheEnabled: true
      cacheTtl: 300000          # 缓存5分钟
      maxRows: 1000             # 默认最大行数
      queryTimeout: 30000       # 查询超时30秒
      includeExecutionPlan: false # 默认不包含执行计划
```

## 5. 实施计划和风险控制

### 5.1 实施阶段规划

#### 5.1.1 第一阶段：多数据源支持（2周）
- 第1周：数据模型扩展、接口设计
- 第2周：实现类开发、单元测试

#### 5.1.2 第二阶段：Sharding能力（3周）
- 第1周：分片管理器、表达式解析器
- 第2周：分片插件接口、执行器改造
- 第3周：集成测试、性能优化

#### 5.1.3 第三阶段：元数据存储迁移（2周）
- 第1周：新存储接口、迁移工具
- 第2周：迁移执行、验证测试

#### 5.1.4 第四阶段：Preview功能完善（3周）
- 第1周：路径分析器、查询链路构建器
- 第2周：预览管理器、结果模型
- 第3周：API集成、前端对接

### 5.2 风险控制措施

#### 5.2.1 技术风险
- **兼容性风险**：保持向后兼容，提供配置开关
- **性能风险**：增加性能监控，设置合理的超时和限制
- **数据风险**：完善的备份和回滚机制

#### 5.2.2 业务风险
- **服务中断风险**：采用灰度发布，逐步切换
- **数据丢失风险**：多重备份，严格的验证流程
- **功能回归风险**：全面的回归测试

### 5.3 测试策略

#### 5.3.1 单元测试
- 覆盖率要求：80%以上
- 重点测试：核心逻辑、边界条件、异常处理

#### 5.3.2 集成测试
- 多数据源场景测试
- 分片功能端到端测试
- 配置迁移完整流程测试
- Preview功能各种路径测试

#### 5.3.3 性能测试
- 大数据量场景测试
- 并发访问测试
- 内存和CPU使用率测试

---

**文档版本**：v1.0  
**编写日期**：2024年12月  
**维护团队**：SCP-Archive开发团队 