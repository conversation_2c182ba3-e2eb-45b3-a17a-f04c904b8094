# 归档节点JSON结构说明

## 文件概览

根据 `添加节点.json` 的数据结构，生成了以下三个JSON文件：

### 1. 添加非根节点.json
- **功能**: 用于添加非根节点（子节点）
- **特点**: 
  - 移除了根节点特有的字段（如索引扫描、分表配置等）
  - 添加了父节点ID输入字段
  - 保留了关联关系配置，这是子节点的核心功能
  - 使用现有的接口：`/scm/rulecenter/archive-management/create-node`

### 2. 编辑节点.json
- **功能**: 用于编辑现有节点
- **特点**:
  - 简化了表单结构，只保留核心编辑字段
  - 修改API为更新接口：`/scm/rulecenter/archive-management/update-node`
  - 移除了复杂的联动逻辑，适合编辑场景
  - 保留了基本的配置项：数据源、数据库、表名、是否归档、默认值过滤、查询条件

### 3. 归档详情.json（已完善）
- **功能**: 展示归档节点的详细信息
- **特点**:
  - 补全了所有字段展示，包括：
    - 基础信息：表名、数据源名称、数据库名称
    - 配置信息：查询条件、索引列、排序方式、索引值范围
    - 归档设置：是否归档、默认值过滤、归档顺序（非根节点显示）
    - 关联关系（非根节点显示）
    - 分表配置：启用分表、分表表达式、分表插件类、分表字段
    - 索引配置：索引扫描、保留天数
    - 系统信息：节点ID、父节点ID、任务ID、状态、创建时间、更新时间
  - 根据是否为根节点（`root`字段）动态显示/隐藏相关字段
  - 提供了编辑和删除操作按钮

## 接口使用规范

所有JSON结构都严格使用示例中已有的接口：

### 数据源相关
- `GET /scm/rulecenter/archive/fetch-data-sources` - 获取数据源列表
- `GET /scm/rulecenter/archive/fetch-data-bases` - 获取数据库列表
- `POST /scm/rulecenter/archive/fetch-tables` - 获取表列表
- `POST /scm/rulecenter/archive/fetch-columns` - 获取字段列表

### 节点管理
- `POST /scm/rulecenter/archive-management/create-node` - 创建节点
- `POST /scm/rulecenter/archive-management/update-node` - 更新节点

### 预览功能
- `POST /scm/rulecenter/archive/preview` - 预览SQL

## 字段说明

### 根节点特有字段
- `indexColumn` - 索引列
- `orderBy` - 排序方式
- `indexValue` - 索引值范围
- `enableScan` - 索引扫描
- `reserveDays` - 保留天数
- `shardingEnabled` - 启用分表
- `shardingExpression` - 分表表达式
- `shardingPluginClass` - 分表插件类
- `shardingField` - 分表字段

### 非根节点特有字段
- `parentNodeId` - 父节点ID
- `relations` - 关联关系
- `archiveType` - 归档顺序

### 通用字段
- `datasourceName` - 数据源名称
- `databaseName` - 数据库名称
- `tableName` - 表名
- `condition` - 查询条件
- `isArchive` - 是否归档
- `filterDefaultValue` - 默认值过滤
- `nodeId` - 节点ID
- `taskId` - 任务ID
- `status` - 状态
- `createTime` - 创建时间
- `updateTime` - 更新时间

## 使用场景

1. **添加根节点**: 使用 `添加节点.json`
2. **添加子节点**: 使用 `添加非根节点.json`
3. **编辑节点**: 使用 `编辑节点.json`
4. **查看详情**: 使用 `归档详情.json`

所有JSON结构都遵循了现有的接口规范，没有引入新的接口调用。 