{"type": "dialog", "actions": [{"actionType": "cancel", "id": "u:c57e58a3902b", "label": "取消", "type": "button"}, {"id": "u:53954b7f8597", "label": "确认", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:daf300074416", "outputVar": "submitResult"}, {"actionType": "closeDialog", "componentId": "u:4523915379be", "expression": "${event.data.submitResult.responseStatus==200}", "stopPropagation": "${event.data.submitResult.responseStatus!=200}"}, {"actionType": "reload", "componentId": "u:e57f0b55f306", "expression": "${event.data.submitResult.responseStatus==200}"}]}}, "reload": "chainSearchPage", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "保存节点配置成功"}, "method": "post", "url": "/scm/rulecenter/archive-management/update-node-config"}, "body": [{"formItemProps": {"hidden": true}, "id": "u:64e15e7bb7a2", "initialValue": "${id}", "label": "ID", "name": "id", "trim": true, "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:7683a330260c", "initialValue": "${taskId}", "label": "任务ID", "name": "taskId", "trim": true, "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:44c34e14204b", "initialValue": "${nodeId}", "label": "节点ID", "name": "nodeId", "trim": true, "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:8dbe8156d262", "initialValue": "${parentNodeId}", "label": "父节点ID", "name": "parentNodeId", "trim": true, "type": "text"}, {"fieldProps": {"placeholder": "请输入表名"}, "formItemProps": {"rules": [{"required": true, "message": "请输入表名"}]}, "id": "u:cdabe0669d30", "label": "表名", "name": "tableName", "trim": true, "type": "text"}, {"fieldProps": {"placeholder": "请输入查询条件，如：id > 0 and status = 1"}, "id": "u:c3186e116846", "label": "查询条件", "name": "condition", "tooltip": "支持SQL WHERE条件语法", "trim": true, "type": "textarea"}, {"fieldProps": {"placeholder": "请输入数据源名称"}, "id": "u:be6e6cd52dce", "label": "数据源名称", "name": "datasourceName", "trim": true, "type": "text"}, {"fieldProps": {"placeholder": "请输入数据库名称"}, "id": "u:f331274f7043", "label": "数据库名称", "name": "databaseName", "trim": true, "type": "text"}, {"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "formItemProps": {"rules": [{"required": true, "message": "请选择是否归档"}]}, "id": "u:d27c3e7bc114", "initialValue": true, "label": "是否归档", "name": "isArchive", "tooltip": "是否对该表进行归档操作", "type": "select"}, {"fieldProps": {"placeholder": "请输入查询字段，多个字段用逗号分隔"}, "id": "u:8b24a2aca615", "label": "查询字段", "name": "queryColumns", "tooltip": "指定查询的字段列表，多个字段用逗号分隔", "trim": true, "type": "textarea"}, {"fieldProps": {"placeholder": "请输入主键列名"}, "formItemProps": {"rules": [{"required": true, "message": "请输入主键列名"}]}, "id": "u:2985a94acf89", "label": "主键列", "name": "primaryKeyColumn", "trim": true, "type": "text"}, {"fieldProps": {"options": [{"label": "激活", "value": 1}, {"label": "禁用", "value": 0}]}, "formItemProps": {"rules": [{"required": true, "message": "请选择节点状态"}]}, "id": "u:723f3e973c3d", "initialValue": 1, "label": "节点状态", "name": "status", "type": "select"}, {"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "id": "u:60c9df7c54e6", "initialValue": false, "label": "是否为根节点", "name": "rootNode", "type": "select"}, {"fieldProps": {"placeholder": "请输入索引字段名"}, "formItemProps": {"hidden": "${enableScan != true}"}, "id": "u:d9155a03bd74", "label": "索引字段", "name": "indexColumn", "tooltip": "用于归档的索引字段", "trim": true, "type": "text"}, {"fieldProps": {"placeholder": "请输入排序字段，如：id desc"}, "id": "u:b4a9bb715634", "label": "排序字段", "name": "orderBy", "tooltip": "数据排序方式", "trim": true, "type": "text"}, {"fieldProps": {"options": [{"label": "先归档子节点", "value": 1}, {"label": "先归档父节点", "value": 2}]}, "formItemProps": {"rules": [{"required": true, "message": "请选择归档类型"}]}, "id": "u:976ec902828b", "initialValue": 1, "label": "归档类型", "name": "archiveType", "tooltip": "先归档父节点表示父节点全部归档后才会归档子节点", "type": "select"}, {"fieldProps": {"options": [{"label": "不过滤", "value": 0}, {"label": "过滤", "value": 1}]}, "id": "u:5b0cfc063328", "initialValue": 0, "label": "过滤默认值", "name": "filterDefaultValue", "tooltip": "查询或者删除的时候，不考虑0或者空字符串", "type": "select"}, {"fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "id": "u:c938fee8cc5a", "initialValue": false, "label": "是否启用分片", "name": "shardingEnabled", "tooltip": "是否启用数据分片处理", "type": "select", "linkage": [{"event": "onChange", "resetKeys": ["shardingExpression", "shardingPluginClass", "shardingField"]}]}, {"fieldProps": {"placeholder": "请输入分片表达式"}, "formItemProps": {"hidden": "${shardingEnabled != true}"}, "id": "u:33a5ab78d907", "label": "分片表达式", "name": "shardingExpression", "tooltip": "数据分片的表达式", "trim": true, "type": "text"}, {"fieldProps": {"placeholder": "请输入分片插件全类名"}, "formItemProps": {"hidden": "${shardingEnabled != true}"}, "id": "u:19e9f24a8266", "label": "分片插件类", "name": "shardingPluginClass", "tooltip": "分片处理插件类的全限定名", "trim": true, "type": "text"}, {"fieldProps": {"placeholder": "请输入分片字段名称"}, "formItemProps": {"hidden": "${shardingEnabled != true}"}, "id": "u:04f3880a8a88", "label": "分片字段", "name": "shardingField", "tooltip": "用于分片的字段名称", "trim": true, "type": "text"}, {"fieldProps": {"placeholder": "请输入插件列表，JSON格式"}, "id": "u:1ac7f07c86ac", "label": "插件列表", "name": "plugins", "tooltip": "节点插件配置，JSON格式", "trim": true, "type": "textarea"}, {"fieldProps": {"placeholder": "请输入关联关系，JSON格式"}, "id": "u:62b113ceece6", "label": "关联关系", "name": "relations", "tooltip": "节点间的关联关系配置，JSON格式", "trim": true, "type": "textarea"}, {"fieldProps": {"placeholder": "请输入索引数据"}, "id": "u:67c02dd390d0", "label": "索引数据", "name": "indexData", "tooltip": "索引相关的数据信息", "trim": true, "type": "textarea"}, {"fieldProps": {"placeholder": "请输入索引字段类型"}, "formItemProps": {"hidden": "${enableScan != true}"}, "id": "u:3504e8889600", "label": "索引字段类型", "name": "indexColumType", "tooltip": "索引字段的数据类型", "trim": true, "type": "text"}, {"fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "id": "u:771d7648c347", "initialValue": false, "label": "启用扫描", "name": "enableScan", "tooltip": "是否启用索引扫描模式", "type": "select", "linkage": [{"event": "onChange", "resetKeys": ["indexColumn", "indexColumType", "indexStart", "indexEnd"]}]}, {"fieldProps": {"placeholder": "请输入索引开始值"}, "formItemProps": {"hidden": "${enableScan != true}"}, "id": "u:5475f8caa37d", "label": "索引开始值", "name": "indexStart", "tooltip": "索引扫描的起始值", "type": "number"}, {"fieldProps": {"placeholder": "请输入索引结束值"}, "formItemProps": {"hidden": "${enableScan != true}"}, "id": "u:738665d9bec6", "label": "索引结束值", "name": "indexEnd", "tooltip": "索引扫描的结束值", "type": "number"}, {"fieldProps": {"placeholder": "请输入保留天数", "min": 0}, "id": "u:b929f26710a2", "label": "保留天数", "name": "reserveDays", "tooltip": "数据保留的天数", "type": "number"}, {"formItemProps": {"hidden": true}, "id": "u:debug_mode", "initialValue": false, "label": "调试模式", "name": "debugMode", "type": "text"}], "id": "u:daf300074416", "onSubmit": "// 表单提交前的数据处理\nconst formData = values;\n\n// 处理分片配置\nif (!formData.shardingEnabled) {\n  formData.shardingExpression = null;\n  formData.shardingPluginClass = null;\n  formData.shardingField = null;\n} else {\n  // 确保分片表达式和插件类只填写一个\n  if (formData.shardingExpression && formData.shardingPluginClass) {\n    const confirmed = confirm('分片表达式和分片插件类只能填写一个，请选择保留哪个？');\n    if (confirmed) {\n      formData.shardingPluginClass = null;\n    } else {\n      formData.shardingExpression = null;\n    }\n  }\n}\n\n// 处理索引范围\nif (formData.indexSpan) {\n  formData.indexStart = formData.indexSpan[0];\n  formData.indexEnd = formData.indexSpan[1];\n  delete formData.indexSpan;\n}\n\n// 设置默认值\nif (formData.status === undefined) {\n  formData.status = 1; // 默认运行状态\n}\nif (formData.isArchive === undefined) {\n  formData.isArchive = true; // 默认归档\n}\nif (formData.debugMode === undefined) {\n  formData.debugMode = false; // 默认关闭调试\n}\nif (formData.archiveType === undefined) {\n  formData.archiveType = 1; // 默认先归档子节点\n}\nif (formData.filterDefaultValue === undefined) {\n  formData.filterDefaultValue = 0; // 默认不过滤\n}\nif (formData.shardingEnabled === undefined) {\n  formData.shardingEnabled = false; // 默认关闭分片\n}\nif (formData.enableScan === undefined) {\n  formData.enableScan = false; // 默认关闭索引扫描\n}\n\nreturn formData;", "type": "form", "wrapperCol": {"style": {"flex": 0.9}}}], "id": "u:4523915379be", "title": "归档节点配置"}