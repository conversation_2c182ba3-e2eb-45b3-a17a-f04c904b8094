{"type": "dialog", "actions": [{"actionType": "cancel", "id": "u:z1x2c3v4b5n6", "label": "取消", "type": "button"}, {"actionType": "submit", "componentId": "u:m7n8o9p0q1r2", "id": "u:a1s2d3f4g5h6", "label": "确认", "level": "primary", "type": "button"}], "body": [{"actions": [], "api": {"messages": {"success": "创建成功"}, "method": "post", "url": "/scm/rulecenter/archive-management/create-node"}, "body": [{"columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data !=null && response.data.length > 0){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${appName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-sources"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据源"}, "formItemProps": {"rules": [{"message": "请选择数据源", "required": true}]}, "id": "u:j1k2l3m4n5o6", "label": "数据源名称", "linkage": [{"event": "onChange", "resetKeys": ["databaseName"], "setValues": {}}], "name": "datasourceName", "type": "select"}], "id": "u:p1q2r3s4t5u6"}, {"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data==null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "datasource": "${datasourceName}"}, "method": "get", "url": "/scm/rulecenter/archive/fetch-data-bases"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择数据库"}, "formItemProps": {"rules": [{"message": "请选择数据库", "required": true}]}, "id": "u:v1w2x3y4z5a6", "label": "数据库名称", "linkage": [{"event": "onChange", "resetKeys": ["tableName"]}], "name": "databaseName", "type": "select"}], "id": "u:b1c2d3e4f5g6"}], "id": "u:h1i2j3k4l5m6", "type": "grid"}, {"columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nresponse.data = response.data == null?[]:response.data.map(v=>{return {label:v,value:v}})\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}"}, "method": "post", "sendOn": "${databaseName!=null && databaseName!=''}", "url": "/scm/rulecenter/archive/fetch-tables"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择表名"}, "formItemProps": {"rules": [{"message": "请选择表名", "required": true}]}, "id": "u:n1o2p3q4r5s6", "label": "表名", "linkage": [{"event": "onChange", "resetKeys": ["indexData"]}], "name": "tableName", "type": "select", "tooltip": "如果是分表，可以选择分表的任何一个"}], "id": "u:t1u2v3w4x5y6"}, {"body": [], "id": "u:z1a2b3c4d5e6"}], "id": "u:f1g2h3i4j5k6", "type": "grid"}, {"columns": [{"body": [{"fieldProps": {"options": [{"label": "是", "value": true}, {"label": "否", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否归档", "required": true}]}, "id": "u:l1m2n3o4p5q6", "label": "是否归档", "name": "isArchive", "type": "select"}], "id": "u:r1s2t3u4v5w6"}, {"body": [{"fieldProps": {"options": [{"label": "先归档子节点", "value": 1}, {"label": "先归档父节点", "value": 2}]}, "hiddenOn": true, "id": "u:x1y2z3a4b5c6", "label": "归档顺序", "name": "archiveType", "type": "select"}], "id": "u:d1e2f3g4h5i6"}], "id": "u:j1k2l3m4n5o6", "type": "grid"}, {"columns": [{"body": [{"fieldProps": {"options": [{"label": "不过滤", "value": 0}, {"label": "过滤", "value": 1}]}, "hiddenOn": "${!isArchive}", "id": "u:p1q2r3s4t5u6", "label": "默认值过滤", "name": "filterDefaultValue", "type": "select"}], "id": "u:v1w2x3y4z5a6"}], "id": "u:b1c2d3e4f5g6", "type": "grid"}, {"id": "u:h1i2j3k4l5m6", "title": "分表配置", "type": "divider"}, {"fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否启用分片", "required": true}]}, "id": "u:n1o2p3q4r5s6", "initialValue": false, "label": "启用分片", "name": "shardingEnabled", "type": "select"}, {"columns": [{"body": [{"fieldProps": {"placeholder": "pink_operate_item_"}, "formItemProps": {"rules": [{"message": "分表表达式格式错误，支持格式：字段名 % 数值 或 前缀${字段名%数值+偏移量}", "test": "function(value) {\n  if (!value) return true;\n  const simplePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\\s*[%]\\s*\\d+$/;\n  const templatePattern = /^[a-zA-Z_][a-zA-Z0-9_]*\\$\\{[a-zA-Z_][a-zA-Z0-9_]*[%]\\d+([+\\-]\\d+)?\\}$/;\n  return simplePattern.test(value.trim()) || templatePattern.test(value.trim());\n}"}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:t1u2v3w4x5y6", "label": "分表前缀", "name": "shardingPrefix", "type": "text", "tooltip": "例如pink_operate_item_"}], "id": "u:z1a2b3c4d5e6"}, {"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data!=null){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name}})\n}\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}", "tableName": "${tableName}"}, "method": "post", "sendOn": "${tableName!=null && tableName != ''}", "url": "/scm/rulecenter/archive/fetch-columns"}, "fieldProps": {"labelInValue": false, "placeholder": "请选择分表字段"}, "formItemProps": {"rules": [{"message": "请选择分表字段", "required": "${shardingEnabled}"}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:f1g2h3i4j5k6", "label": "分片字段", "name": "shardingField", "type": "select"}], "id": "u:l1m2n3o4p5q6"}], "id": "u:r1s2t3u4v5w6", "type": "grid"}, {"columns": [{"body": [{"formItemProps": {"extra": "例如:\"item_id%128\",如果无法使用表达式表述，使用自定义算法"}, "hiddenOn": "${!shardingEnabled}", "id": "u:x1y2z3a4b5c6", "label": "分表算法", "name": "shardingExpression", "trim": true, "type": "text"}], "id": "u:d1e2f3g4h5i6"}, {"body": [{"fieldProps": {"placeholder": "请输入分表插件全类名，如：com.example.ShardingPlugin"}, "formItemProps": {"extra": "如果分表实现非常复杂，使用自定义算法", "hidden": false, "rules": [{"message": "插件类名格式错误，请输入完整的类名", "pattern": "^[a-zA-Z_][a-zA-Z0-9_]*(\\.[a-zA-Z_][a-zA-Z0-9_]*)*$", "required": false}]}, "hiddenOn": "${!shardingEnabled}", "id": "u:j1k2l3m4n5o6", "label": "自定义算法", "name": "shardingPluginClass", "type": "text"}], "id": "u:p1q2r3s4t5u6"}], "id": "u:v1w2x3y4z5a6", "type": "grid"}, {"id": "u:b1c2d3e4f5g6", "title": "索引相关配置", "type": "divider"}, {"fieldProps": {"options": [{"label": "开启", "value": true}, {"label": "关闭", "value": false}]}, "formItemProps": {"rules": [{"message": "请选择是否启用索引扫描", "required": true}]}, "id": "u:h1i2j3k4l5m6", "initialValue": false, "label": "索引扫描", "name": "enableScan", "type": "select"}, {"columns": [{"body": [{"api": {"adaptor": "// 自定义修改 API 返回结果格式\nif(response.data!=null){\n  response.data = response.data.map(v=>{return {label:v.name,value:v.name,type:v.type}})\n}\nreturn response;", "data": {"appName": "${appName}", "dataSourceName": "${datasourceName}", "databaseName": "${databaseName}", "tableName": "${tableName}"}, "method": "post", "sendOn": "${tableName!=null && tableName != ''}", "url": "/scm/rulecenter/archive/fetch-columns"}, "fieldProps": {"labelInValue": true, "placeholder": "请选择索引字段"}, "formItemProps": {"rules": [{"message": "请选择索引字段", "required": false}]}, "hiddenOn": "${!enableScan}", "id": "u:n1o2p3q4r5s6", "label": "索引字段", "linkage": [{"event": "onChange", "resetKeys": ["indexSpan", "reserveDays"], "setValues": {"indexColumType": "${indexData.type}", "indexColumn": "${indexData.value}"}}], "name": "indexData", "type": "select"}], "id": "u:t1u2v3w4x5y6"}, {"body": [{"fieldProps": {"disabled": false}, "formItemProps": {"hidden": false}, "hiddenOn": "${!enableScan}", "id": "u:z1a2b3c4d5e6", "initialValue": "", "label": "索引字段类型", "name": "indexColumType", "proFieldProps": {"mode": "read"}, "trim": true, "type": "text"}], "id": "u:f1g2h3i4j5k6"}], "id": "u:l1m2n3o4p5q6", "type": "grid"}, {"columns": [{"body": [{"fieldProps": {"options": [{"label": "升序", "value": "ASC"}, {"label": "降序", "value": "DESC"}]}, "id": "u:r1s2t3u4v5w6", "initialValue": "ASC", "label": "排序方式", "name": "orderBy", "type": "select"}], "id": "u:x1y2z3a4b5c6"}], "id": "u:d1e2f3g4h5i6", "type": "grid"}, {"fieldProps": {"max": 36500, "min": 1, "placeholder": "请输入保留天数，如：30、90、365"}, "formItemProps": {"rules": [{"required": true}]}, "hiddenOn": "${!(CONTAINS(indexColumType,\"date\")||CONTAINS(indexColumType,\"datetime\")||CONTAINS(indexColumType,\"timestamp\"))}", "id": "u:j1k2l3m4n5o6", "label": "保留天数", "name": "reserveDays", "type": "digit"}, {"endName": "indexEnd", "fieldProps": {"placeholder": ["开始值", "结束值"], "tooltip": "可只填写开始或结束值，如：1000-9999 或 1000- 或 -9999"}, "formItemProps": {"rules": [{"message": "索引值范围格式错误，请输入有效的数字范围", "test": "function(value) {\n  if (!value || !Array.isArray(value)) return true;\n  const [start, end] = value;\n  if (start !== undefined && (isNaN(start) || start < 0)) return false;\n  if (end !== undefined && (isNaN(end) || end < 0)) return false;\n  if (start !== undefined && end !== undefined && start >= end) return false;\n  return true;\n}"}]}, "hiddenOn": "${!(STARTSWITH(indexColumType,\"bigint\")||STARTSWITH(indexColumType,\"int\")||STARTSWITH(indexColumType,\"tinyint\")||STARTSWITH(indexColumType,\"smallint\")||STARTSWITH(indexColumType,\"mediumint\")||STARTSWITH(indexColumType,\"decimal\")||STARTSWITH(indexColumType,\"float\")||STARTSWITH(indexColumType,\"double\")||STARTSWITH(indexColumType,\"varchar\"))}", "id": "u:p1q2r3s4t5u6", "label": "索引值范围", "name": "indexSpan", "startName": "indexStart", "type": "digitRange"}, {"fieldProps": {"placeholder": "如：a.id = b.aid，仅支持等值连接，支持多个字段，如：a.id = b.aid and a.type = b.type", "rows": 3}, "formItemProps": {"hidden": false, "rules": [{"message": "关联关系格式错误，请使用等值连接格式：表别名.字段 = 表别名.字段", "test": "function(value) {\n  if (!value) return true;\n  const relationPattern = /^([a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*(\\s+and\\s+[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*[a-zA-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*)*)$/i;\n  return relationPattern.test(value.trim());\n}"}]}, "hiddenOn": true, "id": "u:v1w2x3y4z5a6", "label": "关联关系", "name": "relations", "type": "textarea"}, {"fieldProps": {"placeholder": "请输入SQL查询条件，如：id > 0 and status = 1 and create_time < '2024-01-01'", "rows": 3}, "formItemProps": {"rules": [{"message": "查询条件格式错误，请检查SQL语法", "test": "function(value) {\n  if (!value) return true;\n  // 简单的SQL条件格式校验\n  const sqlPattern = /^(\\s*[a-zA-Z_][a-zA-Z0-9_]*\\s*[=<>!]+\\s*['\"\\w\\s\\-\\.]+\\s*(and|or)?\\s*)*$/i;\n  return sqlPattern.test(value.trim());\n}"}]}, "id": "u:b1c2d3e4f5g6", "label": "查询条件", "name": "condition", "type": "textarea"}, {"formItemProps": {"hidden": true}, "id": "u:h1i2j3k4l5m6", "label": "taskId", "name": "taskId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:n1o2p3q4r5s6", "label": "nodeId", "name": "nodeId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:t1u2v3w4x5y6", "label": "parentNodeId", "name": "parentNodeId", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:z1a2b3c4d5e6", "initialValue": true, "label": "rootNode", "name": "rootNode", "type": "text"}, {"formItemProps": {"hidden": true}, "id": "u:f1g2h3i4j5k6", "label": "indexColumn", "name": "indexColumn", "type": "text"}], "id": "u:m7n8o9p0q1r2", "onSubmit": "// 表单提交前的数据处理\nconst formData = values;\n\n// 处理分片配置\nif (!formData.shardingEnabled) {\n  formData.shardingExpression = null;\n  formData.shardingPluginClass = null;\n  formData.shardingField = null;\n} else {\n  // 确保分片表达式和插件类只填写一个\n  if (formData.shardingExpression && formData.shardingPluginClass) {\n    const confirmed = confirm('分片表达式和分片插件类只能填写一个，请选择保留哪个？');\n    if (confirmed) {\n      formData.shardingPluginClass = null;\n    } else {\n      formData.shardingExpression = null;\n    }\n  }\n}\n\n// 处理索引范围\nif (formData.indexSpan) {\n  formData.indexStart = formData.indexSpan[0];\n  formData.indexEnd = formData.indexSpan[1];\n  delete formData.indexSpan;\n}\n\n// 设置默认值\nif (formData.status === undefined) {\n  formData.status = 1; // 默认运行状态\n}\nif (formData.isArchive === undefined) {\n  formData.isArchive = true; // 默认归档\n}\nif (formData.debugMode === undefined) {\n  formData.debugMode = false; // 默认关闭调试\n}\nif (formData.archiveType === undefined) {\n  formData.archiveType = 1; // 默认先归档子节点\n}\nif (formData.filterDefaultValue === undefined) {\n  formData.filterDefaultValue = 0; // 默认不过滤\n}\nif (formData.shardingEnabled === undefined) {\n  formData.shardingEnabled = false; // 默认关闭分片\n}\nif (formData.enableScan === undefined) {\n  formData.enableScan = false; // 默认关闭索引扫描\n}\n\nreturn formData;", "type": "form"}], "id": "u:l1m2n3o4p5q6", "size": "lg", "title": "归档节点修改", "width": "1150px"}